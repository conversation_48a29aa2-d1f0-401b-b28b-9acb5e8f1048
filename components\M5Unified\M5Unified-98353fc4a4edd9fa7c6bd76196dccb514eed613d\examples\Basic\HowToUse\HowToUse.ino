
#if defined ( ARDUINO )

#include <Arduino.h>

// If you use SD card, write this.
#include <SD.h>

// If you use SPIFFS, write this.
#include <SPIFFS.h>

#endif

// * The filesystem header must be included before the display library.

//----------------------------------------------------------------

// If you use ATOM Display, write this.
#include <M5AtomDisplay.h>

// If you use Module Display, write this.
#include <M5ModuleDisplay.h>

// If you use Module RCA, write this.
#include <M5ModuleRCA.h>

// If you use Unit GLASS, write this.
#include <M5UnitGLASS.h>

// If you use Unit GLASS2, write this.
#include <M5UnitGLASS2.h>

// If you use Unit OLED, write this.
#include <M5UnitOLED.h>

// If you use Unit Mini OLED, write this.
#include <M5UnitMiniOLED.h>

// If you use Unit LCD, write this.
#include <M5UnitLCD.h>

// If you use UnitRCA (for Video output), write this.
#include <M5UnitRCA.h>

// * The display header must be included before the M5Unified library.

//----------------------------------------------------------------

// Include this to enable the M5 global instance.
#include <M5Unified.h>


extern const uint8_t wav_8bit_44100[46000];

void setup(void)
{
  /// You may output logs to standard output.
  M5_LOGE("this is error LOG");
  M5_LOGW("this is warning LOG");
  M5_LOGI("this is info LOG");
  M5_LOGD("this is debug LOG");
  M5_LOGV("this is verbose LOG");

  auto cfg = M5.config();

#if defined ( ARDUINO )
  cfg.serial_baudrate = 115200;   // default=115200. if "Serial" is not needed, set it to 0.
#endif
  cfg.clear_display = true;  // default=true. clear the screen when begin.
  cfg.output_power  = true;  // default=true. use external port 5V output.
  cfg.internal_imu  = true;  // default=true. use internal IMU.
  cfg.internal_rtc  = true;  // default=true. use internal RTC.
  cfg.internal_spk  = true;  // default=true. use internal speaker.
  cfg.internal_mic  = true;  // default=true. use internal microphone.
  cfg.external_imu  = true;  // default=false. use Unit Accel & Gyro.
  cfg.external_rtc  = true;  // default=false. use Unit RTC.
  cfg.led_brightness = 64;   // default= 0. system LED brightness (0=off / 255=max) (※ not NeoPixel)

  // external speaker setting.
  cfg.external_speaker.module_display = true;  // default=false. use ModuleDisplay AudioOutput
  cfg.external_speaker.hat_spk        = true;  // default=false. use HAT SPK
  cfg.external_speaker.hat_spk2       = true;  // default=false. use HAT SPK2
  cfg.external_speaker.atomic_spk     = true;  // default=false. use ATOMIC SPK
  cfg.external_speaker.module_rca     = false; // default=false. use ModuleRCA AudioOutput

  // external display setting. (Pre-include required)
  cfg.external_display.module_display = true;  // default=true. use ModuleDisplay
  cfg.external_display.atom_display   = true;  // default=true. use AtomDisplay
  cfg.external_display.unit_glass     = false; // default=true. use UnitGLASS
  cfg.external_display.unit_glass2    = false; // default=true. use UnitGLASS2
  cfg.external_display.unit_oled      = false; // default=true. use UnitOLED
  cfg.external_display.unit_mini_oled = false; // default=true. use UnitMiniOLED
  cfg.external_display.unit_lcd       = false; // default=true. use UnitLCD
  cfg.external_display.unit_rca       = false; // default=true. use UnitRCA VideoOutput
  cfg.external_display.module_rca     = false; // default=true. use ModuleRCA VideoOutput
/*
※ Unit OLED, Unit Mini OLED, Unit GLASS2 cannot be distinguished at runtime and may be misidentified as each other.

※ Display with auto-detection
 - module_display
 - atom_display
 - unit_glass
 - unit_glass2
 - unit_oled
 - unit_mini_oled
 - unit_lcd

※ Displays that cannot be auto-detected
 - module_rca
 - unit_rca

※ Note that if you enable a display that cannot be auto-detected, 
   it will operate as if it were connected, even if it is not actually connected.
   When RCA is enabled, it consumes a lot of memory to allocate the frame buffer.
//*/

// Set individual parameters for external displays.
// (※ Use only the items you wish to change. Basically, it can be omitted.)
#if defined ( __M5GFX_M5ATOMDISPLAY__ ) // setting for ATOM Display.
// cfg.atom_display.logical_width  = 1280;
// cfg.atom_display.logical_height = 720;
// cfg.atom_display.refresh_rate   = 60;
#endif
#if defined ( __M5GFX_M5MODULEDISPLAY__ ) // setting for Module Display.
// cfg.module_display.logical_width  = 1280;
// cfg.module_display.logical_height = 720;
// cfg.module_display.refresh_rate   = 60;
#endif
#if defined ( __M5GFX_M5MODULERCA__ ) // setting for Module RCA.
// cfg.module_rca.logical_width  = 216;
// cfg.module_rca.logical_height = 144;
// cfg.module_rca.signal_type    = M5ModuleRCA::signal_type_t::PAL;
// cfg.module_rca.use_psram      = M5ModuleRCA::use_psram_t::psram_use;
#endif
#if defined ( __M5GFX_M5UNITRCA__ ) // setting for Unit RCA.
// cfg.unit_rca.logical_width  = 216;
// cfg.unit_rca.logical_height = 144;
// cfg.unit_rca.signal_type    = M5UnitRCA::signal_type_t::PAL;
// cfg.unit_rca.use_psram      = M5UnitRCA::use_psram_t::psram_use;
#endif
#if defined ( __M5GFX_M5UNITGLASS__ ) // setting for Unit GLASS.
// cfg.unit_glass.pin_sda  = GPIO_NUM_21;
// cfg.unit_glass.pin_scl  = GPIO_NUM_22;
// cfg.unit_glass.i2c_addr = 0x3D;
// cfg.unit_glass.i2c_freq = 400000;
// cfg.unit_glass.i2c_port = I2C_NUM_0;
#endif
#if defined ( __M5GFX_M5UNITGLASS2__ ) // setting for Unit GLASS2.
// cfg.unit_glass2.pin_sda  = GPIO_NUM_21;
// cfg.unit_glass2.pin_scl  = GPIO_NUM_22;
// cfg.unit_glass2.i2c_addr = 0x3C;
// cfg.unit_glass2.i2c_freq = 400000;
// cfg.unit_glass2.i2c_port = I2C_NUM_0;
#endif
#if defined ( __M5GFX_M5UNITOLED__ ) // setting for Unit OLED.
// cfg.unit_oled.pin_sda  = GPIO_NUM_21;
// cfg.unit_oled.pin_scl  = GPIO_NUM_22;
// cfg.unit_oled.i2c_addr = 0x3C;
// cfg.unit_oled.i2c_freq = 400000;
// cfg.unit_oled.i2c_port = I2C_NUM_0;
#endif
#if defined ( __M5GFX_M5UNITMINIOLED__ ) // setting for Unit Mini OLED.
// cfg.unit_mini_oled.pin_sda  = GPIO_NUM_21;
// cfg.unit_mini_oled.pin_scl  = GPIO_NUM_22;
// cfg.unit_mini_oled.i2c_addr = 0x3C;
// cfg.unit_mini_oled.i2c_freq = 400000;
// cfg.unit_mini_oled.i2c_port = I2C_NUM_0;
#endif
#if defined ( __M5GFX_M5UNITLCD__ ) // setting for Unit LCD.
// cfg.unit_lcd.pin_sda  = GPIO_NUM_21;
// cfg.unit_lcd.pin_scl  = GPIO_NUM_22;
// cfg.unit_lcd.i2c_addr = 0x3E;
// cfg.unit_lcd.i2c_freq = 400000;
// cfg.unit_lcd.i2c_port = I2C_NUM_0;
#endif

  // begin M5Unified.
  M5.begin(cfg);


// If an external display is to be used as the main display, it can be listed in order of priority.
  M5.setPrimaryDisplayType( {
      m5::board_t::board_M5ModuleDisplay,
      m5::board_t::board_M5AtomDisplay,
//    m5::board_t::board_M5ModuleRCA,
//    m5::board_t::board_M5UnitGLASS,
//    m5::board_t::board_M5UnitGLASS2,
//    m5::board_t::board_M5UnitMiniOLED,
//    m5::board_t::board_M5UnitOLED,
//    m5::board_t::board_M5UnitLCD,
//    m5::board_t::board_M5UnitRCA,
  } );

  if (M5.Speaker.isEnabled())
  {
    /// set master volume (0~255)
    M5.Speaker.setVolume(64);

    /// play beep sound 2000Hz 100msec (background task)
    M5.Speaker.tone(2000, 100);

    /// wait done
    while (M5.Speaker.isPlaying()) { M5.delay(1); }

    /// play beep sound 1000Hz 100msec (background task)
    M5.Speaker.tone(1000, 100);

    /// wait play beep sound 2000Hz 100msec (background task)
    while (M5.Speaker.isPlaying()) { M5.delay(1); }


    M5.Speaker.playRaw(wav_8bit_44100, sizeof(wav_8bit_44100), 44100, false);
  }

  if (M5.Rtc.isEnabled())
  {
// It is recommended to set UTC for the RTC and ESP32 internal clocks.
//  rtc direct setting.    YYYY  MM  DD      hh  mm  ss
//  M5.Rtc.setDateTime( {{ 2021, 12, 31 }, { 12, 34, 56 }} );
  }

  /// For models with EPD : refresh control
  M5.Display.setEpdMode(epd_mode_t::epd_fastest); // fastest but very-low quality.
//M5.Display.setEpdMode(epd_mode_t::epd_fast   ); // fast but low quality.
//M5.Display.setEpdMode(epd_mode_t::epd_text   ); // slow but high quality. (for text)
//M5.Display.setEpdMode(epd_mode_t::epd_quality); // slow but high quality. (for image)

  /// For models with LCD : backlight control (0~255)
  M5.Display.setBrightness(128);

  if (M5.Display.width() < M5.Display.height())
  { /// Landscape mode.
    M5.Display.setRotation(M5.Display.getRotation() ^ 1);
  }

  // multi display.
  size_t display_count = M5.getDisplayCount();
  for (int i = 0; i < display_count; ++i) {
    M5.Displays(i).startWrite();
    for (int y = 0; y < 128; ++y)
    {
      for (int x = 0; x < 128; ++x)
      {
        M5.Displays(i).writePixel(x, y, M5.Display.color888(x*2,x+y,y*2));
      }
    }
    M5.Displays(i).printf("Display %d\n", i);
    M5.Displays(i).endWrite();
  }

  int textsize = M5.Display.height() / 160;
  if (textsize == 0) { textsize = 1; }
  M5.Display.setTextSize(textsize);

  // run-time branch : hardware model check
  const char* name;
  switch (M5.getBoard())
  {
#if defined (CONFIG_IDF_TARGET_ESP32S3)
  case m5::board_t::board_M5StackCoreS3:
    name = "StackCoreS3";
    break;
  case m5::board_t::board_M5StampS3:
    name = "StampS3";
    break;
  case m5::board_t::board_M5AtomS3U:
    name = "ATOMS3U";
    break;
  case m5::board_t::board_M5AtomS3Lite:
    name = "ATOMS3Lite";
    break;
  case m5::board_t::board_M5AtomS3:
    name = "ATOMS3";
    break;
  case m5::board_t::board_M5Dial:
    name = "Dial";
    break;
  case m5::board_t::board_M5DinMeter:
    name = "DinMeter";
    break;
  case m5::board_t::board_M5Capsule:
    name = "Capsule";
    break;
#elif defined (CONFIG_IDF_TARGET_ESP32C3)
  case m5::board_t::board_M5StampC3:
    name = "StampC3";
    break;
  case m5::board_t::board_M5StampC3U:
    name = "StampC3U";
    break;
#else
  case m5::board_t::board_M5Stack:
    name = "Stack";
    break;
  case m5::board_t::board_M5StackCore2:
    name = "StackCore2";
    break;
  case m5::board_t::board_M5StickC:
    name = "StickC";
    break;
  case m5::board_t::board_M5StickCPlus:
    name = "StickCPlus";
    break;
  case m5::board_t::board_M5StickCPlus2:
    name = "StickCPlus";
    break;
  case m5::board_t::board_M5StackCoreInk:
    name = "CoreInk";
    break;
  case m5::board_t::board_M5Paper:
    name = "Paper";
    break;
  case m5::board_t::board_M5Tough:
    name = "Tough";
    break;
  case m5::board_t::board_M5Station:
    name = "Station";
    break;
  case m5::board_t::board_M5Atom:
    name = "ATOM";
    break;
  case m5::board_t::board_M5AtomPsram:
    name = "ATOM PSRAM";
    break;
  case m5::board_t::board_M5AtomU:
    name = "ATOM U";
    break;
  case m5::board_t::board_M5TimerCam:
    name = "TimerCamera";
    break;
  case m5::board_t::board_M5StampPico:
    name = "StampPico";
    break;
#endif
  default:
    name = "Who am I ?";
    break;
  }
  M5.Display.startWrite();
  M5.Display.print("Core:");
  M5.Display.println(name);
  M5_LOGI("core:%s", name);

  // run-time branch : imu model check
  switch (M5.Imu.getType())
  {
  case m5::imu_t::imu_mpu6050:
    name = "MPU6050";
    break;
  case m5::imu_t::imu_mpu6886:
    name = "MPU6886";
    break;
  case m5::imu_t::imu_mpu9250:
    name = "MPU9250";
    break;
  case m5::imu_t::imu_bmi270:
    name = "BMI270";
    break;
  case m5::imu_t::imu_sh200q:
    name = "SH200Q";
    break;
  default:
    name = "none";
    break;
  }
  M5.Display.print("IMU:");
  M5.Display.println(name);
  M5.Display.endWrite();
  M5_LOGI("imu:%s", name);
}

void loop(void)
{
  M5.delay(1);
  int h = M5.Display.height() / 8;

  M5.update();  

//------------------- Button test
/*
/// List of available buttons:
  M5Stack BASIC/GRAY/GO/FIRE:  BtnA,BtnB,BtnC
  M5Stack Core2:               BtnA,BtnB,BtnC,BtnPWR
  M5Stick C/CPlus:             BtnA,BtnB,     BtnPWR
  M5Stick CoreInk:             BtnA,BtnB,BtnC,BtnPWR,BtnEXT
  M5Paper:                     BtnA,BtnB,BtnC
  M5Station:                   BtnA,BtnB,BtnC,BtnPWR
  M5Stack CoreS3,Tough:                       BtnPWR
  M5Atom Series:               BtnA
  M5Stamp Series:              BtnA
*/

  static constexpr const int colors[] = { TFT_WHITE, TFT_CYAN, TFT_RED, TFT_YELLOW, TFT_BLUE, TFT_GREEN };
  static constexpr const char* const names[] = { "none", "wasHold", "wasClicked", "wasPressed", "wasReleased", "wasDeciedCount" };

  M5.Display.startWrite();

  /// BtnPWR: "wasClicked"/"wasHold"  can be use.
  /// BtnPWR of CoreInk: "isPressed"/"wasPressed"/"isReleased"/"wasReleased"/"wasClicked"/"wasHold"/"isHolding"  can be use.
  int state = M5.BtnPWR.wasHold() ? 1
            : M5.BtnPWR.wasClicked() ? 2
            : M5.BtnPWR.wasPressed() ? 3
            : M5.BtnPWR.wasReleased() ? 4
            : M5.BtnPWR.wasDecideClickCount() ? 5
            : 0;
  if (state)
  {
    M5.Speaker.tone(783.991, 100);
    M5_LOGI("BtnPWR:%s  count:%d", names[state], M5.BtnPWR.getClickCount());
    if (!M5.Display.displayBusy())
    {
      M5.Display.fillRect(0, h*2, h, h-1, colors[state]);
      M5.Display.setCursor(0, h*2);
      M5.Display.printf("%d", M5.BtnPWR.getClickCount());
    }
  }

  /// BtnA,BtnB,BtnC,BtnEXT: "isPressed"/"wasPressed"/"isReleased"/"wasReleased"/"wasClicked"/"wasHold"/"isHolding"  can be use.
  state = M5.BtnA.wasHold() ? 1
        : M5.BtnA.wasClicked() ? 2
        : M5.BtnA.wasPressed() ? 3
        : M5.BtnA.wasReleased() ? 4
        : M5.BtnA.wasDecideClickCount() ? 5
        : 0;
  if (state)
  {
    M5.Speaker.tone(523.251, 100);
    M5_LOGI("BtnA:%s  count:%d", names[state], M5.BtnA.getClickCount());
    if (!M5.Display.displayBusy())
    {
      M5.Display.fillRect(0, h*3, h, h-1, colors[state]);
      M5.Display.setCursor(0, h*3);
      M5.Display.printf("%d", M5.BtnA.getClickCount());
    }
  }

  state = M5.BtnB.wasHold() ? 1
        : M5.BtnB.wasClicked() ? 2
        : M5.BtnB.wasPressed() ? 3
        : M5.BtnB.wasReleased() ? 4
        : M5.BtnB.wasDecideClickCount() ? 5
        : 0;
  if (state)
  {
    M5.Speaker.tone(587.330, 100);
    M5_LOGI("BtnB:%s  count:%d", names[state], M5.BtnB.getClickCount());
    if (!M5.Display.displayBusy())
    {
      M5.Display.fillRect(0, h*4, h, h-1, colors[state]);
      M5.Display.setCursor(0, h*4);
      M5.Display.printf("%d", M5.BtnB.getClickCount());
    }
  }

  state = M5.BtnC.wasHold() ? 1
        : M5.BtnC.wasClicked() ? 2
        : M5.BtnC.wasPressed() ? 3
        : M5.BtnC.wasReleased() ? 4
        : M5.BtnC.wasDecideClickCount() ? 5
        : 0;
  if (state)
  {
    M5.Speaker.tone(659.255, 100);
    M5_LOGI("BtnC:%s  count:%d", names[state], M5.BtnC.getClickCount());
    if (!M5.Display.displayBusy())
    {
      M5.Display.fillRect(0, h*5, h, h-1, colors[state]);
      M5.Display.setCursor(0, h*5);
      M5.Display.printf("%d", M5.BtnC.getClickCount());
    }
  }

  state = M5.BtnEXT.wasHold() ? 1
        : M5.BtnEXT.wasClicked() ? 2
        : M5.BtnEXT.wasPressed() ? 3
        : M5.BtnEXT.wasReleased() ? 4
        : M5.BtnEXT.wasDecideClickCount() ? 5
        : 0;
  if (state)
  {
    M5.Speaker.tone(698.456, 100);
    M5_LOGI("BtnEXT:%s  count:%d", names[state], M5.BtnEXT.getClickCount());
    if (!M5.Display.displayBusy())
    {
      M5.Display.fillRect(0, h*6, h, h-1, colors[state]);
      M5.Display.setCursor(0, h*6);
      M5.Display.printf("%d", M5.BtnEXT.getClickCount());
    }
  }

  M5.Display.endWrite();

  if (!M5.Display.displayBusy())
  {
    static uint32_t prev_sec;
    uint32_t sec = m5gfx::millis() / 1000;
    if (prev_sec != sec)
    {
      prev_sec = sec;

//------------------- Battery level
      static int prev_battery = INT_MAX;
      int battery = M5.Power.getBatteryLevel();
      if (prev_battery != battery)
      {
        prev_battery = battery;
        M5.Display.startWrite();
        M5.Display.setCursor(0, M5.Display.fontHeight() * 3);
        M5.Display.print("Bat:");
        if (battery >= 0)
        {
          M5.Display.printf("%03d", battery);
        }
        else
        {
          M5.Display.print("none");
        }
        M5.Display.endWrite();
      }
//------------------- RTC test
      if (M5.Rtc.isEnabled())
      {
        static constexpr const char* const wd[] = {"Sun","Mon","Tue","Wed","Thr","Fri","Sat","ERR"};
        char buf[32];
//*
        /// Get the date and time from the RTC and display it.
        m5::rtc_datetime_t dt;
        if (M5.Rtc.getDateTime(&dt))
        {
          M5.Display.startWrite();
          snprintf( buf, 30, "%04d/%02d/%02d(%s)"
                  , dt.date.year
                  , dt.date.month
                  , dt.date.date
                  , wd[dt.date.weekDay & 7]
                  );
          M5.Display.drawString(buf, M5.Display.width() / 2, 0);
          snprintf( buf, 30, "%02d:%02d:%02d"
                  , dt.time.hours
                  , dt.time.minutes
                  , dt.time.seconds
                  );
          M5.Display.drawString(buf, M5.Display.width() / 2, M5.Display.fontHeight());
          M5.Display.endWrite();
        }
        else
        {
          M5.Display.drawString("RTC error", M5.Display.width() / 2, M5.Display.fontHeight()>>1);
        }
/*/
/// In the example above, the date and time are obtained through I2C communication with the RTC.
/// However, since M5Unified synchronizes the ESP32's internal clock at startup,
/// it is also possible to get the date and time, as shown in the example below.
/// ※ Note: that there will be an error of a few seconds per day.
///    You may want to call M5.Rtc.setSystemTimeFromRtc() periodically to synchronize.
      auto t = time(nullptr);
      auto time = localtime(&t);
      M5.Display.startWrite();
      snprintf( buf, 30, "%04d/%02d/%02d(%s)"
              , time->tm_year + 1900
              , time->tm_mon + 1
              , time->tm_mday
              , wd[time->tm_wday & 7]
              );
      M5.Display.drawString(buf, M5.Display.width() / 2, 0);
      snprintf( buf, 30, "%02d:%02d:%02d"
              , time->tm_hour
              , time->tm_min
              , time->tm_sec
              );
      M5.Display.drawString(buf, M5.Display.width() / 2, M5.Display.fontHeight());
      M5.Display.endWrite();
//*/
      }
    }
  }

//------------------- IMU test
  if (M5.Imu.isEnabled())
  {
    int ox = (M5.Display.width()+h)>>1;
    static int prev_xpos[6];
    int xpos[6];
    float val[6];
    M5.Imu.getAccel(&val[0], &val[1], &val[2]);
    M5.Imu.getGyro(&val[3], &val[4], &val[5]);
    int color[6] = { TFT_RED, TFT_GREEN, TFT_BLUE, TFT_RED, TFT_GREEN, TFT_BLUE };

    for (int i = 0; i < 3; ++i)
    {
      xpos[i]   = val[i] * 50;
      xpos[i+3] = val[i+3] / 2;
    }

    M5.Display.startWrite();
    M5.Display.setClipRect(h, h, M5.Display.width(), M5.Display.height());
    M5.Display.waitDisplay();
    for (int i = 0; i < 6; ++i)
    {
      if (xpos[i] == prev_xpos[i]) continue;

      int px = prev_xpos[i];
      if ((xpos[i] < 0) != (px < 0))
      {
        if (px)
        {
          M5.Display.fillRect(ox, h * (i+2), px, h, M5.Display.getBaseColor());
        }
        px = 0;
      }
      if (xpos[i] != px)
      {
        if ((xpos[i] > px) != (xpos[i] < 0))
        {
          M5.Display.setColor(color[i]);
        }
        else
        {
          M5.Display.setColor(M5.Display.getBaseColor());
        }
        M5.Display.fillRect(xpos[i] + ox, h * (i+2), px - xpos[i], h);
      }
      prev_xpos[i] = xpos[i];
    }
    M5.Display.clearClipRect();

    M5.Display.endWrite();
  }
  M5.Display.display();
}

#if !defined ( ARDUINO ) && defined ( ESP_PLATFORM )
extern "C" {
  void loopTask(void*)
  {
    setup();
    for (;;) {
      loop();
    }
    vTaskDelete(NULL);
  }

  void app_main()
  {
    xTaskCreatePinnedToCore(loopTask, "loopTask", 8192, NULL, 1, NULL, 1);
  }
}
#endif

const uint8_t wav_8bit_44100[46000] = {
0x80, 0x81, 0x83, 0x84, 0x84, 0x83, 0x82, 0x83, 0x84, 0x83, 0x82, 0x82, 0x83, 0x82, 0x82, 0x86,
0x88, 0x89, 0x89, 0x89, 0x87, 0x85, 0x84, 0x84, 0x84, 0x85, 0x88, 0x8d, 0x92, 0x92, 0x90, 0x8e,
0x8b, 0x87, 0x84, 0x82, 0x81, 0x80, 0x80, 0x81, 0x81, 0x80, 0x7e, 0x7c, 0x7b, 0x79, 0x78, 0x7b,
0x7e, 0x7e, 0x7b, 0x79, 0x78, 0x75, 0x73, 0x74, 0x75, 0x73, 0x72, 0x73, 0x76, 0x78, 0x77, 0x79,
0x7d, 0x7c, 0x7a, 0x7b, 0x7d, 0x7f, 0x7e, 0x7f, 0x81, 0x80, 0x7e, 0x7e, 0x80, 0x81, 0x81, 0x81,
0x84, 0x85, 0x83, 0x82, 0x84, 0x86, 0x86, 0x86, 0x8a, 0x8c, 0x8b, 0x8b, 0x8d, 0x8e, 0x8c, 0x8a,
0x8b, 0x8c, 0x89, 0x88, 0x89, 0x8a, 0x89, 0x86, 0x87, 0x88, 0x86, 0x84, 0x84, 0x83, 0x81, 0x80,
0x81, 0x85, 0x86, 0x85, 0x85, 0x83, 0x80, 0x7c, 0x7b, 0x7d, 0x7b, 0x78, 0x76, 0x74, 0x72, 0x6f,
0x6f, 0x72, 0x71, 0x6f, 0x6f, 0x71, 0x72, 0x72, 0x73, 0x77, 0x78, 0x76, 0x76, 0x79, 0x7a, 0x79,
0x78, 0x7a, 0x7b, 0x79, 0x78, 0x7a, 0x7a, 0x78, 0x76, 0x78, 0x7d, 0x7e, 0x80, 0x86, 0x89, 0x89,
0x88, 0x89, 0x8c, 0x8d, 0x8d, 0x91, 0x93, 0x91, 0x8e, 0x8b, 0x8c, 0x8b, 0x87, 0x89, 0x8d, 0x8c,
0x8c, 0x8d, 0x91, 0x93, 0x90, 0x90, 0x94, 0x94, 0x93, 0x95, 0x97, 0x96, 0x92, 0x8f, 0x91, 0x90,
0x8c, 0x8a, 0x88, 0x85, 0x80, 0x7d, 0x80, 0x83, 0x82, 0x81, 0x83, 0x82, 0x7f, 0x7c, 0x80, 0x82,
0x81, 0x80, 0x80, 0x7f, 0x7b, 0x77, 0x77, 0x78, 0x75, 0x72, 0x74, 0x77, 0x77, 0x77, 0x79, 0x7d,
0x7c, 0x7a, 0x7d, 0x80, 0x80, 0x7f, 0x80, 0x83, 0x81, 0x7f, 0x80, 0x82, 0x80, 0x7c, 0x7b, 0x7d,
0x7d, 0x7c, 0x7f, 0x84, 0x84, 0x82, 0x82, 0x86, 0x89, 0x89, 0x8c, 0x92, 0x92, 0x8f, 0x8e, 0x90,
0x90, 0x8c, 0x8b, 0x8d, 0x8d, 0x8c, 0x8d, 0x92, 0x93, 0x90, 0x8e, 0x90, 0x92, 0x91, 0x93, 0x98,
0x9a, 0x98, 0x94, 0x95, 0x95, 0x92, 0x8f, 0x90, 0x8f, 0x8a, 0x85, 0x86, 0x88, 0x85, 0x83, 0x85,
0x86, 0x83, 0x81, 0x84, 0x88, 0x88, 0x87, 0x89, 0x8a, 0x88, 0x84, 0x84, 0x84, 0x80, 0x7b, 0x7b,
0x7d, 0x7c, 0x79, 0x79, 0x7b, 0x78, 0x74, 0x75, 0x7a, 0x7d, 0x7d, 0x7f, 0x82, 0x81, 0x7f, 0x7f,
0x82, 0x82, 0x80, 0x7e, 0x7f, 0x7e, 0x7b, 0x7b, 0x7e, 0x7e, 0x7b, 0x7a, 0x7e, 0x7f, 0x7e, 0x80,
0x86, 0x89, 0x87, 0x88, 0x8c, 0x8e, 0x8b, 0x89, 0x8c, 0x8d, 0x8a, 0x8b, 0x8e, 0x90, 0x8c, 0x89,
0x8a, 0x8b, 0x88, 0x89, 0x8e, 0x92, 0x91, 0x8f, 0x92, 0x93, 0x90, 0x8e, 0x91, 0x93, 0x8f, 0x8c,
0x8e, 0x8f, 0x8b, 0x87, 0x87, 0x89, 0x85, 0x82, 0x85, 0x8a, 0x89, 0x86, 0x88, 0x8a, 0x89, 0x86,
0x88, 0x8b, 0x88, 0x83, 0x82, 0x84, 0x82, 0x7f, 0x7e, 0x7f, 0x7a, 0x73, 0x72, 0x74, 0x75, 0x74,
0x76, 0x7b, 0x7b, 0x78, 0x78, 0x7d, 0x7e, 0x7c, 0x7d, 0x80, 0x80, 0x7d, 0x7b, 0x7c, 0x7b, 0x75,
0x72, 0x73, 0x75, 0x73, 0x73, 0x77, 0x79, 0x77, 0x77, 0x7b, 0x7f, 0x80, 0x80, 0x83, 0x85, 0x83,
0x83, 0x87, 0x89, 0x87, 0x83, 0x83, 0x83, 0x80, 0x7f, 0x81, 0x85, 0x85, 0x83, 0x86, 0x89, 0x88,
0x87, 0x8b, 0x8f, 0x8f, 0x8e, 0x90, 0x93, 0x92, 0x8d, 0x8c, 0x8b, 0x88, 0x84, 0x84, 0x87, 0x88,
0x85, 0x84, 0x85, 0x84, 0x81, 0x82, 0x87, 0x88, 0x85, 0x85, 0x87, 0x86, 0x83, 0x81, 0x82, 0x80,
0x7a, 0x76, 0x76, 0x75, 0x72, 0x70, 0x71, 0x72, 0x6e, 0x6c, 0x6f, 0x71, 0x71, 0x71, 0x74, 0x77,
0x77, 0x76, 0x78, 0x78, 0x73, 0x6f, 0x6e, 0x6e, 0x6d, 0x6c, 0x6e, 0x6f, 0x6c, 0x69, 0x6a, 0x6e,
0x70, 0x70, 0x75, 0x7a, 0x7a, 0x7b, 0x7d, 0x80, 0x81, 0x7f, 0x7f, 0x81, 0x80, 0x7e, 0x7f, 0x80,
0x80, 0x7d, 0x7c, 0x80, 0x80, 0x7e, 0x80, 0x84, 0x86, 0x86, 0x87, 0x8d, 0x8f, 0x8d, 0x8c, 0x8c,
0x8b, 0x88, 0x86, 0x89, 0x8b, 0x86, 0x84, 0x83, 0x81, 0x7e, 0x7b, 0x7e, 0x81, 0x7f, 0x7f, 0x81,
0x82, 0x80, 0x7f, 0x7f, 0x80, 0x7d, 0x7a, 0x7b, 0x7c, 0x79, 0x75, 0x73, 0x72, 0x6e, 0x69, 0x6b,
0x6d, 0x6c, 0x6a, 0x6b, 0x6f, 0x6f, 0x6c, 0x6f, 0x73, 0x71, 0x6e, 0x6f, 0x70, 0x70, 0x6d, 0x6e,
0x70, 0x6e, 0x69, 0x68, 0x69, 0x69, 0x68, 0x69, 0x6e, 0x70, 0x6f, 0x71, 0x76, 0x78, 0x78, 0x79,
0x7d, 0x7f, 0x7e, 0x7f, 0x81, 0x81, 0x7f, 0x7c, 0x7d, 0x7d, 0x7b, 0x7b, 0x7e, 0x7f, 0x7e, 0x7d,
0x80, 0x83, 0x83, 0x84, 0x87, 0x89, 0x89, 0x88, 0x8a, 0x8c, 0x8a, 0x88, 0x88, 0x87, 0x84, 0x80,
0x80, 0x80, 0x7f, 0x7c, 0x7d, 0x7f, 0x7e, 0x7c, 0x7c, 0x7d, 0x7c, 0x7a, 0x7c, 0x7f, 0x7e, 0x7c,
0x7a, 0x7a, 0x77, 0x71, 0x70, 0x72, 0x70, 0x6e, 0x6d, 0x6d, 0x6c, 0x69, 0x69, 0x6d, 0x6e, 0x6d,
0x6d, 0x6f, 0x70, 0x6f, 0x6e, 0x71, 0x70, 0x6c, 0x6a, 0x6b, 0x6c, 0x6a, 0x68, 0x6b, 0x6c, 0x6a,
0x6b, 0x6f, 0x71, 0x71, 0x72, 0x76, 0x79, 0x7a, 0x7b, 0x7f, 0x80, 0x80, 0x7d, 0x7d, 0x7f, 0x7e,
0x7d, 0x7e, 0x80, 0x7f, 0x7c, 0x7d, 0x80, 0x7f, 0x7e, 0x80, 0x83, 0x84, 0x84, 0x86, 0x89, 0x8a,
0x87, 0x88, 0x89, 0x88, 0x85, 0x85, 0x86, 0x84, 0x80, 0x7f, 0x7f, 0x7e, 0x7c, 0x7b, 0x7d, 0x7d,
0x7a, 0x7a, 0x7e, 0x7e, 0x7e, 0x7d, 0x7e, 0x7e, 0x7a, 0x78, 0x7a, 0x79, 0x77, 0x75, 0x75, 0x74,
0x70, 0x6e, 0x6f, 0x6f, 0x6d, 0x6d, 0x6f, 0x72, 0x71, 0x70, 0x72, 0x73, 0x70, 0x6f, 0x71, 0x73,
0x72, 0x70, 0x71, 0x71, 0x6e, 0x6c, 0x6e, 0x70, 0x70, 0x6f, 0x70, 0x73, 0x73, 0x74, 0x77, 0x7b,
0x7c, 0x7a, 0x7b, 0x7e, 0x7e, 0x7d, 0x7f, 0x80, 0x80, 0x7e, 0x7d, 0x7f, 0x7e, 0x7c, 0x7d, 0x80,
0x80, 0x80, 0x80, 0x84, 0x85, 0x84, 0x85, 0x87, 0x88, 0x88, 0x88, 0x8b, 0x8b, 0x88, 0x86, 0x86,
0x85, 0x82, 0x80, 0x81, 0x81, 0x7f, 0x7d, 0x7f, 0x80, 0x7f, 0x7f, 0x80, 0x80, 0x80, 0x7e, 0x80,
0x80, 0x7f, 0x7e, 0x7e, 0x7d, 0x7b, 0x77, 0x77, 0x77, 0x74, 0x72, 0x71, 0x73, 0x72, 0x70, 0x71,
0x73, 0x71, 0x6f, 0x71, 0x73, 0x74, 0x73, 0x74, 0x75, 0x73, 0x70, 0x70, 0x70, 0x70, 0x6f, 0x6f,
0x71, 0x71, 0x6f, 0x71, 0x73, 0x74, 0x74, 0x75, 0x79, 0x7b, 0x7a, 0x7c, 0x7f, 0x80, 0x7e, 0x7e,
0x80, 0x80, 0x7e, 0x7e, 0x80, 0x80, 0x7f, 0x7f, 0x80, 0x82, 0x81, 0x81, 0x83, 0x85, 0x85, 0x86,
0x8a, 0x8d, 0x8c, 0x8b, 0x8c, 0x8b, 0x89, 0x87, 0x88, 0x89, 0x86, 0x83, 0x84, 0x83, 0x81, 0x80,
0x80, 0x81, 0x81, 0x80, 0x82, 0x84, 0x84, 0x83, 0x83, 0x83, 0x82, 0x80, 0x80, 0x81, 0x7f, 0x7b,
0x7a, 0x79, 0x78, 0x75, 0x74, 0x75, 0x73, 0x71, 0x71, 0x72, 0x73, 0x74, 0x74, 0x76, 0x76, 0x74,
0x74, 0x75, 0x75, 0x73, 0x73, 0x74, 0x73, 0x71, 0x70, 0x71, 0x71, 0x70, 0x70, 0x72, 0x74, 0x74,
0x76, 0x79, 0x7a, 0x7a, 0x7b, 0x7d, 0x7f, 0x7f, 0x80, 0x80, 0x80, 0x80, 0x7f, 0x80, 0x81, 0x80,
0x80, 0x81, 0x82, 0x81, 0x82, 0x85, 0x88, 0x88, 0x88, 0x8b, 0x8c, 0x8c, 0x8c, 0x8d, 0x8e, 0x8d,
0x8b, 0x8b, 0x8c, 0x89, 0x87, 0x86, 0x86, 0x84, 0x82, 0x83, 0x86, 0x86, 0x85, 0x85, 0x86, 0x86,
0x84, 0x84, 0x87, 0x86, 0x84, 0x83, 0x82, 0x80, 0x7e, 0x7c, 0x7c, 0x7a, 0x77, 0x75, 0x76, 0x76,
0x75, 0x74, 0x76, 0x76, 0x75, 0x75, 0x76, 0x77, 0x77, 0x77, 0x77, 0x77, 0x75, 0x74, 0x75, 0x74,
0x72, 0x70, 0x70, 0x71, 0x70, 0x71, 0x73, 0x74, 0x74, 0x74, 0x76, 0x79, 0x7a, 0x7c, 0x7f, 0x80,
0x80, 0x80, 0x80, 0x82, 0x81, 0x80, 0x81, 0x82, 0x82, 0x81, 0x83, 0x85, 0x85, 0x84, 0x86, 0x88,
0x89, 0x89, 0x8c, 0x8e, 0x8f, 0x8e, 0x8e, 0x90, 0x8f, 0x8e, 0x8d, 0x8d, 0x8c, 0x88, 0x88, 0x89,
0x89, 0x87, 0x87, 0x87, 0x87, 0x85, 0x85, 0x88, 0x8a, 0x89, 0x88, 0x89, 0x89, 0x86, 0x84, 0x84,
0x83, 0x80, 0x7e, 0x7e, 0x7d, 0x7b, 0x78, 0x77, 0x77, 0x75, 0x73, 0x75, 0x77, 0x78, 0x77, 0x78,
0x79, 0x78, 0x77, 0x77, 0x78, 0x77, 0x75, 0x75, 0x74, 0x73, 0x71, 0x71, 0x72, 0x72, 0x70, 0x70,
0x72, 0x73, 0x75, 0x78, 0x7b, 0x7c, 0x7d, 0x7f, 0x80, 0x81, 0x81, 0x83, 0x84, 0x84, 0x84, 0x85,
0x85, 0x84, 0x83, 0x83, 0x84, 0x84, 0x85, 0x87, 0x8a, 0x8b, 0x8b, 0x8c, 0x8f, 0x90, 0x8f, 0x91,
0x93, 0x92, 0x90, 0x8f, 0x90, 0x8f, 0x8c, 0x8b, 0x8b, 0x8a, 0x88, 0x87, 0x89, 0x8a, 0x89, 0x89,
0x8a, 0x8b, 0x89, 0x88, 0x89, 0x8a, 0x88, 0x85, 0x85, 0x85, 0x82, 0x80, 0x7f, 0x7e, 0x7a, 0x77,
0x77, 0x78, 0x78, 0x77, 0x78, 0x79, 0x78, 0x76, 0x77, 0x79, 0x7a, 0x79, 0x78, 0x79, 0x79, 0x76,
0x75, 0x75, 0x73, 0x71, 0x6f, 0x70, 0x71, 0x70, 0x71, 0x73, 0x74, 0x74, 0x77, 0x79, 0x7c, 0x7e,
0x80, 0x81, 0x82, 0x83, 0x85, 0x85, 0x86, 0x85, 0x84, 0x84, 0x84, 0x83, 0x84, 0x86, 0x87, 0x87,
0x87, 0x88, 0x89, 0x8a, 0x8c, 0x90, 0x91, 0x92, 0x93, 0x94, 0x94, 0x92, 0x92, 0x92, 0x91, 0x8f,
0x8e, 0x8e, 0x8e, 0x8c, 0x8a, 0x8b, 0x8c, 0x8c, 0x8c, 0x8d, 0x8f, 0x8d, 0x8c, 0x8e, 0x8e, 0x8c,
0x8b, 0x8a, 0x89, 0x86, 0x84, 0x84, 0x85, 0x83, 0x80, 0x7e, 0x7d, 0x7b, 0x78, 0x79, 0x7c, 0x7f,
0x81, 0x84, 0x86, 0x85, 0x82, 0x7f, 0x7e, 0x7b, 0x77, 0x74, 0x73, 0x74, 0x73, 0x71, 0x6f, 0x6d,
0x6b, 0x69, 0x69, 0x6d, 0x72, 0x74, 0x76, 0x78, 0x78, 0x78, 0x78, 0x7b, 0x7d, 0x7c, 0x7b, 0x7c,
0x7e, 0x7f, 0x7f, 0x80, 0x80, 0x80, 0x80, 0x80, 0x81, 0x84, 0x85, 0x88, 0x8b, 0x8d, 0x8f, 0x90,
0x92, 0x94, 0x95, 0x95, 0x97, 0x97, 0x97, 0x97, 0x95, 0x94, 0x93, 0x92, 0x91, 0x92, 0x93, 0x94,
0x94, 0x94, 0x94, 0x93, 0x93, 0x93, 0x93, 0x92, 0x92, 0x91, 0x8f, 0x8d, 0x8c, 0x89, 0x87, 0x84,
0x81, 0x7f, 0x7c, 0x7c, 0x7e, 0x7f, 0x7f, 0x7f, 0x7f, 0x7e, 0x7d, 0x7b, 0x7a, 0x79, 0x77, 0x75,
0x75, 0x75, 0x74, 0x71, 0x70, 0x6e, 0x6a, 0x66, 0x67, 0x69, 0x69, 0x6b, 0x70, 0x78, 0x7c, 0x7c,
0x7e, 0x80, 0x80, 0x7f, 0x7f, 0x80, 0x81, 0x80, 0x7f, 0x81, 0x81, 0x7d, 0x7a, 0x7b, 0x7d, 0x7c,
0x7e, 0x85, 0x8a, 0x8a, 0x89, 0x8b, 0x8e, 0x8e, 0x8f, 0x93, 0x97, 0x97, 0x94, 0x95, 0x97, 0x95,
0x92, 0x93, 0x95, 0x95, 0x93, 0x94, 0x97, 0x98, 0x96, 0x96, 0x99, 0x9a, 0x99, 0x99, 0x9b, 0x9c,
0x9b, 0x9a, 0x9a, 0x99, 0x95, 0x90, 0x8d, 0x8a, 0x87, 0x85, 0x86, 0x87, 0x86, 0x85, 0x83, 0x83,
0x82, 0x81, 0x82, 0x83, 0x81, 0x81, 0x80, 0x7f, 0x7c, 0x78, 0x77, 0x74, 0x6f, 0x6d, 0x6d, 0x6c,
0x6a, 0x6a, 0x6c, 0x6e, 0x6f, 0x71, 0x75, 0x77, 0x76, 0x76, 0x7a, 0x7d, 0x7d, 0x7d, 0x7e, 0x7c,
0x77, 0x73, 0x74, 0x76, 0x76, 0x75, 0x78, 0x7a, 0x79, 0x79, 0x7d, 0x82, 0x85, 0x88, 0x8d, 0x92,
0x94, 0x95, 0x97, 0x9a, 0x9a, 0x97, 0x97, 0x98, 0x98, 0x97, 0x96, 0x97, 0x97, 0x96, 0x95, 0x99,
0x9c, 0x9f, 0xa1, 0xa2, 0xa4, 0xa5, 0xa5, 0xa7, 0xa8, 0xa7, 0xa5, 0xa1, 0x9c, 0x99, 0x96, 0x93,
0x92, 0x91, 0x8f, 0x8c, 0x8a, 0x8a, 0x8b, 0x8b, 0x8b, 0x8d, 0x8f, 0x8f, 0x8e, 0x8d, 0x8d, 0x8a,
0x84, 0x81, 0x81, 0x80, 0x7b, 0x76, 0x73, 0x6f, 0x68, 0x66, 0x6a, 0x6d, 0x6c, 0x6d, 0x70, 0x72,
0x72, 0x73, 0x79, 0x7e, 0x7e, 0x7c, 0x7c, 0x7d, 0x7c, 0x79, 0x7a, 0x7c, 0x79, 0x75, 0x76, 0x79,
0x7a, 0x7a, 0x7f, 0x83, 0x86, 0x88, 0x8c, 0x92, 0x94, 0x94, 0x97, 0x9a, 0x9c, 0x9d, 0x9e, 0x9f,
0x9d, 0x98, 0x95, 0x94, 0x93, 0x93, 0x96, 0x98, 0x99, 0x98, 0x99, 0x9c, 0x9d, 0x9f, 0xa3, 0xa5,
0xa3, 0xa2, 0xa2, 0xa4, 0xa1, 0x9d, 0x9b, 0x9a, 0x95, 0x92, 0x92, 0x94, 0x92, 0x8d, 0x8c, 0x8e,
0x8d, 0x8b, 0x8e, 0x92, 0x91, 0x8c, 0x8a, 0x8d, 0x8b, 0x87, 0x85, 0x84, 0x80, 0x76, 0x70, 0x71,
0x71, 0x6d, 0x6c, 0x6f, 0x70, 0x6c, 0x6b, 0x71, 0x76, 0x76, 0x77, 0x7c, 0x80, 0x80, 0x7d, 0x80,
0x80, 0x7c, 0x77, 0x76, 0x78, 0x78, 0x74, 0x75, 0x78, 0x75, 0x73, 0x76, 0x7c, 0x80, 0x81, 0x84,
0x8a, 0x8b, 0x8d, 0x92, 0x97, 0x98, 0x96, 0x93, 0x93, 0x91, 0x8e, 0x90, 0x92, 0x91, 0x8e, 0x8c,
0x8d, 0x8e, 0x8d, 0x90, 0x95, 0x97, 0x99, 0x9c, 0xa1, 0xa2, 0x9f, 0x9e, 0x9f, 0x9c, 0x99, 0x99,
0x9a, 0x99, 0x93, 0x8f, 0x8d, 0x89, 0x84, 0x85, 0x89, 0x8a, 0x86, 0x85, 0x88, 0x89, 0x85, 0x85,
0x88, 0x87, 0x80, 0x7b, 0x7d, 0x7c, 0x77, 0x73, 0x73, 0x72, 0x6c, 0x66, 0x6a, 0x6e, 0x6b, 0x69,
0x6d, 0x73, 0x73, 0x72, 0x76, 0x7c, 0x78, 0x72, 0x73, 0x77, 0x77, 0x74, 0x75, 0x78, 0x73, 0x6a,
0x69, 0x6e, 0x6f, 0x6e, 0x71, 0x79, 0x7d, 0x7b, 0x7e, 0x85, 0x89, 0x88, 0x89, 0x8e, 0x90, 0x8e,
0x8d, 0x90, 0x8f, 0x89, 0x84, 0x85, 0x86, 0x83, 0x83, 0x86, 0x87, 0x86, 0x87, 0x8d, 0x92, 0x92,
0x93, 0x95, 0x95, 0x93, 0x94, 0x98, 0x9a, 0x97, 0x92, 0x8f, 0x8b, 0x84, 0x80, 0x82, 0x83, 0x80,
0x7e, 0x80, 0x80, 0x7d, 0x7c, 0x7f, 0x80, 0x7f, 0x7d, 0x80, 0x83, 0x80, 0x7c, 0x7b, 0x79, 0x73,
0x6d, 0x6d, 0x6f, 0x6c, 0x67, 0x67, 0x69, 0x67, 0x64, 0x67, 0x6c, 0x6d, 0x6a, 0x6b, 0x71, 0x74,
0x72, 0x74, 0x77, 0x76, 0x70, 0x6c, 0x6e, 0x6f, 0x6b, 0x6a, 0x6d, 0x6f, 0x6d, 0x6c, 0x70, 0x75,
0x75, 0x76, 0x7b, 0x80, 0x82, 0x84, 0x87, 0x8b, 0x89, 0x84, 0x83, 0x83, 0x82, 0x80, 0x80, 0x82,
0x80, 0x7d, 0x7d, 0x80, 0x81, 0x81, 0x83, 0x87, 0x89, 0x8c, 0x8e, 0x92, 0x94, 0x94, 0x91, 0x90,
0x8d, 0x8b, 0x89, 0x87, 0x85, 0x82, 0x7f, 0x7c, 0x7a, 0x78, 0x77, 0x76, 0x76, 0x77, 0x79, 0x7b,
0x7d, 0x7e, 0x7e, 0x7d, 0x79, 0x76, 0x75, 0x75, 0x75, 0x72, 0x6f, 0x6d, 0x68, 0x64, 0x63, 0x62,
0x62, 0x61, 0x61, 0x64, 0x67, 0x67, 0x69, 0x6d, 0x6d, 0x6d, 0x6d, 0x6f, 0x72, 0x71, 0x70, 0x71,
0x70, 0x6e, 0x6c, 0x6c, 0x6d, 0x6d, 0x6d, 0x6f, 0x73, 0x76, 0x78, 0x7a, 0x7e, 0x80, 0x7f, 0x7f,
0x81, 0x84, 0x84, 0x84, 0x85, 0x85, 0x82, 0x80, 0x7e, 0x7f, 0x7f, 0x7d, 0x7d, 0x80, 0x80, 0x82,
0x83, 0x86, 0x8a, 0x89, 0x88, 0x8b, 0x8c, 0x8d, 0x8c, 0x8b, 0x8b, 0x87, 0x82, 0x80, 0x7f, 0x7c,
0x79, 0x76, 0x75, 0x76, 0x74, 0x74, 0x77, 0x78, 0x77, 0x76, 0x78, 0x7a, 0x7a, 0x7a, 0x7b, 0x7a,
0x78, 0x74, 0x71, 0x70, 0x6c, 0x68, 0x66, 0x65, 0x64, 0x62, 0x62, 0x63, 0x64, 0x64, 0x64, 0x66,
0x69, 0x6b, 0x6e, 0x71, 0x74, 0x74, 0x73, 0x73, 0x73, 0x72, 0x72, 0x71, 0x71, 0x71, 0x71, 0x72,
0x73, 0x74, 0x74, 0x75, 0x77, 0x79, 0x7c, 0x7f, 0x80, 0x82, 0x83, 0x84, 0x83, 0x82, 0x82, 0x83,
0x81, 0x80, 0x80, 0x81, 0x80, 0x80, 0x81, 0x82, 0x82, 0x81, 0x84, 0x88, 0x89, 0x8a, 0x8c, 0x8e,
0x8d, 0x8a, 0x88, 0x89, 0x88, 0x83, 0x81, 0x81, 0x80, 0x7c, 0x79, 0x79, 0x7a, 0x76, 0x74, 0x76,
0x77, 0x77, 0x77, 0x79, 0x7d, 0x7c, 0x79, 0x79, 0x7a, 0x77, 0x73, 0x71, 0x71, 0x6f, 0x69, 0x67,
0x68, 0x65, 0x60, 0x5f, 0x61, 0x62, 0x62, 0x64, 0x68, 0x6c, 0x6c, 0x6d, 0x70, 0x73, 0x74, 0x74,
0x76, 0x77, 0x77, 0x76, 0x76, 0x77, 0x75, 0x73, 0x73, 0x73, 0x73, 0x75, 0x77, 0x7a, 0x7c, 0x7d,
0x80, 0x80, 0x80, 0x83, 0x85, 0x86, 0x87, 0x87, 0x89, 0x88, 0x86, 0x86, 0x86, 0x84, 0x83, 0x82,
0x84, 0x86, 0x85, 0x86, 0x89, 0x89, 0x88, 0x88, 0x8a, 0x8c, 0x89, 0x87, 0x8b, 0x8a, 0x86, 0x83,
0x83, 0x82, 0x7f, 0x7a, 0x7b, 0x7c, 0x7a, 0x78, 0x79, 0x7b, 0x7a, 0x77, 0x78, 0x7c, 0x7b, 0x78,
0x78, 0x7b, 0x7a, 0x75, 0x73, 0x74, 0x72, 0x6c, 0x68, 0x68, 0x68, 0x64, 0x63, 0x66, 0x68, 0x67,
0x66, 0x69, 0x6d, 0x6e, 0x6f, 0x73, 0x77, 0x78, 0x78, 0x7a, 0x7c, 0x7c, 0x79, 0x78, 0x78, 0x77,
0x76, 0x75, 0x76, 0x78, 0x77, 0x77, 0x79, 0x7b, 0x7c, 0x7e, 0x80, 0x82, 0x85, 0x87, 0x8a, 0x8b,
0x8a, 0x8a, 0x88, 0x87, 0x88, 0x87, 0x86, 0x86, 0x86, 0x86, 0x85, 0x83, 0x84, 0x86, 0x86, 0x87,
0x89, 0x8b, 0x8b, 0x8b, 0x8b, 0x8b, 0x8a, 0x87, 0x85, 0x86, 0x86, 0x82, 0x80, 0x80, 0x80, 0x7e,
0x7b, 0x7b, 0x7b, 0x7a, 0x79, 0x7a, 0x7d, 0x7d, 0x7b, 0x7c, 0x7d, 0x7b, 0x77, 0x74, 0x74, 0x74,
0x70, 0x6e, 0x6f, 0x6e, 0x6a, 0x68, 0x69, 0x6a, 0x6a, 0x69, 0x6c, 0x6f, 0x71, 0x73, 0x76, 0x79,
0x7b, 0x79, 0x79, 0x7c, 0x7d, 0x7b, 0x7a, 0x7a, 0x7a, 0x79, 0x77, 0x77, 0x79, 0x79, 0x79, 0x7a,
0x7d, 0x7f, 0x80, 0x82, 0x86, 0x89, 0x89, 0x8a, 0x8b, 0x8c, 0x8c, 0x8b, 0x8c, 0x8d, 0x8a, 0x87,
0x87, 0x88, 0x86, 0x84, 0x84, 0x86, 0x87, 0x86, 0x87, 0x8a, 0x8c, 0x8b, 0x89, 0x8a, 0x8c, 0x8b,
0x89, 0x8a, 0x8a, 0x88, 0x85, 0x82, 0x82, 0x82, 0x80, 0x7e, 0x7e, 0x7d, 0x7b, 0x7b, 0x7c, 0x7d,
0x7d, 0x7b, 0x7b, 0x7c, 0x7a, 0x78, 0x77, 0x77, 0x75, 0x72, 0x71, 0x72, 0x70, 0x6d, 0x6b, 0x6b,
0x6d, 0x6e, 0x6e, 0x70, 0x72, 0x74, 0x74, 0x76, 0x78, 0x7c, 0x7c, 0x7d, 0x80, 0x7f, 0x7c, 0x7b,
0x7b, 0x7b, 0x7b, 0x7a, 0x7a, 0x7c, 0x7d, 0x7d, 0x7e, 0x80, 0x83, 0x83, 0x84, 0x88, 0x8b, 0x8c,
0x8e, 0x8f, 0x91, 0x90, 0x8e, 0x8d, 0x8d, 0x8b, 0x88, 0x86, 0x86, 0x87, 0x85, 0x84, 0x87, 0x88,
0x86, 0x85, 0x88, 0x8b, 0x8c, 0x8d, 0x8f, 0x91, 0x8f, 0x8c, 0x8c, 0x8b, 0x8a, 0x88, 0x85, 0x84,
0x84, 0x80, 0x7e, 0x7e, 0x7e, 0x7b, 0x79, 0x79, 0x7b, 0x7b, 0x7a, 0x7c, 0x7d, 0x7b, 0x7a, 0x78,
0x78, 0x78, 0x76, 0x75, 0x75, 0x74, 0x71, 0x70, 0x71, 0x71, 0x71, 0x6f, 0x70, 0x72, 0x73, 0x75,
0x77, 0x79, 0x7c, 0x7c, 0x7c, 0x7d, 0x7e, 0x7d, 0x7d, 0x7e, 0x7f, 0x7f, 0x7e, 0x7f, 0x80, 0x80,
0x80, 0x80, 0x81, 0x84, 0x87, 0x89, 0x8a, 0x8c, 0x8e, 0x8e, 0x8e, 0x90, 0x90, 0x8f, 0x8f, 0x90,
0x8e, 0x8b, 0x8a, 0x8a, 0x8a, 0x87, 0x84, 0x86, 0x89, 0x88, 0x88, 0x8b, 0x8f, 0x8f, 0x8d, 0x8e,
0x91, 0x91, 0x8e, 0x8d, 0x8e, 0x8e, 0x8b, 0x87, 0x86, 0x85, 0x81, 0x7e, 0x7c, 0x7b, 0x7b, 0x7a,
0x79, 0x7c, 0x7c, 0x7a, 0x7b, 0x7c, 0x7b, 0x7c, 0x7c, 0x7c, 0x7c, 0x7a, 0x7a, 0x7b, 0x77, 0x74,
0x74, 0x73, 0x72, 0x70, 0x6f, 0x72, 0x75, 0x74, 0x75, 0x78, 0x7a, 0x7c, 0x7c, 0x7d, 0x80, 0x80,
0x80, 0x80, 0x82, 0x82, 0x80, 0x7f, 0x7f, 0x80, 0x80, 0x80, 0x81, 0x84, 0x86, 0x87, 0x87, 0x89,
0x8c, 0x8d, 0x8e, 0x90, 0x91, 0x92, 0x8f, 0x8e, 0x8f, 0x8e, 0x8b, 0x88, 0x87, 0x87, 0x87, 0x85,
0x86, 0x89, 0x8a, 0x89, 0x89, 0x8c, 0x8e, 0x8e, 0x8e, 0x90, 0x90, 0x90, 0x90, 0x8f, 0x8e, 0x8b,
0x87, 0x85, 0x83, 0x7e, 0x7d, 0x7e, 0x7e, 0x7d, 0x7a, 0x7a, 0x7c, 0x7a, 0x79, 0x7c, 0x80, 0x7f,
0x7d, 0x7e, 0x80, 0x80, 0x7e, 0x7b, 0x7a, 0x79, 0x77, 0x76, 0x77, 0x76, 0x71, 0x6d, 0x6b, 0x6b,
0x6b, 0x6d, 0x75, 0x7e, 0x7e, 0x7f, 0x88, 0x97, 0xa2, 0xa3, 0x9a, 0x95, 0x9a, 0x9a, 0x90, 0x89,
0x84, 0x82, 0x82, 0x7d, 0x78, 0x7e, 0x80, 0x7c, 0x77, 0x6f, 0x6c, 0x71, 0x6e, 0x6a, 0x70, 0x76,
0x7f, 0x85, 0x80, 0x80, 0x8e, 0x95, 0x92, 0x8e, 0x8c, 0x96, 0xa3, 0x9c, 0x8f, 0x91, 0x9c, 0xa8,
0xaa, 0x9a, 0x90, 0x9b, 0xa4, 0xa3, 0x9c, 0x8e, 0x89, 0x93, 0x91, 0x7e, 0x6d, 0x66, 0x6a, 0x70,
0x62, 0x50, 0x51, 0x58, 0x5a, 0x57, 0x4a, 0x47, 0x58, 0x61, 0x60, 0x63, 0x64, 0x6d, 0x7c, 0x7a,
0x70, 0x72, 0x77, 0x7f, 0x87, 0x7e, 0x75, 0x83, 0x94, 0x9a, 0x99, 0x8d, 0x8f, 0xa3, 0xa8, 0xa1,
0x9e, 0x9b, 0xa3, 0xb0, 0xa4, 0x91, 0x8c, 0x89, 0x89, 0x87, 0x75, 0x69, 0x6f, 0x73, 0x73, 0x70,
0x62, 0x5f, 0x6a, 0x67, 0x5f, 0x61, 0x63, 0x6b, 0x77, 0x77, 0x76, 0x7f, 0x81, 0x85, 0x8c, 0x87,
0x86, 0x94, 0x9d, 0xa2, 0xaa, 0xa9, 0xad, 0xbb, 0xba, 0xb3, 0xb5, 0xb6, 0xba, 0xc2, 0xb8, 0xb2,
0xb8, 0xb3, 0xaa, 0xa5, 0x94, 0x87, 0x89, 0x81, 0x78, 0x74, 0x6b, 0x69, 0x6f, 0x63, 0x58, 0x59,
0x55, 0x57, 0x5d, 0x55, 0x56, 0x66, 0x6a, 0x6b, 0x6c, 0x63, 0x65, 0x70, 0x6d, 0x6f, 0x79, 0x7a,
0x86, 0x98, 0x97, 0x96, 0x9d, 0xa0, 0xaa, 0xb2, 0xab, 0xae, 0xba, 0xbc, 0xbe, 0xbd, 0xaf, 0xad,
0xad, 0x9f, 0x96, 0x94, 0x89, 0x87, 0x87, 0x7d, 0x77, 0x75, 0x6d, 0x6e, 0x6c, 0x5f, 0x63, 0x6f,
0x6f, 0x71, 0x75, 0x74, 0x7f, 0x85, 0x7c, 0x7d, 0x82, 0x82, 0x90, 0x9a, 0x95, 0x9b, 0xa6, 0xa9,
0xb4, 0xb7, 0xaa, 0xaf, 0xba, 0xbb, 0xbf, 0xbb, 0xb4, 0xbd, 0xbe, 0xae, 0xaa, 0xa4, 0x93, 0x91,
0x8b, 0x7d, 0x7a, 0x74, 0x69, 0x6c, 0x66, 0x58, 0x59, 0x56, 0x4f, 0x54, 0x54, 0x54, 0x63, 0x65,
0x5c, 0x62, 0x66, 0x66, 0x6e, 0x6b, 0x67, 0x75, 0x7e, 0x81, 0x8e, 0x92, 0x8f, 0x9c, 0xa5, 0xa7,
0xab, 0xa9, 0xab, 0xbc, 0xc0, 0xba, 0xbb, 0xb7, 0xb1, 0xb2, 0xa6, 0x95, 0x91, 0x87, 0x7c, 0x7d,
0x79, 0x6e, 0x6d, 0x67, 0x63, 0x65, 0x5b, 0x56, 0x62, 0x63, 0x5f, 0x69, 0x6f, 0x72, 0x7a, 0x76,
0x74, 0x80, 0x80, 0x80, 0x8a, 0x8e, 0x93, 0xa1, 0xa4, 0xa6, 0xad, 0xa9, 0xa9, 0xb6, 0xb7, 0xb3,
0xb8, 0xba, 0xbe, 0xc2, 0xb4, 0xa7, 0xa6, 0x98, 0x88, 0x82, 0x78, 0x6f, 0x6c, 0x62, 0x5c, 0x59,
0x4b, 0x42, 0x44, 0x3d, 0x38, 0x3c, 0x3d, 0x44, 0x4f, 0x4d, 0x4c, 0x58, 0x5a, 0x57, 0x5c, 0x60,
0x6a, 0x78, 0x7d, 0x84, 0x91, 0x91, 0x93, 0x9f, 0xa2, 0xa1, 0xa7, 0xaa, 0xb4, 0xbf, 0xb9, 0xb7,
0xbb, 0xb2, 0xa6, 0x9e, 0x92, 0x8d, 0x8a, 0x7b, 0x75, 0x78, 0x6f, 0x67, 0x65, 0x5b, 0x56, 0x56,
0x50, 0x54, 0x5a, 0x54, 0x59, 0x67, 0x67, 0x65, 0x66, 0x66, 0x70, 0x79, 0x75, 0x7b, 0x84, 0x86,
0x8f, 0x99, 0x9a, 0x9e, 0xa1, 0x9f, 0xaa, 0xb5, 0xb1, 0xb2, 0xba, 0xb9, 0xb8, 0xb1, 0xa3, 0x9e,
0x97, 0x85, 0x7e, 0x7c, 0x6f, 0x64, 0x5f, 0x56, 0x51, 0x4b, 0x3d, 0x39, 0x3b, 0x36, 0x38, 0x3f,
0x3e, 0x41, 0x45, 0x45, 0x50, 0x57, 0x50, 0x53, 0x5e, 0x63, 0x6c, 0x73, 0x75, 0x80, 0x8a, 0x8b,
0x95, 0x9e, 0x9b, 0xa1, 0xab, 0xb0, 0xb8, 0xb7, 0xaf, 0xb3, 0xb4, 0xa8, 0xa1, 0x9a, 0x8e, 0x88,
0x80, 0x77, 0x77, 0x70, 0x62, 0x5f, 0x5e, 0x58, 0x54, 0x51, 0x4f, 0x54, 0x55, 0x54, 0x5f, 0x66,
0x63, 0x67, 0x6e, 0x70, 0x74, 0x73, 0x74, 0x80, 0x88, 0x88, 0x8f, 0x96, 0x99, 0x9f, 0xa3, 0xa4,
0xab, 0xac, 0xa9, 0xb1, 0xb8, 0xb3, 0xaf, 0xa9, 0xa0, 0x99, 0x8c, 0x7f, 0x7c, 0x75, 0x66, 0x62,
0x5d, 0x53, 0x4d, 0x43, 0x3a, 0x3b, 0x36, 0x30, 0x37, 0x3b, 0x3c, 0x44, 0x48, 0x4b, 0x52, 0x4f,
0x4d, 0x59, 0x61, 0x64, 0x6d, 0x75, 0x7e, 0x88, 0x8b, 0x90, 0x9b, 0x9d, 0x9c, 0xa6, 0xaf, 0xb4,
0xb9, 0xb8, 0xb7, 0xb8, 0xae, 0xa3, 0xa0, 0x99, 0x8e, 0x87, 0x81, 0x7e, 0x7a, 0x6e, 0x64, 0x64,
0x5e, 0x54, 0x51, 0x4f, 0x50, 0x55, 0x59, 0x5e, 0x66, 0x63, 0x60, 0x69, 0x6f, 0x6e, 0x6f, 0x72,
0x79, 0x85, 0x89, 0x8d, 0x96, 0x98, 0x98, 0x9f, 0xa3, 0xa9, 0xaf, 0xaf, 0xb3, 0xbd, 0xba, 0xb0,
0xab, 0xa3, 0x9b, 0x93, 0x87, 0x80, 0x7b, 0x6d, 0x65, 0x64, 0x5b, 0x4e, 0x43, 0x3a, 0x3b, 0x3c,
0x38, 0x39, 0x3e, 0x40, 0x45, 0x4b, 0x4e, 0x51, 0x52, 0x51, 0x5a, 0x65, 0x67, 0x6a, 0x74, 0x7f,
0x86, 0x8a, 0x8d, 0x94, 0x9b, 0x9f, 0xa8, 0xb3, 0xb6, 0xb5, 0xb7, 0xb8, 0xb8, 0xb5, 0xab, 0xa4,
0xa1, 0x98, 0x90, 0x8c, 0x84, 0x7c, 0x74, 0x6b, 0x68, 0x65, 0x5a, 0x54, 0x56, 0x57, 0x59, 0x5c,
0x5e, 0x64, 0x68, 0x68, 0x6d, 0x72, 0x6f, 0x70, 0x76, 0x7c, 0x83, 0x87, 0x8a, 0x92, 0x9a, 0x9d,
0xa2, 0xa7, 0xaa, 0xae, 0xb1, 0xb3, 0xb9, 0xba, 0xb3, 0xaf, 0xab, 0xa3, 0x99, 0x8f, 0x86, 0x80,
0x75, 0x6a, 0x66, 0x5f, 0x54, 0x4a, 0x44, 0x40, 0x3e, 0x3b, 0x3a, 0x40, 0x46, 0x4a, 0x4f, 0x54,
0x56, 0x57, 0x58, 0x5c, 0x65, 0x69, 0x6b, 0x74, 0x7f, 0x85, 0x8b, 0x92, 0x97, 0x9c, 0x9f, 0xa4,
0xae, 0xb4, 0xb5, 0xb8, 0xbc, 0xbc, 0xba, 0xb3, 0xaa, 0xa5, 0x9d, 0x93, 0x8f, 0x89, 0x80, 0x7a,
0x73, 0x6d, 0x69, 0x5f, 0x55, 0x55, 0x58, 0x5b, 0x60, 0x63, 0x66, 0x6a, 0x6b, 0x6e, 0x73, 0x72,
0x70, 0x76, 0x7d, 0x84, 0x8b, 0x8f, 0x93, 0x9a, 0x9f, 0xa1, 0xa4, 0xa7, 0xab, 0xb1, 0xb5, 0xba,
0xbd, 0xb8, 0xb0, 0xad, 0xa8, 0x9d, 0x92, 0x88, 0x80, 0x7b, 0x74, 0x6c, 0x64, 0x58, 0x4b, 0x45,
0x43, 0x40, 0x3f, 0x3d, 0x40, 0x49, 0x4f, 0x51, 0x54, 0x56, 0x56, 0x5c, 0x60, 0x65, 0x6b, 0x6f,
0x76, 0x82, 0x89, 0x8b, 0x90, 0x93, 0x9a, 0xa2, 0xa6, 0xad, 0xb5, 0xb6, 0xb9, 0xbf, 0xbf, 0xbc,
0xb6, 0xac, 0xa7, 0xa4, 0x9c, 0x93, 0x8c, 0x84, 0x7e, 0x77, 0x6f, 0x68, 0x60, 0x58, 0x58, 0x5c,
0x5c, 0x5d, 0x60, 0x64, 0x6a, 0x6d, 0x6d, 0x6f, 0x72, 0x74, 0x79, 0x80, 0x85, 0x89, 0x8d, 0x91,
0x98, 0x9f, 0xa1, 0xa3, 0xa7, 0xab, 0xaf, 0xb4, 0xb7, 0xb9, 0xb7, 0xb1, 0xad, 0xa9, 0xa0, 0x93,
0x8a, 0x83, 0x7e, 0x76, 0x6b, 0x63, 0x5b, 0x51, 0x4a, 0x46, 0x42, 0x40, 0x3f, 0x42, 0x48, 0x4d,
0x4f, 0x54, 0x58, 0x5b, 0x5f, 0x62, 0x67, 0x6d, 0x70, 0x74, 0x7f, 0x88, 0x8d, 0x91, 0x96, 0x9b,
0xa1, 0xa5, 0xaa, 0xb1, 0xb6, 0xb9, 0xbe, 0xc0, 0xbe, 0xb8, 0xaf, 0xa9, 0xa5, 0x9d, 0x93, 0x8c,
0x85, 0x7f, 0x79, 0x71, 0x6a, 0x61, 0x58, 0x54, 0x57, 0x5a, 0x5c, 0x5d, 0x61, 0x69, 0x6e, 0x70,
0x71, 0x72, 0x73, 0x77, 0x7f, 0x84, 0x89, 0x8d, 0x92, 0x99, 0x9f, 0xa1, 0xa0, 0xa2, 0xa7, 0xac,
0xb2, 0xb6, 0xb8, 0xb6, 0xb2, 0xad, 0xaa, 0xa1, 0x93, 0x89, 0x83, 0x7f, 0x79, 0x70, 0x64, 0x5c,
0x53, 0x4b, 0x47, 0x42, 0x3d, 0x3c, 0x3f, 0x48, 0x4f, 0x50, 0x51, 0x57, 0x5d, 0x60, 0x62, 0x65,
0x6b, 0x71, 0x78, 0x81, 0x89, 0x8c, 0x8e, 0x92, 0x99, 0xa1, 0xa3, 0xa7, 0xae, 0xb5, 0xba, 0xbf,
0xbf, 0xbc, 0xb6, 0xb0, 0xab, 0xa7, 0x9f, 0x94, 0x8c, 0x88, 0x83, 0x7a, 0x71, 0x69, 0x60, 0x58,
0x55, 0x55, 0x57, 0x59, 0x5a, 0x61, 0x69, 0x6c, 0x6d, 0x6e, 0x71, 0x75, 0x7a, 0x7e, 0x84, 0x88,
0x8b, 0x91, 0x97, 0x9b, 0x9c, 0x9b, 0x9e, 0xa6, 0xab, 0xad, 0xb0, 0xb3, 0xb4, 0xb2, 0xad, 0xa8,
0xa1, 0x96, 0x8d, 0x89, 0x82, 0x7a, 0x71, 0x67, 0x5f, 0x56, 0x4d, 0x47, 0x42, 0x3e, 0x3d, 0x3f,
0x45, 0x4b, 0x4f, 0x52, 0x57, 0x5c, 0x61, 0x64, 0x67, 0x6c, 0x71, 0x78, 0x81, 0x89, 0x8d, 0x90,
0x94, 0x9a, 0xa0, 0xa2, 0xa6, 0xac, 0xb1, 0xb7, 0xbb, 0xbd, 0xbd, 0xb8, 0xb0, 0xac, 0xa8, 0xa0,
0x96, 0x8e, 0x88, 0x84, 0x7d, 0x73, 0x6b, 0x61, 0x58, 0x55, 0x55, 0x55, 0x55, 0x56, 0x5e, 0x69,
0x6d, 0x6e, 0x6f, 0x71, 0x76, 0x7b, 0x7f, 0x84, 0x89, 0x8b, 0x91, 0x99, 0x9d, 0x9d, 0x9b, 0x9d,
0xa4, 0xaa, 0xad, 0xaf, 0xb1, 0xb1, 0xb1, 0xaf, 0xab, 0xa1, 0x95, 0x8d, 0x8a, 0x85, 0x7c, 0x71,
0x69, 0x62, 0x5b, 0x52, 0x49, 0x41, 0x3c, 0x3a, 0x3f, 0x47, 0x4b, 0x4c, 0x50, 0x58, 0x60, 0x64,
0x65, 0x68, 0x6e, 0x74, 0x7b, 0x83, 0x89, 0x8c, 0x90, 0x96, 0x9e, 0xa2, 0xa2, 0xa4, 0xac, 0xb4,
0xbb, 0xbe, 0xbe, 0xbc, 0xb7, 0xb4, 0xb1, 0xa9, 0x9e, 0x96, 0x90, 0x8d, 0x88, 0x7e, 0x72, 0x6b,
0x62, 0x5a, 0x56, 0x54, 0x53, 0x54, 0x57, 0x5f, 0x68, 0x6b, 0x6b, 0x6e, 0x72, 0x76, 0x79, 0x7d,
0x82, 0x86, 0x8b, 0x92, 0x98, 0x9b, 0x9a, 0x9a, 0x9f, 0xa6, 0xa9, 0xa9, 0xad, 0xb1, 0xb3, 0xb1,
0xb0, 0xac, 0xa3, 0x99, 0x92, 0x8d, 0x87, 0x7e, 0x74, 0x6e, 0x67, 0x5c, 0x53, 0x4b, 0x45, 0x40,
0x3d, 0x3f, 0x45, 0x48, 0x4a, 0x50, 0x57, 0x5e, 0x63, 0x65, 0x69, 0x70, 0x76, 0x7c, 0x84, 0x8a,
0x8d, 0x92, 0x9a, 0xa2, 0xa4, 0xa1, 0xa2, 0xa8, 0xae, 0xb4, 0xba, 0xbe, 0xbf, 0xbb, 0xba, 0xbc,
0xbb, 0xb2, 0xa5, 0x9d, 0x9c, 0x96, 0x88, 0x7a, 0x6d, 0x63, 0x5b, 0x53, 0x4d, 0x4c, 0x4b, 0x4d,
0x54, 0x56, 0x56, 0x58, 0x5a, 0x5e, 0x64, 0x6b, 0x74, 0x7b, 0x7d, 0x84, 0x93, 0x9f, 0xa1, 0x9e,
0x9d, 0xa8, 0xb4, 0xb4, 0xad, 0xae, 0xb8, 0xc2, 0xc2, 0xb6, 0xad, 0xa9, 0xa4, 0x9e, 0x97, 0x8a,
0x7f, 0x7b, 0x74, 0x65, 0x55, 0x48, 0x42, 0x3e, 0x31, 0x27, 0x2b, 0x33, 0x35, 0x34, 0x34, 0x3e,
0x4e, 0x54, 0x55, 0x5b, 0x64, 0x6f, 0x7d, 0x83, 0x86, 0x8d, 0x96, 0xa4, 0xaf, 0xaa, 0xa5, 0xb0,
0xbe, 0xc7, 0xc9, 0xc5, 0xc9, 0xd2, 0xcf, 0xc7, 0xc1, 0xb8, 0xb0, 0xac, 0xa0, 0x95, 0x8d, 0x80,
0x75, 0x6c, 0x5b, 0x4f, 0x4c, 0x4a, 0x46, 0x40, 0x3c, 0x45, 0x50, 0x4e, 0x4d, 0x54, 0x5a, 0x63,
0x69, 0x6c, 0x74, 0x7e, 0x84, 0x8e, 0x98, 0x9a, 0x9d, 0xa5, 0xad, 0xb4, 0xb6, 0xb4, 0xba, 0xc4,
0xc7, 0xc6, 0xc5, 0xc2, 0xc1, 0xc2, 0xbd, 0xb9, 0xb0, 0x9e, 0x92, 0x8b, 0x7d, 0x6c, 0x5e, 0x4f,
0x45, 0x3b, 0x30, 0x32, 0x38, 0x32, 0x2d, 0x2d, 0x30, 0x39, 0x3c, 0x38, 0x41, 0x52, 0x5d, 0x6a,
0x73, 0x79, 0x86, 0x95, 0x9e, 0xa8, 0xac, 0xb0, 0xc3, 0xd1, 0xd0, 0xcf, 0xd5, 0xe0, 0xe9, 0xe2,
0xd2, 0xd0, 0xd0, 0xc7, 0xbd, 0xaf, 0xa4, 0x9f, 0x93, 0x7e, 0x6c, 0x5c, 0x51, 0x4d, 0x44, 0x33,
0x2a, 0x2d, 0x35, 0x3a, 0x32, 0x2c, 0x37, 0x49, 0x50, 0x53, 0x57, 0x62, 0x73, 0x7d, 0x81, 0x88,
0x91, 0x9b, 0xaa, 0xb3, 0xb5, 0xbc, 0xc7, 0xd4, 0xde, 0xda, 0xd4, 0xd8, 0xdd, 0xd7, 0xcb, 0xc0,
0xbd, 0xbb, 0xab, 0x95, 0x86, 0x7a, 0x6e, 0x60, 0x4d, 0x3b, 0x32, 0x2f, 0x2f, 0x2d, 0x24, 0x1f,
0x26, 0x30, 0x34, 0x32, 0x34, 0x42, 0x53, 0x5c, 0x61, 0x69, 0x77, 0x84, 0x8e, 0x96, 0xa2, 0xab,
0xb4, 0xc1, 0xcd, 0xd6, 0xde, 0xe4, 0xed, 0xf0, 0xe9, 0xe5, 0xe7, 0xe4, 0xdb, 0xcf, 0xc4, 0xbf,
0xb6, 0xa1, 0x8d, 0x7d, 0x6e, 0x62, 0x54, 0x46, 0x3d, 0x37, 0x34, 0x38, 0x38, 0x30, 0x2e, 0x37,
0x41, 0x46, 0x45, 0x4a, 0x5b, 0x6a, 0x70, 0x76, 0x7d, 0x85, 0x90, 0x99, 0xa1, 0xaf, 0xba, 0xc3,
0xce, 0xd7, 0xd8, 0xdc, 0xdf, 0xde, 0xdc, 0xd5, 0xcf, 0xcf, 0xc9, 0xb8, 0xa9, 0x9c, 0x8f, 0x82,
0x6f, 0x5b, 0x4f, 0x44, 0x39, 0x35, 0x2e, 0x25, 0x24, 0x26, 0x29, 0x2e, 0x2c, 0x2d, 0x3b, 0x49,
0x51, 0x58, 0x5f, 0x6d, 0x7b, 0x80, 0x88, 0x98, 0xa4, 0xae, 0xbb, 0xc5, 0xcd, 0xd6, 0xdd, 0xe4,
0xe9, 0xe7, 0xe5, 0xe6, 0xe3, 0xda, 0xd0, 0xc8, 0xc4, 0xb9, 0xa4, 0x90, 0x84, 0x78, 0x69, 0x58,
0x49, 0x40, 0x3b, 0x35, 0x33, 0x32, 0x2d, 0x2b, 0x30, 0x35, 0x39, 0x3d, 0x41, 0x4c, 0x58, 0x5e,
0x65, 0x73, 0x7d, 0x86, 0x91, 0x99, 0xa5, 0xb3, 0xba, 0xc1, 0xcd, 0xd3, 0xd5, 0xdb, 0xdb, 0xd8,
0xd5, 0xce, 0xcb, 0xc6, 0xb6, 0xa6, 0x9d, 0x91, 0x81, 0x71, 0x5c, 0x4d, 0x44, 0x37, 0x2c, 0x27,
0x21, 0x1e, 0x20, 0x22, 0x26, 0x29, 0x28, 0x2f, 0x3c, 0x44, 0x4b, 0x55, 0x60, 0x6c, 0x78, 0x80,
0x8c, 0x98, 0x9f, 0xaa, 0xb9, 0xc4, 0xce, 0xd6, 0xda, 0xe2, 0xe7, 0xe4, 0xe3, 0xe0, 0xd7, 0xd0,
0xcc, 0xc3, 0xb9, 0xa9, 0x94, 0x86, 0x7b, 0x66, 0x51, 0x44, 0x38, 0x31, 0x2c, 0x24, 0x22, 0x21,
0x1c, 0x1e, 0x26, 0x28, 0x2a, 0x31, 0x38, 0x47, 0x55, 0x5a, 0x64, 0x72, 0x7a, 0x84, 0x92, 0x9b,
0xa6, 0xb0, 0xb7, 0xc5, 0xd0, 0xd0, 0xd2, 0xd4, 0xd1, 0xd0, 0xcd, 0xc5, 0xc0, 0xb8, 0xa8, 0x9f,
0x96, 0x81, 0x6f, 0x5f, 0x4f, 0x46, 0x3c, 0x2c, 0x25, 0x23, 0x1d, 0x1c, 0x1d, 0x1b, 0x1e, 0x22,
0x24, 0x30, 0x3a, 0x3c, 0x45, 0x54, 0x5e, 0x69, 0x75, 0x7e, 0x8b, 0x97, 0x9e, 0xae, 0xbc, 0xc1,
0xc9, 0xd3, 0xda, 0xe2, 0xe2, 0xda, 0xda, 0xd9, 0xd0, 0xca, 0xc2, 0xb5, 0xa8, 0x99, 0x86, 0x7b,
0x6c, 0x55, 0x46, 0x3d, 0x32, 0x2c, 0x25, 0x1d, 0x1d, 0x1d, 0x1b, 0x21, 0x26, 0x25, 0x2c, 0x36,
0x3d, 0x4a, 0x53, 0x58, 0x68, 0x75, 0x7c, 0x88, 0x94, 0x9b, 0xa7, 0xb0, 0xb8, 0xc6, 0xcc, 0xca,
0xcf, 0xd2, 0xcd, 0xcc, 0xc6, 0xbc, 0xb6, 0xaa, 0x9d, 0x96, 0x88, 0x74, 0x65, 0x57, 0x4a, 0x40,
0x30, 0x22, 0x21, 0x1e, 0x1a, 0x1c, 0x1e, 0x1e, 0x22, 0x26, 0x2d, 0x37, 0x3a, 0x3f, 0x4e, 0x5d,
0x68, 0x73, 0x7c, 0x85, 0x93, 0x9e, 0xa7, 0xb5, 0xbe, 0xc4, 0xce, 0xd8, 0xde, 0xe0, 0xdc, 0xd7,
0xd6, 0xd1, 0xc7, 0xbf, 0xb4, 0xa7, 0x9b, 0x8d, 0x80, 0x70, 0x5b, 0x48, 0x40, 0x37, 0x2e, 0x27,
0x20, 0x1f, 0x22, 0x22, 0x23, 0x28, 0x29, 0x2b, 0x33, 0x3d, 0x47, 0x50, 0x56, 0x62, 0x71, 0x7c,
0x82, 0x8c, 0x97, 0xa3, 0xad, 0xb7, 0xc1, 0xc9, 0xcb, 0xce, 0xd3, 0xd3, 0xcc, 0xc5, 0xbe, 0xb7,
0xb0, 0xa5, 0x98, 0x8b, 0x7c, 0x6c, 0x60, 0x53, 0x43, 0x34, 0x29, 0x25, 0x25, 0x22, 0x1f, 0x21,
0x24, 0x27, 0x2a, 0x30, 0x36, 0x3c, 0x43, 0x4e, 0x5d, 0x67, 0x6f, 0x78, 0x84, 0x92, 0x9e, 0xa6,
0xb1, 0xbc, 0xc5, 0xcd, 0xd7, 0xdd, 0xdf, 0xdb, 0xd8, 0xd7, 0xd4, 0xcb, 0xc0, 0xb7, 0xae, 0xa1,
0x92, 0x84, 0x76, 0x63, 0x51, 0x45, 0x3d, 0x34, 0x29, 0x24, 0x25, 0x27, 0x26, 0x26, 0x29, 0x2e,
0x32, 0x37, 0x40, 0x49, 0x4f, 0x57, 0x63, 0x72, 0x7d, 0x84, 0x8b, 0x96, 0xa2, 0xac, 0xb5, 0xbe,
0xc6, 0xca, 0xcc, 0xd2, 0xd4, 0xce, 0xc5, 0xc0, 0xbb, 0xb4, 0xa9, 0x9b, 0x8f, 0x84, 0x76, 0x67,
0x5b, 0x4c, 0x3b, 0x2e, 0x2b, 0x2a, 0x27, 0x23, 0x23, 0x28, 0x2e, 0x30, 0x32, 0x39, 0x3f, 0x44,
0x50, 0x5f, 0x6b, 0x73, 0x7a, 0x86, 0x97, 0xa0, 0xa6, 0xb0, 0xbb, 0xc4, 0xcd, 0xd4, 0xdc, 0xe0,
0xdc, 0xdb, 0xda, 0xd5, 0xcc, 0xc0, 0xb6, 0xb1, 0xa8, 0x97, 0x88, 0x7b, 0x6a, 0x59, 0x4b, 0x41,
0x38, 0x2a, 0x23, 0x28, 0x2c, 0x2b, 0x29, 0x2b, 0x2f, 0x35, 0x39, 0x40, 0x4a, 0x51, 0x59, 0x67,
0x76, 0x80, 0x85, 0x8b, 0x97, 0xa5, 0xae, 0xb3, 0xbb, 0xc6, 0xcc, 0xd0, 0xd5, 0xd6, 0xd0, 0xc6,
0xc0, 0xbe, 0xb8, 0xa9, 0x9c, 0x94, 0x89, 0x7c, 0x6e, 0x5e, 0x50, 0x40, 0x31, 0x2e, 0x2e, 0x2a,
0x28, 0x28, 0x2c, 0x34, 0x35, 0x33, 0x3b, 0x43, 0x49, 0x55, 0x62, 0x6d, 0x76, 0x7c, 0x87, 0x9a,
0xa2, 0xa4, 0xae, 0xba, 0xc5, 0xce, 0xd5, 0xdc, 0xe0, 0xdd, 0xdb, 0xdb, 0xd7, 0xcd, 0xc0, 0xb6,
0xb3, 0xac, 0x9c, 0x8b, 0x7f, 0x70, 0x5f, 0x51, 0x45, 0x3a, 0x2e, 0x28, 0x2a, 0x2e, 0x2d, 0x29,
0x2b, 0x33, 0x3a, 0x3d, 0x43, 0x49, 0x51, 0x5b, 0x68, 0x76, 0x80, 0x86, 0x8d, 0x99, 0xa6, 0xb0,
0xb5, 0xbb, 0xc6, 0xcf, 0xd3, 0xd7, 0xd7, 0xd0, 0xc8, 0xc2, 0xc0, 0xba, 0xac, 0x9e, 0x97, 0x8f,
0x82, 0x73, 0x62, 0x53, 0x46, 0x38, 0x33, 0x31, 0x2b, 0x26, 0x29, 0x30, 0x37, 0x39, 0x37, 0x3b,
0x45, 0x4d, 0x55, 0x60, 0x6b, 0x75, 0x7e, 0x8a, 0x9a, 0xa2, 0xa5, 0xad, 0xba, 0xc7, 0xcf, 0xd3,
0xd9, 0xdf, 0xe0, 0xdf, 0xde, 0xd9, 0xce, 0xc1, 0xba, 0xb6, 0xae, 0xa0, 0x8f, 0x80, 0x76, 0x67,
0x54, 0x46, 0x3b, 0x2e, 0x29, 0x2b, 0x2d, 0x2c, 0x2a, 0x2d, 0x36, 0x3a, 0x3c, 0x42, 0x49, 0x52,
0x5e, 0x6a, 0x77, 0x81, 0x87, 0x8f, 0x9d, 0xa8, 0xb0, 0xb6, 0xbb, 0xc6, 0xd1, 0xd6, 0xd9, 0xd9,
0xd4, 0xcd, 0xc6, 0xc1, 0xbb, 0xae, 0xa1, 0x9b, 0x94, 0x87, 0x78, 0x67, 0x57, 0x49, 0x3e, 0x36,
0x32, 0x2c, 0x29, 0x2b, 0x31, 0x37, 0x39, 0x38, 0x3b, 0x44, 0x4e, 0x56, 0x5f, 0x6a, 0x74, 0x7f,
0x8c, 0x99, 0xa2, 0xa6, 0xac, 0xb8, 0xc6, 0xcf, 0xd3, 0xd8, 0xde, 0xe1, 0xe2, 0xdf, 0xd8, 0xcd,
0xc3, 0xbc, 0xb8, 0xb0, 0xa1, 0x91, 0x83, 0x7a, 0x6c, 0x59, 0x48, 0x3b, 0x30, 0x2b, 0x2c, 0x2d,
0x2b, 0x29, 0x2d, 0x36, 0x3c, 0x3e, 0x41, 0x49, 0x54, 0x61, 0x6d, 0x78, 0x80, 0x87, 0x91, 0x9e,
0xa9, 0xb0, 0xb3, 0xb9, 0xc6, 0xd2, 0xd6, 0xd8, 0xd7, 0xd4, 0xd0, 0xcb, 0xc4, 0xbb, 0xae, 0xa4,
0x9f, 0x99, 0x8c, 0x7b, 0x68, 0x5b, 0x50, 0x43, 0x38, 0x30, 0x2b, 0x2a, 0x2c, 0x32, 0x37, 0x37,
0x37, 0x3d, 0x46, 0x4f, 0x56, 0x5c, 0x66, 0x74, 0x80, 0x8c, 0x97, 0x9e, 0xa5, 0xaf, 0xbb, 0xc7,
0xcd, 0xd1, 0xd8, 0xdf, 0xe4, 0xe5, 0xe0, 0xd7, 0xcf, 0xc6, 0xbf, 0xb8, 0xae, 0xa1, 0x94, 0x88,
0x7e, 0x6e, 0x5b, 0x4b, 0x3c, 0x31, 0x2c, 0x2a, 0x29, 0x29, 0x29, 0x2c, 0x35, 0x3b, 0x3d, 0x41,
0x49, 0x54, 0x5e, 0x6b, 0x78, 0x80, 0x8a, 0x95, 0xa0, 0xaa, 0xb1, 0xb4, 0xbb, 0xc9, 0xd2, 0xd6,
0xd8, 0xd8, 0xd7, 0xd4, 0xce, 0xc6, 0xbd, 0xb1, 0xa7, 0xa0, 0x98, 0x8c, 0x7d, 0x6e, 0x60, 0x53,
0x45, 0x39, 0x30, 0x2a, 0x28, 0x2a, 0x30, 0x34, 0x35, 0x37, 0x3d, 0x45, 0x4d, 0x54, 0x5a, 0x64,
0x73, 0x80, 0x8c, 0x97, 0x9e, 0xa5, 0xaf, 0xbc, 0xc6, 0xcd, 0xd2, 0xd8, 0xe0, 0xe6, 0xe6, 0xe3,
0xdc, 0xd2, 0xc8, 0xc1, 0xba, 0xae, 0xa0, 0x94, 0x8a, 0x80, 0x71, 0x5d, 0x4a, 0x3c, 0x33, 0x2d,
0x29, 0x27, 0x26, 0x28, 0x2e, 0x36, 0x3a, 0x3b, 0x3f, 0x47, 0x53, 0x61, 0x6c, 0x75, 0x7f, 0x8a,
0x96, 0xa1, 0xaa, 0xae, 0xb3, 0xbc, 0xc7, 0xd2, 0xd6, 0xd6, 0xd7, 0xd8, 0xd6, 0xd0, 0xc7, 0xbb,
0xb0, 0xa8, 0xa2, 0x9b, 0x8f, 0x7f, 0x6e, 0x61, 0x57, 0x48, 0x3a, 0x2e, 0x28, 0x28, 0x2b, 0x2e,
0x30, 0x31, 0x35, 0x3c, 0x45, 0x4b, 0x51, 0x58, 0x65, 0x74, 0x81, 0x8c, 0x96, 0x9f, 0xa9, 0xb2,
0xbc, 0xc5, 0xca, 0xd0, 0xd9, 0xe2, 0xe8, 0xe7, 0xe1, 0xd9, 0xd1, 0xca, 0xc3, 0xb8, 0xab, 0x9f,
0x94, 0x8c, 0x80, 0x6e, 0x5c, 0x4d, 0x41, 0x36, 0x2c, 0x27, 0x26, 0x27, 0x29, 0x2d, 0x32, 0x37,
0x3a, 0x3d, 0x46, 0x52, 0x5d, 0x68, 0x74, 0x7f, 0x8a, 0x95, 0x9f, 0xa7, 0xad, 0xb4, 0xbd, 0xc7,
0xd0, 0xd5, 0xd7, 0xda, 0xdc, 0xd8, 0xd1, 0xc7, 0xbd, 0xb7, 0xaf, 0xa4, 0x9a, 0x92, 0x8b, 0x81,
0x73, 0x63, 0x53, 0x44, 0x38, 0x30, 0x30, 0x3a, 0x41, 0x3f, 0x3f, 0x44, 0x4c, 0x54, 0x5c, 0x62,
0x6b, 0x79, 0x85, 0x8b, 0x8f, 0x94, 0x98, 0xa2, 0xb4, 0xbf, 0xbd, 0xbd, 0xc2, 0xc8, 0xd1, 0xdb,
0xdb, 0xd5, 0xd3, 0xce, 0xc2, 0xb6, 0xac, 0x9d, 0x8d, 0x84, 0x7e, 0x70, 0x61, 0x52, 0x41, 0x34,
0x32, 0x30, 0x2a, 0x2a, 0x31, 0x32, 0x34, 0x3b, 0x3e, 0x3f, 0x48, 0x54, 0x59, 0x60, 0x6b, 0x73,
0x7c, 0x8e, 0xa1, 0xaa, 0xb3, 0xc1, 0xcc, 0xd5, 0xe2, 0xe8, 0xe5, 0xe6, 0xeb, 0xe6, 0xdf, 0xda,
0xce, 0xbe, 0xb3, 0xaa, 0x9c, 0x8f, 0x8a, 0x81, 0x76, 0x6b, 0x5b, 0x45, 0x35, 0x2d, 0x24, 0x1b,
0x1a, 0x16, 0x11, 0x14, 0x1e, 0x23, 0x28, 0x32, 0x3b, 0x46, 0x5b, 0x6e, 0x78, 0x82, 0x8f, 0x98,
0x9f, 0xab, 0xb5, 0xb7, 0xbe, 0xca, 0xd1, 0xd7, 0xe1, 0xe4, 0xe4, 0xe9, 0xec, 0xe2, 0xd4, 0xc9,
0xbc, 0xb0, 0xac, 0xa7, 0x94, 0x80, 0x71, 0x5f, 0x4f, 0x46, 0x3d, 0x33, 0x34, 0x3c, 0x3e, 0x40,
0x48, 0x4d, 0x4e, 0x56, 0x60, 0x5f, 0x60, 0x6c, 0x77, 0x7e, 0x89, 0x91, 0x92, 0x9a, 0xa8, 0xb3,
0xbc, 0xc9, 0xd1, 0xd1, 0xd9, 0xe6, 0xe8, 0xe3, 0xe0, 0xd4, 0xc2, 0xb8, 0xb1, 0xa3, 0x97, 0x8e,
0x81, 0x75, 0x6e, 0x64, 0x51, 0x43, 0x3c, 0x33, 0x2e, 0x31, 0x2f, 0x28, 0x2c, 0x35, 0x36, 0x37,
0x3d, 0x42, 0x4b, 0x60, 0x74, 0x7d, 0x8a, 0x9c, 0xa7, 0xb3, 0xc6, 0xd2, 0xd4, 0xdc, 0xe6, 0xe8,
0xec, 0xf5, 0xf5, 0xef, 0xf1, 0xef, 0xe1, 0xd5, 0xcd, 0xc0, 0xb6, 0xb7, 0xb2, 0x9f, 0x8e, 0x7f,
0x6d, 0x5f, 0x56, 0x45, 0x31, 0x2c, 0x2f, 0x2c, 0x30, 0x38, 0x36, 0x35, 0x43, 0x52, 0x59, 0x63,
0x71, 0x78, 0x81, 0x92, 0x9b, 0x9c, 0xa4, 0xad, 0xae, 0xb6, 0xc3, 0xc5, 0xc5, 0xd1, 0xde, 0xe3,
0xe6, 0xe4, 0xd8, 0xcb, 0xc5, 0xbb, 0xab, 0x9e, 0x91, 0x80, 0x75, 0x6e, 0x5d, 0x48, 0x3d, 0x35,
0x2c, 0x2b, 0x31, 0x30, 0x31, 0x3c, 0x45, 0x47, 0x4c, 0x51, 0x50, 0x59, 0x6a, 0x74, 0x7a, 0x85,
0x91, 0x98, 0xa5, 0xb8, 0xc3, 0xc9, 0xd5, 0xdf, 0xe5, 0xf1, 0xfd, 0xfd, 0xfb, 0xfb, 0xf2, 0xe2,
0xd7, 0xca, 0xb9, 0xad, 0xa7, 0x9c, 0x8f, 0x86, 0x7a, 0x68, 0x5e, 0x55, 0x43, 0x34, 0x2f, 0x2b,
0x27, 0x2b, 0x2c, 0x26, 0x25, 0x2e, 0x35, 0x3c, 0x4b, 0x58, 0x61, 0x6f, 0x80, 0x8b, 0x93, 0xa1,
0xa9, 0xa9, 0xb0, 0xb9, 0xb8, 0xb9, 0xc2, 0xc6, 0xc6, 0xc9, 0xc8, 0xbe, 0xb7, 0xb3, 0xaa, 0xa2,
0x9e, 0x94, 0x85, 0x7f, 0x78, 0x67, 0x54, 0x46, 0x37, 0x29, 0x28, 0x2b, 0x29, 0x2b, 0x33, 0x38,
0x3d, 0x49, 0x53, 0x58, 0x65, 0x77, 0x7f, 0x85, 0x93, 0x9b, 0x9f, 0xaa, 0xb6, 0xba, 0xbe, 0xc5,
0xcc, 0xd3, 0xdf, 0xea, 0xeb, 0xec, 0xee, 0xe6, 0xd9, 0xd0, 0xc2, 0xae, 0x9f, 0x93, 0x83, 0x76,
0x6a, 0x58, 0x46, 0x3a, 0x31, 0x22, 0x18, 0x17, 0x15, 0x12, 0x18, 0x1a, 0x14, 0x14, 0x1b, 0x1f,
0x25, 0x31, 0x39, 0x3f, 0x4e, 0x60, 0x6d, 0x7b, 0x8c, 0x97, 0x9f, 0xad, 0xb8, 0xbc, 0xc3, 0xcd,
0xd1, 0xd1, 0xd5, 0xd2, 0xc7, 0xc0, 0xb9, 0xac, 0xa2, 0x9e, 0x95, 0x8a, 0x89, 0x84, 0x76, 0x69,
0x5e, 0x4e, 0x3e, 0x3a, 0x38, 0x32, 0x2f, 0x30, 0x2d, 0x2d, 0x37, 0x3e, 0x42, 0x4e, 0x5e, 0x65,
0x70, 0x81, 0x8b, 0x90, 0x9c, 0xa6, 0xa8, 0xab, 0xb1, 0xb2, 0xb2, 0xba, 0xc2, 0xc2, 0xc6, 0xc9,
0xc3, 0xbc, 0xb8, 0xad, 0x9d, 0x92, 0x87, 0x79, 0x6d, 0x63, 0x51, 0x3c, 0x2f, 0x24, 0x15, 0x0c,
0x0a, 0x06, 0x04, 0x0b, 0x10, 0x12, 0x1a, 0x23, 0x29, 0x32, 0x3f, 0x48, 0x4f, 0x5c, 0x6b, 0x77,
0x84, 0x94, 0x9d, 0xa2, 0xad, 0xb9, 0xc3, 0xd0, 0xdb, 0xdf, 0xe3, 0xe8, 0xe5, 0xdc, 0xd4, 0xc8,
0xb6, 0xa7, 0xa0, 0x93, 0x85, 0x7e, 0x74, 0x65, 0x5b, 0x51, 0x42, 0x37, 0x34, 0x30, 0x2b, 0x2a,
0x28, 0x22, 0x20, 0x27, 0x2c, 0x2c, 0x33, 0x3a, 0x40, 0x4e, 0x5f, 0x6a, 0x75, 0x82, 0x8e, 0x96,
0x9f, 0xa7, 0xaa, 0xaf, 0xb9, 0xbe, 0xbf, 0xc3, 0xc3, 0xbb, 0xb6, 0xb3, 0xa8, 0x9b, 0x93, 0x89,
0x7f, 0x78, 0x70, 0x62, 0x51, 0x46, 0x3a, 0x2c, 0x24, 0x1f, 0x17, 0x14, 0x18, 0x1a, 0x1c, 0x24,
0x2c, 0x32, 0x3d, 0x4b, 0x54, 0x5f, 0x6e, 0x7b, 0x83, 0x90, 0x9d, 0xa1, 0xa5, 0xad, 0xb3, 0xb9,
0xc4, 0xcb, 0xce, 0xd4, 0xdb, 0xda, 0xd5, 0xd0, 0xc6, 0xb7, 0xac, 0xa2, 0x94, 0x85, 0x7a, 0x6b,
0x59, 0x4c, 0x40, 0x31, 0x25, 0x22, 0x1e, 0x19, 0x1a, 0x1c, 0x19, 0x1c, 0x25, 0x2a, 0x2d, 0x35,
0x3a, 0x40, 0x4d, 0x5c, 0x65, 0x71, 0x80, 0x89, 0x91, 0x9d, 0xa8, 0xae, 0xb5, 0xc0, 0xc7, 0xcb,
0xd1, 0xd2, 0xcc, 0xc8, 0xc3, 0xb6, 0xab, 0xa3, 0x97, 0x8a, 0x82, 0x7b, 0x6d, 0x60, 0x57, 0x4c,
0x40, 0x39, 0x32, 0x27, 0x23, 0x24, 0x22, 0x23, 0x2a, 0x2d, 0x2f, 0x39, 0x44, 0x4c, 0x58, 0x68,
0x76, 0x83, 0x92, 0x9f, 0xa4, 0xab, 0xb2, 0xb5, 0xb8, 0xbf, 0xc3, 0xc4, 0xc7, 0xca, 0xc7, 0xc2,
0xbe, 0xb5, 0xa8, 0xa0, 0x99, 0x8e, 0x84, 0x7b, 0x6b, 0x5b, 0x50, 0x43, 0x33, 0x28, 0x21, 0x18,
0x13, 0x14, 0x14, 0x13, 0x18, 0x20, 0x25, 0x2e, 0x39, 0x40, 0x4a, 0x5a, 0x68, 0x71, 0x7f, 0x8d,
0x97, 0x9f, 0xaa, 0xb3, 0xb9, 0xc1, 0xca, 0xd1, 0xda, 0xe2, 0xe2, 0xdf, 0xde, 0xd8, 0xcb, 0xc0,
0xb7, 0xa8, 0x99, 0x8e, 0x81, 0x72, 0x65, 0x5a, 0x4e, 0x45, 0x3e, 0x35, 0x2e, 0x2c, 0x2c, 0x29,
0x2b, 0x2f, 0x2f, 0x30, 0x35, 0x3c, 0x41, 0x4a, 0x56, 0x61, 0x6f, 0x7e, 0x87, 0x90, 0x9c, 0xa5,
0xa9, 0xb0, 0xb9, 0xbe, 0xc1, 0xc5, 0xc6, 0xc3, 0xbe, 0xb8, 0xae, 0xa6, 0x9f, 0x95, 0x8a, 0x82,
0x7a, 0x6c, 0x61, 0x59, 0x4d, 0x41, 0x39, 0x32, 0x2a, 0x26, 0x25, 0x23, 0x24, 0x2a, 0x2e, 0x32,
0x3c, 0x46, 0x4d, 0x59, 0x6b, 0x79, 0x82, 0x91, 0x9f, 0xa9, 0xb4, 0xbd, 0xc2, 0xc7, 0xcd, 0xd2,
0xd6, 0xde, 0xe3, 0xe3, 0xe1, 0xe1, 0xdc, 0xd2, 0xc9, 0xc0, 0xb1, 0xa3, 0x95, 0x84, 0x75, 0x67,
0x58, 0x49, 0x3c, 0x30, 0x25, 0x1d, 0x1c, 0x1b, 0x18, 0x1c, 0x21, 0x24, 0x27, 0x2e, 0x35, 0x3b,
0x44, 0x4d, 0x57, 0x65, 0x73, 0x7d, 0x86, 0x91, 0x9a, 0xa0, 0xaa, 0xb4, 0xbb, 0xc2, 0xca, 0xcf,
0xd0, 0xcd, 0xc8, 0xc1, 0xbb, 0xb5, 0xa8, 0x9c, 0x94, 0x89, 0x7c, 0x73, 0x6b, 0x60, 0x56, 0x4f,
0x49, 0x44, 0x41, 0x3d, 0x3b, 0x3d, 0x41, 0x42, 0x42, 0x49, 0x50, 0x55, 0x60, 0x6e, 0x77, 0x82,
0x92, 0xa1, 0xae, 0xba, 0xc3, 0xc7, 0xcd, 0xd3, 0xd5, 0xd7, 0xdb, 0xdc, 0xd8, 0xd5, 0xd3, 0xcb,
0xc0, 0xb6, 0xac, 0xa1, 0x96, 0x89, 0x7b, 0x6e, 0x61, 0x53, 0x45, 0x38, 0x2a, 0x1d, 0x15, 0x14,
0x13, 0x0f, 0x10, 0x14, 0x19, 0x20, 0x29, 0x31, 0x3c, 0x48, 0x53, 0x61, 0x73, 0x80, 0x88, 0x93,
0xa0, 0xa9, 0xaf, 0xb5, 0xbc, 0xc6, 0xcf, 0xd7, 0xdc, 0xdf, 0xe0, 0xdd, 0xd9, 0xd7, 0xd1, 0xc4,
0xb8, 0xae, 0xa1, 0x92, 0x85, 0x79, 0x6b, 0x5f, 0x56, 0x4f, 0x4a, 0x46, 0x42, 0x40, 0x44, 0x49,
0x48, 0x49, 0x50, 0x53, 0x56, 0x5d, 0x66, 0x6d, 0x75, 0x80, 0x8c, 0x99, 0xa4, 0xab, 0xb0, 0xb8,
0xc0, 0xc4, 0xc8, 0xcf, 0xd1, 0xcd, 0xc9, 0xc7, 0xc0, 0xb4, 0xaa, 0xa1, 0x96, 0x8b, 0x80, 0x73,
0x68, 0x5f, 0x53, 0x47, 0x40, 0x37, 0x2b, 0x24, 0x23, 0x21, 0x1e, 0x1e, 0x1f, 0x24, 0x2b, 0x32,
0x3a, 0x45, 0x51, 0x5c, 0x6d, 0x80, 0x92, 0x9e, 0xaa, 0xb7, 0xc2, 0xca, 0xd0, 0xd4, 0xd8, 0xde,
0xe3, 0xe3, 0xe3, 0xe2, 0xde, 0xda, 0xd9, 0xd4, 0xc8, 0xbe, 0xb6, 0xa9, 0x9b, 0x8e, 0x80, 0x70,
0x61, 0x55, 0x49, 0x40, 0x3a, 0x31, 0x2a, 0x2c, 0x31, 0x33, 0x35, 0x3c, 0x42, 0x46, 0x4f, 0x58,
0x5e, 0x67, 0x73, 0x7e, 0x8a, 0x95, 0x9c, 0xa0, 0xa8, 0xb1, 0xb8, 0xbe, 0xc6, 0xca, 0xca, 0xcb,
0xcc, 0xc9, 0xc0, 0xb5, 0xac, 0xa4, 0x9a, 0x8c, 0x7f, 0x73, 0x68, 0x5d, 0x52, 0x4a, 0x43, 0x3c,
0x38, 0x37, 0x38, 0x39, 0x38, 0x3a, 0x40, 0x46, 0x4a, 0x4d, 0x54, 0x5c, 0x63, 0x70, 0x81, 0x8f,
0x9b, 0xa9, 0xb6, 0xc2, 0xcd, 0xd6, 0xdb, 0xe1, 0xe6, 0xe8, 0xe7, 0xe7, 0xe3, 0xd7, 0xce, 0xca,
0xc3, 0xb5, 0xa9, 0x9f, 0x92, 0x85, 0x7b, 0x6e, 0x60, 0x54, 0x48, 0x3c, 0x34, 0x2e, 0x25, 0x1d,
0x1e, 0x23, 0x24, 0x26, 0x2c, 0x33, 0x38, 0x42, 0x4f, 0x5b, 0x67, 0x74, 0x81, 0x8f, 0x9c, 0xa4,
0xa9, 0xb1, 0xba, 0xc0, 0xc5, 0xcc, 0xd2, 0xd3, 0xd4, 0xd7, 0xd8, 0xd5, 0xcf, 0xc8, 0xc0, 0xb8,
0xac, 0x9d, 0x8f, 0x81, 0x73, 0x67, 0x5b, 0x4f, 0x44, 0x3b, 0x38, 0x39, 0x3b, 0x3c, 0x3f, 0x45,
0x4c, 0x51, 0x56, 0x5d, 0x63, 0x68, 0x71, 0x7f, 0x87, 0x8f, 0x97, 0xa1, 0xaa, 0xb4, 0xbb, 0xc0,
0xc6, 0xcd, 0xd1, 0xd1, 0xd2, 0xd2, 0xcb, 0xc3, 0xbe, 0xb6, 0xa9, 0x9c, 0x91, 0x85, 0x7c, 0x72,
0x66, 0x59, 0x4f, 0x46, 0x3c, 0x36, 0x32, 0x2f, 0x2c, 0x2e, 0x33, 0x35, 0x38, 0x3d, 0x43, 0x4b,
0x54, 0x60, 0x6c, 0x77, 0x80, 0x8c, 0x9d, 0xad, 0xbb, 0xc4, 0xcb, 0xd1, 0xd7, 0xda, 0xdc, 0xdf,
0xe0, 0xdf, 0xdd, 0xdc, 0xd8, 0xce, 0xc6, 0xc1, 0xb7, 0xaa, 0x9f, 0x93, 0x85, 0x7a, 0x6e, 0x60,
0x54, 0x49, 0x3e, 0x35, 0x33, 0x32, 0x2d, 0x2a, 0x2e, 0x34, 0x39, 0x3f, 0x46, 0x4c, 0x53, 0x5d,
0x69, 0x76, 0x80, 0x8b, 0x94, 0x9e, 0xab, 0xb3, 0xb6, 0xbb, 0xc0, 0xc4, 0xc6, 0xc7, 0xc6, 0xc1,
0xbe, 0xbb, 0xb7, 0xae, 0xa2, 0x96, 0x8c, 0x82, 0x78, 0x6c, 0x60, 0x54, 0x4b, 0x45, 0x3f, 0x3a,
0x36, 0x32, 0x32, 0x39, 0x40, 0x43, 0x47, 0x4f, 0x56, 0x5c, 0x67, 0x72, 0x7a, 0x83, 0x91, 0x9e,
0xab, 0xb7, 0xc1, 0xc9, 0xd3, 0xdf, 0xe5, 0xe7, 0xeb, 0xed, 0xec, 0xec, 0xeb, 0xe3, 0xd6, 0xca,
0xbf, 0xb1, 0xa4, 0x98, 0x88, 0x79, 0x6c, 0x5f, 0x52, 0x47, 0x3d, 0x32, 0x2b, 0x29, 0x28, 0x25,
0x24, 0x26, 0x29, 0x2f, 0x36, 0x3a, 0x3e, 0x46, 0x51, 0x5d, 0x6a, 0x74, 0x7f, 0x8e, 0x9e, 0xab,
0xb5, 0xbc, 0xc2, 0xc8, 0xd0, 0xd6, 0xd8, 0xd9, 0xd8, 0xd1, 0xca, 0xc7, 0xc1, 0xb8, 0xb3, 0xab,
0x9d, 0x90, 0x86, 0x7a, 0x6e, 0x66, 0x5f, 0x54, 0x4e, 0x4a, 0x44, 0x40, 0x44, 0x49, 0x49, 0x4c,
0x53, 0x59, 0x5f, 0x6c, 0x78, 0x7d, 0x87, 0x97, 0xa2, 0xad, 0xbb, 0xc3, 0xc4, 0xcb, 0xd3, 0xd5,
0xd7, 0xdd, 0xde, 0xda, 0xd9, 0xd7, 0xce, 0xc7, 0xc1, 0xb7, 0xad, 0xa5, 0x99, 0x88, 0x79, 0x6b,
0x5b, 0x4c, 0x40, 0x34, 0x29, 0x21, 0x1e, 0x1b, 0x1b, 0x1d, 0x1f, 0x24, 0x2e, 0x36, 0x3c, 0x44,
0x4c, 0x54, 0x60, 0x6f, 0x7a, 0x83, 0x91, 0x9e, 0xa7, 0xb3, 0xbd, 0xc2, 0xca, 0xd6, 0xdc, 0xdf,
0xe4, 0xe5, 0xdf, 0xd9, 0xd7, 0xce, 0xc2, 0xba, 0xb2, 0xa3, 0x99, 0x92, 0x84, 0x77, 0x72, 0x6b,
0x5e, 0x5b, 0x5c, 0x54, 0x4e, 0x52, 0x52, 0x4d, 0x51, 0x58, 0x57, 0x5a, 0x63, 0x68, 0x6b, 0x78,
0x85, 0x8c, 0x99, 0xa9, 0xb2, 0xb7, 0xc0, 0xc9, 0xca, 0xcf, 0xd5, 0xd2, 0xcd, 0xce, 0xca, 0xc1,
0xbb, 0xb4, 0xa8, 0x9e, 0x98, 0x8d, 0x80, 0x77, 0x6b, 0x5c, 0x51, 0x49, 0x3e, 0x34, 0x31, 0x2c,
0x27, 0x29, 0x2b, 0x2c, 0x33, 0x3d, 0x42, 0x49, 0x57, 0x62, 0x69, 0x75, 0x83, 0x8c, 0x96, 0xa6,
0xb0, 0xb5, 0xc0, 0xca, 0xcb, 0xd0, 0xda, 0xdb, 0xdb, 0xe2, 0xe4, 0xdd, 0xdb, 0xd9, 0xcf, 0xc5,
0xc1, 0xb7, 0xa6, 0x9b, 0x90, 0x7f, 0x70, 0x67, 0x5c, 0x4e, 0x48, 0x46, 0x3f, 0x3c, 0x3e, 0x3f,
0x3e, 0x46, 0x50, 0x51, 0x53, 0x5c, 0x60, 0x64, 0x71, 0x7d, 0x81, 0x8a, 0x96, 0x9f, 0xa9, 0xb5,
0xbd, 0xc0, 0xc7, 0xd0, 0xd3, 0xd4, 0xd6, 0xd0, 0xc8, 0xc5, 0xbf, 0xb3, 0xaa, 0xa0, 0x92, 0x87,
0x7f, 0x73, 0x66, 0x5c, 0x53, 0x4a, 0x45, 0x42, 0x3a, 0x33, 0x34, 0x34, 0x31, 0x36, 0x3c, 0x3d,
0x42, 0x4e, 0x57, 0x5d, 0x6a, 0x75, 0x7d, 0x8c, 0x9e, 0xa7, 0xae, 0xbb, 0xc3, 0xc5, 0xcb, 0xd1,
0xcf, 0xcc, 0xcf, 0xcf, 0xc9, 0xc6, 0xc1, 0xb6, 0xae, 0xac, 0xa4, 0x97, 0x8e, 0x86, 0x77, 0x6d,
0x68, 0x5d, 0x4d, 0x45, 0x41, 0x3a, 0x37, 0x37, 0x35, 0x34, 0x3b, 0x44, 0x49, 0x50, 0x57, 0x5b,
0x63, 0x71, 0x7c, 0x82, 0x8c, 0x96, 0xa0, 0xab, 0xb5, 0xbb, 0xbf, 0xc5, 0xca, 0xd0, 0xd5, 0xd5,
0xcf, 0xcb, 0xc6, 0xc0, 0xb8, 0xae, 0xa0, 0x91, 0x86, 0x7d, 0x6f, 0x60, 0x54, 0x48, 0x3e, 0x3b,
0x37, 0x2e, 0x29, 0x2a, 0x2a, 0x2c, 0x34, 0x37, 0x36, 0x3a, 0x43, 0x49, 0x4f, 0x59, 0x60, 0x66,
0x74, 0x84, 0x8e, 0x97, 0xa3, 0xae, 0xb4, 0xc0, 0xc9, 0xca, 0xca, 0xce, 0xce, 0xca, 0xc6, 0xbe,
0xb2, 0xa9, 0xa5, 0x9e, 0x90, 0x86, 0x7d, 0x70, 0x67, 0x63, 0x5a, 0x4e, 0x45, 0x41, 0x3c, 0x3a,
0x39, 0x37, 0x37, 0x3c, 0x42, 0x47, 0x4d, 0x53, 0x59, 0x62, 0x6d, 0x79, 0x81, 0x89, 0x92, 0x9c,
0xa8, 0xb1, 0xb6, 0xba, 0xbd, 0xc0, 0xc5, 0xca, 0xc9, 0xc2, 0xbf, 0xbd, 0xb9, 0xb3, 0xab, 0x9e,
0x91, 0x87, 0x7d, 0x6d, 0x5f, 0x51, 0x43, 0x37, 0x33, 0x2c, 0x20, 0x1b, 0x1b, 0x1b, 0x1d, 0x25,
0x2b, 0x2c, 0x34, 0x3e, 0x47, 0x4f, 0x58, 0x5d, 0x63, 0x6e, 0x7e, 0x86, 0x8c, 0x96, 0xa0, 0xa6,
0xb0, 0xba, 0xbc, 0xbe, 0xc3, 0xc6, 0xc4, 0xc2, 0xbe, 0xb4, 0xae, 0xaa, 0xa3, 0x97, 0x8b, 0x81,
0x77, 0x6d, 0x68, 0x5f, 0x54, 0x4b, 0x47, 0x43, 0x40, 0x3f, 0x3c, 0x3c, 0x3f, 0x45, 0x4a, 0x4e,
0x53, 0x58, 0x61, 0x6b, 0x76, 0x80, 0x88, 0x91, 0x9c, 0xa7, 0xaf, 0xb3, 0xb8, 0xbb, 0xbd, 0xc0,
0xc1, 0xbe, 0xb7, 0xb2, 0xad, 0xa8, 0xa3, 0x9b, 0x90, 0x85, 0x7e, 0x74, 0x67, 0x5c, 0x53, 0x47,
0x3b, 0x35, 0x2e, 0x22, 0x1b, 0x19, 0x18, 0x18, 0x1d, 0x21, 0x23, 0x2b, 0x36, 0x3e, 0x46, 0x52,
0x5b, 0x63, 0x70, 0x80, 0x8a, 0x92, 0x9d, 0xa7, 0xae, 0xb7, 0xc0, 0xc3, 0xc5, 0xc9, 0xca, 0xc9,
0xc7, 0xc3, 0xbc, 0xb5, 0xb1, 0xaa, 0x9d, 0x90, 0x87, 0x7e, 0x74, 0x6c, 0x63, 0x58, 0x50, 0x4b,
0x49, 0x46, 0x44, 0x41, 0x42, 0x44, 0x48, 0x4c, 0x4e, 0x50, 0x54, 0x5a, 0x62, 0x69, 0x72, 0x79,
0x80, 0x8b, 0x97, 0x9f, 0xa4, 0xac, 0xb3, 0xb8, 0xbc, 0xbd, 0xba, 0xb5, 0xb1, 0xad, 0xa6, 0xa1,
0x98, 0x8b, 0x80, 0x79, 0x6f, 0x62, 0x57, 0x4f, 0x46, 0x3d, 0x38, 0x34, 0x2b, 0x27, 0x27, 0x27,
0x29, 0x2c, 0x2e, 0x30, 0x39, 0x43, 0x4a, 0x50, 0x59, 0x64, 0x6c, 0x78, 0x87, 0x91, 0x98, 0xa2,
0xad, 0xb5, 0xbc, 0xc3, 0xc7, 0xc9, 0xcb, 0xcd, 0xcb, 0xc8, 0xc6, 0xc1, 0xbb, 0xb7, 0xaf, 0xa1,
0x94, 0x89, 0x80, 0x75, 0x69, 0x5d, 0x52, 0x48, 0x41, 0x3e, 0x3b, 0x37, 0x37, 0x39, 0x3d, 0x42,
0x47, 0x4a, 0x4f, 0x56, 0x5d, 0x63, 0x6a, 0x73, 0x79, 0x7e, 0x87, 0x91, 0x98, 0x9c, 0xa3, 0xac,
0xb2, 0xb7, 0xba, 0xb9, 0xb7, 0xb8, 0xb7, 0xb3, 0xaf, 0xa9, 0x9e, 0x94, 0x8c, 0x82, 0x74, 0x69,
0x61, 0x57, 0x4d, 0x46, 0x40, 0x38, 0x34, 0x35, 0x35, 0x34, 0x36, 0x39, 0x3b, 0x42, 0x4b, 0x4f,
0x52, 0x5b, 0x66, 0x6f, 0x79, 0x85, 0x8e, 0x95, 0x9f, 0xaa, 0xb1, 0xb6, 0xbb, 0xbe, 0xbf, 0xc1,
0xc1, 0xbd, 0xb8, 0xb5, 0xb3, 0xad, 0xa8, 0xa2, 0x98, 0x8c, 0x85, 0x80, 0x77, 0x6b, 0x61, 0x58,
0x51, 0x4b, 0x48, 0x42, 0x3d, 0x3c, 0x3e, 0x40, 0x43, 0x46, 0x49, 0x4e, 0x59, 0x63, 0x69, 0x71,
0x7b, 0x83, 0x8c, 0x98, 0xa2, 0xa8, 0xad, 0xb5, 0xbd, 0xc2, 0xc5, 0xc5, 0xc4, 0xc3, 0xc3, 0xc2,
0xbe, 0xb8, 0xb1, 0xa9, 0x9f, 0x97, 0x8c, 0x7d, 0x6f, 0x67, 0x5c, 0x4f, 0x47, 0x40, 0x37, 0x33,
0x34, 0x33, 0x33, 0x34, 0x36, 0x38, 0x3d, 0x45, 0x49, 0x4a, 0x50, 0x5b, 0x62, 0x69, 0x74, 0x7d,
0x83, 0x8f, 0x9b, 0xa5, 0xab, 0xb2, 0xb8, 0xbc, 0xc0, 0xc1, 0xbf, 0xbb, 0xb8, 0xb7, 0xb1, 0xaa,
0xa3, 0x99, 0x8e, 0x87, 0x82, 0x78, 0x6c, 0x64, 0x5f, 0x5a, 0x58, 0x56, 0x53, 0x50, 0x50, 0x53,
0x57, 0x59, 0x5a, 0x5c, 0x61, 0x68, 0x71, 0x75, 0x7a, 0x82, 0x8d, 0x95, 0xa0, 0xaa, 0xb0, 0xb3,
0xbb, 0xc4, 0xc7, 0xc7, 0xc6, 0xc2, 0xbf, 0xbf, 0xbb, 0xb4, 0xae, 0xa8, 0xa0, 0x99, 0x92, 0x88,
0x79, 0x6b, 0x62, 0x58, 0x4d, 0x41, 0x38, 0x2e, 0x29, 0x2a, 0x29, 0x26, 0x27, 0x2c, 0x2f, 0x35,
0x3d, 0x42, 0x45, 0x4e, 0x5a, 0x64, 0x6c, 0x75, 0x7e, 0x85, 0x90, 0x9d, 0xa5, 0xaa, 0xaf, 0xb6,
0xbb, 0xbf, 0xc2, 0xc2, 0xc0, 0xbf, 0xc1, 0xc0, 0xbb, 0xb7, 0xaf, 0xa5, 0x9f, 0x98, 0x8d, 0x80,
0x78, 0x71, 0x6c, 0x67, 0x64, 0x61, 0x5d, 0x5e, 0x62, 0x65, 0x65, 0x66, 0x65, 0x67, 0x6e, 0x73,
0x75, 0x78, 0x7e, 0x85, 0x8c, 0x94, 0x9d, 0xa4, 0xa9, 0xb0, 0xb9, 0xbc, 0xbc, 0xbb, 0xb8, 0xb6,
0xb5, 0xb0, 0xa8, 0xa1, 0x9b, 0x92, 0x89, 0x82, 0x7a, 0x6c, 0x5e, 0x57, 0x51, 0x47, 0x3d, 0x36,
0x30, 0x2d, 0x2d, 0x2e, 0x2d, 0x2e, 0x32, 0x36, 0x39, 0x40, 0x45, 0x48, 0x51, 0x5f, 0x6a, 0x73,
0x7f, 0x89, 0x94, 0xa1, 0xad, 0xb6, 0xbb, 0xc2, 0xc8, 0xcd, 0xcf, 0xd1, 0xcf, 0xcc, 0xcb, 0xcc,
0xca, 0xc6, 0xc0, 0xb9, 0xb2, 0xaa, 0xa1, 0x95, 0x87, 0x7c, 0x75, 0x6d, 0x66, 0x60, 0x5c, 0x57,
0x57, 0x5b, 0x5c, 0x5c, 0x5d, 0x5e, 0x61, 0x67, 0x6b, 0x6d, 0x6e, 0x73, 0x7a, 0x7f, 0x84, 0x8c,
0x91, 0x94, 0x9a, 0xa4, 0xa9, 0xaa, 0xaa, 0xab, 0xac, 0xac, 0xac, 0xa7, 0xa1, 0x9d, 0x99, 0x91,
0x89, 0x80, 0x73, 0x66, 0x5e, 0x56, 0x4c, 0x43, 0x3c, 0x38, 0x36, 0x39, 0x3d, 0x3e, 0x3f, 0x45,
0x4b, 0x50, 0x56, 0x5a, 0x5e, 0x67, 0x71, 0x7a, 0x81, 0x8a, 0x94, 0xa0, 0xaa, 0xb4, 0xbd, 0xc2,
0xc6, 0xcc, 0xd0, 0xd0, 0xce, 0xcb, 0xc7, 0xc5, 0xc4, 0xbf, 0xb9, 0xb5, 0xaf, 0xa8, 0xa0, 0x99,
0x8d, 0x80, 0x76, 0x6f, 0x66, 0x5c, 0x54, 0x50, 0x4d, 0x4b, 0x4a, 0x49, 0x49, 0x4a, 0x4c, 0x4f,
0x56, 0x5d, 0x61, 0x65, 0x6e, 0x79, 0x80, 0x87, 0x90, 0x98, 0x9d, 0xa3, 0xaa, 0xad, 0xac, 0xac,
0xac, 0xac, 0xac, 0xab, 0xa8, 0xa5, 0xa5, 0xa4, 0x9e, 0x97, 0x91, 0x88, 0x7e, 0x76, 0x6e, 0x64,
0x58, 0x50, 0x4d, 0x4c, 0x4b, 0x4b, 0x4c, 0x4e, 0x52, 0x57, 0x59, 0x5c, 0x60, 0x63, 0x67, 0x6f,
0x77, 0x7b, 0x80, 0x89, 0x93, 0x9d, 0xa5, 0xab, 0xb0, 0xb7, 0xbd, 0xc0, 0xc1, 0xbe, 0xbb, 0xb8,
0xb5, 0xb3, 0xaf, 0xa7, 0xa0, 0x9b, 0x93, 0x8c, 0x85, 0x7b, 0x6f, 0x66, 0x61, 0x5c, 0x57, 0x53,
0x51, 0x52, 0x53, 0x57, 0x57, 0x55, 0x55, 0x58, 0x5a, 0x5d, 0x61, 0x64, 0x68, 0x70, 0x7b, 0x83,
0x8a, 0x95, 0xa2, 0xaa, 0xb0, 0xb9, 0xc0, 0xc1, 0xc1, 0xc2, 0xc1, 0xc0, 0xbf, 0xbb, 0xb5, 0xb2,
0xaf, 0xa8, 0xa1, 0x9b, 0x94, 0x8a, 0x7e, 0x6e, 0x61, 0x57, 0x50, 0x4c, 0x43, 0x3d, 0x41, 0x45,
0x44, 0x46, 0x4c, 0x52, 0x55, 0x53, 0x51, 0x55, 0x5c, 0x61, 0x67, 0x70, 0x7d, 0x8a, 0x93, 0x99,
0x9c, 0x9f, 0xa4, 0xaa, 0xad, 0xa7, 0x9d, 0x98, 0x9b, 0x9f, 0x9f, 0x9d, 0x9e, 0xa2, 0xa6, 0xa0,
0x94, 0x8a, 0x81, 0x74, 0x69, 0x68, 0x68, 0x66, 0x62, 0x5e, 0x5f, 0x63, 0x5f, 0x5a, 0x5b, 0x63,
0x69, 0x6d, 0x71, 0x79, 0x7d, 0x78, 0x74, 0x7c, 0x84, 0x88, 0x8d, 0x9a, 0xa9, 0xb4, 0xbb, 0xbe,
0xbe, 0xbd, 0xbc, 0xbb, 0xbd, 0xbf, 0xbc, 0xb6, 0xb3, 0xb1, 0xa8, 0x9d, 0x96, 0x90, 0x86, 0x77,
0x68, 0x5d, 0x50, 0x43, 0x3c, 0x37, 0x34, 0x31, 0x2c, 0x28, 0x2a, 0x27, 0x21, 0x21, 0x28, 0x31,
0x38, 0x40, 0x4d, 0x59, 0x62, 0x6e, 0x7c, 0x88, 0x93, 0x9f, 0xaa, 0xb4, 0xbe, 0xc2, 0xc1, 0xc1,
0xc4, 0xc5, 0xc6, 0xc9, 0xc8, 0xc1, 0xbd, 0xbc, 0xb8, 0xb1, 0xa8, 0xa1, 0x9c, 0x93, 0x88, 0x7d,
0x70, 0x69, 0x66, 0x62, 0x60, 0x63, 0x66, 0x65, 0x63, 0x67, 0x6f, 0x74, 0x75, 0x78, 0x81, 0x8f,
0x9a, 0x9f, 0xa4, 0xb0, 0xbc, 0xc2, 0xc5, 0xcb, 0xcf, 0xcf, 0xcb, 0xc9, 0xc6, 0xbd, 0xb4, 0xac,
0xa5, 0x9e, 0x96, 0x8a, 0x7c, 0x6f, 0x64, 0x57, 0x4e, 0x46, 0x3b, 0x2f, 0x26, 0x25, 0x22, 0x1b,
0x1c, 0x25, 0x2f, 0x38, 0x40, 0x46, 0x4c, 0x55, 0x5a, 0x5c, 0x66, 0x73, 0x7a, 0x7e, 0x8a, 0x9c,
0xa9, 0xaf, 0xb6, 0xbf, 0xc8, 0xcd, 0xd0, 0xd2, 0xcf, 0xcd, 0xcb, 0xc4, 0xbe, 0xbb, 0xb6, 0xb3,
0xb4, 0xae, 0xa3, 0x99, 0x93, 0x89, 0x80, 0x7a, 0x79, 0x79, 0x78, 0x76, 0x77, 0x78, 0x78, 0x78,
0x7a, 0x80, 0x85, 0x87, 0x86, 0x85, 0x87, 0x86, 0x83, 0x84, 0x88, 0x8b, 0x8d, 0x92, 0x96, 0x97,
0x93, 0x8c, 0x87, 0x88, 0x89, 0x87, 0x84, 0x83, 0x82, 0x7e, 0x78, 0x76, 0x77, 0x74, 0x72, 0x73,
0x72, 0x6a, 0x5d, 0x53, 0x4d, 0x4a, 0x49, 0x4a, 0x4b, 0x4d, 0x4f, 0x4f, 0x51, 0x55, 0x58, 0x57,
0x5a, 0x64, 0x6b, 0x6a, 0x6a, 0x6f, 0x73, 0x78, 0x7e, 0x86, 0x92, 0x9c, 0xa2, 0xa3, 0xa8, 0xae,
0xaf, 0xad, 0xb3, 0xbe, 0xc4, 0xc5, 0xc4, 0xc5, 0xc5, 0xc3, 0xc0, 0xbd, 0xb9, 0xb5, 0xae, 0xa3,
0x96, 0x88, 0x7b, 0x70, 0x6b, 0x67, 0x61, 0x5c, 0x5a, 0x57, 0x4f, 0x46, 0x41, 0x3e, 0x3d, 0x3d,
0x40, 0x48, 0x51, 0x55, 0x55, 0x5e, 0x70, 0x7f, 0x86, 0x8d, 0x98, 0xa2, 0xa3, 0xa2, 0xa4, 0xa6,
0xa6, 0xa5, 0xa4, 0xa5, 0xa5, 0x9e, 0x94, 0x8f, 0x8d, 0x84, 0x78, 0x70, 0x69, 0x5d, 0x4f, 0x47,
0x43, 0x3a, 0x33, 0x32, 0x38, 0x3e, 0x3d, 0x39, 0x3b, 0x43, 0x49, 0x49, 0x4d, 0x5d, 0x6c, 0x75,
0x7f, 0x8f, 0x9f, 0xa8, 0xae, 0xb9, 0xc6, 0xcb, 0xcc, 0xce, 0xd2, 0xd4, 0xd0, 0xc7, 0xc3, 0xc1,
0xba, 0xaf, 0xa8, 0xa4, 0x98, 0x83, 0x75, 0x6e, 0x62, 0x50, 0x42, 0x3d, 0x3d, 0x39, 0x34, 0x35,
0x39, 0x3e, 0x42, 0x47, 0x4f, 0x59, 0x5c, 0x5d, 0x66, 0x71, 0x75, 0x79, 0x82, 0x8f, 0x98, 0x9f,
0xa8, 0xb0, 0xb5, 0xb4, 0xb2, 0xb5, 0xb8, 0xb5, 0xac, 0xa8, 0xa9, 0xa4, 0x96, 0x8a, 0x86, 0x83,
0x7b, 0x71, 0x6a, 0x62, 0x5a, 0x4f, 0x43, 0x3e, 0x3f, 0x3f, 0x3f, 0x42, 0x4b, 0x52, 0x54, 0x57,
0x5d, 0x63, 0x68, 0x70, 0x78, 0x7d, 0x7e, 0x80, 0x85, 0x8d, 0x90, 0x91, 0x96, 0xa0, 0xa6, 0xa2,
0x9b, 0x99, 0x98, 0x91, 0x89, 0x87, 0x8c, 0x8a, 0x82, 0x7f, 0x82, 0x83, 0x80, 0x7c, 0x7c, 0x80,
0x82, 0x7d, 0x74, 0x71, 0x6f, 0x6a, 0x6b, 0x72, 0x76, 0x75, 0x76, 0x7c, 0x80, 0x7f, 0x7b, 0x7c,
0x81, 0x85, 0x85, 0x85, 0x89, 0x8d, 0x8b, 0x87, 0x8b, 0x96, 0x99, 0x93, 0x95, 0x9c, 0x9e, 0x98,
0x92, 0x90, 0x94, 0x96, 0x96, 0x98, 0x9b, 0x9b, 0x95, 0x91, 0x92, 0x90, 0x86, 0x7e, 0x7d, 0x7d,
0x73, 0x66, 0x61, 0x5f, 0x59, 0x54, 0x53, 0x55, 0x55, 0x4e, 0x46, 0x46, 0x48, 0x42, 0x3b, 0x3c,
0x45, 0x4e, 0x4f, 0x52, 0x5e, 0x69, 0x6f, 0x77, 0x85, 0x91, 0x97, 0x9d, 0xa3, 0xab, 0xaf, 0xac,
0xac, 0xb4, 0xba, 0xb9, 0xb7, 0xb9, 0xba, 0xb3, 0xac, 0xa9, 0xa5, 0x9d, 0x91, 0x89, 0x89, 0x87,
0x81, 0x78, 0x71, 0x71, 0x72, 0x6d, 0x67, 0x68, 0x6a, 0x66, 0x66, 0x69, 0x6c, 0x6e, 0x73, 0x7e,
0x8b, 0x96, 0x9e, 0xa5, 0xad, 0xb4, 0xb5, 0xb9, 0xbf, 0xc3, 0xc0, 0xba, 0xba, 0xbc, 0xb5, 0xa7,
0x9f, 0x9d, 0x9a, 0x8e, 0x7f, 0x6f, 0x64, 0x57, 0x44, 0x35, 0x2d, 0x28, 0x23, 0x1d, 0x1e, 0x22,
0x22, 0x20, 0x25, 0x2e, 0x37, 0x3e, 0x48, 0x53, 0x5b, 0x62, 0x6b, 0x76, 0x82, 0x8b, 0x95, 0xa2,
0xb2, 0xbc, 0xbb, 0xbd, 0xc5, 0xcb, 0xc9, 0xc5, 0xc9, 0xce, 0xc9, 0xc3, 0xc0, 0xba, 0xb5, 0xac,
0xa0, 0x9b, 0x99, 0x8f, 0x80, 0x75, 0x72, 0x6c, 0x66, 0x67, 0x6d, 0x70, 0x72, 0x78, 0x7f, 0x80,
0x80, 0x80, 0x85, 0x8e, 0x93, 0x94, 0x95, 0x9b, 0xa2, 0xa1, 0xa0, 0xa6, 0xac, 0xab, 0xa6, 0xa5,
0xa5, 0x9c, 0x8f, 0x87, 0x83, 0x80, 0x7a, 0x74, 0x70, 0x6e, 0x69, 0x60, 0x5b, 0x5b, 0x55, 0x4f,
0x4d, 0x51, 0x50, 0x46, 0x42, 0x46, 0x4b, 0x4d, 0x4d, 0x51, 0x5b, 0x61, 0x63, 0x63, 0x68, 0x71,
0x73, 0x74, 0x7d, 0x88, 0x8f, 0x90, 0x96, 0x9e, 0xa2, 0xa4, 0xa6, 0xa9, 0xaa, 0xa9, 0xa9, 0xaa,
0xa8, 0xa5, 0xa1, 0xa3, 0xad, 0xb1, 0xad, 0xad, 0xb3, 0xb6, 0xae, 0xa7, 0xa5, 0xa4, 0x9f, 0x98,
0x94, 0x92, 0x91, 0x8c, 0x81, 0x7f, 0x84, 0x82, 0x7a, 0x74, 0x74, 0x73, 0x6b, 0x65, 0x64, 0x61,
0x60, 0x63, 0x69, 0x6e, 0x71, 0x74, 0x78, 0x7c, 0x80, 0x81, 0x83, 0x8a, 0x91, 0x91, 0x8f, 0x92,
0x97, 0x91, 0x8c, 0x8e, 0x92, 0x91, 0x88, 0x80, 0x7c, 0x78, 0x73, 0x69, 0x60, 0x61, 0x62, 0x5f,
0x5b, 0x5b, 0x5c, 0x57, 0x53, 0x55, 0x57, 0x56, 0x53, 0x56, 0x5c, 0x5e, 0x61, 0x65, 0x6e, 0x7e,
0x87, 0x91, 0x9f, 0xad, 0xb5, 0xb4, 0xb8, 0xc3, 0xc7, 0xc5, 0xc6, 0xca, 0xce, 0xcb, 0xc5, 0xbf,
0xbb, 0xb8, 0xb2, 0xa8, 0xa1, 0x98, 0x8a, 0x7a, 0x72, 0x6c, 0x5e, 0x54, 0x54, 0x54, 0x53, 0x4f,
0x4f, 0x51, 0x4f, 0x4e, 0x4f, 0x56, 0x63, 0x6a, 0x6a, 0x70, 0x7f, 0x89, 0x89, 0x8c, 0x9a, 0xa5,
0xab, 0xad, 0xae, 0xb1, 0xaf, 0xae, 0xad, 0xab, 0xac, 0xaa, 0xa3, 0xa0, 0x9d, 0x95, 0x88, 0x7e,
0x79, 0x6d, 0x60, 0x5a, 0x55, 0x4b, 0x3e, 0x3a, 0x3d, 0x3f, 0x40, 0x42, 0x49, 0x57, 0x5f, 0x5f,
0x5f, 0x65, 0x71, 0x75, 0x79, 0x83, 0x8d, 0x92, 0x96, 0x9e, 0xa6, 0xa9, 0xac, 0xaf, 0xaf, 0xb1,
0xb1, 0xac, 0xa7, 0xa2, 0x9e, 0x98, 0x96, 0x97, 0x92, 0x8a, 0x87, 0x87, 0x84, 0x79, 0x70, 0x6f,
0x6e, 0x70, 0x6e, 0x6a, 0x6c, 0x6f, 0x70, 0x6f, 0x72, 0x7a, 0x7a, 0x78, 0x7e, 0x82, 0x82, 0x81,
0x83, 0x88, 0x8b, 0x92, 0x99, 0x9d, 0xa1, 0xa3, 0xa5, 0xa5, 0xa3, 0xa1, 0x9b, 0x96, 0x9a, 0x98,
0x8f, 0x88, 0x85, 0x85, 0x82, 0x80, 0x81, 0x82, 0x82, 0x7d, 0x76, 0x73, 0x70, 0x6b, 0x62, 0x5e,
0x63, 0x64, 0x5f, 0x5b, 0x5c, 0x5d, 0x5b, 0x5b, 0x5d, 0x5b, 0x5a, 0x5c, 0x5e, 0x61, 0x60, 0x5e,
0x61, 0x68, 0x74, 0x78, 0x77, 0x80, 0x88, 0x8a, 0x88, 0x8b, 0x94, 0x98, 0x9a, 0x9f, 0xa6, 0xad,
0xae, 0xab, 0xab, 0xac, 0xb0, 0xac, 0xa5, 0xa3, 0xa0, 0x98, 0x92, 0x8e, 0x8b, 0x83, 0x80, 0x85,
0x87, 0x83, 0x7f, 0x7c, 0x7a, 0x77, 0x73, 0x6f, 0x6f, 0x75, 0x74, 0x6e, 0x72, 0x7b, 0x80, 0x7f,
0x84, 0x94, 0x9e, 0xa1, 0xa3, 0xa4, 0xa7, 0xa9, 0xa8, 0xa6, 0xa4, 0xa6, 0xa4, 0x9f, 0x9f, 0x9a,
0x8f, 0x85, 0x81, 0x7c, 0x6d, 0x61, 0x5d, 0x54, 0x49, 0x3e, 0x3a, 0x39, 0x34, 0x2f, 0x2e, 0x30,
0x36, 0x36, 0x30, 0x30, 0x3a, 0x46, 0x4b, 0x51, 0x60, 0x6e, 0x7a, 0x85, 0x90, 0x99, 0xa0, 0xaa,
0xb2, 0xb7, 0xbd, 0xc0, 0xbf, 0xc1, 0xc3, 0xc2, 0xc0, 0xc1, 0xc4, 0xc0, 0xb9, 0xb8, 0xb4, 0xa8,
0x99, 0x90, 0x8b, 0x84, 0x7e, 0x75, 0x6b, 0x6a, 0x69, 0x66, 0x63, 0x66, 0x6e, 0x70, 0x71, 0x79,
0x7b, 0x7b, 0x7f, 0x83, 0x89, 0x8f, 0x96, 0x9c, 0x9e, 0xa5, 0xaa, 0xa9, 0xa9, 0xa9, 0xa6, 0xa0,
0x9d, 0x9d, 0x95, 0x89, 0x82, 0x7f, 0x7b, 0x73, 0x6b, 0x64, 0x5e, 0x5a, 0x51, 0x45, 0x3e, 0x3b,
0x38, 0x36, 0x37, 0x3f, 0x42, 0x41, 0x46, 0x4c, 0x51, 0x55, 0x59, 0x5f, 0x63, 0x69, 0x71, 0x76,
0x7b, 0x80, 0x83, 0x8c, 0x9a, 0xa5, 0xa5, 0xa6, 0xb2, 0xb7, 0xb4, 0xae, 0xad, 0xb0, 0xaf, 0xac,
0xaa, 0xa7, 0xa7, 0xa5, 0x9d, 0x9b, 0x9f, 0xa0, 0x9a, 0x95, 0x98, 0x96, 0x8e, 0x8a, 0x89, 0x86,
0x83, 0x85, 0x8a, 0x87, 0x83, 0x82, 0x80, 0x82, 0x80, 0x79, 0x75, 0x78, 0x7d, 0x77, 0x71, 0x76,
0x7b, 0x78, 0x74, 0x77, 0x7e, 0x7e, 0x7b, 0x79, 0x78, 0x79, 0x79, 0x77, 0x77, 0x7b, 0x80, 0x82,
0x83, 0x86, 0x84, 0x7f, 0x7e, 0x7c, 0x74, 0x6b, 0x69, 0x6a, 0x64, 0x5b, 0x59, 0x59, 0x59, 0x58,
0x5b, 0x5e, 0x61, 0x65, 0x60, 0x59, 0x5b, 0x61, 0x62, 0x63, 0x68, 0x73, 0x7a, 0x80, 0x87, 0x89,
0x8d, 0x97, 0xa0, 0xa7, 0xa9, 0xaa, 0xad, 0xb1, 0xb6, 0xb7, 0xb6, 0xb9, 0xc1, 0xc5, 0xbf, 0xba,
0xb8, 0xaf, 0xa4, 0x9c, 0x96, 0x92, 0x8c, 0x81, 0x77, 0x6e, 0x6a, 0x63, 0x58, 0x54, 0x53, 0x53,
0x54, 0x54, 0x54, 0x4f, 0x4b, 0x51, 0x58, 0x5c, 0x64, 0x71, 0x7e, 0x89, 0x93, 0x98, 0x99, 0x9e,
0xa2, 0xa1, 0xa2, 0xa8, 0xab, 0xa4, 0x9e, 0x9e, 0x9d, 0x9b, 0x97, 0x91, 0x8c, 0x88, 0x82, 0x77,
0x66, 0x5d, 0x59, 0x52, 0x4a, 0x46, 0x42, 0x3b, 0x38, 0x39, 0x36, 0x36, 0x3d, 0x44, 0x4c, 0x55,
0x5f, 0x67, 0x6e, 0x77, 0x80, 0x86, 0x94, 0xa4, 0xac, 0xaf, 0xb6, 0xc2, 0xc5, 0xbe, 0xbb, 0xbc,
0xbe, 0xbc, 0xb5, 0xae, 0xaa, 0xa8, 0xa1, 0x97, 0x91, 0x8f, 0x89, 0x80, 0x7b, 0x77, 0x6c, 0x61,
0x60, 0x5e, 0x5c, 0x60, 0x67, 0x6b, 0x6b, 0x6b, 0x70, 0x76, 0x7b, 0x7b, 0x79, 0x80, 0x8a, 0x8e,
0x8b, 0x8e, 0x9c, 0xa2, 0xa0, 0xa3, 0xac, 0xb1, 0xb0, 0xb0, 0xae, 0xa8, 0xa2, 0x9a, 0x90, 0x8b,
0x89, 0x83, 0x7d, 0x78, 0x73, 0x6b, 0x66, 0x66, 0x62, 0x5a, 0x56, 0x58, 0x58, 0x50, 0x48, 0x46,
0x48, 0x50, 0x56, 0x58, 0x5e, 0x68, 0x6f, 0x6d, 0x69, 0x6e, 0x71, 0x6f, 0x72, 0x7c, 0x85, 0x8b,
0x8c, 0x8b, 0x8a, 0x8b, 0x8d, 0x8d, 0x8e, 0x8e, 0x8f, 0x8d, 0x8d, 0x90, 0x8b, 0x85, 0x8a, 0x94,
0x96, 0x93, 0x93, 0x96, 0x95, 0x93, 0x91, 0x8e, 0x8f, 0x90, 0x8c, 0x88, 0x8a, 0x8f, 0x8e, 0x8a,
0x8d, 0x93, 0x94, 0x92, 0x91, 0x8d, 0x86, 0x83, 0x82, 0x7f, 0x7c, 0x7d, 0x7e, 0x80, 0x81, 0x80,
0x79, 0x77, 0x7d, 0x7f, 0x7c, 0x7f, 0x86, 0x8a, 0x88, 0x86, 0x88, 0x8b, 0x8b, 0x8a, 0x89, 0x89,
0x8a, 0x85, 0x7a, 0x71, 0x71, 0x6e, 0x66, 0x60, 0x5b, 0x56, 0x53, 0x50, 0x4b, 0x46, 0x41, 0x42,
0x45, 0x46, 0x45, 0x44, 0x45, 0x49, 0x4f, 0x51, 0x58, 0x68, 0x76, 0x7a, 0x80, 0x8e, 0x98, 0x99,
0x99, 0xa2, 0xaf, 0xb8, 0xbb, 0xba, 0xc0, 0xc9, 0xcb, 0xc9, 0xc9, 0xce, 0xcd, 0xc5, 0xbf, 0xbb,
0xb0, 0xa0, 0x95, 0x90, 0x85, 0x7a, 0x74, 0x70, 0x6b, 0x63, 0x5c, 0x57, 0x57, 0x58, 0x55, 0x57,
0x63, 0x6f, 0x70, 0x6e, 0x76, 0x83, 0x88, 0x8a, 0x93, 0x9d, 0xa5, 0xaa, 0xac, 0xac, 0xac, 0xa9,
0xa3, 0xa0, 0xa2, 0x9d, 0x91, 0x89, 0x88, 0x80, 0x72, 0x67, 0x62, 0x58, 0x49, 0x3f, 0x3b, 0x34,
0x29, 0x21, 0x1d, 0x20, 0x27, 0x2b, 0x2c, 0x34, 0x40, 0x45, 0x46, 0x4c, 0x54, 0x5c, 0x67, 0x76,
0x85, 0x91, 0x9a, 0xa4, 0xad, 0xb3, 0xba, 0xc2, 0xc9, 0xcf, 0xd2, 0xcd, 0xc8, 0xc7, 0xc1, 0xb4,
0xac, 0xac, 0xaa, 0xa0, 0x94, 0x8d, 0x87, 0x80, 0x77, 0x6d, 0x69, 0x6f, 0x71, 0x6d, 0x6b, 0x6c,
0x6e, 0x6c, 0x6c, 0x74, 0x7d, 0x80, 0x84, 0x89, 0x89, 0x89, 0x8a, 0x8a, 0x8c, 0x91, 0x96, 0x99,
0x9b, 0x99, 0x91, 0x89, 0x89, 0x8a, 0x81, 0x77, 0x74, 0x73, 0x6c, 0x5f, 0x55, 0x51, 0x51, 0x50,
0x4e, 0x4d, 0x52, 0x54, 0x4b, 0x41, 0x42, 0x48, 0x46, 0x44, 0x4b, 0x54, 0x5a, 0x5d, 0x61, 0x64,
0x68, 0x6e, 0x76, 0x7f, 0x83, 0x82, 0x81, 0x82, 0x86, 0x85, 0x80, 0x81, 0x8b, 0x8e, 0x8a, 0x89,
0x8d, 0x8e, 0x89, 0x85, 0x8a, 0x92, 0x96, 0x95, 0x96, 0x9c, 0xa0, 0x9f, 0x9b, 0x9f, 0xa8, 0xaa,
0xa7, 0xa8, 0xa9, 0x9f, 0x96, 0x94, 0x94, 0x8e, 0x87, 0x83, 0x84, 0x82, 0x7c, 0x72, 0x6d, 0x6e,
0x6a, 0x60, 0x5c, 0x5e, 0x5c, 0x52, 0x4d, 0x52, 0x59, 0x5a, 0x5b, 0x64, 0x6d, 0x73, 0x76, 0x77,
0x78, 0x7b, 0x7d, 0x7e, 0x82, 0x88, 0x89, 0x87, 0x8b, 0x8f, 0x8b, 0x83, 0x81, 0x85, 0x82, 0x7a,
0x73, 0x6f, 0x68, 0x5f, 0x55, 0x4d, 0x4d, 0x4d, 0x46, 0x40, 0x43, 0x47, 0x42, 0x3e, 0x42, 0x48,
0x50, 0x57, 0x60, 0x6b, 0x74, 0x7d, 0x85, 0x8e, 0x98, 0xa2, 0xaa, 0xb4, 0xc0, 0xc3, 0xc0, 0xc1,
0xc6, 0xc5, 0xc0, 0xbf, 0xc2, 0xbf, 0xb5, 0xac, 0xa6, 0x9f, 0x94, 0x85, 0x79, 0x73, 0x6f, 0x64,
0x56, 0x4d, 0x4b, 0x49, 0x44, 0x45, 0x4c, 0x51, 0x54, 0x59, 0x5e, 0x62, 0x66, 0x69, 0x6e, 0x78,
0x84, 0x8b, 0x91, 0x9b, 0xa3, 0xa2, 0xa3, 0xaa, 0xb0, 0xaf, 0xaa, 0xab, 0xac, 0xa2, 0x94, 0x8b,
0x85, 0x83, 0x7d, 0x70, 0x68, 0x66, 0x5c, 0x4c, 0x43, 0x41, 0x3e, 0x38, 0x35, 0x3a, 0x3f, 0x3d,
0x3c, 0x3f, 0x47, 0x50, 0x57, 0x61, 0x6e, 0x77, 0x79, 0x7c, 0x85, 0x8f, 0x90, 0x92, 0x9f, 0xac,
0xaf, 0xab, 0xab, 0xaf, 0xaf, 0xa9, 0xa5, 0xa4, 0xa4, 0x9e, 0x95, 0x8e, 0x8c, 0x88, 0x80, 0x7c,
0x80, 0x84, 0x80, 0x7c, 0x7e, 0x7d, 0x76, 0x70, 0x73, 0x76, 0x76, 0x77, 0x7c, 0x83, 0x87, 0x84,
0x82, 0x89, 0x92, 0x94, 0x91, 0x94, 0x9b, 0x9a, 0x92, 0x91, 0x94, 0x95, 0x91, 0x8f, 0x8f, 0x8f,
0x8c, 0x84, 0x7e, 0x7c, 0x7a, 0x77, 0x75, 0x76, 0x76, 0x72, 0x70, 0x72, 0x71, 0x6b, 0x68, 0x6e,
0x74, 0x72, 0x6d, 0x6b, 0x6a, 0x68, 0x64, 0x5e, 0x5f, 0x64, 0x65, 0x61, 0x60, 0x65, 0x65, 0x61,
0x5e, 0x61, 0x64, 0x65, 0x65, 0x66, 0x68, 0x6d, 0x72, 0x75, 0x7a, 0x80, 0x87, 0x8f, 0x98, 0x9e,
0x9f, 0x9e, 0xa1, 0xa7, 0xa8, 0xa9, 0xaf, 0xb5, 0xb6, 0xb6, 0xb5, 0xb4, 0xb3, 0xaf, 0xaa, 0xa6,
0xa6, 0xa5, 0x9a, 0x8d, 0x89, 0x87, 0x81, 0x7b, 0x78, 0x79, 0x77, 0x73, 0x6f, 0x6b, 0x6a, 0x69,
0x69, 0x6e, 0x76, 0x7b, 0x7d, 0x81, 0x89, 0x8c, 0x8c, 0x90, 0x9a, 0xa1, 0xa1, 0xa1, 0xa4, 0xa4,
0x9e, 0x95, 0x8f, 0x91, 0x91, 0x88, 0x7f, 0x7b, 0x79, 0x6f, 0x61, 0x57, 0x52, 0x4a, 0x41, 0x3c,
0x39, 0x35, 0x2f, 0x2b, 0x2f, 0x35, 0x38, 0x3c, 0x45, 0x51, 0x5a, 0x5b, 0x5f, 0x6c, 0x77, 0x7b,
0x83, 0x96, 0xa6, 0xac, 0xae, 0xb6, 0xc0, 0xc5, 0xc5, 0xc6, 0xcc, 0xd1, 0xce, 0xc6, 0xc1, 0xbf,
0xb7, 0xae, 0xa9, 0xa9, 0xa3, 0x98, 0x8f, 0x8b, 0x83, 0x79, 0x71, 0x70, 0x71, 0x6e, 0x6d, 0x71,
0x76, 0x76, 0x73, 0x75, 0x80, 0x8a, 0x8b, 0x8a, 0x91, 0x99, 0x99, 0x95, 0x96, 0x9c, 0xa0, 0x9f,
0x9d, 0x9f, 0x9f, 0x9a, 0x92, 0x8d, 0x8a, 0x84, 0x7c, 0x75, 0x71, 0x6b, 0x62, 0x5b, 0x56, 0x50,
0x4a, 0x48, 0x4b, 0x4d, 0x4b, 0x46, 0x44, 0x44, 0x45, 0x42, 0x42, 0x4b, 0x57, 0x5d, 0x60, 0x66,
0x6e, 0x73, 0x77, 0x7e, 0x85, 0x8c, 0x92, 0x95, 0x97, 0x9a, 0x9e, 0x9d, 0x9d, 0xa1, 0xa6, 0xa7,
0xa6, 0xa7, 0xa7, 0xa3, 0x9e, 0xa0, 0xa3, 0xa2, 0xa1, 0xa2, 0xa6, 0xa7, 0xa5, 0xa1, 0xa0, 0xa4,
0xa5, 0xa2, 0xa1, 0xa1, 0x9e, 0x97, 0x91, 0x8e, 0x8d, 0x89, 0x85, 0x84, 0x86, 0x83, 0x80, 0x7c,
0x77, 0x75, 0x71, 0x6e, 0x70, 0x71, 0x6f, 0x6c, 0x6c, 0x70, 0x6e, 0x6c, 0x70, 0x77, 0x7c, 0x7b,
0x7a, 0x7b, 0x7d, 0x7a, 0x74, 0x76, 0x7d, 0x80, 0x7c, 0x79, 0x7b, 0x7d, 0x76, 0x6e, 0x6d, 0x6d,
0x6a, 0x65, 0x61, 0x5e, 0x59, 0x53, 0x51, 0x52, 0x53, 0x52, 0x53, 0x5b, 0x62, 0x62, 0x5f, 0x64,
0x6f, 0x76, 0x7b, 0x84, 0x93, 0x9d, 0xa0, 0xa5, 0xaf, 0xb8, 0xbe, 0xc0, 0xc4, 0xcd, 0xd1, 0xcd,
0xc8, 0xc6, 0xc4, 0xbd, 0xb7, 0xb5, 0xb2, 0xad, 0xa4, 0x9c, 0x95, 0x8b, 0x7e, 0x74, 0x6e, 0x6a,
0x64, 0x5e, 0x5a, 0x58, 0x55, 0x51, 0x54, 0x5b, 0x5f, 0x61, 0x65, 0x6b, 0x72, 0x75, 0x76, 0x7c,
0x84, 0x8c, 0x93, 0x98, 0x9e, 0xa2, 0xa3, 0xa1, 0xa0, 0xa1, 0x9f, 0x9b, 0x98, 0x94, 0x8d, 0x81,
0x79, 0x71, 0x69, 0x62, 0x5d, 0x58, 0x54, 0x4f, 0x48, 0x42, 0x40, 0x3f, 0x3e, 0x3f, 0x45, 0x4d,
0x53, 0x56, 0x5b, 0x64, 0x6d, 0x76, 0x7d, 0x85, 0x90, 0x98, 0x9d, 0xa1, 0xa5, 0xaa, 0xac, 0xaf,
0xb5, 0xba, 0xbb, 0xba, 0xb8, 0xb5, 0xaf, 0xa9, 0xa4, 0xa0, 0x9b, 0x95, 0x91, 0x8d, 0x88, 0x80,
0x7a, 0x79, 0x7c, 0x7b, 0x77, 0x75, 0x75, 0x74, 0x71, 0x6f, 0x72, 0x76, 0x79, 0x7d, 0x81, 0x87,
0x89, 0x88, 0x88, 0x89, 0x8c, 0x8e, 0x90, 0x94, 0x95, 0x92, 0x8d, 0x8c, 0x8b, 0x86, 0x82, 0x82,
0x85, 0x85, 0x80, 0x7a, 0x79, 0x77, 0x73, 0x6f, 0x6f, 0x71, 0x6f, 0x6b, 0x6c, 0x6e, 0x6e, 0x69,
0x68, 0x6b, 0x6d, 0x6d, 0x6b, 0x6a, 0x6a, 0x67, 0x65, 0x65, 0x68, 0x6a, 0x6b, 0x6f, 0x74, 0x74,
0x72, 0x70, 0x71, 0x75, 0x76, 0x76, 0x7b, 0x80, 0x84, 0x84, 0x84, 0x89, 0x90, 0x93, 0x96, 0x9b,
0xa2, 0xa5, 0xa4, 0xa3, 0xa5, 0xa7, 0xa7, 0xa9, 0xad, 0xaf, 0xad, 0xa9, 0xa7, 0xa3, 0x9b, 0x94,
0x91, 0x8f, 0x8b, 0x85, 0x7e, 0x79, 0x75, 0x6f, 0x6a, 0x68, 0x68, 0x68, 0x69, 0x6b, 0x6e, 0x70,
0x70, 0x71, 0x76, 0x7d, 0x82, 0x87, 0x8c, 0x91, 0x95, 0x99, 0x9b, 0x9d, 0x9e, 0xa0, 0xa3, 0xa3,
0x9f, 0x99, 0x90, 0x86, 0x82, 0x7f, 0x7a, 0x76, 0x72, 0x6b, 0x63, 0x59, 0x4e, 0x45, 0x3f, 0x39,
0x35, 0x35, 0x36, 0x36, 0x35, 0x36, 0x3b, 0x43, 0x4a, 0x52, 0x5b, 0x68, 0x72, 0x7a, 0x81, 0x8a,
0x94, 0x9e, 0xa8, 0xb4, 0xbd, 0xc1, 0xc4, 0xc6, 0xc7, 0xc5, 0xc3, 0xc3, 0xc2, 0xbe, 0xb8, 0xb1,
0xac, 0xa3, 0x97, 0x8f, 0x8b, 0x88, 0x83, 0x7d, 0x77, 0x72, 0x6e, 0x68, 0x65, 0x66, 0x68, 0x6a,
0x6d, 0x72, 0x77, 0x7a, 0x7d, 0x83, 0x8b, 0x91, 0x96, 0x9d, 0xa4, 0xa6, 0xa5, 0xa4, 0xa5, 0xa4,
0xa1, 0x9f, 0xa2, 0xa2, 0x9c, 0x91, 0x89, 0x81, 0x78, 0x6d, 0x64, 0x5e, 0x57, 0x50, 0x4b, 0x47,
0x42, 0x3d, 0x3a, 0x3b, 0x3d, 0x3e, 0x3d, 0x3e, 0x41, 0x45, 0x49, 0x4e, 0x55, 0x5d, 0x64, 0x6d,
0x77, 0x7e, 0x80, 0x81, 0x86, 0x8e, 0x96, 0x9a, 0x9d, 0xa1, 0xa5, 0xa6, 0xa4, 0xa4, 0xa4, 0xa2,
0xa1, 0xa3, 0xa5, 0xa5, 0xa1, 0x9f, 0x9e, 0x9c, 0x9a, 0x99, 0x99, 0x9b, 0x9c, 0x9c, 0x9d, 0x9c,
0x9a, 0x98, 0x98, 0x9a, 0x9d, 0x9b, 0x95, 0x90, 0x8e, 0x8a, 0x87, 0x85, 0x85, 0x84, 0x85, 0x84,
0x7f, 0x7d, 0x7c, 0x77, 0x71, 0x6b, 0x64, 0x63, 0x69, 0x6d, 0x6c, 0x6e, 0x71, 0x73, 0x74, 0x77,
0x7f, 0x86, 0x88, 0x82, 0x7a, 0x74, 0x77, 0x7b, 0x77, 0x73, 0x70, 0x6c, 0x6a, 0x68, 0x61, 0x59,
0x5a, 0x5c, 0x59, 0x55, 0x4e, 0x42, 0x3c, 0x3c, 0x3a, 0x36, 0x36, 0x37, 0x3e, 0x51, 0x64, 0x69,
0x69, 0x70, 0x7c, 0x8a, 0x9b, 0xa4, 0xa5, 0xac, 0xbb, 0xc8, 0xd0, 0xd8, 0xd8, 0xd5, 0xdd, 0xe9,
0xe9, 0xe1, 0xd7, 0xc7, 0xba, 0xba, 0xbe, 0xb9, 0xb0, 0xa9, 0x9d, 0x93, 0x8d, 0x82, 0x6f, 0x60,
0x56, 0x4a, 0x44, 0x46, 0x43, 0x37, 0x33, 0x38, 0x3d, 0x43, 0x48, 0x48, 0x49, 0x53, 0x5b, 0x5e,
0x62, 0x67, 0x6b, 0x73, 0x82, 0x8f, 0x93, 0x93, 0x8f, 0x89, 0x89, 0x8f, 0x8d, 0x84, 0x80, 0x7d,
0x78, 0x78, 0x78, 0x6f, 0x66, 0x64, 0x60, 0x5b, 0x5c, 0x5c, 0x55, 0x50, 0x53, 0x55, 0x58, 0x64,
0x6e, 0x70, 0x78, 0x86, 0x8c, 0x8e, 0x95, 0x9a, 0x9b, 0xa7, 0xb3, 0xb2, 0xb0, 0xb5, 0xb2, 0xac,
0xaf, 0xb2, 0xab, 0xa5, 0xa3, 0x9a, 0x91, 0x91, 0x8a, 0x7b, 0x72, 0x6e, 0x67, 0x66, 0x6e, 0x6d,
0x65, 0x62, 0x64, 0x64, 0x66, 0x6e, 0x6f, 0x6d, 0x73, 0x7a, 0x7c, 0x84, 0x8f, 0x91, 0x95, 0xa2,
0xab, 0xaf, 0xb4, 0xb5, 0xaf, 0xb3, 0xba, 0xb8, 0xb2, 0xb4, 0xb2, 0xac, 0xad, 0xab, 0x9c, 0x8d,
0x84, 0x79, 0x72, 0x74, 0x70, 0x62, 0x59, 0x57, 0x50, 0x4c, 0x52, 0x51, 0x46, 0x46, 0x4a, 0x45,
0x43, 0x48, 0x47, 0x46, 0x52, 0x5b, 0x5b, 0x61, 0x6b, 0x6a, 0x68, 0x70, 0x75, 0x74, 0x7e, 0x88,
0x8a, 0x90, 0x9a, 0x9d, 0x9c, 0xa3, 0xa9, 0xaa, 0xb0, 0xb8, 0xb6, 0xb3, 0xb8, 0xbc, 0xbb, 0xbf,
0xc5, 0xc4, 0xc5, 0xc7, 0xc2, 0xbc, 0xbd, 0xba, 0xb0, 0xad, 0xad, 0xa7, 0xa2, 0xa2, 0x98, 0x89,
0x84, 0x7f, 0x71, 0x6b, 0x69, 0x5f, 0x56, 0x55, 0x4d, 0x44, 0x46, 0x48, 0x43, 0x47, 0x54, 0x59,
0x57, 0x5d, 0x62, 0x62, 0x6d, 0x7d, 0x80, 0x83, 0x92, 0x9d, 0xa1, 0xab, 0xb6, 0xb4, 0xb6, 0xc0,
0xc0, 0xbd, 0xbf, 0xbb, 0xad, 0xa6, 0xa7, 0xa2, 0x9a, 0x98, 0x91, 0x83, 0x80, 0x7c, 0x6b, 0x5c,
0x55, 0x4a, 0x42, 0x45, 0x45, 0x3f, 0x3f, 0x44, 0x46, 0x4d, 0x59, 0x5e, 0x5e, 0x66, 0x6d, 0x6e,
0x74, 0x7e, 0x80, 0x84, 0x94, 0x9f, 0xa4, 0xad, 0xb4, 0xb0, 0xae, 0xb2, 0xad, 0xa3, 0xa4, 0xa4,
0x9e, 0x9e, 0xa4, 0x9e, 0x97, 0x96, 0x91, 0x88, 0x8a, 0x8b, 0x82, 0x7e, 0x80, 0x7d, 0x7a, 0x84,
0x91, 0x94, 0x9b, 0xa8, 0xab, 0xab, 0xb2, 0xb5, 0xb3, 0xb6, 0xb8, 0xb1, 0xaf, 0xb5, 0xb2, 0xa8,
0xa3, 0x9f, 0x92, 0x89, 0x85, 0x7b, 0x6e, 0x64, 0x56, 0x46, 0x3b, 0x31, 0x25, 0x20, 0x21, 0x1f,
0x19, 0x16, 0x15, 0x12, 0x15, 0x1c, 0x1f, 0x24, 0x2e, 0x34, 0x3c, 0x4f, 0x60, 0x69, 0x75, 0x85,
0x92, 0x9e, 0xaf, 0xbc, 0xbe, 0xc3, 0xcb, 0xcb, 0xcd, 0xd5, 0xd8, 0xd5, 0xd9, 0xd9, 0xcd, 0xc4,
0xbe, 0xb3, 0xa7, 0xa5, 0xa0, 0x91, 0x87, 0x84, 0x7f, 0x7b, 0x7f, 0x7d, 0x74, 0x72, 0x74, 0x6f,
0x6b, 0x6a, 0x65, 0x5f, 0x61, 0x65, 0x66, 0x6b, 0x74, 0x73, 0x70, 0x74, 0x76, 0x74, 0x76, 0x7c,
0x7e, 0x81, 0x87, 0x87, 0x83, 0x86, 0x88, 0x86, 0x87, 0x88, 0x83, 0x80, 0x80, 0x80, 0x7d, 0x80,
0x81, 0x80, 0x81, 0x85, 0x81, 0x7e, 0x7f, 0x7c, 0x73, 0x70, 0x70, 0x6a, 0x66, 0x68, 0x66, 0x5e,
0x58, 0x52, 0x47, 0x43, 0x45, 0x40, 0x39, 0x39, 0x38, 0x34, 0x36, 0x3b, 0x3c, 0x3e, 0x48, 0x52,
0x57, 0x5e, 0x64, 0x66, 0x6f, 0x7e, 0x89, 0x92, 0xa0, 0xad, 0xb3, 0xbb, 0xc8, 0xcd, 0xcf, 0xd4,
0xd8, 0xda, 0xdf, 0xe2, 0xdc, 0xd7, 0xd5, 0xcf, 0xc8, 0xc4, 0xbe, 0xb1, 0xa7, 0x9f, 0x93, 0x84,
0x78, 0x6c, 0x61, 0x5b, 0x55, 0x4d, 0x49, 0x48, 0x43, 0x42, 0x47, 0x4b, 0x4a, 0x4b, 0x4f, 0x4f,
0x51, 0x53, 0x54, 0x53, 0x59, 0x60, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x6b, 0x68, 0x5e, 0x5a, 0x5a,
0x58, 0x58, 0x59, 0x57, 0x52, 0x51, 0x52, 0x50, 0x50, 0x53, 0x51, 0x4d, 0x50, 0x56, 0x5a, 0x65,
0x73, 0x7e, 0x89, 0x99, 0xa4, 0xaa, 0xb3, 0xbc, 0xbf, 0xc0, 0xc2, 0xc2, 0xc3, 0xc9, 0xcd, 0xcb,
0xc9, 0xc6, 0xbd, 0xb5, 0xb1, 0xaa, 0x9e, 0x92, 0x84, 0x76, 0x6b, 0x64, 0x5a, 0x52, 0x4f, 0x49,
0x42, 0x3e, 0x3b, 0x35, 0x30, 0x33, 0x38, 0x3a, 0x3f, 0x46, 0x4f, 0x5c, 0x69, 0x72, 0x79, 0x85,
0x8e, 0x95, 0xa4, 0xb2, 0xb5, 0xb4, 0xb6, 0xb7, 0xb7, 0xb9, 0xb9, 0xb6, 0xb3, 0xb0, 0xa7, 0x9d,
0x98, 0x8e, 0x80, 0x76, 0x6f, 0x64, 0x57, 0x4f, 0x49, 0x43, 0x42, 0x40, 0x3c, 0x3b, 0x3b, 0x3a,
0x37, 0x39, 0x38, 0x33, 0x32, 0x3a, 0x42, 0x4a, 0x54, 0x5e, 0x65, 0x6c, 0x71, 0x76, 0x7d, 0x83,
0x85, 0x89, 0x91, 0x94, 0x95, 0x98, 0x9e, 0xa2, 0xa3, 0xa6, 0xa5, 0xa5, 0xa8, 0xa8, 0xa7, 0xa9,
0xac, 0xac, 0xad, 0xb3, 0xb7, 0xb8, 0xb7, 0xb3, 0xad, 0xa7, 0xa3, 0x9a, 0x93, 0x92, 0x8f, 0x87,
0x81, 0x7c, 0x73, 0x6c, 0x69, 0x65, 0x5f, 0x59, 0x55, 0x4f, 0x4a, 0x4a, 0x49, 0x47, 0x4a, 0x4b,
0x4a, 0x4c, 0x50, 0x4f, 0x4f, 0x55, 0x5c, 0x64, 0x6c, 0x75, 0x7d, 0x83, 0x8d, 0x91, 0x92, 0x94,
0x97, 0x9a, 0x9f, 0xa4, 0xa4, 0xa3, 0xa1, 0x9d, 0x9a, 0x98, 0x94, 0x8c, 0x86, 0x81, 0x7a, 0x72,
0x6e, 0x68, 0x60, 0x5d, 0x5e, 0x5b, 0x59, 0x58, 0x56, 0x57, 0x5d, 0x63, 0x63, 0x65, 0x6c, 0x71,
0x75, 0x7a, 0x7d, 0x7e, 0x80, 0x84, 0x87, 0x8f, 0x98, 0x9a, 0x98, 0x99, 0x99, 0x94, 0x92, 0x91,
0x8f, 0x8d, 0x8b, 0x89, 0x83, 0x80, 0x7f, 0x7e, 0x7f, 0x7e, 0x7a, 0x75, 0x75, 0x7a, 0x7c, 0x7e,
0x85, 0x8e, 0x96, 0x9e, 0xa8, 0xb2, 0xb9, 0xbb, 0xb9, 0xb8, 0xb8, 0xb6, 0xb1, 0xb0, 0xb1, 0xaf,
0xab, 0xa6, 0x9f, 0x97, 0x8e, 0x83, 0x77, 0x6c, 0x5d, 0x4b, 0x41, 0x3a, 0x31, 0x28, 0x22, 0x1e,
0x19, 0x16, 0x13, 0x0f, 0x0e, 0x10, 0x10, 0x13, 0x1e, 0x2b, 0x37, 0x46, 0x56, 0x65, 0x71, 0x7f,
0x8a, 0x97, 0xa7, 0xb6, 0xc1, 0xc7, 0xcd, 0xd2, 0xd8, 0xe0, 0xe5, 0xe3, 0xe3, 0xe3, 0xdf, 0xd9,
0xd2, 0xca, 0xc2, 0xb8, 0xad, 0xa1, 0x98, 0x90, 0x87, 0x7e, 0x79, 0x75, 0x71, 0x6c, 0x68, 0x65,
0x65, 0x66, 0x62, 0x5d, 0x5c, 0x5e, 0x62, 0x68, 0x70, 0x75, 0x78, 0x7f, 0x83, 0x85, 0x87, 0x89,
0x88, 0x87, 0x88, 0x87, 0x86, 0x86, 0x86, 0x84, 0x84, 0x88, 0x88, 0x84, 0x82, 0x80, 0x7e, 0x7d,
0x7b, 0x78, 0x77, 0x7a, 0x7f, 0x80, 0x80, 0x7f, 0x7c, 0x78, 0x75, 0x6f, 0x69, 0x68, 0x68, 0x66,
0x63, 0x62, 0x63, 0x63, 0x64, 0x63, 0x62, 0x64, 0x63, 0x60, 0x5d, 0x5f, 0x62, 0x64, 0x66, 0x66,
0x69, 0x6e, 0x73, 0x75, 0x77, 0x7c, 0x81, 0x89, 0x92, 0x9c, 0xa4, 0xaf, 0xb9, 0xbd, 0xc1, 0xc7,
0xca, 0xcb, 0xce, 0xd2, 0xd1, 0xd0, 0xcf, 0xcb, 0xc6, 0xc3, 0xc0, 0xb9, 0xb1, 0xa9, 0xa0, 0x99,
0x94, 0x8c, 0x81, 0x7b, 0x76, 0x71, 0x6b, 0x65, 0x61, 0x5e, 0x5a, 0x57, 0x52, 0x50, 0x51, 0x52,
0x54, 0x57, 0x5a, 0x5c, 0x5b, 0x5d, 0x5f, 0x63, 0x69, 0x6c, 0x6b, 0x69, 0x68, 0x67, 0x67, 0x66,
0x61, 0x5e, 0x5e, 0x5c, 0x57, 0x54, 0x56, 0x59, 0x5a, 0x59, 0x58, 0x5b, 0x61, 0x67, 0x6b, 0x73,
0x7d, 0x84, 0x8f, 0x9d, 0xab, 0xb9, 0xc6, 0xce, 0xd0, 0xd3, 0xd6, 0xd7, 0xd6, 0xd7, 0xd8, 0xd6,
0xd8, 0xd9, 0xd4, 0xcf, 0xca, 0xc2, 0xb5, 0xa7, 0x99, 0x8c, 0x82, 0x78, 0x6e, 0x63, 0x5a, 0x52,
0x4a, 0x45, 0x41, 0x3d, 0x37, 0x31, 0x2d, 0x2c, 0x30, 0x39, 0x45, 0x51, 0x5b, 0x67, 0x75, 0x80,
0x8a, 0x94, 0x9f, 0xa9, 0xae, 0xb0, 0xb3, 0xb8, 0xbd, 0xc0, 0xc2, 0xc2, 0xc1, 0xbe, 0xb7, 0xb1,
0xac, 0xa3, 0x97, 0x8a, 0x7d, 0x71, 0x69, 0x62, 0x59, 0x50, 0x48, 0x40, 0x3d, 0x3b, 0x38, 0x38,
0x3a, 0x3d, 0x3e, 0x3d, 0x41, 0x4a, 0x53, 0x5a, 0x63, 0x6e, 0x78, 0x81, 0x8a, 0x92, 0x99, 0x9f,
0xa2, 0xa2, 0xa4, 0xa9, 0xac, 0xae, 0xaf, 0xaf, 0xaf, 0xb0, 0xb3, 0xb3, 0xb0, 0xad, 0xab, 0xa7,
0xa0, 0x9c, 0x9a, 0x9c, 0x9f, 0xa0, 0xa0, 0xa0, 0xa0, 0x9c, 0x97, 0x92, 0x8e, 0x8a, 0x87, 0x85,
0x82, 0x81, 0x81, 0x81, 0x80, 0x7e, 0x7b, 0x78, 0x75, 0x6f, 0x69, 0x66, 0x64, 0x60, 0x5b, 0x57,
0x55, 0x57, 0x58, 0x55, 0x54, 0x56, 0x58, 0x5a, 0x5e, 0x63, 0x6a, 0x72, 0x78, 0x7c, 0x80, 0x85,
0x8b, 0x8f, 0x90, 0x92, 0x95, 0x97, 0x9a, 0x99, 0x98, 0x9a, 0x9b, 0x99, 0x96, 0x92, 0x8f, 0x90,
0x8d, 0x8a, 0x88, 0x83, 0x7f, 0x7b, 0x78, 0x77, 0x77, 0x75, 0x70, 0x6c, 0x6a, 0x6a, 0x6d, 0x6f,
0x72, 0x76, 0x79, 0x7d, 0x81, 0x84, 0x88, 0x8e, 0x94, 0x95, 0x95, 0x96, 0x97, 0x99, 0x98, 0x94,
0x90, 0x8b, 0x86, 0x82, 0x80, 0x7f, 0x7f, 0x7d, 0x79, 0x77, 0x75, 0x74, 0x76, 0x79, 0x7a, 0x7a,
0x7e, 0x82, 0x86, 0x8e, 0x99, 0xa2, 0xaa, 0xad, 0xaa, 0xa9, 0xaf, 0xb2, 0xb1, 0xaf, 0xae, 0xaf,
0xaf, 0xaf, 0xb0, 0xaf, 0xaa, 0xa2, 0x94, 0x84, 0x7a, 0x6d, 0x5f, 0x52, 0x45, 0x36, 0x2b, 0x25,
0x1e, 0x19, 0x18, 0x18, 0x15, 0x0f, 0x07, 0x05, 0x0c, 0x19, 0x22, 0x2a, 0x38, 0x49, 0x5a, 0x6f,
0x83, 0x93, 0x9e, 0xaa, 0xb3, 0xbc, 0xc7, 0xcf, 0xd3, 0xd8, 0xdf, 0xe5, 0xea, 0xf0, 0xef, 0xe8,
0xe5, 0xe2, 0xd9, 0xca, 0xba, 0xac, 0xa0, 0x99, 0x91, 0x86, 0x7c, 0x74, 0x6c, 0x65, 0x64, 0x64,
0x5e, 0x58, 0x55, 0x51, 0x50, 0x58, 0x5d, 0x5e, 0x62, 0x6a, 0x72, 0x7b, 0x82, 0x84, 0x84, 0x86,
0x85, 0x81, 0x7f, 0x7f, 0x80, 0x7f, 0x80, 0x81, 0x82, 0x83, 0x81, 0x7f, 0x7d, 0x7b, 0x77, 0x70,
0x69, 0x63, 0x61, 0x63, 0x68, 0x6b, 0x6b, 0x6d, 0x6e, 0x6f, 0x70, 0x70, 0x6f, 0x6c, 0x6c, 0x6b,
0x6d, 0x74, 0x7c, 0x7d, 0x7f, 0x82, 0x86, 0x86, 0x85, 0x81, 0x7d, 0x7c, 0x7c, 0x79, 0x74, 0x72,
0x71, 0x71, 0x73, 0x75, 0x74, 0x73, 0x76, 0x77, 0x77, 0x7c, 0x83, 0x89, 0x8e, 0x93, 0x9a, 0xa4,
0xae, 0xb4, 0xb7, 0xbb, 0xc1, 0xc6, 0xc7, 0xc9, 0xc9, 0xc9, 0xc9, 0xc7, 0xc1, 0xba, 0xb6, 0xb4,
0xaf, 0xa8, 0xa3, 0x9e, 0x99, 0x91, 0x88, 0x80, 0x77, 0x6d, 0x61, 0x55, 0x4d, 0x49, 0x47, 0x47,
0x49, 0x4d, 0x4e, 0x4a, 0x49, 0x4e, 0x53, 0x55, 0x54, 0x50, 0x4c, 0x4e, 0x54, 0x5a, 0x5a, 0x56,
0x50, 0x4e, 0x51, 0x54, 0x52, 0x51, 0x52, 0x56, 0x5a, 0x5f, 0x68, 0x70, 0x73, 0x76, 0x7b, 0x81,
0x87, 0x8b, 0x90, 0x9a, 0xa6, 0xb1, 0xb9, 0xc0, 0xc6, 0xca, 0xd1, 0xd8, 0xdc, 0xda, 0xd7, 0xd8,
0xdb, 0xde, 0xe0, 0xdf, 0xd9, 0xd0, 0xc4, 0xb9, 0xb0, 0xa6, 0x97, 0x83, 0x74, 0x69, 0x60, 0x58,
0x50, 0x4a, 0x45, 0x40, 0x3a, 0x34, 0x30, 0x2d, 0x2d, 0x31, 0x38, 0x40, 0x4a, 0x56, 0x60, 0x6b,
0x78, 0x84, 0x8e, 0x93, 0x95, 0x98, 0xa0, 0xaa, 0xb0, 0xb0, 0xb2, 0xb7, 0xbb, 0xbc, 0xbe, 0xbb,
0xb5, 0xad, 0xa4, 0x9a, 0x90, 0x86, 0x79, 0x6a, 0x5e, 0x56, 0x4e, 0x47, 0x41, 0x3c, 0x39, 0x3c,
0x41, 0x42, 0x43, 0x45, 0x49, 0x50, 0x59, 0x62, 0x6a, 0x73, 0x7f, 0x89, 0x95, 0xa1, 0xaa, 0xad,
0xae, 0xae, 0xaf, 0xb1, 0xb2, 0xb1, 0xb0, 0xb4, 0xba, 0xbf, 0xc1, 0xc1, 0xc1, 0xbf, 0xb9, 0xb2,
0xaa, 0xa1, 0x98, 0x91, 0x8e, 0x90, 0x92, 0x92, 0x91, 0x8f, 0x8d, 0x8e, 0x8d, 0x89, 0x81, 0x7a,
0x75, 0x75, 0x79, 0x7b, 0x7b, 0x7d, 0x80, 0x82, 0x7f, 0x7c, 0x7a, 0x75, 0x6c, 0x64, 0x5e, 0x5a,
0x59, 0x58, 0x55, 0x55, 0x57, 0x57, 0x56, 0x59, 0x5d, 0x5e, 0x60, 0x66, 0x6a, 0x6b, 0x70, 0x78,
0x80, 0x88, 0x8f, 0x93, 0x96, 0x9c, 0xa1, 0xa3, 0xaa, 0xb2, 0xb1, 0xac, 0xac, 0xad, 0xac, 0xac,
0xac, 0xa8, 0xa5, 0xa5, 0xa1, 0x9d, 0x9c, 0x98, 0x8e, 0x85, 0x7f, 0x76, 0x6f, 0x6c, 0x6c, 0x6c,
0x6f, 0x72, 0x73, 0x76, 0x78, 0x79, 0x7a, 0x7c, 0x78, 0x71, 0x6f, 0x73, 0x77, 0x7a, 0x7e, 0x80,
0x80, 0x80, 0x81, 0x80, 0x81, 0x84, 0x7f, 0x7a, 0x7d, 0x80, 0x83, 0x89, 0x8e, 0x8d, 0x8e, 0x92,
0x92, 0x90, 0x93, 0x96, 0x96, 0x9b, 0xa1, 0xa3, 0xa6, 0xad, 0xb2, 0xb3, 0xb5, 0xb6, 0xb1, 0xac,
0xac, 0xac, 0xa9, 0xa7, 0xa1, 0x96, 0x8d, 0x88, 0x7f, 0x72, 0x66, 0x58, 0x48, 0x3c, 0x34, 0x2c,
0x28, 0x28, 0x25, 0x22, 0x21, 0x1f, 0x1d, 0x20, 0x25, 0x29, 0x30, 0x3a, 0x46, 0x53, 0x63, 0x72,
0x7f, 0x8c, 0x9a, 0xa1, 0xa7, 0xb2, 0xbb, 0xc1, 0xcb, 0xd4, 0xda, 0xe0, 0xe8, 0xeb, 0xeb, 0xec,
0xe7, 0xdb, 0xd0, 0xc8, 0xbc, 0xb0, 0xa6, 0x9b, 0x8d, 0x80, 0x75, 0x6a, 0x60, 0x5c, 0x56, 0x4f,
0x4e, 0x50, 0x4d, 0x4c, 0x51, 0x54, 0x55, 0x5b, 0x61, 0x64, 0x6a, 0x71, 0x77, 0x7f, 0x86, 0x8a,
0x8a, 0x8b, 0x8e, 0x8d, 0x8b, 0x8c, 0x8b, 0x89, 0x8a, 0x8e, 0x91, 0x95, 0x96, 0x91, 0x8c, 0x87,
0x7d, 0x73, 0x6d, 0x67, 0x60, 0x5d, 0x5e, 0x5f, 0x60, 0x64, 0x67, 0x69, 0x6d, 0x6d, 0x65, 0x5f,
0x60, 0x5f, 0x60, 0x67, 0x6e, 0x70, 0x76, 0x7e, 0x80, 0x82, 0x82, 0x7d, 0x72, 0x6c, 0x6a, 0x66,
0x65, 0x6a, 0x6e, 0x70, 0x75, 0x7a, 0x7b, 0x7e, 0x82, 0x82, 0x80, 0x80, 0x82, 0x83, 0x8a, 0x95,
0x9c, 0xa3, 0xaa, 0xaf, 0xb2, 0xb6, 0xba, 0xbd, 0xbf, 0xc1, 0xbf, 0xbc, 0xbc, 0xbc, 0xbb, 0xbb,
0xb9, 0xb1, 0xa8, 0xa2, 0x9a, 0x94, 0x8e, 0x85, 0x78, 0x6c, 0x62, 0x56, 0x4e, 0x4a, 0x44, 0x3e,
0x3e, 0x3f, 0x3f, 0x41, 0x43, 0x41, 0x3e, 0x3f, 0x3a, 0x33, 0x32, 0x36, 0x36, 0x39, 0x3f, 0x41,
0x43, 0x4a, 0x50, 0x54, 0x59, 0x5e, 0x5c, 0x59, 0x5c, 0x63, 0x6b, 0x77, 0x83, 0x89, 0x8d, 0x95,
0x9c, 0x9e, 0xa1, 0xa4, 0xa4, 0xa4, 0xa8, 0xae, 0xb5, 0xc0, 0xca, 0xce, 0xd2, 0xd6, 0xd5, 0xd2,
0xd1, 0xce, 0xc9, 0xc4, 0xbc, 0xb4, 0xae, 0xa9, 0xa3, 0x9d, 0x95, 0x84, 0x71, 0x63, 0x58, 0x4c,
0x45, 0x40, 0x3a, 0x36, 0x34, 0x30, 0x2d, 0x31, 0x34, 0x32, 0x34, 0x3b, 0x3f, 0x45, 0x52, 0x5f,
0x68, 0x72, 0x7e, 0x83, 0x88, 0x90, 0x97, 0x99, 0x9e, 0xa2, 0xa4, 0xa8, 0xb1, 0xb4, 0xb1, 0xb1,
0xae, 0xa5, 0x9a, 0x91, 0x87, 0x7f, 0x77, 0x6e, 0x63, 0x5a, 0x52, 0x4c, 0x48, 0x45, 0x40, 0x3b,
0x39, 0x39, 0x39, 0x3e, 0x45, 0x4c, 0x53, 0x59, 0x61, 0x6a, 0x73, 0x7b, 0x81, 0x8a, 0x92, 0x97,
0x9b, 0xa1, 0xa8, 0xae, 0xb4, 0xb6, 0xb4, 0xb1, 0xb1, 0xb1, 0xb2, 0xb4, 0xb3, 0xae, 0xab, 0xa7,
0x9d, 0x96, 0x94, 0x8f, 0x85, 0x80, 0x7e, 0x7d, 0x7f, 0x82, 0x83, 0x86, 0x88, 0x86, 0x80, 0x7b,
0x79, 0x76, 0x72, 0x70, 0x6f, 0x6e, 0x70, 0x76, 0x7a, 0x78, 0x75, 0x71, 0x65, 0x5a, 0x54, 0x50,
0x4c, 0x4b, 0x49, 0x49, 0x4d, 0x51, 0x53, 0x56, 0x57, 0x55, 0x51, 0x4d, 0x4c, 0x4e, 0x56, 0x60,
0x6a, 0x73, 0x7b, 0x81, 0x8a, 0x96, 0x9e, 0xa3, 0xa7, 0xaa, 0xa8, 0xa9, 0xae, 0xb3, 0xb9, 0xbf,
0xbe, 0xba, 0xb8, 0xb4, 0xad, 0xa7, 0xa1, 0x99, 0x90, 0x8a, 0x81, 0x7b, 0x78, 0x78, 0x77, 0x73,
0x71, 0x6f, 0x70, 0x73, 0x76, 0x73, 0x6e, 0x6c, 0x69, 0x62, 0x5e, 0x5f, 0x61, 0x5f, 0x5d, 0x5f,
0x62, 0x67, 0x6b, 0x6f, 0x73, 0x75, 0x73, 0x70, 0x70, 0x72, 0x78, 0x7f, 0x84, 0x88, 0x8c, 0x90,
0x94, 0x9a, 0x9a, 0x96, 0x93, 0x92, 0x91, 0x91, 0x97, 0xa0, 0xa6, 0xac, 0xb1, 0xb1, 0xb2, 0xb3,
0xaf, 0xa9, 0xa4, 0x9c, 0x92, 0x8a, 0x86, 0x82, 0x80, 0x7f, 0x7a, 0x6d, 0x5e, 0x52, 0x48, 0x3f,
0x36, 0x2f, 0x2a, 0x28, 0x28, 0x29, 0x2b, 0x31, 0x39, 0x3d, 0x40, 0x47, 0x4f, 0x59, 0x63, 0x6e,
0x7a, 0x87, 0x92, 0x9b, 0xa3, 0xab, 0xb5, 0xbe, 0xc0, 0xc0, 0xc2, 0xc7, 0xcc, 0xd2, 0xd5, 0xd4,
0xd1, 0xcd, 0xc5, 0xba, 0xb2, 0xac, 0xa3, 0x97, 0x8d, 0x84, 0x7c, 0x76, 0x72, 0x6a, 0x62, 0x5e,
0x59, 0x52, 0x4f, 0x51, 0x55, 0x59, 0x5d, 0x60, 0x63, 0x69, 0x6f, 0x73, 0x76, 0x7a, 0x7d, 0x7e,
0x80, 0x85, 0x8a, 0x90, 0x95, 0x96, 0x94, 0x91, 0x8f, 0x8e, 0x8d, 0x8b, 0x88, 0x86, 0x82, 0x7e,
0x7b, 0x78, 0x75, 0x72, 0x6d, 0x67, 0x65, 0x69, 0x6d, 0x6f, 0x73, 0x78, 0x7c, 0x7e, 0x7d, 0x7a,
0x79, 0x7a, 0x78, 0x73, 0x71, 0x75, 0x79, 0x7c, 0x81, 0x86, 0x86, 0x86, 0x85, 0x7f, 0x77, 0x74,
0x73, 0x71, 0x70, 0x71, 0x72, 0x76, 0x7f, 0x81, 0x80, 0x80, 0x80, 0x7c, 0x77, 0x78, 0x7c, 0x81,
0x89, 0x91, 0x98, 0x9f, 0xaa, 0xb4, 0xb9, 0xba, 0xbc, 0xba, 0xb7, 0xb5, 0xb5, 0xb6, 0xba, 0xbf,
0xbf, 0xbb, 0xb7, 0xb5, 0xaf, 0xa4, 0x99, 0x8f, 0x87, 0x7d, 0x72, 0x6a, 0x65, 0x63, 0x62, 0x5d,
0x57, 0x55, 0x55, 0x52, 0x4e, 0x4e, 0x4c, 0x46, 0x41, 0x3e, 0x3b, 0x39, 0x3b, 0x3c, 0x38, 0x37,
0x3d, 0x44, 0x48, 0x4f, 0x55, 0x59, 0x5e, 0x64, 0x66, 0x68, 0x72, 0x7b, 0x80, 0x87, 0x91, 0x99,
0xa0, 0xa8, 0xad, 0xae, 0xaf, 0xb1, 0xb0, 0xb0, 0xb6, 0xc0, 0xc8, 0xcd, 0xd2, 0xd7, 0xd9, 0xdc,
0xdd, 0xd9, 0xd0, 0xc8, 0xc0, 0xb4, 0xaa, 0xa5, 0xa2, 0x9e, 0x99, 0x92, 0x85, 0x7a, 0x70, 0x64,
0x55, 0x48, 0x41, 0x39, 0x32, 0x30, 0x34, 0x39, 0x3d, 0x41, 0x42, 0x44, 0x4a, 0x52, 0x56, 0x5b,
0x63, 0x6d, 0x75, 0x7f, 0x89, 0x91, 0x98, 0x9e, 0xa0, 0x9d, 0x9c, 0x9f, 0x9f, 0x9e, 0xa0, 0xa2,
0xa0, 0x9c, 0x99, 0x95, 0x90, 0x8a, 0x82, 0x78, 0x6d, 0x68, 0x65, 0x62, 0x5e, 0x5a, 0x58, 0x57,
0x57, 0x56, 0x54, 0x56, 0x5a, 0x5d, 0x60, 0x65, 0x6c, 0x72, 0x7a, 0x81, 0x86, 0x88, 0x8e, 0x94,
0x98, 0x9a, 0xa0, 0xa8, 0xac, 0xae, 0xb0, 0xb3, 0xb6, 0xb8, 0xb6, 0xb1, 0xac, 0xab, 0xa8, 0xa0,
0x9b, 0x9b, 0x97, 0x93, 0x8f, 0x8b, 0x86, 0x86, 0x85, 0x83, 0x83, 0x86, 0x87, 0x85, 0x83, 0x84,
0x84, 0x80, 0x7b, 0x76, 0x71, 0x6f, 0x70, 0x6f, 0x6c, 0x6c, 0x6d, 0x6d, 0x6c, 0x69, 0x63, 0x5c,
0x58, 0x54, 0x4e, 0x4b, 0x4c, 0x4f, 0x52, 0x55, 0x56, 0x58, 0x59, 0x59, 0x58, 0x5a, 0x5e, 0x61,
0x64, 0x69, 0x73, 0x80, 0x8a, 0x92, 0x9b, 0xa1, 0xa4, 0xa8, 0xab, 0xa9, 0xa9, 0xad, 0xb0, 0xb4,
0xb8, 0xbd, 0xc1, 0xc3, 0xc2, 0xbf, 0xb6, 0xad, 0xa6, 0x9d, 0x94, 0x8e, 0x8c, 0x88, 0x85, 0x84,
0x83, 0x82, 0x80, 0x7f, 0x7b, 0x75, 0x71, 0x6d, 0x67, 0x61, 0x5e, 0x5d, 0x5c, 0x59, 0x56, 0x53,
0x53, 0x54, 0x54, 0x52, 0x55, 0x58, 0x5b, 0x5e, 0x65, 0x6b, 0x70, 0x72, 0x76, 0x7b, 0x7f, 0x87,
0x90, 0x95, 0x96, 0x99, 0x9c, 0x9d, 0x9e, 0xa1, 0xa5, 0xa9, 0xab, 0xab, 0xab, 0xae, 0xb5, 0xb8,
0xb6, 0xb3, 0xad, 0xa3, 0x9a, 0x93, 0x8c, 0x84, 0x80, 0x7a, 0x73, 0x6e, 0x6c, 0x69, 0x61, 0x56,
0x4d, 0x43, 0x3a, 0x34, 0x2e, 0x2b, 0x2f, 0x33, 0x33, 0x3c, 0x4d, 0x55, 0x56, 0x60, 0x6a, 0x71,
0x7d, 0x85, 0x85, 0x8d, 0x9e, 0xa7, 0xa8, 0xaf, 0xb7, 0xbb, 0xbf, 0xbf, 0xb6, 0xaf, 0xb4, 0xbe,
0xc3, 0xc4, 0xbf, 0xb7, 0xb4, 0xb0, 0xa3, 0x95, 0x88, 0x79, 0x72, 0x7a, 0x7c, 0x6d, 0x5f, 0x5e,
0x61, 0x66, 0x67, 0x65, 0x62, 0x60, 0x5d, 0x5b, 0x5f, 0x66, 0x71, 0x80, 0x89, 0x85, 0x82, 0x86,
0x85, 0x80, 0x82, 0x85, 0x80, 0x7a, 0x80, 0x88, 0x8a, 0x89, 0x87, 0x86, 0x88, 0x89, 0x82, 0x7a,
0x75, 0x6e, 0x69, 0x6b, 0x6e, 0x70, 0x79, 0x85, 0x89, 0x87, 0x85, 0x82, 0x82, 0x89, 0x8e, 0x8d,
0x90, 0x93, 0x8e, 0x8b, 0x8b, 0x83, 0x79, 0x77, 0x75, 0x6f, 0x6d, 0x6e, 0x6c, 0x6d, 0x76, 0x7b,
0x78, 0x76, 0x7a, 0x80, 0x83, 0x87, 0x8b, 0x8b, 0x89, 0x87, 0x86, 0x86, 0x86, 0x89, 0x8b, 0x85,
0x7e, 0x7d, 0x7b, 0x7a, 0x82, 0x8d, 0x8c, 0x8b, 0x92, 0x97, 0x9a, 0x9f, 0xa3, 0xa2, 0xa3, 0xad,
0xb6, 0xb8, 0xbf, 0xca, 0xca, 0xc5, 0xc4, 0xbd, 0xae, 0xa2, 0x99, 0x8e, 0x83, 0x79, 0x6a, 0x5f,
0x5d, 0x5d, 0x5a, 0x57, 0x54, 0x4e, 0x46, 0x43, 0x42, 0x42, 0x46, 0x4a, 0x4a, 0x4c, 0x52, 0x53,
0x52, 0x54, 0x54, 0x53, 0x53, 0x50, 0x4e, 0x57, 0x63, 0x6a, 0x6c, 0x6d, 0x71, 0x75, 0x7b, 0x82,
0x8b, 0x90, 0x94, 0x9a, 0xa2, 0xa9, 0xb3, 0xc1, 0xcc, 0xd0, 0xd4, 0xde, 0xe8, 0xec, 0xed, 0xef,
0xef, 0xea, 0xe1, 0xd8, 0xd0, 0xc6, 0xbc, 0xb0, 0x9d, 0x8b, 0x7f, 0x75, 0x6c, 0x68, 0x63, 0x5a,
0x51, 0x4c, 0x4a, 0x47, 0x46, 0x48, 0x47, 0x43, 0x46, 0x50, 0x56, 0x5f, 0x6a, 0x6e, 0x6b, 0x6d,
0x71, 0x70, 0x72, 0x78, 0x7d, 0x7b, 0x78, 0x7a, 0x7f, 0x82, 0x86, 0x88, 0x87, 0x85, 0x85, 0x87,
0x8d, 0x91, 0x92, 0x98, 0x9d, 0x9e, 0xa0, 0xa4, 0xa1, 0x95, 0x8a, 0x82, 0x79, 0x71, 0x6d, 0x67,
0x5f, 0x5e, 0x5f, 0x5c, 0x58, 0x59, 0x5c, 0x5e, 0x63, 0x6b, 0x73, 0x7c, 0x84, 0x8c, 0x95, 0x9d,
0xa3, 0xab, 0xb2, 0xb5, 0xb8, 0xba, 0xb9, 0xb5, 0xb4, 0xb5, 0xb0, 0xa9, 0xa3, 0x99, 0x8b, 0x84,
0x80, 0x76, 0x6a, 0x67, 0x66, 0x65, 0x6a, 0x73, 0x77, 0x7c, 0x83, 0x89, 0x8d, 0x94, 0x9b, 0x9f,
0x9f, 0x9d, 0x9a, 0x98, 0x94, 0x8f, 0x89, 0x80, 0x72, 0x67, 0x5c, 0x50, 0x48, 0x49, 0x4a, 0x46,
0x44, 0x47, 0x49, 0x4c, 0x53, 0x58, 0x5a, 0x5f, 0x64, 0x67, 0x6d, 0x74, 0x79, 0x7c, 0x7b, 0x78,
0x76, 0x77, 0x78, 0x76, 0x75, 0x75, 0x72, 0x70, 0x72, 0x77, 0x7c, 0x84, 0x8b, 0x89, 0x8b, 0x94,
0x9f, 0xa9, 0xb4, 0xbc, 0xc3, 0xcb, 0xd2, 0xd4, 0xd2, 0xcc, 0xc4, 0xb8, 0xad, 0xa4, 0x9e, 0x96,
0x8c, 0x86, 0x81, 0x78, 0x6d, 0x69, 0x65, 0x5c, 0x56, 0x57, 0x55, 0x4e, 0x4e, 0x55, 0x57, 0x59,
0x5c, 0x5b, 0x59, 0x5c, 0x60, 0x5e, 0x5a, 0x5a, 0x5a, 0x59, 0x5c, 0x64, 0x6a, 0x6b, 0x6e, 0x70,
0x6f, 0x6f, 0x74, 0x76, 0x77, 0x7c, 0x84, 0x8e, 0x95, 0x9a, 0xa4, 0xb0, 0xb7, 0xbc, 0xc2, 0xc4,
0xc2, 0xc2, 0xc0, 0xb9, 0xb1, 0xa8, 0x9e, 0x94, 0x88, 0x7d, 0x71, 0x65, 0x5b, 0x51, 0x49, 0x44,
0x41, 0x3f, 0x3e, 0x3e, 0x41, 0x47, 0x48, 0x49, 0x50, 0x5a, 0x62, 0x6a, 0x74, 0x7b, 0x80, 0x85,
0x89, 0x89, 0x89, 0x8b, 0x8f, 0x91, 0x91, 0x94, 0x96, 0x96, 0x98, 0x9b, 0x9b, 0x97, 0x96, 0x98,
0x96, 0x95, 0x98, 0x9c, 0x9a, 0x9a, 0xa1, 0xa2, 0x9d, 0x9b, 0x98, 0x8c, 0x82, 0x80, 0x79, 0x6e,
0x68, 0x68, 0x63, 0x5e, 0x5b, 0x58, 0x58, 0x5c, 0x60, 0x5f, 0x60, 0x64, 0x69, 0x6f, 0x77, 0x80,
0x88, 0x8b, 0x89, 0x8b, 0x90, 0x93, 0x93, 0x95, 0x92, 0x8a, 0x87, 0x89, 0x88, 0x81, 0x7c, 0x76,
0x6c, 0x61, 0x5b, 0x59, 0x58, 0x59, 0x5a, 0x5b, 0x5e, 0x64, 0x6c, 0x77, 0x7f, 0x85, 0x8c, 0x90,
0x90, 0x94, 0x9b, 0xa0, 0xa0, 0x9e, 0x99, 0x92, 0x8c, 0x88, 0x80, 0x74, 0x6a, 0x63, 0x5e, 0x5d,
0x61, 0x66, 0x6a, 0x6f, 0x75, 0x79, 0x7b, 0x80, 0x85, 0x89, 0x8a, 0x8d, 0x90, 0x8e, 0x8d, 0x8f,
0x8f, 0x8b, 0x89, 0x89, 0x82, 0x7c, 0x7d, 0x7f, 0x7b, 0x78, 0x7b, 0x7c, 0x7c, 0x80, 0x85, 0x8a,
0x8e, 0x95, 0x9a, 0x9c, 0xa1, 0xa7, 0xac, 0xaf, 0xad, 0xaa, 0xa6, 0xa0, 0x96, 0x90, 0x8a, 0x80,
0x78, 0x73, 0x6c, 0x64, 0x5f, 0x5b, 0x53, 0x4c, 0x49, 0x47, 0x44, 0x44, 0x45, 0x46, 0x49, 0x50,
0x53, 0x52, 0x52, 0x55, 0x56, 0x57, 0x57, 0x59, 0x5d, 0x5e, 0x5f, 0x66, 0x6e, 0x74, 0x7b, 0x81,
0x84, 0x85, 0x89, 0x8c, 0x8c, 0x90, 0x9b, 0xa4, 0xa8, 0xb0, 0xbc, 0xc6, 0xd1, 0xdd, 0xe1, 0xde,
0xdb, 0xdb, 0xd8, 0xd3, 0xce, 0xc8, 0xbb, 0xaa, 0x9c, 0x90, 0x82, 0x75, 0x6a, 0x5e, 0x4f, 0x47,
0x46, 0x44, 0x42, 0x45, 0x48, 0x48, 0x4a, 0x50, 0x55, 0x5b, 0x65, 0x6c, 0x6d, 0x70, 0x75, 0x7a,
0x7e, 0x80, 0x82, 0x82, 0x80, 0x7d, 0x7c, 0x7d, 0x7d, 0x7d, 0x7d, 0x7b, 0x76, 0x74, 0x76, 0x78,
0x76, 0x75, 0x75, 0x73, 0x76, 0x7d, 0x82, 0x85, 0x89, 0x8b, 0x87, 0x82, 0x82, 0x84, 0x81, 0x7c,
0x79, 0x76, 0x71, 0x6c, 0x6c, 0x71, 0x74, 0x74, 0x77, 0x7c, 0x7e, 0x80, 0x87, 0x8b, 0x8c, 0x92,
0x9b, 0x9f, 0xa2, 0xaa, 0xac, 0xab, 0xae, 0xb0, 0xab, 0xa6, 0xa9, 0xa9, 0xa2, 0x9c, 0x99, 0x91,
0x87, 0x80, 0x79, 0x6d, 0x65, 0x65, 0x64, 0x62, 0x64, 0x69, 0x6f, 0x73, 0x7a, 0x84, 0x8c, 0x92,
0x97, 0x9a, 0x99, 0x99, 0x9d, 0x9c, 0x94, 0x8b, 0x85, 0x7d, 0x71, 0x67, 0x5d, 0x53, 0x4c, 0x47,
0x45, 0x46, 0x4c, 0x54, 0x5c, 0x62, 0x67, 0x6d, 0x73, 0x77, 0x7c, 0x80, 0x83, 0x82, 0x82, 0x81,
0x80, 0x80, 0x83, 0x82, 0x7d, 0x7a, 0x79, 0x77, 0x76, 0x76, 0x78, 0x78, 0x77, 0x7a, 0x80, 0x87,
0x90, 0x99, 0xa2, 0xa8, 0xad, 0xb5, 0xbd, 0xc1, 0xc5, 0xc8, 0xc7, 0xc1, 0xbf, 0xbc, 0xb4, 0xad,
0xab, 0xa3, 0x94, 0x8c, 0x87, 0x80, 0x78, 0x75, 0x70, 0x67, 0x62, 0x61, 0x5e, 0x5c, 0x5d, 0x5e,
0x5c, 0x5b, 0x5b, 0x5c, 0x5c, 0x5c, 0x59, 0x56, 0x56, 0x56, 0x59, 0x5c, 0x5f, 0x64, 0x6b, 0x6d,
0x6c, 0x6f, 0x76, 0x78, 0x78, 0x7d, 0x81, 0x83, 0x89, 0x93, 0x9c, 0xa4, 0xb0, 0xba, 0xc0, 0xc5,
0xc8, 0xc8, 0xc4, 0xbf, 0xb8, 0xb0, 0xa7, 0x9d, 0x93, 0x86, 0x79, 0x6e, 0x64, 0x59, 0x4e, 0x4b,
0x4a, 0x46, 0x44, 0x48, 0x4d, 0x51, 0x57, 0x61, 0x69, 0x70, 0x7a, 0x83, 0x89, 0x8c, 0x90, 0x93,
0x95, 0x96, 0x98, 0x9b, 0x9d, 0x9d, 0x9c, 0x99, 0x95, 0x94, 0x94, 0x91, 0x8c, 0x8c, 0x8a, 0x84,
0x82, 0x84, 0x82, 0x80, 0x80, 0x82, 0x80, 0x81, 0x86, 0x89, 0x88, 0x88, 0x88, 0x85, 0x82, 0x80,
0x7f, 0x7d, 0x7a, 0x75, 0x71, 0x6d, 0x6b, 0x6d, 0x72, 0x72, 0x72, 0x76, 0x79, 0x79, 0x7d, 0x82,
0x83, 0x84, 0x8a, 0x8e, 0x8e, 0x93, 0x98, 0x97, 0x93, 0x94, 0x92, 0x8c, 0x87, 0x85, 0x80, 0x7a,
0x73, 0x6c, 0x64, 0x5d, 0x58, 0x58, 0x59, 0x58, 0x58, 0x5e, 0x66, 0x6c, 0x75, 0x80, 0x87, 0x8e,
0x99, 0xa3, 0xa6, 0xaa, 0xaf, 0xae, 0xa9, 0xa4, 0x9f, 0x97, 0x8e, 0x85, 0x7b, 0x71, 0x6a, 0x65,
0x61, 0x5e, 0x61, 0x6a, 0x71, 0x75, 0x7c, 0x86, 0x8d, 0x8f, 0x95, 0x9a, 0x99, 0x98, 0x9a, 0x97,
0x90, 0x8e, 0x8f, 0x8a, 0x84, 0x82, 0x80, 0x7a, 0x75, 0x70, 0x6b, 0x67, 0x69, 0x6b, 0x6a, 0x6b,
0x73, 0x7c, 0x80, 0x84, 0x8f, 0x98, 0x9c, 0xa0, 0xa7, 0xaa, 0xa9, 0xaa, 0xac, 0xa9, 0xa5, 0xa4,
0xa1, 0x99, 0x92, 0x8b, 0x80, 0x73, 0x6c, 0x63, 0x5b, 0x58, 0x56, 0x50, 0x4c, 0x4e, 0x4f, 0x4f,
0x53, 0x56, 0x58, 0x5c, 0x5f, 0x5e, 0x5f, 0x61, 0x62, 0x63, 0x67, 0x69, 0x6a, 0x70, 0x78, 0x7b,
0x7d, 0x80, 0x80, 0x80, 0x85, 0x8c, 0x90, 0x93, 0x9b, 0xa3, 0xa9, 0xb2, 0xbf, 0xca, 0xd1, 0xd7,
0xdc, 0xde, 0xdb, 0xd7, 0xd3, 0xc9, 0xbb, 0xb0, 0xa5, 0x92, 0x82, 0x7b, 0x71, 0x5f, 0x50, 0x49,
0x42, 0x3d, 0x3d, 0x40, 0x41, 0x47, 0x50, 0x56, 0x5c, 0x68, 0x76, 0x7d, 0x80, 0x85, 0x89, 0x8b,
0x8c, 0x8e, 0x8c, 0x89, 0x8a, 0x8c, 0x8a, 0x86, 0x87, 0x89, 0x82, 0x7a, 0x77, 0x74, 0x6d, 0x6a,
0x6a, 0x66, 0x61, 0x62, 0x64, 0x62, 0x64, 0x6c, 0x72, 0x74, 0x77, 0x7b, 0x7f, 0x80, 0x82, 0x83,
0x85, 0x83, 0x82, 0x82, 0x82, 0x80, 0x7e, 0x80, 0x80, 0x80, 0x82, 0x87, 0x8a, 0x8e, 0x95, 0x98,
0x9a, 0xa1, 0xa8, 0xaa, 0xab, 0xb1, 0xb4, 0xb2, 0xae, 0xad, 0xab, 0xa5, 0x9f, 0x9a, 0x94, 0x8a,
0x82, 0x7c, 0x71, 0x64, 0x5e, 0x5e, 0x58, 0x52, 0x55, 0x58, 0x59, 0x5e, 0x67, 0x6d, 0x74, 0x83,
0x8d, 0x8f, 0x94, 0x9e, 0xa1, 0x9e, 0x9e, 0x9c, 0x93, 0x8a, 0x83, 0x7a, 0x6e, 0x64, 0x5a, 0x52,
0x4c, 0x49, 0x4b, 0x52, 0x58, 0x5b, 0x61, 0x68, 0x6e, 0x75, 0x7c, 0x80, 0x84, 0x87, 0x86, 0x81,
0x80, 0x83, 0x83, 0x81, 0x80, 0x7f, 0x7c, 0x7b, 0x7a, 0x78, 0x75, 0x72, 0x70, 0x71, 0x73, 0x75,
0x7a, 0x82, 0x87, 0x8a, 0x92, 0x9c, 0xa4, 0xac, 0xb5, 0xbc, 0xc2, 0xc8, 0xc8, 0xc4, 0xc4, 0xc8,
0xc3, 0xb8, 0xb0, 0xa8, 0x9c, 0x90, 0x86, 0x79, 0x6b, 0x64, 0x61, 0x5d, 0x59, 0x57, 0x57, 0x55,
0x52, 0x51, 0x52, 0x52, 0x51, 0x54, 0x5a, 0x5c, 0x5c, 0x5a, 0x57, 0x57, 0x5f, 0x66, 0x66, 0x68,
0x6a, 0x68, 0x64, 0x67, 0x6d, 0x70, 0x76, 0x7f, 0x82, 0x86, 0x8f, 0x99, 0xa0, 0xa9, 0xb3, 0xb8,
0xbc, 0xc1, 0xc4, 0xc2, 0xbe, 0xb7, 0xae, 0xa6, 0x9c, 0x8d, 0x80, 0x75, 0x66, 0x56, 0x4d, 0x47,
0x40, 0x3e, 0x43, 0x48, 0x4c, 0x53, 0x5b, 0x63, 0x6e, 0x7a, 0x84, 0x90, 0x99, 0x9f, 0xa4, 0xaa,
0xab, 0xa7, 0xa3, 0xa3, 0xa4, 0xa3, 0x9e, 0x9a, 0x9b, 0x9b, 0x95, 0x90, 0x8d, 0x87, 0x80, 0x7d,
0x7a, 0x73, 0x6f, 0x6e, 0x69, 0x60, 0x60, 0x66, 0x6a, 0x6d, 0x72, 0x73, 0x72, 0x73, 0x72, 0x72,
0x76, 0x78, 0x75, 0x72, 0x72, 0x70, 0x6f, 0x71, 0x72, 0x75, 0x7c, 0x80, 0x7e, 0x7f, 0x87, 0x8c,
0x8d, 0x91, 0x98, 0x9c, 0xa1, 0xa4, 0xa4, 0xa2, 0xa3, 0xa2, 0x9d, 0x97, 0x90, 0x87, 0x80, 0x7b,
0x73, 0x66, 0x59, 0x50, 0x4f, 0x51, 0x52, 0x50, 0x4f, 0x4f, 0x56, 0x61, 0x6a, 0x70, 0x7c, 0x89,
0x92, 0x97, 0xa1, 0xad, 0xb7, 0xbf, 0xc1, 0xb9, 0xb0, 0xaa, 0xa1, 0x98, 0x8f, 0x82, 0x73, 0x68,
0x64, 0x63, 0x65, 0x67, 0x67, 0x6a, 0x73, 0x7b, 0x7f, 0x83, 0x87, 0x87, 0x89, 0x8d, 0x8d, 0x8e,
0x94, 0x98, 0x94, 0x8f, 0x8a, 0x82, 0x7e, 0x7e, 0x7c, 0x75, 0x70, 0x6e, 0x6a, 0x67, 0x66, 0x66,
0x68, 0x6a, 0x6c, 0x70, 0x75, 0x7b, 0x81, 0x8b, 0x97, 0x9f, 0xa7, 0xb0, 0xb9, 0xbf, 0xc2, 0xc1,
0xbf, 0xba, 0xaf, 0xa5, 0x9b, 0x8f, 0x81, 0x75, 0x6b, 0x60, 0x55, 0x4c, 0x45, 0x43, 0x43, 0x44,
0x46, 0x4b, 0x51, 0x57, 0x5e, 0x64, 0x67, 0x6b, 0x75, 0x7b, 0x7a, 0x7a, 0x7f, 0x81, 0x81, 0x83,
0x85, 0x84, 0x83, 0x82, 0x80, 0x80, 0x82, 0x84, 0x86, 0x8b, 0x91, 0x9a, 0xa4, 0xae, 0xb8, 0xc2,
0xc8, 0xc9, 0xcb, 0xcd, 0xca, 0xc5, 0xc1, 0xb7, 0xa8, 0x9b, 0x8f, 0x80, 0x74, 0x6a, 0x5a, 0x48,
0x40, 0x3d, 0x39, 0x37, 0x3c, 0x3f, 0x42, 0x4c, 0x59, 0x63, 0x6e, 0x7c, 0x86, 0x8f, 0x96, 0x9d,
0xa6, 0xac, 0xad, 0xac, 0xaf, 0xaf, 0xaa, 0xa5, 0xa1, 0x9c, 0x96, 0x8e, 0x82, 0x78, 0x71, 0x68,
0x5e, 0x55, 0x4e, 0x47, 0x41, 0x40, 0x42, 0x48, 0x50, 0x56, 0x5d, 0x67, 0x71, 0x7a, 0x81, 0x86,
0x87, 0x89, 0x8d, 0x90, 0x90, 0x92, 0x91, 0x8d, 0x8b, 0x8b, 0x89, 0x87, 0x8a, 0x8c, 0x8b, 0x89,
0x8d, 0x94, 0x9b, 0xa3, 0xac, 0xb1, 0xb3, 0xb3, 0xb5, 0xb8, 0xb5, 0xae, 0xa8, 0xa0, 0x96, 0x90,
0x8c, 0x82, 0x77, 0x6f, 0x65, 0x59, 0x53, 0x50, 0x49, 0x46, 0x4b, 0x4c, 0x4c, 0x54, 0x60, 0x68,
0x73, 0x82, 0x8e, 0x94, 0x9c, 0xa3, 0xa7, 0xaa, 0xaa, 0xa5, 0xa0, 0x9b, 0x94, 0x8b, 0x84, 0x7a,
0x6d, 0x64, 0x5d, 0x56, 0x53, 0x53, 0x4e, 0x4d, 0x52, 0x56, 0x56, 0x5c, 0x64, 0x66, 0x6b, 0x75,
0x7c, 0x80, 0x87, 0x8e, 0x8e, 0x8f, 0x92, 0x92, 0x8f, 0x8d, 0x8a, 0x84, 0x80, 0x7d, 0x79, 0x75,
0x73, 0x72, 0x72, 0x71, 0x71, 0x77, 0x80, 0x86, 0x8d, 0x99, 0xa7, 0xaf, 0xb8, 0xc5, 0xce, 0xce,
0xce, 0xcf, 0xc8, 0xbf, 0xb7, 0xad, 0x9f, 0x91, 0x83, 0x77, 0x68, 0x5c, 0x53, 0x4c, 0x42, 0x3b,
0x3a, 0x3a, 0x3b, 0x42, 0x4b, 0x4d, 0x50, 0x59, 0x61, 0x66, 0x6e, 0x76, 0x78, 0x77, 0x7a, 0x7d,
0x7c, 0x7c, 0x7d, 0x7c, 0x7a, 0x77, 0x75, 0x75, 0x76, 0x77, 0x7b, 0x7f, 0x80, 0x84, 0x8d, 0x94,
0x97, 0xa0, 0xa9, 0xa9, 0xa7, 0xaa, 0xa9, 0xa3, 0x9f, 0x9a, 0x91, 0x87, 0x80, 0x75, 0x68, 0x61,
0x59, 0x4c, 0x45, 0x44, 0x43, 0x43, 0x49, 0x51, 0x57, 0x61, 0x6b, 0x75, 0x80, 0x8d, 0x98, 0xa7,
0xb4, 0xb9, 0xbf, 0xc9, 0xce, 0xcc, 0xcd, 0xcc, 0xc4, 0xb8, 0xae, 0xa3, 0x94, 0x86, 0x7b, 0x6d,
0x5e, 0x53, 0x4a, 0x41, 0x3b, 0x37, 0x36, 0x37, 0x3b, 0x42, 0x4b, 0x56, 0x63, 0x6f, 0x77, 0x7c,
0x81, 0x89, 0x8d, 0x8f, 0x92, 0x92, 0x8d, 0x8b, 0x89, 0x84, 0x80, 0x80, 0x7e, 0x79, 0x77, 0x7a,
0x7c, 0x7c, 0x80, 0x88, 0x8e, 0x92, 0x98, 0x9e, 0xa0, 0xa0, 0xa1, 0xa1, 0x9b, 0x93, 0x90, 0x8b,
0x81, 0x7b, 0x76, 0x6c, 0x62, 0x5d, 0x58, 0x4f, 0x4a, 0x4b, 0x4b, 0x4a, 0x4f, 0x59, 0x62, 0x6d,
0x7b, 0x86, 0x91, 0x9c, 0xa5, 0xac, 0xb4, 0xba, 0xbc, 0xbc, 0xb7, 0xaf, 0xab, 0xa7, 0x9d, 0x92,
0x8a, 0x80, 0x72, 0x69, 0x63, 0x5a, 0x53, 0x53, 0x53, 0x4f, 0x52, 0x59, 0x5f, 0x63, 0x6a, 0x72,
0x78, 0x80, 0x8a, 0x91, 0x94, 0x98, 0x9b, 0x98, 0x90, 0x8a, 0x88, 0x85, 0x7f, 0x79, 0x75, 0x6d,
0x66, 0x63, 0x62, 0x5e, 0x5e, 0x64, 0x6a, 0x6e, 0x79, 0x86, 0x91, 0x9c, 0xac, 0xb6, 0xbb, 0xc2,
0xc7, 0xc3, 0xbd, 0xb7, 0xaf, 0xa3, 0x95, 0x8a, 0x80, 0x75, 0x69, 0x5e, 0x53, 0x49, 0x42, 0x3d,
0x39, 0x38, 0x3a, 0x40, 0x46, 0x4e, 0x58, 0x63, 0x6e, 0x7a, 0x83, 0x89, 0x8f, 0x93, 0x94, 0x93,
0x95, 0x97, 0x96, 0x92, 0x8f, 0x8a, 0x88, 0x87, 0x85, 0x82, 0x80, 0x80, 0x80, 0x83, 0x87, 0x8d,
0x94, 0x9a, 0x9e, 0xa0, 0xa3, 0xa8, 0xaa, 0xa8, 0xa6, 0xa2, 0x99, 0x91, 0x89, 0x80, 0x77, 0x6e,
0x63, 0x58, 0x4e, 0x47, 0x45, 0x46, 0x48, 0x4b, 0x4e, 0x53, 0x5a, 0x64, 0x70, 0x7e, 0x8b, 0x98,
0xa3, 0xac, 0xb8, 0xc5, 0xcf, 0xd3, 0xd2, 0xcb, 0xc2, 0xb8, 0xac, 0x9e, 0x8f, 0x80, 0x6d, 0x5b,
0x4d, 0x41, 0x38, 0x32, 0x2c, 0x27, 0x28, 0x2d, 0x34, 0x3c, 0x47, 0x54, 0x60, 0x6c, 0x78, 0x82,
0x8c, 0x99, 0xa1, 0xa1, 0x9f, 0x9e, 0x9e, 0x9d, 0x9b, 0x99, 0x99, 0x95, 0x8f, 0x8b, 0x88, 0x86,
0x87, 0x89, 0x8a, 0x8b, 0x8e, 0x95, 0x9a, 0x9d, 0xa2, 0xa5, 0xa5, 0xa6, 0xa6, 0xa3, 0x9f, 0x9d,
0x97, 0x8c, 0x83, 0x7d, 0x75, 0x6c, 0x64, 0x5c, 0x56, 0x50, 0x49, 0x45, 0x47, 0x4d, 0x57, 0x63,
0x6f, 0x79, 0x83, 0x8d, 0x95, 0x9b, 0xa2, 0xa9, 0xac, 0xae, 0xae, 0xab, 0xa6, 0xa2, 0x9c, 0x91,
0x85, 0x7c, 0x71, 0x64, 0x58, 0x4e, 0x47, 0x40, 0x39, 0x35, 0x36, 0x3c, 0x42, 0x4c, 0x57, 0x5f,
0x69, 0x75, 0x80, 0x8b, 0x98, 0xa2, 0xa8, 0xaa, 0xa9, 0xa5, 0xa2, 0x9f, 0x98, 0x8d, 0x84, 0x7e,
0x77, 0x71, 0x6c, 0x6a, 0x69, 0x6a, 0x6d, 0x72, 0x7d, 0x89, 0x98, 0xa7, 0xb3, 0xbc, 0xc4, 0xcb,
0xcd, 0xce, 0xcc, 0xc6, 0xbc, 0xaf, 0xa1, 0x95, 0x8c, 0x82, 0x77, 0x6b, 0x60, 0x55, 0x4c, 0x44,
0x3c, 0x35, 0x33, 0x33, 0x35, 0x3c, 0x49, 0x56, 0x63, 0x6f, 0x7a, 0x82, 0x89, 0x90, 0x94, 0x95,
0x97, 0x97, 0x95, 0x91, 0x8b, 0x85, 0x82, 0x7f, 0x79, 0x71, 0x6b, 0x66, 0x64, 0x62, 0x63, 0x67,
0x6e, 0x74, 0x7a, 0x81, 0x8a, 0x91, 0x96, 0x9a, 0x9a, 0x98, 0x98, 0x96, 0x91, 0x8a, 0x84, 0x7e,
0x76, 0x6d, 0x63, 0x5d, 0x5b, 0x5a, 0x5a, 0x59, 0x5c, 0x60, 0x65, 0x6b, 0x74, 0x80, 0x8f, 0x9b,
0xa7, 0xb5, 0xc2, 0xcd, 0xd9, 0xe1, 0xe3, 0xe0, 0xdb, 0xd1, 0xc0, 0xae, 0x9d, 0x89, 0x74, 0x62,
0x52, 0x44, 0x3b, 0x34, 0x2e, 0x29, 0x28, 0x2a, 0x2e, 0x35, 0x40, 0x4e, 0x5b, 0x66, 0x71, 0x7e,
0x88, 0x92, 0x9c, 0xa1, 0xa0, 0x9d, 0x99, 0x94, 0x8e, 0x8a, 0x86, 0x81, 0x7d, 0x79, 0x76, 0x74,
0x72, 0x70, 0x6d, 0x6b, 0x6c, 0x71, 0x77, 0x7d, 0x84, 0x8a, 0x8e, 0x90, 0x93, 0x96, 0x99, 0x9a,
0x9b, 0x97, 0x90, 0x89, 0x82, 0x7a, 0x70, 0x68, 0x63, 0x5d, 0x55, 0x53, 0x53, 0x56, 0x5e, 0x67,
0x70, 0x7d, 0x8a, 0x97, 0xa3, 0xac, 0xb2, 0xb8, 0xbc, 0xbb, 0xbc, 0xbf, 0xbb, 0xb3, 0xaf, 0xa8,
0x99, 0x8a, 0x7e, 0x6e, 0x5e, 0x52, 0x49, 0x3e, 0x36, 0x32, 0x30, 0x32, 0x3a, 0x43, 0x4c, 0x57,
0x65, 0x72, 0x7e, 0x88, 0x93, 0x9d, 0xa5, 0xaa, 0xae, 0xb0, 0xab, 0xa5, 0x9d, 0x91, 0x83, 0x78,
0x6e, 0x65, 0x5d, 0x5b, 0x5b, 0x5a, 0x5b, 0x60, 0x66, 0x6f, 0x7f, 0x8f, 0x9a, 0xa4, 0xaf, 0xb6,
0xb9, 0xba, 0xbb, 0xb8, 0xb0, 0xa6, 0x9d, 0x94, 0x88, 0x7f, 0x77, 0x6c, 0x61, 0x5a, 0x53, 0x4b,
0x44, 0x40, 0x3c, 0x37, 0x37, 0x3e, 0x48, 0x55, 0x69, 0x7a, 0x86, 0x92, 0x9d, 0xa6, 0xab, 0xaf,
0xb4, 0xb7, 0xb3, 0xac, 0xa8, 0xa2, 0x98, 0x90, 0x8a, 0x80, 0x73, 0x6b, 0x66, 0x5e, 0x5a, 0x5c,
0x5b, 0x5d, 0x68, 0x76, 0x80, 0x8a, 0x97, 0x9d, 0x9d, 0x9f, 0xa1, 0xa1, 0x9e, 0x9a, 0x95, 0x8e,
0x83, 0x7a, 0x73, 0x6a, 0x60, 0x5c, 0x5b, 0x56, 0x51, 0x52, 0x56, 0x57, 0x5c, 0x69, 0x73, 0x7d,
0x8e, 0xa2, 0xad, 0xb9, 0xc8, 0xd0, 0xd3, 0xd6, 0xd8, 0xd2, 0xc5, 0xb5, 0xa2, 0x8b, 0x73, 0x5f,
0x4f, 0x3f, 0x32, 0x2b, 0x28, 0x24, 0x23, 0x27, 0x2b, 0x31, 0x3e, 0x4e, 0x5b, 0x69, 0x7c, 0x8c,
0x97, 0xa2, 0xae, 0xb5, 0xb7, 0xb8, 0xb8, 0xb0, 0xa6, 0x9f, 0x97, 0x8c, 0x84, 0x82, 0x80, 0x7c,
0x78, 0x74, 0x70, 0x6d, 0x6a, 0x6b, 0x6d, 0x71, 0x7b, 0x85, 0x8c, 0x94, 0x9e, 0xa4, 0xa7, 0xae,
0xb5, 0xb5, 0xb0, 0xac, 0xa5, 0x96, 0x88, 0x7e, 0x74, 0x68, 0x60, 0x59, 0x55, 0x52, 0x53, 0x58,
0x5e, 0x65, 0x70, 0x7e, 0x8a, 0x96, 0xa2, 0xaa, 0xad, 0xad, 0xb0, 0xb0, 0xad, 0xab, 0xaa, 0xa1,
0x94, 0x88, 0x7b, 0x67, 0x56, 0x4c, 0x40, 0x32, 0x28, 0x24, 0x21, 0x23, 0x2a, 0x34, 0x3e, 0x4a,
0x5b, 0x6b, 0x7a, 0x8a, 0x98, 0xa1, 0xa9, 0xb2, 0xbc, 0xc4, 0xc6, 0xc6, 0xc0, 0xb2, 0xa2, 0x96,
0x89, 0x7c, 0x73, 0x6e, 0x68, 0x62, 0x63, 0x68, 0x6d, 0x75, 0x80, 0x8d, 0x98, 0xa4, 0xb0, 0xb8,
0xbf, 0xc3, 0xc3, 0xbf, 0xba, 0xb6, 0xb3, 0xae, 0xa2, 0x97, 0x8d, 0x80, 0x73, 0x6c, 0x63, 0x58,
0x50, 0x48, 0x3d, 0x35, 0x33, 0x36, 0x3a, 0x42, 0x52, 0x62, 0x70, 0x81, 0x92, 0x9d, 0xa5, 0xad,
0xb2, 0xb3, 0xb3, 0xb3, 0xaf, 0xa5, 0x99, 0x8f, 0x82, 0x74, 0x6a, 0x61, 0x56, 0x4b, 0x43, 0x3e,
0x3a, 0x3b, 0x46, 0x52, 0x5f, 0x70, 0x83, 0x91, 0x9a, 0xa2, 0xa7, 0xa7, 0xa8, 0xa9, 0xa6, 0xa2,
0x9f, 0x98, 0x8f, 0x86, 0x7e, 0x77, 0x71, 0x6d, 0x69, 0x65, 0x61, 0x60, 0x63, 0x6b, 0x73, 0x7c,
0x8b, 0x9d, 0xad, 0xbe, 0xce, 0xd7, 0xdc, 0xe0, 0xe0, 0xdb, 0xd5, 0xcd, 0xbc, 0xa7, 0x92, 0x7c,
0x64, 0x53, 0x48, 0x3f, 0x37, 0x30, 0x2e, 0x2d, 0x2d, 0x32, 0x3d, 0x49, 0x53, 0x60, 0x70, 0x7f,
0x8c, 0x9a, 0xa6, 0xab, 0xae, 0xb2, 0xb1, 0xad, 0xa9, 0xa1, 0x94, 0x85, 0x7b, 0x72, 0x6a, 0x66,
0x64, 0x5e, 0x57, 0x51, 0x4d, 0x4b, 0x4b, 0x51, 0x5a, 0x64, 0x6e, 0x7a, 0x87, 0x96, 0xa4, 0xb1,
0xb9, 0xbd, 0xbf, 0xc0, 0xbb, 0xb2, 0xa9, 0x9c, 0x8c, 0x80, 0x78, 0x71, 0x6d, 0x6b, 0x69, 0x66,
0x65, 0x6a, 0x72, 0x7c, 0x85, 0x93, 0xa1, 0xa9, 0xae, 0xb4, 0xb9, 0xba, 0xba, 0xba, 0xb6, 0xaf,
0xa5, 0x9a, 0x8d, 0x7f, 0x6e, 0x5e, 0x4e, 0x40, 0x38, 0x31, 0x2b, 0x2b, 0x2f, 0x32, 0x3a, 0x49,
0x57, 0x66, 0x76, 0x84, 0x90, 0x9a, 0xa3, 0xab, 0xb3, 0xba, 0xbe, 0xc0, 0xbd, 0xb3, 0xa9, 0x9d,
0x8b, 0x7d, 0x71, 0x66, 0x5d, 0x59, 0x57, 0x55, 0x56, 0x5e, 0x68, 0x71, 0x7c, 0x87, 0x91, 0x9b,
0xa3, 0xa9, 0xad, 0xb0, 0xaf, 0xad, 0xac, 0xac, 0xa9, 0xa3, 0x9a, 0x8e, 0x82, 0x78, 0x6b, 0x60,
0x59, 0x51, 0x4a, 0x47, 0x42, 0x3e, 0x42, 0x4b, 0x57, 0x64, 0x74, 0x82, 0x8f, 0x9d, 0xaa, 0xb6,
0xbf, 0xc3, 0xc1, 0xc0, 0xbf, 0xb9, 0xb1, 0xa7, 0x97, 0x86, 0x7b, 0x6f, 0x63, 0x5a, 0x52, 0x48,
0x40, 0x3f, 0x41, 0x47, 0x55, 0x67, 0x77, 0x85, 0x92, 0x9c, 0xa2, 0xa7, 0xaa, 0xac, 0xab, 0xa8,
0xa3, 0x9c, 0x96, 0x8f, 0x89, 0x82, 0x78, 0x6e, 0x69, 0x64, 0x5f, 0x5e, 0x5d, 0x5d, 0x61, 0x69,
0x74, 0x82, 0x93, 0xa3, 0xae, 0xb4, 0xb9, 0xbe, 0xc0, 0xc0, 0xbe, 0xb8, 0xae, 0xa1, 0x91, 0x81,
0x71, 0x61, 0x55, 0x4c, 0x43, 0x3c, 0x39, 0x38, 0x37, 0x3b, 0x42, 0x4a, 0x55, 0x64, 0x72, 0x80,
0x8f, 0x9d, 0xa6, 0xae, 0xb5, 0xba, 0xbe, 0xc0, 0xbc, 0xb5, 0xaa, 0x9b, 0x8d, 0x83, 0x78, 0x6e,
0x69, 0x61, 0x57, 0x52, 0x50, 0x4b, 0x49, 0x4d, 0x54, 0x5d, 0x6a, 0x78, 0x83, 0x8f, 0xa1, 0xb2,
0xbc, 0xc4, 0xcb, 0xce, 0xcd, 0xc7, 0xbe, 0xb1, 0xa1, 0x92, 0x87, 0x7e, 0x75, 0x72, 0x6e, 0x67,
0x61, 0x61, 0x64, 0x68, 0x6f, 0x7a, 0x83, 0x8d, 0x97, 0x9d, 0x9f, 0xa1, 0xa4, 0xa5, 0xa2, 0x9d,
0x95, 0x8b, 0x80, 0x74, 0x67, 0x59, 0x4c, 0x41, 0x37, 0x30, 0x2c, 0x2e, 0x32, 0x33, 0x37, 0x44,
0x53, 0x62, 0x74, 0x83, 0x8f, 0x9a, 0xa5, 0xad, 0xb2, 0xb9, 0xc2, 0xc5, 0xc2, 0xbd, 0xb6, 0xac,
0x9e, 0x91, 0x86, 0x7c, 0x71, 0x69, 0x66, 0x63, 0x62, 0x67, 0x6e, 0x74, 0x7b, 0x86, 0x92, 0x9b,
0xa4, 0xac, 0xaf, 0xb1, 0xb4, 0xb7, 0xb8, 0xba, 0xbc, 0xb8, 0xac, 0xa0, 0x95, 0x87, 0x7a, 0x6e,
0x64, 0x59, 0x4f, 0x49, 0x44, 0x3f, 0x40, 0x45, 0x4b, 0x53, 0x60, 0x6f, 0x7d, 0x88, 0x94, 0x9e,
0xa6, 0xac, 0xb2, 0xb4, 0xb1, 0xad, 0xa9, 0x9d, 0x89, 0x7c, 0x71, 0x64, 0x59, 0x52, 0x4a, 0x41,
0x3b, 0x38, 0x37, 0x3c, 0x48, 0x56, 0x64, 0x71, 0x80, 0x8c, 0x95, 0x9c, 0xa4, 0xa9, 0xac, 0xb0,
0xb2, 0xae, 0xa8, 0xa4, 0x9f, 0x95, 0x8a, 0x83, 0x7f, 0x7a, 0x76, 0x75, 0x72, 0x6c, 0x6b, 0x70,
0x78, 0x83, 0x92, 0xa0, 0xaa, 0xb0, 0xb6, 0xbb, 0xbd, 0xbe, 0xbf, 0xbd, 0xb8, 0xb0, 0xa6, 0x96,
0x85, 0x78, 0x6b, 0x5c, 0x50, 0x4a, 0x48, 0x44, 0x42, 0x42, 0x43, 0x47, 0x51, 0x5f, 0x6b, 0x79,
0x87, 0x93, 0x97, 0x9c, 0xa4, 0xa9, 0xae, 0xb3, 0xb3, 0xaf, 0xa5, 0x98, 0x8a, 0x79, 0x67, 0x5c,
0x52, 0x45, 0x3b, 0x37, 0x33, 0x2e, 0x2e, 0x34, 0x39, 0x43, 0x53, 0x65, 0x74, 0x84, 0x96, 0xa4,
0xaf, 0xbb, 0xc8, 0xd0, 0xd4, 0xd6, 0xd1, 0xc5, 0xb9, 0xac, 0x9e, 0x91, 0x88, 0x84, 0x80, 0x7a,
0x75, 0x73, 0x71, 0x72, 0x79, 0x80, 0x86, 0x8d, 0x96, 0x9d, 0x9e, 0x9f, 0xa2, 0xa0, 0x9e, 0x9e,
0x9b, 0x95, 0x8e, 0x85, 0x79, 0x6a, 0x5d, 0x53, 0x4a, 0x42, 0x41, 0x41, 0x40, 0x3e, 0x40, 0x48,
0x52, 0x5e, 0x6e, 0x7d, 0x88, 0x93, 0x9d, 0xa2, 0xa6, 0xab, 0xb0, 0xb1, 0xb0, 0xb0, 0xac, 0xa1,
0x95, 0x8a, 0x7c, 0x6d, 0x63, 0x5c, 0x56, 0x53, 0x53, 0x55, 0x55, 0x57, 0x60, 0x6c, 0x77, 0x82,
0x8f, 0x99, 0x9f, 0xa3, 0xa8, 0xab, 0xaf, 0xb5, 0xb8, 0xb6, 0xb3, 0xad, 0xa2, 0x93, 0x86, 0x7a,
0x6d, 0x62, 0x5c, 0x57, 0x4f, 0x4a, 0x49, 0x4a, 0x4d, 0x53, 0x60, 0x6f, 0x7e, 0x8b, 0x97, 0x9f,
0xa4, 0xa9, 0xaf, 0xb3, 0xb6, 0xb6, 0xb2, 0xa9, 0x9c, 0x8f, 0x80, 0x71, 0x67, 0x60, 0x59, 0x53,
0x51, 0x4e, 0x4b, 0x4b, 0x51, 0x5a, 0x62, 0x6e, 0x7d, 0x88, 0x90, 0x95, 0x99, 0x9b, 0x9c, 0xa0,
0xa4, 0xa6, 0xa5, 0xa2, 0x99, 0x8e, 0x85, 0x7e, 0x76, 0x70, 0x6d, 0x6c, 0x68, 0x65, 0x66, 0x66,
0x66, 0x6b, 0x75, 0x7f, 0x87, 0x91, 0x9a, 0xa0, 0xa2, 0xa2, 0xa2, 0xa4, 0xa3, 0xa1, 0x9d, 0x94,
0x89, 0x7c, 0x6d, 0x60, 0x58, 0x52, 0x4e, 0x4e, 0x50, 0x50, 0x4f, 0x52, 0x5a, 0x63, 0x6c, 0x79,
0x86, 0x92, 0x9b, 0xa1, 0xa8, 0xab, 0xad, 0xb0, 0xb2, 0xb0, 0xac, 0xa4, 0x98, 0x89, 0x78, 0x69,
0x5c, 0x4f, 0x47, 0x43, 0x3e, 0x3b, 0x3a, 0x3b, 0x3e, 0x46, 0x53, 0x62, 0x71, 0x82, 0x95, 0xa1,
0xaa, 0xb4, 0xbe, 0xc7, 0xcd, 0xcf, 0xcf, 0xc9, 0xc0, 0xb6, 0xa9, 0x99, 0x8c, 0x81, 0x7a, 0x74,
0x71, 0x6f, 0x6a, 0x68, 0x6b, 0x6f, 0x72, 0x76, 0x7d, 0x80, 0x82, 0x85, 0x84, 0x80, 0x7f, 0x7f,
0x80, 0x7f, 0x7c, 0x77, 0x70, 0x65, 0x5a, 0x51, 0x49, 0x42, 0x3e, 0x3e, 0x3f, 0x41, 0x45, 0x4b,
0x51, 0x5a, 0x66, 0x71, 0x7c, 0x8a, 0x97, 0x9f, 0xa4, 0xa9, 0xad, 0xaf, 0xaf, 0xb0, 0xb0, 0xab,
0xa1, 0x97, 0x8a, 0x7b, 0x6f, 0x67, 0x60, 0x5c, 0x5c, 0x5d, 0x5e, 0x60, 0x68, 0x72, 0x7b, 0x85,
0x93, 0x9d, 0xa5, 0xaf, 0xb6, 0xb8, 0xb8, 0xba, 0xba, 0xb7, 0xb4, 0xaf, 0xa7, 0x9b, 0x8d, 0x80,
0x73, 0x65, 0x5c, 0x57, 0x51, 0x4b, 0x48, 0x47, 0x44, 0x48, 0x51, 0x5b, 0x65, 0x73, 0x7e, 0x84,
0x8a, 0x91, 0x95, 0x94, 0x94, 0x95, 0x92, 0x8c, 0x85, 0x7f, 0x75, 0x68, 0x5c, 0x52, 0x4c, 0x47,
0x45, 0x46, 0x47, 0x47, 0x4d, 0x56, 0x5e, 0x68, 0x75, 0x7f, 0x88, 0x91, 0x97, 0x97, 0x99, 0x9e,
0xa2, 0xa2, 0xa3, 0xa3, 0x9f, 0x98, 0x91, 0x8c, 0x85, 0x7e, 0x79, 0x77, 0x75, 0x73, 0x73, 0x72,
0x72, 0x73, 0x79, 0x80, 0x86, 0x8d, 0x96, 0x9d, 0xa1, 0xa7, 0xaa, 0xa8, 0xa9, 0xab, 0xa9, 0xa1,
0x98, 0x8d, 0x80, 0x73, 0x67, 0x5d, 0x56, 0x52, 0x52, 0x52, 0x50, 0x52, 0x59, 0x5f, 0x66, 0x6f,
0x79, 0x80, 0x87, 0x90, 0x97, 0x9a, 0x9b, 0x9d, 0x9c, 0x98, 0x93, 0x8b, 0x7f, 0x72, 0x66, 0x59,
0x4c, 0x41, 0x3a, 0x34, 0x30, 0x30, 0x2f, 0x2e, 0x31, 0x38, 0x43, 0x50, 0x5f, 0x6e, 0x7f, 0x8f,
0x9c, 0xa8, 0xb5, 0xbe, 0xc5, 0xcc, 0xcd, 0xca, 0xc7, 0xc1, 0xb6, 0xac, 0xa1, 0x94, 0x8a, 0x83,
0x7f, 0x7a, 0x76, 0x75, 0x76, 0x79, 0x7d, 0x81, 0x85, 0x8a, 0x8c, 0x8d, 0x8d, 0x8b, 0x88, 0x87,
0x86, 0x85, 0x84, 0x80, 0x7a, 0x71, 0x6b, 0x64, 0x5a, 0x52, 0x4e, 0x4a, 0x47, 0x47, 0x48, 0x4c,
0x52, 0x59, 0x61, 0x6a, 0x75, 0x7f, 0x87, 0x8f, 0x95, 0x9b, 0x9e, 0xa0, 0xa2, 0xa4, 0xa3, 0x9f,
0x98, 0x8d, 0x80, 0x73, 0x68, 0x5c, 0x52, 0x4b, 0x48, 0x49, 0x4b, 0x50, 0x56, 0x60, 0x6d, 0x7a,
0x83, 0x8e, 0x9a, 0xa3, 0xab, 0xb0, 0xb3, 0xb4, 0xb4, 0xb3, 0xb1, 0xac, 0xa3, 0x99, 0x8f, 0x84,
0x7b, 0x73, 0x6c, 0x65, 0x61, 0x5d, 0x58, 0x55, 0x54, 0x54, 0x57, 0x5f, 0x69, 0x73, 0x7c, 0x84,
0x8d, 0x92, 0x94, 0x97, 0x9a, 0x9a, 0x99, 0x95, 0x90, 0x88, 0x81, 0x7b, 0x72, 0x68, 0x62, 0x5e,
0x5a, 0x57, 0x56, 0x56, 0x59, 0x5f, 0x68, 0x72, 0x7b, 0x84, 0x90, 0x98, 0x9c, 0x9f, 0xa1, 0xa0,
0x9e, 0x9e, 0x9e, 0x9b, 0x96, 0x8f, 0x87, 0x80, 0x7c, 0x77, 0x72, 0x6c, 0x69, 0x67, 0x63, 0x60,
0x5f, 0x60, 0x64, 0x6a, 0x72, 0x78, 0x7e, 0x84, 0x8d, 0x92, 0x95, 0x9a, 0x9f, 0xa0, 0xa0, 0xa1,
0x9c, 0x91, 0x86, 0x7e, 0x72, 0x66, 0x61, 0x61, 0x5d, 0x5c, 0x5e, 0x60, 0x63, 0x69, 0x74, 0x7c,
0x83, 0x8b, 0x92, 0x95, 0x9a, 0xa1, 0xa3, 0xa1, 0x9f, 0x9e, 0x99, 0x90, 0x87, 0x7e, 0x6f, 0x61,
0x58, 0x51, 0x4c, 0x49, 0x4b, 0x4d, 0x4c, 0x4c, 0x50, 0x53, 0x56, 0x61, 0x6e, 0x79, 0x83, 0x91,
0x9b, 0xa4, 0xaf, 0xbb, 0xc2, 0xc6, 0xc9, 0xcb, 0xc7, 0xc0, 0xb6, 0xad, 0xa1, 0x93, 0x89, 0x83,
0x7c, 0x76, 0x75, 0x72, 0x6d, 0x6c, 0x71, 0x76, 0x79, 0x7d, 0x81, 0x83, 0x83, 0x82, 0x7f, 0x7a,
0x78, 0x78, 0x77, 0x73, 0x6f, 0x6c, 0x64, 0x5c, 0x59, 0x56, 0x51, 0x4e, 0x4f, 0x51, 0x50, 0x52,
0x59, 0x5e, 0x62, 0x6b, 0x78, 0x81, 0x89, 0x91, 0x97, 0x9b, 0x9e, 0xa0, 0xa2, 0xa3, 0xa7, 0xa9,
0xa6, 0xa0, 0x97, 0x8b, 0x7f, 0x72, 0x69, 0x63, 0x5f, 0x5f, 0x62, 0x64, 0x69, 0x73, 0x80, 0x8a,
0x97, 0xa3, 0xac, 0xb4, 0xba, 0xbd, 0xbe, 0xbe, 0xbb, 0xb7, 0xb3, 0xae, 0xa7, 0x9f, 0x94, 0x87,
0x7e, 0x76, 0x6f, 0x69, 0x67, 0x66, 0x63, 0x5e, 0x5a, 0x57, 0x56, 0x5a, 0x61, 0x68, 0x6d, 0x72,
0x78, 0x7c, 0x7e, 0x80, 0x83, 0x83, 0x81, 0x80, 0x7c, 0x78, 0x73, 0x6d, 0x65, 0x5e, 0x59, 0x57,
0x56, 0x57, 0x5b, 0x5e, 0x5f, 0x64, 0x6d, 0x76, 0x80, 0x90, 0x9e, 0xa7, 0xac, 0xb1, 0xb3, 0xb1,
0xad, 0xaa, 0xa6, 0xa2, 0x9b, 0x92, 0x8c, 0x86, 0x80, 0x7e, 0x7c, 0x78, 0x76, 0x77, 0x77, 0x76,
0x74, 0x74, 0x75, 0x77, 0x7d, 0x84, 0x8c, 0x93, 0x9b, 0x9e, 0x9e, 0x9f, 0xa2, 0xa4, 0xa4, 0xa3,
0xa3, 0x9f, 0x95, 0x89, 0x80, 0x76, 0x6e, 0x6a, 0x67, 0x65, 0x66, 0x6a, 0x6e, 0x72, 0x78, 0x7f,
0x83, 0x88, 0x8f, 0x95, 0x95, 0x96, 0x97, 0x92, 0x8b, 0x86, 0x81, 0x7b, 0x73, 0x6a, 0x60, 0x54,
0x47, 0x40, 0x3c, 0x3c, 0x40, 0x47, 0x4e, 0x53, 0x57, 0x5b, 0x5f, 0x65, 0x6e, 0x79, 0x80, 0x8b,
0x96, 0x9f, 0xa7, 0xb0, 0xb8, 0xbd, 0xbf, 0xc2, 0xc4, 0xc2, 0xbb, 0xb2, 0xa8, 0x9c, 0x90, 0x89,
0x85, 0x82, 0x82, 0x83, 0x82, 0x81, 0x81, 0x84, 0x88, 0x8e, 0x96, 0x9c, 0x9e, 0x9d, 0x9c, 0x98,
0x92, 0x8d, 0x89, 0x84, 0x7e, 0x79, 0x73, 0x6d, 0x66, 0x61, 0x5d, 0x58, 0x56, 0x58, 0x5c, 0x5f,
0x63, 0x67, 0x69, 0x6a, 0x70, 0x79, 0x80, 0x86, 0x8d, 0x8f, 0x8d, 0x8c, 0x8a, 0x89, 0x89, 0x8a,
0x8b, 0x88, 0x82, 0x7c, 0x73, 0x67, 0x5f, 0x58, 0x52, 0x50, 0x52, 0x59, 0x61, 0x6a, 0x76, 0x82,
0x8d, 0x99, 0xa8, 0xb3, 0xbb, 0xc2, 0xc5, 0xc2, 0xbc, 0xb8, 0xb4, 0xac, 0xa8, 0xa5, 0x9c, 0x91,
0x87, 0x7e, 0x76, 0x71, 0x6e, 0x6e, 0x6e, 0x6e, 0x6e, 0x6d, 0x6d, 0x6e, 0x6f, 0x73, 0x77, 0x7a,
0x7f, 0x83, 0x85, 0x85, 0x86, 0x84, 0x82, 0x80, 0x81, 0x80, 0x7c, 0x78, 0x72, 0x6a, 0x64, 0x60,
0x5f, 0x62, 0x66, 0x6c, 0x72, 0x77, 0x7c, 0x82, 0x8b, 0x96, 0xa2, 0xad, 0xb4, 0xb7, 0xb9, 0xb8,
0xb2, 0xa8, 0x9f, 0x94, 0x89, 0x83, 0x7d, 0x74, 0x6c, 0x69, 0x65, 0x61, 0x61, 0x63, 0x65, 0x69,
0x6e, 0x6e, 0x6c, 0x6c, 0x6f, 0x73, 0x7a, 0x83, 0x8b, 0x90, 0x94, 0x95, 0x94, 0x94, 0x94, 0x95,
0x94, 0x92, 0x91, 0x8e, 0x87, 0x80, 0x7b, 0x75, 0x71, 0x70, 0x71, 0x75, 0x7b, 0x81, 0x85, 0x88,
0x8c, 0x91, 0x96, 0x9b, 0xa0, 0xa4, 0xa3, 0x9f, 0x99, 0x90, 0x86, 0x80, 0x7b, 0x74, 0x6c, 0x65,
0x5d, 0x55, 0x50, 0x4e, 0x50, 0x53, 0x5c, 0x65, 0x6b, 0x72, 0x78, 0x7c, 0x7f, 0x81, 0x86, 0x8e,
0x94, 0x99, 0xa1, 0xa7, 0xa8, 0xa9, 0xa9, 0xa9, 0xa9, 0xa9, 0xa6, 0xa0, 0x97, 0x8d, 0x82, 0x79,
0x72, 0x70, 0x71, 0x73, 0x78, 0x7e, 0x80, 0x80, 0x85, 0x89, 0x8c, 0x91, 0x96, 0x9a, 0x9b, 0x98,
0x95, 0x8e, 0x84, 0x7e, 0x77, 0x6f, 0x6b, 0x68, 0x64, 0x5f, 0x5a, 0x55, 0x54, 0x56, 0x5a, 0x60,
0x68, 0x6d, 0x72, 0x76, 0x77, 0x78, 0x7e, 0x83, 0x86, 0x88, 0x8c, 0x8c, 0x89, 0x87, 0x86, 0x84,
0x81, 0x83, 0x84, 0x81, 0x7f, 0x7e, 0x78, 0x6e, 0x68, 0x67, 0x68, 0x6d, 0x76, 0x82, 0x8d, 0x95,
0xa0, 0xac, 0xb4, 0xbd, 0xc8, 0xce, 0xce, 0xcd, 0xc9, 0xbf, 0xb3, 0xaa, 0xa3, 0x9b, 0x92, 0x8c,
0x84, 0x7d, 0x74, 0x6d, 0x66, 0x60, 0x5e, 0x62, 0x65, 0x67, 0x6a, 0x6b, 0x6a, 0x68, 0x67, 0x68,
0x69, 0x6b, 0x6e, 0x6f, 0x6a, 0x67, 0x64, 0x61, 0x5f, 0x5f, 0x60, 0x60, 0x5f, 0x5e, 0x5d, 0x59,
0x58, 0x5b, 0x5f, 0x66, 0x70, 0x7c, 0x86, 0x8e, 0x97, 0xa1, 0xa9, 0xb0, 0xb9, 0xc1, 0xc4, 0xc5,
0xc3, 0xb9, 0xab, 0xa0, 0x96, 0x8a, 0x80, 0x7d, 0x78, 0x72, 0x6e, 0x6c, 0x6b, 0x6a, 0x6d, 0x72,
0x76, 0x7a, 0x80, 0x81, 0x81, 0x82, 0x85, 0x8a, 0x8e, 0x93, 0x99, 0x9c, 0x9c, 0x9b, 0x98, 0x92,
0x8c, 0x8a, 0x89, 0x87, 0x86, 0x85, 0x81, 0x7b, 0x76, 0x72, 0x6e, 0x6d, 0x71, 0x78, 0x7c, 0x7f,
0x83, 0x88, 0x8a, 0x8d, 0x91, 0x93, 0x93, 0x91, 0x8d, 0x84, 0x7a, 0x6f, 0x64, 0x5a, 0x52, 0x4e,
0x4c, 0x48, 0x45, 0x47, 0x47, 0x48, 0x4e, 0x57, 0x62, 0x70, 0x7c, 0x83, 0x87, 0x89, 0x8d, 0x91,
0x93, 0x95, 0x9b, 0x9f, 0x9f, 0x9f, 0x9f, 0x9d, 0x9a, 0x99, 0x97, 0x93, 0x90, 0x8d, 0x88, 0x82,
0x7f, 0x7e, 0x7c, 0x7d, 0x83, 0x8b, 0x93, 0x9b, 0xa0, 0xa3, 0xa6, 0xa8, 0xaa, 0xae, 0xb0, 0xb2,
0xb1, 0xaa, 0xa1, 0x96, 0x8b, 0x80, 0x77, 0x6e, 0x67, 0x61, 0x5c, 0x59, 0x57, 0x56, 0x56, 0x59,
0x5f, 0x64, 0x6b, 0x6f, 0x6f, 0x6f, 0x70, 0x6f, 0x6e, 0x6f, 0x71, 0x74, 0x74, 0x72, 0x6f, 0x6c,
0x68, 0x65, 0x66, 0x68, 0x69, 0x6b, 0x6c, 0x69, 0x67, 0x66, 0x65, 0x65, 0x6a, 0x75, 0x81, 0x8c,
0x98, 0xa5, 0xb0, 0xb7, 0xbe, 0xc4, 0xc8, 0xc8, 0xc7, 0xc3, 0xb8, 0xad, 0xa5, 0x9a, 0x8f, 0x8a,
0x86, 0x81, 0x7b, 0x76, 0x71, 0x6b, 0x68, 0x67, 0x68, 0x6d, 0x74, 0x7c, 0x80, 0x7f, 0x80, 0x80,
0x7c, 0x79, 0x78, 0x74, 0x71, 0x6f, 0x6c, 0x68, 0x65, 0x63, 0x64, 0x64, 0x65, 0x67, 0x68, 0x67,
0x66, 0x65, 0x65, 0x68, 0x6e, 0x79, 0x85, 0x90, 0x9a, 0xa4, 0xac, 0xb2, 0xb7, 0xbc, 0xbd, 0xbd,
0xbb, 0xb5, 0xab, 0x9f, 0x92, 0x84, 0x76, 0x6b, 0x64, 0x5e, 0x59, 0x58, 0x5a, 0x59, 0x58, 0x5a,
0x5e, 0x63, 0x6a, 0x72, 0x76, 0x78, 0x7b, 0x80, 0x81, 0x82, 0x87, 0x8b, 0x8c, 0x8c, 0x8c, 0x87,
0x80, 0x7d, 0x7b, 0x79, 0x79, 0x7d, 0x80, 0x80, 0x81, 0x81, 0x80, 0x7c, 0x7a, 0x7c, 0x80, 0x85,
0x8d, 0x93, 0x98, 0x9d, 0xa2, 0xa5, 0xa6, 0xa5, 0xa3, 0x9c, 0x92, 0x88, 0x7c, 0x6e, 0x63, 0x5d,
0x59, 0x57, 0x57, 0x58, 0x5a, 0x5d, 0x60, 0x65, 0x6b, 0x73, 0x7c, 0x85, 0x8e, 0x94, 0x98, 0x9a,
0x99, 0x96, 0x94, 0x92, 0x8f, 0x8c, 0x8b, 0x87, 0x82, 0x7e, 0x79, 0x75, 0x75, 0x76, 0x76, 0x73,
0x72, 0x72, 0x72, 0x72, 0x75, 0x7c, 0x82, 0x8a, 0x93, 0x99, 0x9d, 0x9f, 0xa2, 0xa2, 0xa1, 0xa1,
0xa3, 0xa1, 0x9c, 0x97, 0x8f, 0x83, 0x77, 0x6d, 0x64, 0x5b, 0x56, 0x55, 0x55, 0x55, 0x57, 0x5b,
0x5f, 0x65, 0x6c, 0x73, 0x76, 0x78, 0x78, 0x77, 0x75, 0x74, 0x74, 0x74, 0x72, 0x74, 0x76, 0x75,
0x73, 0x71, 0x6f, 0x6e, 0x6f, 0x72, 0x75, 0x78, 0x7c, 0x80, 0x83, 0x84, 0x87, 0x8b, 0x91, 0x9a,
0xa5, 0xaf, 0xb8, 0xbf, 0xc5, 0xc8, 0xc9, 0xca, 0xc8, 0xc2, 0xba, 0xb2, 0xa9, 0x9c, 0x90, 0x87,
0x7f, 0x77, 0x71, 0x6d, 0x66, 0x61, 0x60, 0x5e, 0x5b, 0x5c, 0x61, 0x66, 0x6d, 0x74, 0x77, 0x76,
0x73, 0x6e, 0x68, 0x60, 0x57, 0x52, 0x4f, 0x4c, 0x4b, 0x4c, 0x4d, 0x4e, 0x51, 0x57, 0x5a, 0x5d,
0x61, 0x64, 0x65, 0x68, 0x70, 0x78, 0x80, 0x8c, 0x9b, 0xa8, 0xb3, 0xbc, 0xc1, 0xc2, 0xc2, 0xc1,
0xbe, 0xb7, 0xb1, 0xad, 0xa6, 0x9c, 0x91, 0x87, 0x7d, 0x74, 0x70, 0x6d, 0x69, 0x67, 0x69, 0x6c,
0x6d, 0x72, 0x7a, 0x80, 0x85, 0x8d, 0x91, 0x92, 0x92, 0x93, 0x92, 0x90, 0x8e, 0x8b, 0x87, 0x83,
0x80, 0x7d, 0x78, 0x73, 0x71, 0x73, 0x74, 0x76, 0x7a, 0x7c, 0x7b, 0x7a, 0x7b, 0x78, 0x76, 0x79,
0x7d, 0x80, 0x85, 0x8a, 0x8e, 0x91, 0x93, 0x94, 0x91, 0x8a, 0x81, 0x7a, 0x71, 0x66, 0x5d, 0x56,
0x4f, 0x4c, 0x4e, 0x51, 0x54, 0x59, 0x5f, 0x64, 0x69, 0x6f, 0x77, 0x7d, 0x81, 0x8a, 0x93, 0x96,
0x99, 0x9a, 0x96, 0x91, 0x8d, 0x88, 0x82, 0x7d, 0x7a, 0x78, 0x76, 0x74, 0x75, 0x77, 0x7a, 0x7d,
0x80, 0x83, 0x86, 0x89, 0x8f, 0x95, 0x99, 0xa0, 0xa7, 0xac, 0xb2, 0xb8, 0xba, 0xb9, 0xb9, 0xb7,
0xb3, 0xae, 0xab, 0xa7, 0xa0, 0x98, 0x90, 0x85, 0x78, 0x6c, 0x62, 0x5a, 0x55, 0x52, 0x51, 0x53,
0x57, 0x5d, 0x62, 0x66, 0x67, 0x69, 0x69, 0x67, 0x64, 0x61, 0x5e, 0x5c, 0x5b, 0x5a, 0x58, 0x59,
0x5c, 0x5e, 0x5e, 0x5c, 0x5d, 0x5d, 0x5d, 0x61, 0x68, 0x6d, 0x74, 0x7c, 0x83, 0x8a, 0x91, 0x99,
0xa0, 0xa7, 0xae, 0xb5, 0xba, 0xbd, 0xbe, 0xc0, 0xc0, 0xbe, 0xbb, 0xb6, 0xb0, 0xa9, 0xa3, 0x9b,
0x90, 0x86, 0x80, 0x7a, 0x75, 0x70, 0x6e, 0x6d, 0x6d, 0x6f, 0x73, 0x75, 0x77, 0x7c, 0x81, 0x82,
0x81, 0x80, 0x79, 0x71, 0x69, 0x63, 0x5c, 0x58, 0x57, 0x56, 0x56, 0x59, 0x5d, 0x61, 0x62, 0x66,
0x6b, 0x6d, 0x6d, 0x70, 0x74, 0x7a, 0x80, 0x89, 0x94, 0x9d, 0xa8, 0xb1, 0xb5, 0xb7, 0xb7, 0xb2,
0xac, 0xa5, 0x9e, 0x9a, 0x96, 0x90, 0x8c, 0x86, 0x7e, 0x76, 0x6f, 0x68, 0x62, 0x5d, 0x5a, 0x59,
0x59, 0x5e, 0x65, 0x6d, 0x74, 0x7b, 0x80, 0x84, 0x87, 0x87, 0x86, 0x82, 0x7f, 0x7e, 0x7c, 0x78,
0x75, 0x75, 0x75, 0x73, 0x74, 0x75, 0x74, 0x75, 0x7a, 0x7d, 0x7e, 0x7f, 0x81, 0x83, 0x84, 0x88,
0x8d, 0x8f, 0x92, 0x98, 0x9c, 0x9d, 0xa0, 0xa1, 0xa0, 0x9f, 0x9c, 0x97, 0x8f, 0x86, 0x80, 0x7b,
0x74, 0x70, 0x6e, 0x6c, 0x6a, 0x6c, 0x6e, 0x70, 0x72, 0x76, 0x79, 0x7b, 0x7d, 0x80, 0x83, 0x86,
0x88, 0x8a, 0x88, 0x85, 0x82, 0x7f, 0x79, 0x72, 0x6c, 0x67, 0x61, 0x5f, 0x61, 0x63, 0x66, 0x6a,
0x70, 0x76, 0x7a, 0x7e, 0x82, 0x85, 0x8a, 0x91, 0x97, 0x9c, 0xa0, 0xa5, 0xaa, 0xac, 0xad, 0xad,
0xaa, 0xa5, 0xa2, 0x9f, 0x9a, 0x93, 0x8d, 0x86, 0x7e, 0x74, 0x6d, 0x67, 0x5f, 0x5a, 0x59, 0x56,
0x56, 0x59, 0x5e, 0x62, 0x65, 0x69, 0x6d, 0x6b, 0x6a, 0x6a, 0x67, 0x65, 0x66, 0x67, 0x67, 0x69,
0x6d, 0x6f, 0x6f, 0x71, 0x74, 0x75, 0x73, 0x76, 0x7c, 0x80, 0x86, 0x8d, 0x96, 0x9c, 0xa3, 0xab,
0xb2, 0xb6, 0xb9, 0xbc, 0xbc, 0xb9, 0xb7, 0xb7, 0xb5, 0xb4, 0xb3, 0xb2, 0xae, 0xa9, 0xa2, 0x99,
0x90, 0x87, 0x80, 0x76, 0x6e, 0x68, 0x63, 0x61, 0x5f, 0x5e, 0x60, 0x60, 0x62, 0x64, 0x65, 0x64,
0x63, 0x61, 0x5c, 0x57, 0x53, 0x52, 0x50, 0x4f, 0x4e, 0x4f, 0x4e, 0x4f, 0x52, 0x56, 0x5a, 0x5f,
0x66, 0x6b, 0x6d, 0x71, 0x78, 0x7e, 0x83, 0x8d, 0x98, 0xa1, 0xa9, 0xb2, 0xb6, 0xb8, 0xb8, 0xb7,
0xb3, 0xad, 0xaa, 0xa8, 0xa6, 0xa3, 0xa0, 0x9d, 0x99, 0x96, 0x92, 0x8c, 0x86, 0x80, 0x7b, 0x74,
0x6f, 0x6f, 0x72, 0x75, 0x7b, 0x81, 0x87, 0x8a, 0x8c, 0x8b, 0x86, 0x81, 0x80, 0x7e, 0x78, 0x75,
0x74, 0x73, 0x71, 0x70, 0x6f, 0x6e, 0x6f, 0x70, 0x71, 0x71, 0x71, 0x71, 0x72, 0x72, 0x74, 0x78,
0x7d, 0x83, 0x88, 0x8b, 0x8d, 0x8e, 0x8e, 0x8c, 0x8a, 0x8a, 0x8a, 0x88, 0x83, 0x80, 0x7d, 0x79,
0x75, 0x72, 0x70, 0x6e, 0x6e, 0x6d, 0x6b, 0x6a, 0x6b, 0x6c, 0x6c, 0x6d, 0x6f, 0x72, 0x77, 0x79,
0x7b, 0x7d, 0x7d, 0x7d, 0x7c, 0x7c, 0x7b, 0x7a, 0x78, 0x75, 0x71, 0x6f, 0x6f, 0x72, 0x77, 0x7e,
0x84, 0x8c, 0x93, 0x99, 0x9e, 0xa3, 0xa7, 0xac, 0xaf, 0xb2, 0xb6, 0xba, 0xbc, 0xbc, 0xba, 0xb8,
0xb4, 0xaf, 0xaa, 0xa5, 0x9e, 0x98, 0x91, 0x88, 0x80, 0x78, 0x73, 0x6e, 0x6a, 0x66, 0x60, 0x5a,
0x59, 0x56, 0x53, 0x53, 0x54, 0x55, 0x55, 0x53, 0x52, 0x52, 0x50, 0x50, 0x50, 0x50, 0x53, 0x57,
0x5a, 0x5e, 0x60, 0x63, 0x65, 0x65, 0x67, 0x6c, 0x73, 0x79, 0x80, 0x86, 0x8c, 0x94, 0x9c, 0xa2,
0xa8, 0xad, 0xb1, 0xb3, 0xb4, 0xb1, 0xae, 0xad, 0xab, 0xac, 0xac, 0xab, 0xac, 0xac, 0xa9, 0xa6,
0xa1, 0x9a, 0x93, 0x8d, 0x86, 0x82, 0x7f, 0x7b, 0x77, 0x75, 0x73, 0x71, 0x70, 0x70, 0x70, 0x6d,
0x6a, 0x67, 0x62, 0x5e, 0x5b, 0x5a, 0x5c, 0x5e, 0x60, 0x62, 0x64, 0x65, 0x66, 0x65, 0x66, 0x69,
0x6d, 0x70, 0x74, 0x79, 0x7d, 0x80, 0x86, 0x8c, 0x93, 0x9b, 0xa1, 0xa4, 0xa7, 0xa8, 0xa6, 0xa1,
0x9c, 0x99, 0x97, 0x97, 0x97, 0x96, 0x94, 0x93, 0x90, 0x8d, 0x89, 0x86, 0x83, 0x80, 0x7a, 0x74,
0x6d, 0x69, 0x68, 0x68, 0x69, 0x6c, 0x6e, 0x71, 0x72, 0x70, 0x6e, 0x6c, 0x68, 0x67, 0x68, 0x6b,
0x6d, 0x70, 0x73, 0x75, 0x76, 0x77, 0x78, 0x7a, 0x7d, 0x80, 0x80, 0x80, 0x81, 0x81, 0x82, 0x87,
0x8d, 0x93, 0x98, 0x9d, 0xa0, 0xa2, 0xa3, 0xa1, 0x9e, 0x9c, 0x9b, 0x9a, 0x98, 0x97, 0x96, 0x93,
0x90, 0x8d, 0x8a, 0x87, 0x86, 0x85, 0x82, 0x7e, 0x77, 0x72, 0x6e, 0x6a, 0x69, 0x69, 0x69, 0x6b,
0x6b, 0x6a, 0x69, 0x69, 0x68, 0x65, 0x65, 0x67, 0x68, 0x68, 0x69, 0x6b, 0x6b, 0x6d, 0x70, 0x74,
0x79, 0x7f, 0x84, 0x8b, 0x8e, 0x91, 0x94, 0x97, 0x9b, 0xa1, 0xa4, 0xa7, 0xa9, 0xab, 0xab, 0xa8,
0xa4, 0xa0, 0x9b, 0x98, 0x95, 0x91, 0x8d, 0x87, 0x81, 0x7d, 0x78, 0x74, 0x71, 0x6f, 0x6d, 0x6a,
0x67, 0x63, 0x61, 0x5d, 0x59, 0x58, 0x57, 0x56, 0x57, 0x57, 0x57, 0x56, 0x58, 0x5a, 0x5e, 0x66,
0x70, 0x76, 0x7a, 0x7e, 0x80, 0x80, 0x82, 0x85, 0x88, 0x8e, 0x95, 0x9c, 0x9f, 0xa2, 0xa7, 0xac,
0xac, 0xad, 0xaf, 0xaf, 0xae, 0xae, 0xae, 0xaa, 0xa8, 0xa8, 0xa7, 0xa5, 0xa6, 0xa8, 0xa6, 0xa1,
0x9b, 0x94, 0x8b, 0x84, 0x80, 0x7f, 0x7c, 0x77, 0x73, 0x6e, 0x67, 0x60, 0x5d, 0x5a, 0x56, 0x51,
0x4d, 0x49, 0x46, 0x44, 0x41, 0x40, 0x41, 0x46, 0x4d, 0x52, 0x58, 0x5e, 0x61, 0x63, 0x64, 0x65,
0x68, 0x6d, 0x73, 0x7a, 0x7f, 0x82, 0x88, 0x8d, 0x93, 0x9a, 0xa2, 0xa8, 0xac, 0xae, 0xae, 0xac,
0xa9, 0xa8, 0xa8, 0xa8, 0xaa, 0xae, 0xb0, 0xaf, 0xad, 0xac, 0xa9, 0xa2, 0x9e, 0x9a, 0x95, 0x8f,
0x8b, 0x87, 0x80, 0x7b, 0x79, 0x76, 0x72, 0x71, 0x70, 0x6c, 0x66, 0x60, 0x5d, 0x59, 0x59, 0x5d,
0x64, 0x6a, 0x70, 0x76, 0x78, 0x78, 0x77, 0x78, 0x76, 0x74, 0x76, 0x77, 0x77, 0x78, 0x7b, 0x7d,
0x7f, 0x81, 0x85, 0x87, 0x8a, 0x8f, 0x91, 0x8f, 0x8e, 0x8d, 0x8a, 0x89, 0x8b, 0x8e, 0x8e, 0x8e,
0x8d, 0x89, 0x85, 0x81, 0x80, 0x80, 0x7f, 0x7c, 0x79, 0x74, 0x6d, 0x67, 0x63, 0x60, 0x5e, 0x5f,
0x60, 0x61, 0x61, 0x62, 0x64, 0x64, 0x63, 0x66, 0x6a, 0x71, 0x79, 0x80, 0x85, 0x8a, 0x8e, 0x92,
0x95, 0x98, 0x9c, 0xa2, 0xa6, 0xa6, 0xa7, 0xa7, 0xa7, 0xa9, 0xad, 0xb0, 0xb2, 0xb5, 0xb5, 0xb1,
0xad, 0xa8, 0xa2, 0x9c, 0x97, 0x94, 0x91, 0x8d, 0x8a, 0x86, 0x80, 0x7a, 0x75, 0x70, 0x6c, 0x69,
0x67, 0x63, 0x5c, 0x55, 0x50, 0x4a, 0x46, 0x45, 0x44, 0x44, 0x44, 0x44, 0x43, 0x42, 0x45, 0x4c,
0x56, 0x60, 0x6a, 0x72, 0x78, 0x7c, 0x7e, 0x80, 0x80, 0x83, 0x88, 0x8d, 0x91, 0x96, 0x9a, 0x9d,
0x9e, 0x9f, 0xa0, 0xa0, 0xa1, 0xa3, 0xa6, 0xa8, 0xa9, 0xa9, 0xa8, 0xaa, 0xac, 0xae, 0xae, 0xad,
0xab, 0xa5, 0x9e, 0x98, 0x95, 0x93, 0x92, 0x91, 0x8f, 0x8b, 0x84, 0x7d, 0x74, 0x6b, 0x62, 0x5b,
0x55, 0x50, 0x4c, 0x4a, 0x4a, 0x49, 0x48, 0x4a, 0x4e, 0x53, 0x59, 0x61, 0x68, 0x6c, 0x6e, 0x6f,
0x72, 0x75, 0x79, 0x7d, 0x81, 0x86, 0x8a, 0x8c, 0x8c, 0x8e, 0x93, 0x97, 0x9a, 0x9e, 0xa0, 0xa0,
0xa1, 0xa1, 0xa0, 0xa0, 0xa1, 0xa3, 0xa5, 0xa6, 0xa8, 0xa7, 0xa4, 0x9f, 0x98, 0x91, 0x8a, 0x86,
0x82, 0x7e, 0x79, 0x74, 0x6d, 0x64, 0x5e, 0x5a, 0x55, 0x51, 0x4f, 0x4c, 0x49, 0x46, 0x47, 0x4b,
0x53, 0x5d, 0x68, 0x72, 0x7a, 0x80, 0x84, 0x84, 0x83, 0x83, 0x82, 0x82, 0x85, 0x89, 0x8d, 0x91,
0x94, 0x96, 0x96, 0x96, 0x96, 0x98, 0x9a, 0x9d, 0x9e, 0x9d, 0x9c, 0x9d, 0x9e, 0xa1, 0xa4, 0xa5,
0xa5, 0xa2, 0x9c, 0x94, 0x8e, 0x89, 0x85, 0x81, 0x7f, 0x7d, 0x79, 0x73, 0x6c, 0x65, 0x5e, 0x58,
0x54, 0x51, 0x51, 0x52, 0x52, 0x53, 0x56, 0x58, 0x5c, 0x61, 0x68, 0x71, 0x7b, 0x82, 0x86, 0x89,
0x8b, 0x8e, 0x91, 0x92, 0x93, 0x96, 0x97, 0x96, 0x94, 0x93, 0x93, 0x95, 0x97, 0x99, 0x9b, 0x9c,
0x9c, 0x9a, 0x98, 0x94, 0x91, 0x8f, 0x8e, 0x8d, 0x8d, 0x8d, 0x8b, 0x87, 0x81, 0x7c, 0x78, 0x74,
0x6f, 0x6c, 0x69, 0x63, 0x5e, 0x59, 0x55, 0x51, 0x4e, 0x4e, 0x50, 0x50, 0x50, 0x52, 0x54, 0x58,
0x60, 0x69, 0x72, 0x7b, 0x84, 0x8c, 0x92, 0x95, 0x99, 0x9a, 0x9a, 0x9b, 0x9c, 0x9f, 0xa1, 0xa2,
0xa2, 0xa3, 0xa2, 0xa1, 0xa0, 0xa0, 0xa0, 0xa2, 0xa5, 0xa7, 0xa5, 0xa6, 0xa8, 0xa9, 0xa9, 0xa9,
0xa7, 0xa2, 0x9c, 0x97, 0x91, 0x8b, 0x87, 0x84, 0x81, 0x7d, 0x77, 0x71, 0x69, 0x5f, 0x55, 0x4b,
0x40, 0x38, 0x34, 0x33, 0x32, 0x34, 0x37, 0x3b, 0x3e, 0x43, 0x4a, 0x50, 0x56, 0x5d, 0x62, 0x66,
0x6c, 0x73, 0x7a, 0x80, 0x85, 0x89, 0x8d, 0x8f, 0x90, 0x93, 0x95, 0x96, 0x99, 0x9d, 0xa1, 0xa6,
0xad, 0xb3, 0xb8, 0xbb, 0xbf, 0xc1, 0xc0, 0xc0, 0xc1, 0xc0, 0xbc, 0xb7, 0xb2, 0xab, 0xa4, 0x9d,
0x98, 0x90, 0x87, 0x80, 0x79, 0x6f, 0x66, 0x5f, 0x56, 0x4d, 0x48, 0x46, 0x42, 0x41, 0x45, 0x4a,
0x4e, 0x55, 0x5f, 0x67, 0x6c, 0x73, 0x7b, 0x7e, 0x7f, 0x80, 0x82, 0x80, 0x81, 0x85, 0x88, 0x89,
0x8a, 0x8c, 0x8c, 0x8a, 0x89, 0x88, 0x87, 0x87, 0x88, 0x89, 0x8a, 0x8d, 0x92, 0x95, 0x97, 0x99,
0x99, 0x95, 0x8f, 0x8a, 0x85, 0x80, 0x7a, 0x77, 0x73, 0x70, 0x70, 0x70, 0x6e, 0x69, 0x64, 0x60,
0x5b, 0x54, 0x51, 0x53, 0x54, 0x57, 0x5d, 0x63, 0x69, 0x72, 0x7d, 0x85, 0x8c, 0x92, 0x98, 0x9b,
0x9d, 0xa0, 0xa3, 0xa5, 0xa8, 0xa9, 0xa8, 0xa5, 0xa2, 0xa1, 0xa0, 0x9d, 0x9b, 0x9b, 0x99, 0x97,
0x98, 0x99, 0x98, 0x98, 0x99, 0x98, 0x96, 0x96, 0x98, 0x96, 0x91, 0x8d, 0x89, 0x82, 0x7c, 0x77,
0x71, 0x6a, 0x63, 0x5d, 0x56, 0x4e, 0x49, 0x47, 0x44, 0x41, 0x40, 0x40, 0x40, 0x3f, 0x41, 0x46,
0x4b, 0x52, 0x5c, 0x64, 0x6c, 0x75, 0x7e, 0x83, 0x88, 0x8e, 0x93, 0x93, 0x94, 0x96, 0x97, 0x96,
0x95, 0x96, 0x96, 0x97, 0x9b, 0x9e, 0xa0, 0xa1, 0xa4, 0xa5, 0xa6, 0xa8, 0xab, 0xaf, 0xb4, 0xb6,
0xb7, 0xb4, 0xaf, 0xab, 0xa8, 0xa3, 0x9f, 0x9b, 0x95, 0x8d, 0x85, 0x7e, 0x76, 0x6d, 0x65, 0x5c,
0x52, 0x4a, 0x45, 0x41, 0x3e, 0x3e, 0x3f, 0x42, 0x46, 0x4a, 0x51, 0x59, 0x5c, 0x5f, 0x63, 0x66,
0x6a, 0x72, 0x7c, 0x81, 0x87, 0x8c, 0x8f, 0x8e, 0x8b, 0x8b, 0x8a, 0x8a, 0x8c, 0x8f, 0x91, 0x95,
0x9b, 0xa3, 0xaa, 0xb1, 0xb8, 0xbb, 0xbd, 0xbd, 0xbc, 0xb8, 0xb2, 0xac, 0xa4, 0x9d, 0x98, 0x94,
0x8f, 0x87, 0x7f, 0x77, 0x6b, 0x5e, 0x53, 0x4c, 0x45, 0x3f, 0x3b, 0x39, 0x37, 0x39, 0x3d, 0x43,
0x4c, 0x55, 0x5d, 0x65, 0x6c, 0x72, 0x77, 0x7c, 0x81, 0x87, 0x8d, 0x92, 0x98, 0x9d, 0xa0, 0xa2,
0xa1, 0x9f, 0x9c, 0x9a, 0x99, 0x98, 0x96, 0x96, 0x95, 0x94, 0x95, 0x98, 0x9b, 0x9f, 0xa3, 0xa6,
0xa6, 0xa4, 0x9f, 0x97, 0x8f, 0x88, 0x82, 0x7d, 0x78, 0x75, 0x72, 0x70, 0x6d, 0x68, 0x63, 0x5f,
0x5a, 0x55, 0x51, 0x50, 0x4f, 0x4f, 0x51, 0x57, 0x5d, 0x67, 0x74, 0x7f, 0x86, 0x8e, 0x93, 0x94,
0x93, 0x93, 0x93, 0x95, 0x97, 0x98, 0x98, 0x97, 0x95, 0x91, 0x8c, 0x88, 0x85, 0x83, 0x81, 0x80,
0x80, 0x80, 0x81, 0x84, 0x89, 0x8c, 0x91, 0x98, 0x9d, 0x9d, 0x9b, 0x98, 0x91, 0x8a, 0x84, 0x80,
0x7b, 0x76, 0x72, 0x6d, 0x66, 0x5f, 0x5a, 0x57, 0x54, 0x52, 0x51, 0x52, 0x53, 0x52, 0x52, 0x52,
0x55, 0x5c, 0x63, 0x6b, 0x74, 0x7f, 0x86, 0x8c, 0x91, 0x96, 0x9b, 0x9e, 0xa2, 0xa5, 0xa5, 0xa4,
0xa1, 0x9d, 0x9b, 0x99, 0x9b, 0x9e, 0xa1, 0xa3, 0xa5, 0xa6, 0xa5, 0xa3, 0xa4, 0xa5, 0xa6, 0xa8,
0xab, 0xab, 0xaa, 0xa7, 0xa2, 0x9c, 0x95, 0x8f, 0x89, 0x80, 0x77, 0x6e, 0x62, 0x56, 0x4c, 0x44,
0x3d, 0x3a, 0x38, 0x38, 0x38, 0x36, 0x35, 0x34, 0x36, 0x3b, 0x42, 0x49, 0x51, 0x58, 0x5f, 0x67,
0x6d, 0x73, 0x7a, 0x80, 0x86, 0x8e, 0x93, 0x94, 0x94, 0x92, 0x8f, 0x8f, 0x90, 0x92, 0x96, 0x9d,
0xa4, 0xad, 0xb4, 0xba, 0xc0, 0xc5, 0xc9, 0xcd, 0xce, 0xcb, 0xc7, 0xc0, 0xb8, 0xaf, 0xa8, 0xa3,
0x9d, 0x96, 0x8e, 0x85, 0x7b, 0x6e, 0x62, 0x56, 0x4b, 0x44, 0x3f, 0x3d, 0x3e, 0x40, 0x43, 0x46,
0x49, 0x4e, 0x55, 0x5b, 0x60, 0x67, 0x6d, 0x72, 0x76, 0x7c, 0x80, 0x85, 0x8d, 0x96, 0x9c, 0x9e,
0x9f, 0x9d, 0x99, 0x93, 0x8f, 0x8a, 0x87, 0x85, 0x86, 0x87, 0x88, 0x8b, 0x8e, 0x90, 0x90, 0x92,
0x94, 0x92, 0x90, 0x8d, 0x87, 0x7f, 0x78, 0x72, 0x6c, 0x69, 0x69, 0x69, 0x67, 0x65, 0x62, 0x5e,
0x5a, 0x59, 0x5a, 0x5a, 0x5d, 0x61, 0x65, 0x68, 0x6c, 0x73, 0x7e, 0x87, 0x91, 0x9a, 0xa2, 0xa7,
0xa9, 0xa9, 0xa5, 0xa0, 0x9f, 0xa0, 0xa0, 0xa0, 0xa1, 0xa0, 0x9c, 0x98, 0x93, 0x8e, 0x89, 0x87,
0x88, 0x88, 0x86, 0x87, 0x88, 0x89, 0x8e, 0x95, 0x9b, 0x9e, 0x9f, 0x9e, 0x98, 0x8e, 0x83, 0x7c,
0x74, 0x6d, 0x69, 0x65, 0x60, 0x5c, 0x59, 0x54, 0x4e, 0x4d, 0x4e, 0x4c, 0x49, 0x4a, 0x4a, 0x47,
0x45, 0x47, 0x4b, 0x50, 0x5a, 0x66, 0x72, 0x7b, 0x82, 0x88, 0x8b, 0x8e, 0x92, 0x97, 0x9a, 0x9d,
0xa1, 0xa1, 0x9e, 0x9e, 0x9f, 0xa1, 0xa3, 0xa6, 0xa9, 0xaa, 0xac, 0xac, 0xaa, 0xa8, 0xa9, 0xab,
0xac, 0xad, 0xaf, 0xb1, 0xaf, 0xaa, 0xa2, 0x9a, 0x90, 0x86, 0x7e, 0x75, 0x6a, 0x60, 0x57, 0x4e,
0x47, 0x44, 0x45, 0x46, 0x47, 0x4a, 0x4b, 0x48, 0x47, 0x48, 0x49, 0x4d, 0x53, 0x5b, 0x64, 0x6e,
0x79, 0x80, 0x84, 0x87, 0x8a, 0x8d, 0x8d, 0x8e, 0x8f, 0x8f, 0x8e, 0x8c, 0x8c, 0x8d, 0x8f, 0x94,
0x9a, 0xa1, 0xa9, 0xaf, 0xb2, 0xb4, 0xb5, 0xb7, 0xb8, 0xb6, 0xb5, 0xb4, 0xb0, 0xa8, 0xa0, 0x97,
0x8c, 0x82, 0x7a, 0x73, 0x6d, 0x65, 0x5c, 0x52, 0x48, 0x41, 0x3e, 0x3b, 0x3a, 0x3f, 0x44, 0x47,
0x4a, 0x4f, 0x53, 0x56, 0x5b, 0x63, 0x6c, 0x75, 0x7d, 0x84, 0x88, 0x8d, 0x93, 0x9a, 0xa0, 0xa5,
0xaa, 0xae, 0xad, 0xa9, 0xa3, 0x9c, 0x96, 0x93, 0x91, 0x92, 0x94, 0x97, 0x98, 0x9a, 0x9c, 0x9c,
0x99, 0x96, 0x96, 0x95, 0x90, 0x89, 0x82, 0x7a, 0x71, 0x6b, 0x67, 0x65, 0x66, 0x66, 0x65, 0x61,
0x5d, 0x5a, 0x59, 0x5a, 0x5d, 0x60, 0x65, 0x6a, 0x6f, 0x74, 0x7a, 0x7f, 0x84, 0x8b, 0x93, 0x99,
0x9d, 0xa0, 0x9e, 0x99, 0x95, 0x92, 0x8d, 0x8a, 0x8c, 0x8d, 0x8c, 0x89, 0x87, 0x85, 0x81, 0x7e,
0x7b, 0x7b, 0x7c, 0x7d, 0x7e, 0x80, 0x84, 0x89, 0x8e, 0x93, 0x98, 0x9c, 0x9d, 0x9a, 0x93, 0x8a,
0x80, 0x75, 0x6c, 0x66, 0x63, 0x63, 0x63, 0x63, 0x61, 0x5f, 0x5d, 0x5a, 0x56, 0x53, 0x51, 0x4f,
0x4f, 0x51, 0x55, 0x59, 0x5e, 0x68, 0x74, 0x80, 0x8c, 0x95, 0x9b, 0x9e, 0x9f, 0x9f, 0x9f, 0xa1,
0xa5, 0xa9, 0xad, 0xaf, 0xb0, 0xb0, 0xad, 0xa9, 0xa8, 0xa7, 0xa6, 0xa6, 0xa6, 0xa5, 0xa4, 0xa3,
0xa3, 0xa3, 0xa3, 0xa5, 0xa6, 0xa3, 0x9d, 0x95, 0x89, 0x7b, 0x6d, 0x63, 0x5a, 0x51, 0x4b, 0x47,
0x41, 0x3b, 0x3a, 0x39, 0x39, 0x3a, 0x3d, 0x42, 0x44, 0x46, 0x48, 0x4b, 0x4d, 0x52, 0x59, 0x64,
0x70, 0x7b, 0x84, 0x89, 0x8c, 0x8e, 0x8d, 0x8b, 0x8c, 0x8d, 0x8f, 0x91, 0x93, 0x94, 0x95, 0x98,
0x9d, 0xa4, 0xaa, 0xaf, 0xb4, 0xb7, 0xb9, 0xba, 0xba, 0xba, 0xba, 0xbc, 0xbc, 0xb9, 0xb6, 0xb0,
0xa5, 0x99, 0x8d, 0x82, 0x79, 0x70, 0x6a, 0x64, 0x5d, 0x57, 0x50, 0x4a, 0x47, 0x45, 0x46, 0x47,
0x49, 0x4c, 0x50, 0x54, 0x59, 0x5e, 0x65, 0x6e, 0x77, 0x80, 0x87, 0x8b, 0x90, 0x95, 0x96, 0x97,
0x99, 0x9b, 0x9c, 0x9d, 0x9d, 0x99, 0x92, 0x8b, 0x85, 0x81, 0x7f, 0x80, 0x82, 0x86, 0x89, 0x8b,
0x8c, 0x8a, 0x88, 0x87, 0x85, 0x83, 0x7f, 0x78, 0x72, 0x6c, 0x66, 0x63, 0x61, 0x60, 0x62, 0x64,
0x65, 0x67, 0x66, 0x64, 0x65, 0x68, 0x6c, 0x70, 0x76, 0x7f, 0x87, 0x8d, 0x91, 0x94, 0x97, 0x9b,
0x9f, 0xa2, 0xa4, 0xa5, 0xa3, 0x9e, 0x99, 0x94, 0x91, 0x90, 0x90, 0x91, 0x92, 0x8f, 0x8b, 0x87,
0x84, 0x82, 0x81, 0x80, 0x81, 0x83, 0x87, 0x8b, 0x90, 0x92, 0x93, 0x95, 0x97, 0x98, 0x97, 0x92,
0x8b, 0x81, 0x76, 0x6c, 0x64, 0x5f, 0x5e, 0x5f, 0x5f, 0x5f, 0x5c, 0x56, 0x51, 0x4d, 0x49, 0x45,
0x43, 0x44, 0x46, 0x49, 0x4e, 0x56, 0x60, 0x6b, 0x77, 0x80, 0x89, 0x92, 0x98, 0x9a, 0x9b, 0x9b,
0x9b, 0x9e, 0xa3, 0xa9, 0xb0, 0xb4, 0xb5, 0xb2, 0xad, 0xa8, 0xa4, 0xa3, 0xa2, 0xa0, 0xa1, 0xa2,
0xa2, 0xa1, 0xa2, 0xa3, 0xa1, 0xa0, 0x9d, 0x96, 0x8d, 0x84, 0x7a, 0x6f, 0x66, 0x5e, 0x59, 0x55,
0x53, 0x53, 0x52, 0x4f, 0x4b, 0x49, 0x49, 0x49, 0x4b, 0x50, 0x54, 0x58, 0x5d, 0x5f, 0x62, 0x68,
0x70, 0x78, 0x80, 0x88, 0x8e, 0x91, 0x93, 0x93, 0x92, 0x91, 0x93, 0x94, 0x96, 0x97, 0x98, 0x99,
0x99, 0x9b, 0x9d, 0x9f, 0xa1, 0xa3, 0xa6, 0xaa, 0xac, 0xaa, 0xa8, 0xa7, 0xa5, 0xa4, 0xa6, 0xa4,
0x9f, 0x97, 0x8d, 0x81, 0x76, 0x6c, 0x63, 0x5c, 0x58, 0x53, 0x4f, 0x49, 0x45, 0x42, 0x40, 0x40,
0x41, 0x44, 0x47, 0x4d, 0x54, 0x5c, 0x63, 0x6b, 0x74, 0x7d, 0x86, 0x8f, 0x98, 0x9e, 0xa1, 0xa2,
0xa1, 0xa0, 0xa2, 0xa5, 0xa8, 0xa8, 0xa7, 0xa2, 0x9a, 0x92, 0x8d, 0x8b, 0x89, 0x88, 0x89, 0x8c,
0x8d, 0x8d, 0x8e, 0x8d, 0x8a, 0x87, 0x84, 0x7f, 0x78, 0x73, 0x6f, 0x6a, 0x66, 0x64, 0x64, 0x65,
0x69, 0x6d, 0x6f, 0x70, 0x6e, 0x6c, 0x6a, 0x6b, 0x70, 0x77, 0x7e, 0x84, 0x8b, 0x8f, 0x92, 0x93,
0x94, 0x95, 0x96, 0x95, 0x94, 0x93, 0x91, 0x8f, 0x8b, 0x89, 0x88, 0x86, 0x86, 0x86, 0x85, 0x84,
0x80, 0x7c, 0x79, 0x76, 0x73, 0x75, 0x79, 0x7e, 0x82, 0x85, 0x88, 0x8a, 0x89, 0x8a, 0x8d, 0x8e,
0x8c, 0x89, 0x83, 0x7d, 0x74, 0x6d, 0x67, 0x65, 0x64, 0x65, 0x65, 0x62, 0x60, 0x5c, 0x57, 0x52,
0x50, 0x4d, 0x4c, 0x4f, 0x57, 0x5f, 0x66, 0x6e, 0x79, 0x82, 0x8b, 0x95, 0x9f, 0xa7, 0xab, 0xac,
0xac, 0xab, 0xab, 0xaf, 0xb6, 0xbc, 0xc0, 0xc0, 0xbd, 0xb6, 0xaf, 0xa9, 0xa1, 0x9b, 0x98, 0x96,
0x96, 0x95, 0x95, 0x94, 0x92, 0x90, 0x8e, 0x8a, 0x85, 0x80, 0x79, 0x71, 0x67, 0x5d, 0x55, 0x4f,
0x4d, 0x4d, 0x4e, 0x4c, 0x49, 0x44, 0x41, 0x3f, 0x3e, 0x41, 0x45, 0x49, 0x4f, 0x56, 0x5b, 0x60,
0x65, 0x6c, 0x73, 0x7b, 0x83, 0x8b, 0x91, 0x94, 0x96, 0x98, 0x97, 0x97, 0x9a, 0x9b, 0x9c, 0x9f,
0x9f, 0x9e, 0x9e, 0x9f, 0x9e, 0xa0, 0xa4, 0xa7, 0xa9, 0xab, 0xac, 0xaa, 0xa9, 0xa8, 0xa8, 0xaa,
0xa9, 0xa6, 0xa2, 0x9c, 0x91, 0x85, 0x7a, 0x71, 0x68, 0x62, 0x5e, 0x59, 0x53, 0x4f, 0x4d, 0x4a,
0x48, 0x48, 0x48, 0x4d, 0x53, 0x59, 0x61, 0x67, 0x6e, 0x76, 0x7e, 0x85, 0x90, 0x98, 0x9c, 0xa0,
0xa3, 0xa4, 0xa2, 0xa2, 0xa3, 0xa4, 0xa3, 0xa2, 0x9d, 0x96, 0x8e, 0x86, 0x7f, 0x78, 0x74, 0x73,
0x74, 0x75, 0x76, 0x76, 0x74, 0x72, 0x71, 0x6e, 0x6b, 0x6a, 0x68, 0x67, 0x66, 0x65, 0x64, 0x65,
0x68, 0x6b, 0x70, 0x73, 0x73, 0x73, 0x73, 0x73, 0x73, 0x76, 0x7b, 0x80, 0x88, 0x90, 0x96, 0x9a,
0x9b, 0x9b, 0x9b, 0x9d, 0x9d, 0x9d, 0x9e, 0x9f, 0x9e, 0x9d, 0x9b, 0x98, 0x96, 0x94, 0x93, 0x92,
0x90, 0x8b, 0x86, 0x82, 0x7d, 0x7a, 0x79, 0x7a, 0x7d, 0x80, 0x82, 0x84, 0x85, 0x85, 0x87, 0x89,
0x89, 0x89, 0x89, 0x85, 0x81, 0x7c, 0x73, 0x6c, 0x68, 0x65, 0x64, 0x63, 0x60, 0x5e, 0x5a, 0x54,
0x4e, 0x49, 0x46, 0x45, 0x48, 0x4d, 0x53, 0x5a, 0x62, 0x69, 0x72, 0x7f, 0x88, 0x8f, 0x97, 0x9f,
0xa3, 0xa5, 0xa8, 0xaa, 0xac, 0xb1, 0xb7, 0xbb, 0xbb, 0xb9, 0xb4, 0xac, 0xa2, 0x99, 0x91, 0x8d,
0x8b, 0x8a, 0x89, 0x89, 0x89, 0x89, 0x88, 0x87, 0x86, 0x85, 0x82, 0x7f, 0x7b, 0x76, 0x70, 0x6b,
0x68, 0x65, 0x63, 0x62, 0x60, 0x5d, 0x57, 0x53, 0x50, 0x4e, 0x4e, 0x50, 0x55, 0x5b, 0x60, 0x65,
0x6a, 0x6d, 0x71, 0x78, 0x80, 0x86, 0x8d, 0x95, 0x9c, 0x9f, 0xa2, 0xa4, 0xa4, 0xa3, 0xa2, 0xa3,
0xa2, 0xa0, 0x9e, 0x9c, 0x9b, 0x99, 0x99, 0x99, 0x98, 0x98, 0x98, 0x97, 0x94, 0x93, 0x91, 0x90,
0x92, 0x93, 0x91, 0x8e, 0x8b, 0x85, 0x7c, 0x72, 0x69, 0x61, 0x58, 0x52, 0x4d, 0x49, 0x46, 0x44,
0x43, 0x42, 0x41, 0x44, 0x47, 0x4c, 0x53, 0x5b, 0x64, 0x6d, 0x77, 0x82, 0x8c, 0x95, 0x9d, 0xa5,
0xac, 0xb1, 0xb5, 0xb8, 0xb8, 0xb7, 0xb6, 0xb5, 0xb0, 0xaa, 0xa3, 0x99, 0x8f, 0x86, 0x7e, 0x77,
0x73, 0x72, 0x72, 0x72, 0x73, 0x73, 0x71, 0x72, 0x72, 0x71, 0x6f, 0x6f, 0x70, 0x73, 0x74, 0x76,
0x7a, 0x7a, 0x79, 0x7a, 0x7b, 0x79, 0x77, 0x76, 0x76, 0x76, 0x77, 0x7c, 0x82, 0x88, 0x8e, 0x93,
0x97, 0x97, 0x95, 0x95, 0x93, 0x91, 0x90, 0x91, 0x90, 0x91, 0x92, 0x92, 0x90, 0x8d, 0x89, 0x85,
0x80, 0x7d, 0x79, 0x75, 0x72, 0x6f, 0x6d, 0x6d, 0x6d, 0x6f, 0x71, 0x72, 0x75, 0x76, 0x78, 0x7b,
0x7d, 0x80, 0x82, 0x83, 0x83, 0x82, 0x80, 0x7c, 0x78, 0x74, 0x6f, 0x6c, 0x6a, 0x68, 0x64, 0x61,
0x60, 0x5d, 0x5a, 0x58, 0x58, 0x5a, 0x5d, 0x62, 0x69, 0x71, 0x79, 0x83, 0x8e, 0x96, 0x9f, 0xa7,
0xad, 0xb2, 0xb5, 0xb8, 0xbd, 0xc0, 0xc2, 0xc4, 0xc3, 0xc1, 0xbd, 0xb6, 0xac, 0xa1, 0x98, 0x90,
0x88, 0x84, 0x81, 0x7f, 0x7e, 0x7e, 0x7f, 0x7f, 0x7f, 0x7e, 0x7c, 0x78, 0x74, 0x71, 0x6e, 0x6b,
0x68, 0x65, 0x61, 0x5d, 0x59, 0x53, 0x4c, 0x46, 0x41, 0x40, 0x40, 0x42, 0x46, 0x4b, 0x50, 0x55,
0x5b, 0x5f, 0x63, 0x68, 0x6f, 0x76, 0x7e, 0x86, 0x8d, 0x95, 0x9d, 0xa3, 0xa9, 0xac, 0xac, 0xac,
0xaa, 0xa6, 0xa4, 0xa1, 0x9f, 0x9f, 0xa1, 0xa1, 0xa2, 0xa1, 0xa0, 0x9e, 0x9a, 0x96, 0x95, 0x95,
0x95, 0x96, 0x98, 0x98, 0x95, 0x92, 0x8e, 0x86, 0x7e, 0x77, 0x6e, 0x65, 0x5d, 0x55, 0x4f, 0x4b,
0x48, 0x48, 0x4b, 0x4d, 0x52, 0x56, 0x5a, 0x61, 0x68, 0x6f, 0x79, 0x83, 0x8f, 0x9b, 0xa4, 0xac,
0xb1, 0xb3, 0xb5, 0xb7, 0xb8, 0xb7, 0xb5, 0xb1, 0xaa, 0xa2, 0x98, 0x8d, 0x81, 0x76, 0x6b, 0x63,
0x5c, 0x57, 0x56, 0x56, 0x55, 0x56, 0x59, 0x5b, 0x5e, 0x61, 0x63, 0x64, 0x64, 0x65, 0x67, 0x6b,
0x6f, 0x75, 0x79, 0x7c, 0x7c, 0x7a, 0x79, 0x77, 0x75, 0x76, 0x79, 0x7e, 0x85, 0x8d, 0x94, 0x9b,
0xa1, 0xa3, 0xa2, 0xa1, 0xa0, 0xa0, 0x9f, 0x9f, 0x9e, 0x9c, 0x9a, 0x9b, 0x9c, 0x9c, 0x9a, 0x97,
0x93, 0x8d, 0x86, 0x80, 0x7c, 0x79, 0x79, 0x7a, 0x7c, 0x7d, 0x7d, 0x7e, 0x7d, 0x7d, 0x7d, 0x7d,
0x80, 0x82, 0x86, 0x88, 0x88, 0x88, 0x87, 0x82, 0x7f, 0x7b, 0x75, 0x6f, 0x6c, 0x65, 0x5e, 0x58,
0x54, 0x50, 0x4e, 0x4e, 0x51, 0x53, 0x54, 0x56, 0x58, 0x5c, 0x62, 0x6a, 0x75, 0x80, 0x88, 0x93,
0x9a, 0x9e, 0xa2, 0xa7, 0xa9, 0xab, 0xae, 0xb1, 0xb1, 0xb1, 0xb0, 0xab, 0xa3, 0x9b, 0x93, 0x8b,
0x86, 0x84, 0x81, 0x80, 0x7f, 0x7e, 0x7e, 0x7e, 0x80, 0x84, 0x87, 0x87, 0x85, 0x82, 0x7f, 0x7b,
0x7a, 0x79, 0x76, 0x74, 0x70, 0x6a, 0x63, 0x5c, 0x55, 0x4f, 0x4d, 0x4e, 0x52, 0x58, 0x60, 0x67,
0x6c, 0x71, 0x73, 0x75, 0x79, 0x7d, 0x84, 0x8d, 0x94, 0x9a, 0x9f, 0xa3, 0xa8, 0xad, 0xb0, 0xb3,
0xb3, 0xb0, 0xaa, 0xa4, 0x9d, 0x98, 0x96, 0x94, 0x94, 0x95, 0x96, 0x93, 0x90, 0x8b, 0x85, 0x7f,
0x7b, 0x79, 0x78, 0x78, 0x79, 0x78, 0x76, 0x70, 0x69, 0x63, 0x5c, 0x56, 0x52, 0x4d, 0x46, 0x41,
0x3c, 0x3a, 0x3b, 0x41, 0x49, 0x54, 0x5e, 0x67, 0x6e, 0x75, 0x7e, 0x86, 0x8f, 0x9a, 0xa6, 0xb1,
0xb9, 0xbe, 0xc1, 0xc2, 0xbf, 0xbc, 0xba, 0xb7, 0xb3, 0xaf, 0xa8, 0x9d, 0x90, 0x84, 0x79, 0x6e,
0x67, 0x65, 0x64, 0x64, 0x64, 0x65, 0x64, 0x64, 0x68, 0x6c, 0x6f, 0x73, 0x75, 0x75, 0x76, 0x77,
0x79, 0x7c, 0x7f, 0x82, 0x86, 0x88, 0x88, 0x86, 0x83, 0x80, 0x7e, 0x7e, 0x80, 0x86, 0x8e, 0x96,
0x9b, 0x9e, 0x9d, 0x9a, 0x96, 0x92, 0x8f, 0x8d, 0x89, 0x85, 0x83, 0x80, 0x7c, 0x79, 0x78, 0x79,
0x79, 0x77, 0x74, 0x70, 0x6c, 0x69, 0x65, 0x65, 0x67, 0x6c, 0x72, 0x78, 0x7c, 0x7f, 0x7e, 0x7d,
0x7e, 0x7e, 0x7f, 0x82, 0x85, 0x87, 0x87, 0x85, 0x82, 0x7f, 0x79, 0x75, 0x73, 0x70, 0x6d, 0x6a,
0x67, 0x62, 0x5e, 0x5f, 0x62, 0x64, 0x69, 0x6f, 0x74, 0x78, 0x7c, 0x80, 0x86, 0x8c, 0x95, 0x9e,
0xa5, 0xab, 0xaf, 0xb1, 0xb0, 0xae, 0xac, 0xac, 0xad, 0xaf, 0xb0, 0xaf, 0xab, 0xa5, 0x9e, 0x97,
0x90, 0x8d, 0x8a, 0x89, 0x89, 0x88, 0x85, 0x82, 0x80, 0x7f, 0x7e, 0x7c, 0x7a, 0x76, 0x72, 0x6d,
0x67, 0x60, 0x5a, 0x56, 0x54, 0x52, 0x50, 0x4d, 0x4a, 0x45, 0x41, 0x3f, 0x41, 0x45, 0x4c, 0x56,
0x5f, 0x67, 0x6d, 0x70, 0x73, 0x77, 0x7b, 0x80, 0x87, 0x8d, 0x93, 0x99, 0x9e, 0xa3, 0xa6, 0xa9,
0xab, 0xac, 0xae, 0xad, 0xaa, 0xa7, 0xa5, 0xa2, 0x9f, 0x9e, 0xa0, 0xa2, 0xa2, 0xa0, 0x9d, 0x95,
0x8d, 0x87, 0x83, 0x80, 0x7e, 0x7c, 0x7b, 0x78, 0x74, 0x6f, 0x68, 0x62, 0x5d, 0x59, 0x55, 0x53,
0x53, 0x54, 0x54, 0x57, 0x5d, 0x64, 0x6c, 0x77, 0x82, 0x8d, 0x95, 0x9c, 0xa1, 0xa5, 0xaa, 0xb0,
0xb6, 0xb9, 0xba, 0xb8, 0xb5, 0xaf, 0xa7, 0xa0, 0x97, 0x8e, 0x87, 0x80, 0x76, 0x6d, 0x63, 0x59,
0x51, 0x4b, 0x49, 0x49, 0x4c, 0x50, 0x55, 0x59, 0x5b, 0x5c, 0x5f, 0x61, 0x63, 0x67, 0x69, 0x6b,
0x6f, 0x73, 0x75, 0x77, 0x7b, 0x80, 0x83, 0x86, 0x88, 0x88, 0x88, 0x88, 0x8a, 0x8c, 0x90, 0x98,
0xa0, 0xa7, 0xab, 0xac, 0xab, 0xa7, 0xa1, 0x9d, 0x98, 0x92, 0x8e, 0x8b, 0x89, 0x86, 0x83, 0x82,
0x80, 0x7f, 0x7f, 0x7e, 0x7c, 0x7c, 0x7c, 0x7b, 0x7b, 0x7d, 0x80, 0x85, 0x8c, 0x92, 0x97, 0x9a,
0x99, 0x97, 0x96, 0x94, 0x91, 0x8e, 0x8a, 0x85, 0x81, 0x7d, 0x75, 0x6d, 0x67, 0x60, 0x5b, 0x57,
0x53, 0x50, 0x4f, 0x4e, 0x4e, 0x4e, 0x4f, 0x54, 0x5b, 0x61, 0x68, 0x6e, 0x76, 0x7c, 0x80, 0x86,
0x8c, 0x90, 0x93, 0x95, 0x95, 0x96, 0x96, 0x95, 0x93, 0x92, 0x95, 0x98, 0x97, 0x98, 0x99, 0x96,
0x93, 0x90, 0x8e, 0x8b, 0x8b, 0x8d, 0x91, 0x93, 0x94, 0x95, 0x93, 0x90, 0x8d, 0x89, 0x83, 0x7d,
0x77, 0x71, 0x6b, 0x66, 0x62, 0x5f, 0x5d, 0x5c, 0x5b, 0x5c, 0x5e, 0x5e, 0x5f, 0x61, 0x65, 0x6b,
0x71, 0x77, 0x7f, 0x86, 0x8c, 0x90, 0x94, 0x97, 0x9a, 0x9d, 0xa0, 0xa2, 0xa6, 0xa8, 0xaa, 0xaa,
0xa8, 0xa5, 0xa3, 0xa0, 0x9d, 0x9b, 0x99, 0x97, 0x94, 0x90, 0x8c, 0x88, 0x84, 0x81, 0x7f, 0x7a,
0x75, 0x71, 0x6b, 0x65, 0x61, 0x5e, 0x5b, 0x57, 0x52, 0x4f, 0x4c, 0x47, 0x42, 0x40, 0x3d, 0x3d,
0x41, 0x47, 0x4d, 0x56, 0x60, 0x69, 0x74, 0x7e, 0x86, 0x90, 0x99, 0xa4, 0xac, 0xb2, 0xb8, 0xbf,
0xc3, 0xc4, 0xc3, 0xc1, 0xbe, 0xba, 0xb4, 0xae, 0xa4, 0x99, 0x90, 0x87, 0x7e, 0x78, 0x72, 0x6d,
0x68, 0x63, 0x61, 0x5f, 0x5e, 0x60, 0x65, 0x6a, 0x6f, 0x75, 0x7a, 0x7d, 0x7e, 0x80, 0x80, 0x81,
0x81, 0x82, 0x84, 0x87, 0x89, 0x8a, 0x8b, 0x89, 0x89, 0x89, 0x88, 0x88, 0x89, 0x8a, 0x8b, 0x8e,
0x90, 0x90, 0x90, 0x91, 0x90, 0x8c, 0x88, 0x83, 0x7e, 0x77, 0x70, 0x6c, 0x68, 0x65, 0x63, 0x63,
0x64, 0x64, 0x63, 0x62, 0x62, 0x63, 0x64, 0x67, 0x6c, 0x71, 0x78, 0x7f, 0x83, 0x88, 0x8d, 0x92,
0x99, 0x9e, 0xa0, 0xa1, 0x9e, 0x9a, 0x95, 0x8f, 0x89, 0x83, 0x7d, 0x77, 0x72, 0x6d, 0x67, 0x64,
0x62, 0x60, 0x60, 0x62, 0x64, 0x67, 0x6b, 0x6f, 0x74, 0x78, 0x7c, 0x80, 0x88, 0x91, 0x99, 0xa0,
0xa5, 0xa7, 0xa7, 0xa5, 0xa4, 0xa2, 0xa1, 0xa1, 0xa2, 0xa1, 0xa0, 0x9f, 0x9f, 0x9d, 0x9c, 0x9a,
0x97, 0x93, 0x8f, 0x8b, 0x88, 0x86, 0x84, 0x83, 0x84, 0x86, 0x87, 0x85, 0x83, 0x7d, 0x72, 0x68,
0x5e, 0x54, 0x4d, 0x48, 0x45, 0x42, 0x41, 0x3f, 0x3f, 0x3f, 0x43, 0x47, 0x4c, 0x51, 0x58, 0x5e,
0x65, 0x6b, 0x72, 0x79, 0x80, 0x88, 0x90, 0x97, 0x9e, 0xa1, 0xa3, 0xa5, 0xa6, 0xa7, 0xa8, 0xa9,
0xaa, 0xaa, 0xa8, 0xa5, 0xa0, 0x9d, 0x9c, 0x9a, 0x99, 0x9a, 0x99, 0x96, 0x91, 0x8d, 0x88, 0x83,
0x80, 0x7e, 0x7e, 0x7e, 0x7c, 0x7a, 0x76, 0x71, 0x6c, 0x67, 0x60, 0x5a, 0x58, 0x57, 0x55, 0x56,
0x58, 0x5c, 0x62, 0x6c, 0x78, 0x81, 0x8a, 0x93, 0x9b, 0xa1, 0xa4, 0xa7, 0xab, 0xaf, 0xb5, 0xba,
0xbc, 0xbb, 0xb7, 0xb1, 0xa9, 0xa0, 0x96, 0x8d, 0x83, 0x7a, 0x71, 0x67, 0x5d, 0x54, 0x4d, 0x46,
0x42, 0x41, 0x40, 0x42, 0x45, 0x49, 0x4d, 0x53, 0x58, 0x60, 0x6b, 0x74, 0x7b, 0x80, 0x83, 0x85,
0x85, 0x84, 0x83, 0x85, 0x88, 0x8c, 0x8d, 0x8d, 0x8d, 0x8b, 0x88, 0x87, 0x89, 0x8c, 0x8e, 0x93,
0x99, 0x9b, 0x9c, 0x9c, 0x98, 0x94, 0x92, 0x91, 0x8e, 0x8b, 0x87, 0x82, 0x7f, 0x7c, 0x7b, 0x7c,
0x7c, 0x7e, 0x80, 0x7f, 0x7c, 0x7b, 0x7b, 0x7a, 0x7b, 0x81, 0x89, 0x8f, 0x96, 0x9d, 0xa1, 0xa3,
0xa5, 0xa7, 0xa7, 0xa7, 0xa6, 0xa4, 0x9f, 0x96, 0x8d, 0x82, 0x76, 0x6b, 0x64, 0x5e, 0x57, 0x50,
0x4b, 0x47, 0x43, 0x42, 0x43, 0x45, 0x49, 0x50, 0x57, 0x5b, 0x61, 0x67, 0x6c, 0x72, 0x7b, 0x83,
0x8a, 0x90, 0x94, 0x95, 0x94, 0x91, 0x8f, 0x8e, 0x90, 0x92, 0x95, 0x97, 0x98, 0x97, 0x94, 0x8f,
0x8a, 0x87, 0x86, 0x85, 0x85, 0x88, 0x8b, 0x8b, 0x8d, 0x92, 0x95, 0x96, 0x97, 0x95, 0x91, 0x8a,
0x80, 0x76, 0x6d, 0x65, 0x60, 0x60, 0x61, 0x60, 0x60, 0x61, 0x60, 0x5e, 0x60, 0x63, 0x68, 0x71,
0x7c, 0x85, 0x8f, 0x98, 0x9e, 0xa2, 0xa5, 0xa9, 0xae, 0xb0, 0xb2, 0xb3, 0xb1, 0xad, 0xab, 0xa7,
0xa3, 0xa1, 0x9f, 0x9a, 0x93, 0x8c, 0x84, 0x7b, 0x73, 0x6e, 0x6c, 0x6a, 0x6a, 0x69, 0x66, 0x62,
0x5e, 0x5b, 0x58, 0x56, 0x56, 0x59, 0x5c, 0x5d, 0x5b, 0x57, 0x50, 0x4c, 0x49, 0x48, 0x4a, 0x4d,
0x51, 0x58, 0x5f, 0x68, 0x70, 0x78, 0x83, 0x90, 0x9c, 0xa5, 0xad, 0xb2, 0xb5, 0xb7, 0xb8, 0xbb,
0xbf, 0xc1, 0xc1, 0xbf, 0xba, 0xb0, 0xa4, 0x9a, 0x91, 0x88, 0x81, 0x7c, 0x76, 0x71, 0x6c, 0x66,
0x5e, 0x59, 0x57, 0x58, 0x5a, 0x60, 0x6a, 0x72, 0x78, 0x80, 0x89, 0x90, 0x97, 0x9e, 0xa3, 0xa6,
0xa7, 0xa2, 0x9c, 0x97, 0x95, 0x93, 0x92, 0x90, 0x8d, 0x87, 0x81, 0x7b, 0x75, 0x6f, 0x6c, 0x6c,
0x6f, 0x73, 0x77, 0x79, 0x78, 0x75, 0x70, 0x6b, 0x65, 0x60, 0x5e, 0x5f, 0x5f, 0x60, 0x61, 0x62,
0x64, 0x67, 0x69, 0x6d, 0x70, 0x72, 0x71, 0x70, 0x70, 0x72, 0x76, 0x7e, 0x89, 0x93, 0x9c, 0xa3,
0xa6, 0xa7, 0xa7, 0xa5, 0xa3, 0xa1, 0xa0, 0xa0, 0x9e, 0x98, 0x91, 0x88, 0x80, 0x77, 0x6d, 0x66,
0x62, 0x5f, 0x5d, 0x5b, 0x59, 0x5a, 0x5e, 0x64, 0x6e, 0x7a, 0x82, 0x8a, 0x8f, 0x92, 0x94, 0x98,
0x9e, 0xa3, 0xa7, 0xac, 0xaf, 0xaf, 0xac, 0xa8, 0xa3, 0x9f, 0x9d, 0x9c, 0x99, 0x97, 0x96, 0x90,
0x88, 0x80, 0x76, 0x70, 0x6d, 0x6d, 0x71, 0x75, 0x77, 0x79, 0x78, 0x76, 0x74, 0x74, 0x73, 0x71,
0x6c, 0x64, 0x5a, 0x51, 0x4b, 0x48, 0x47, 0x49, 0x4b, 0x4d, 0x4d, 0x4e, 0x4e, 0x4f, 0x52, 0x59,
0x64, 0x72, 0x80, 0x8e, 0x9a, 0xa4, 0xaa, 0xac, 0xad, 0xac, 0xac, 0xae, 0xaf, 0xb0, 0xaf, 0xad,
0xaa, 0xa9, 0xa7, 0xa7, 0xa5, 0xa0, 0x97, 0x8d, 0x82, 0x79, 0x71, 0x6d, 0x6e, 0x71, 0x74, 0x77,
0x78, 0x76, 0x75, 0x74, 0x72, 0x71, 0x71, 0x73, 0x75, 0x79, 0x7b, 0x79, 0x76, 0x74, 0x71, 0x70,
0x71, 0x73, 0x75, 0x79, 0x7d, 0x81, 0x86, 0x8d, 0x96, 0x9e, 0xa7, 0xae, 0xaf, 0xae, 0xaa, 0xa6,
0xa3, 0xa0, 0x9d, 0x9b, 0x96, 0x90, 0x89, 0x80, 0x75, 0x69, 0x5f, 0x56, 0x4f, 0x4b, 0x48, 0x45,
0x43, 0x42, 0x40, 0x40, 0x41, 0x46, 0x4f, 0x59, 0x65, 0x70, 0x7a, 0x82, 0x8d, 0x97, 0xa0, 0xa8,
0xad, 0xaf, 0xad, 0xa8, 0xa3, 0x9e, 0x98, 0x94, 0x92, 0x8e, 0x8a, 0x86, 0x80, 0x7b, 0x77, 0x72,
0x71, 0x73, 0x77, 0x7d, 0x82, 0x85, 0x85, 0x83, 0x7f, 0x7b, 0x79, 0x78, 0x78, 0x7b, 0x7f, 0x81,
0x84, 0x87, 0x8b, 0x8f, 0x93, 0x95, 0x96, 0x93, 0x8f, 0x8d, 0x8c, 0x8b, 0x8d, 0x94, 0x9c, 0xa1,
0xa6, 0xa9, 0xa6, 0xa0, 0x9a, 0x94, 0x8d, 0x89, 0x88, 0x86, 0x82, 0x7e, 0x77, 0x6d, 0x61, 0x56,
0x4d, 0x44, 0x3b, 0x35, 0x33, 0x34, 0x38, 0x3f, 0x49, 0x55, 0x62, 0x6e, 0x78, 0x7f, 0x82, 0x84,
0x86, 0x88, 0x8e, 0x95, 0x9a, 0xa1, 0xa6, 0xa8, 0xa7, 0xa4, 0x9e, 0x97, 0x93, 0x90, 0x8c, 0x87,
0x83, 0x7f, 0x7c, 0x77, 0x75, 0x75, 0x74, 0x74, 0x77, 0x7a, 0x7b, 0x7e, 0x81, 0x84, 0x87, 0x8b,
0x8d, 0x8d, 0x8a, 0x86, 0x82, 0x7e, 0x7a, 0x77, 0x77, 0x76, 0x75, 0x75, 0x75, 0x76, 0x77, 0x79,
0x7d, 0x84, 0x8b, 0x94, 0x9e, 0xa7, 0xae, 0xb2, 0xb2, 0xaf, 0xad, 0xab, 0xa6, 0xa1, 0x9e, 0x9b,
0x98, 0x95, 0x92, 0x8f, 0x8b, 0x85, 0x7e, 0x73, 0x66, 0x5b, 0x50, 0x46, 0x41, 0x3f, 0x3f, 0x41,
0x46, 0x4d, 0x51, 0x54, 0x57, 0x5a, 0x5b, 0x5e, 0x63, 0x6a, 0x6f, 0x73, 0x79, 0x7d, 0x7e, 0x80,
0x7f, 0x7d, 0x7b, 0x79, 0x78, 0x79, 0x7e, 0x85, 0x8f, 0x98, 0xa0, 0xa7, 0xac, 0xb0, 0xb1, 0xb0,
0xad, 0xa9, 0xa6, 0xa5, 0xa2, 0x9e, 0x9c, 0x98, 0x93, 0x8c, 0x84, 0x7d, 0x76, 0x6f, 0x68, 0x64,
0x61, 0x61, 0x62, 0x65, 0x68, 0x6d, 0x71, 0x75, 0x79, 0x80, 0x88, 0x8f, 0x98, 0xa3, 0xac, 0xb4,
0xb9, 0xbc, 0xbb, 0xb7, 0xb0, 0xa7, 0x9d, 0x92, 0x87, 0x7e, 0x76, 0x6c, 0x63, 0x5d, 0x59, 0x55,
0x51, 0x4d, 0x4c, 0x4e, 0x4e, 0x4f, 0x52, 0x56, 0x58, 0x5a, 0x5d, 0x5f, 0x62, 0x65, 0x68, 0x6a,
0x6e, 0x75, 0x7c, 0x83, 0x88, 0x8d, 0x90, 0x91, 0x91, 0x8f, 0x8d, 0x8c, 0x8d, 0x8f, 0x90, 0x91,
0x93, 0x96, 0x99, 0x9a, 0x9a, 0x98, 0x97, 0x97, 0x96, 0x94, 0x93, 0x91, 0x8e, 0x8b, 0x87, 0x82,
0x7d, 0x75, 0x6d, 0x65, 0x5d, 0x58, 0x58, 0x5d, 0x66, 0x73, 0x7e, 0x87, 0x90, 0x97, 0x9c, 0x9f,
0xa0, 0xa1, 0xa3, 0xa5, 0xa8, 0xac, 0xad, 0xad, 0xac, 0xaa, 0xa5, 0x9f, 0x98, 0x8f, 0x85, 0x7c,
0x71, 0x66, 0x5e, 0x5b, 0x58, 0x58, 0x59, 0x56, 0x53, 0x4f, 0x4e, 0x4e, 0x51, 0x56, 0x5c, 0x65,
0x6d, 0x74, 0x78, 0x78, 0x77, 0x76, 0x75, 0x73, 0x71, 0x6f, 0x6f, 0x6f, 0x6e, 0x6d, 0x6f, 0x73,
0x79, 0x80, 0x86, 0x8c, 0x92, 0x98, 0x9c, 0xa0, 0xa5, 0xa8, 0xab, 0xad, 0xae, 0xad, 0xa9, 0xa3,
0x9f, 0x9b, 0x99, 0x98, 0x99, 0x97, 0x92, 0x8c, 0x83, 0x79, 0x6e, 0x66, 0x5f, 0x5d, 0x5c, 0x5a,
0x5b, 0x5d, 0x61, 0x66, 0x6d, 0x74, 0x7a, 0x80, 0x87, 0x8e, 0x92, 0x94, 0x94, 0x95, 0x97, 0x98,
0x99, 0x9a, 0x97, 0x91, 0x89, 0x81, 0x7c, 0x7a, 0x7a, 0x7e, 0x83, 0x89, 0x8c, 0x8d, 0x8c, 0x8a,
0x87, 0x84, 0x80, 0x7e, 0x7c, 0x7b, 0x78, 0x74, 0x70, 0x6c, 0x68, 0x65, 0x62, 0x60, 0x5c, 0x58,
0x55, 0x52, 0x4f, 0x50, 0x55, 0x5d, 0x66, 0x6d, 0x72, 0x78, 0x7c, 0x80, 0x86, 0x90, 0x99, 0xa4,
0xb1, 0xb9, 0xbd, 0xbd, 0xba, 0xb3, 0xab, 0xa1, 0x98, 0x90, 0x88, 0x81, 0x7a, 0x72, 0x6c, 0x67,
0x64, 0x64, 0x65, 0x68, 0x69, 0x68, 0x68, 0x68, 0x6a, 0x6e, 0x76, 0x7f, 0x85, 0x8c, 0x91, 0x92,
0x92, 0x93, 0x95, 0x98, 0x9e, 0xa3, 0xa5, 0xa7, 0xa6, 0xa2, 0x9b, 0x94, 0x8f, 0x8b, 0x87, 0x83,
0x81, 0x7f, 0x7a, 0x76, 0x76, 0x77, 0x77, 0x79, 0x7d, 0x80, 0x80, 0x7d, 0x77, 0x71, 0x6a, 0x67,
0x67, 0x65, 0x62, 0x5e, 0x59, 0x53, 0x4e, 0x4c, 0x4e, 0x53, 0x5e, 0x6c, 0x77, 0x80, 0x86, 0x8b,
0x8f, 0x92, 0x96, 0x9b, 0x9d, 0xa2, 0xa7, 0xa6, 0xa2, 0xa0, 0x9c, 0x98, 0x94, 0x92, 0x8e, 0x86,
0x7d, 0x74, 0x68, 0x5e, 0x5a, 0x5b, 0x5d, 0x62, 0x65, 0x67, 0x66, 0x64, 0x66, 0x6a, 0x71, 0x7c,
0x86, 0x91, 0x9b, 0xa2, 0xa5, 0xa6, 0xa3, 0xa2, 0xa1, 0x9f, 0x9d, 0x9c, 0x99, 0x96, 0x92, 0x8e,
0x8c, 0x8d, 0x90, 0x94, 0x97, 0x97, 0x94, 0x91, 0x8d, 0x89, 0x8a, 0x8d, 0x90, 0x92, 0x91, 0x8d,
0x88, 0x80, 0x7a, 0x75, 0x72, 0x70, 0x6f, 0x6a, 0x64, 0x5e, 0x57, 0x4f, 0x47, 0x41, 0x3f, 0x3f,
0x42, 0x45, 0x48, 0x4b, 0x4f, 0x57, 0x61, 0x6d, 0x7a, 0x86, 0x90, 0x97, 0x9a, 0x99, 0x97, 0x98,
0x9a, 0x9c, 0x9b, 0x9a, 0x98, 0x91, 0x8a, 0x84, 0x80, 0x7e, 0x7f, 0x82, 0x87, 0x8c, 0x90, 0x92,
0x92, 0x92, 0x93, 0x94, 0x94, 0x95, 0x97, 0x96, 0x92, 0x8e, 0x89, 0x84, 0x84, 0x85, 0x85, 0x84,
0x81, 0x7d, 0x77, 0x71, 0x6e, 0x6f, 0x73, 0x7a, 0x81, 0x87, 0x8a, 0x8d, 0x8f, 0x90, 0x94, 0x99,
0xa0, 0xa5, 0xa8, 0xaa, 0xa8, 0xa2, 0x99, 0x91, 0x88, 0x80, 0x79, 0x71, 0x68, 0x5f, 0x55, 0x4b,
0x44, 0x40, 0x3f, 0x44, 0x4a, 0x4c, 0x4d, 0x4e, 0x4f, 0x50, 0x55, 0x5e, 0x68, 0x74, 0x7f, 0x86,
0x8a, 0x8c, 0x8e, 0x91, 0x94, 0x97, 0x9c, 0x9f, 0x9f, 0x9f, 0x9b, 0x95, 0x8e, 0x89, 0x84, 0x82,
0x82, 0x82, 0x80, 0x7d, 0x7a, 0x79, 0x7c, 0x82, 0x8a, 0x91, 0x97, 0x99, 0x97, 0x93, 0x8d, 0x88,
0x86, 0x87, 0x88, 0x89, 0x89, 0x87, 0x83, 0x80, 0x7e, 0x7c, 0x7f, 0x84, 0x8a, 0x8f, 0x94, 0x97,
0x9a, 0x9b, 0x9c, 0xa1, 0xa6, 0xa8, 0xa7, 0xa5, 0x9f, 0x95, 0x8b, 0x82, 0x7c, 0x77, 0x73, 0x6e,
0x67, 0x5f, 0x54, 0x49, 0x3f, 0x39, 0x36, 0x37, 0x3c, 0x41, 0x45, 0x47, 0x49, 0x4f, 0x56, 0x5f,
0x6a, 0x76, 0x80, 0x8a, 0x91, 0x97, 0x9a, 0x9c, 0x9d, 0xa0, 0xa2, 0xa1, 0x9f, 0x9e, 0x99, 0x92,
0x8d, 0x8a, 0x89, 0x8b, 0x8f, 0x8f, 0x8f, 0x8d, 0x89, 0x84, 0x81, 0x82, 0x87, 0x8d, 0x92, 0x96,
0x97, 0x96, 0x92, 0x8e, 0x8b, 0x89, 0x86, 0x84, 0x81, 0x7d, 0x78, 0x70, 0x69, 0x64, 0x60, 0x61,
0x66, 0x69, 0x6b, 0x6d, 0x6f, 0x72, 0x77, 0x7f, 0x89, 0x95, 0x9f, 0xa7, 0xab, 0xab, 0xa8, 0xa3,
0x9e, 0x9b, 0x97, 0x92, 0x8c, 0x86, 0x80, 0x76, 0x6d, 0x66, 0x62, 0x63, 0x64, 0x67, 0x6a, 0x6b,
0x6c, 0x6c, 0x6d, 0x70, 0x75, 0x7a, 0x7f, 0x81, 0x80, 0x7c, 0x76, 0x72, 0x70, 0x71, 0x74, 0x75,
0x76, 0x74, 0x71, 0x6c, 0x65, 0x60, 0x62, 0x66, 0x6d, 0x77, 0x80, 0x87, 0x8c, 0x90, 0x94, 0x98,
0x9d, 0xa3, 0xa8, 0xaa, 0xaa, 0xaa, 0xa4, 0x9d, 0x9a, 0x99, 0x97, 0x96, 0x92, 0x8a, 0x80, 0x75,
0x6a, 0x61, 0x5e, 0x5e, 0x62, 0x67, 0x6c, 0x6f, 0x72, 0x73, 0x76, 0x7a, 0x7f, 0x87, 0x90, 0x98,
0x9d, 0x9f, 0xa1, 0xa0, 0x9e, 0x9f, 0xa1, 0xa1, 0x9f, 0x9b, 0x94, 0x8a, 0x7e, 0x74, 0x6d, 0x67,
0x66, 0x67, 0x67, 0x66, 0x64, 0x62, 0x61, 0x63, 0x69, 0x70, 0x76, 0x7c, 0x80, 0x7f, 0x7b, 0x77,
0x73, 0x73, 0x74, 0x75, 0x78, 0x7a, 0x7a, 0x79, 0x76, 0x74, 0x72, 0x72, 0x78, 0x7f, 0x84, 0x8a,
0x8d, 0x8e, 0x91, 0x96, 0x9a, 0xa1, 0xa7, 0xa9, 0xa7, 0x9f, 0x94, 0x89, 0x80, 0x78, 0x76, 0x74,
0x71, 0x6d, 0x69, 0x63, 0x5a, 0x53, 0x4e, 0x4b, 0x4c, 0x53, 0x5a, 0x62, 0x6a, 0x72, 0x79, 0x80,
0x8a, 0x96, 0xa1, 0xab, 0xb4, 0xb8, 0xb9, 0xb8, 0xb7, 0xb7, 0xb8, 0xba, 0xbc, 0xb9, 0xb3, 0xaa,
0x9e, 0x91, 0x87, 0x80, 0x7c, 0x79, 0x78, 0x76, 0x72, 0x6c, 0x68, 0x65, 0x63, 0x65, 0x69, 0x6f,
0x74, 0x76, 0x76, 0x74, 0x70, 0x6d, 0x6c, 0x6c, 0x6b, 0x68, 0x64, 0x5e, 0x56, 0x4f, 0x4b, 0x4b,
0x50, 0x59, 0x61, 0x69, 0x6e, 0x71, 0x73, 0x77, 0x7e, 0x87, 0x91, 0x9a, 0xa2, 0xa7, 0xa8, 0xa5,
0xa1, 0x9d, 0x98, 0x94, 0x93, 0x8f, 0x89, 0x83, 0x7e, 0x77, 0x72, 0x70, 0x71, 0x76, 0x7c, 0x81,
0x86, 0x88, 0x87, 0x88, 0x8c, 0x92, 0x99, 0xa1, 0xa6, 0xa7, 0xa5, 0x9f, 0x97, 0x92, 0x8f, 0x8c,
0x8c, 0x8b, 0x87, 0x80, 0x79, 0x71, 0x6b, 0x66, 0x66, 0x6b, 0x70, 0x76, 0x7d, 0x80, 0x82, 0x84,
0x86, 0x89, 0x8d, 0x8e, 0x8f, 0x8e, 0x8b, 0x85, 0x80, 0x7b, 0x7c, 0x7d, 0x7e, 0x7f, 0x7b, 0x72,
0x67, 0x5b, 0x50, 0x4a, 0x48, 0x4a, 0x50, 0x58, 0x5e, 0x64, 0x69, 0x6d, 0x72, 0x78, 0x7f, 0x85,
0x8a, 0x90, 0x94, 0x96, 0x95, 0x95, 0x96, 0x98, 0x99, 0x98, 0x95, 0x8f, 0x85, 0x7b, 0x72, 0x6a,
0x67, 0x6a, 0x6f, 0x76, 0x7d, 0x81, 0x84, 0x86, 0x87, 0x8c, 0x92, 0x97, 0x9a, 0x9c, 0x9c, 0x99,
0x95, 0x94, 0x93, 0x92, 0x93, 0x96, 0x98, 0x98, 0x96, 0x92, 0x8c, 0x89, 0x87, 0x88, 0x8a, 0x8e,
0x91, 0x93, 0x93, 0x92, 0x90, 0x91, 0x92, 0x91, 0x91, 0x8e, 0x87, 0x7e, 0x71, 0x63, 0x58, 0x52,
0x4e, 0x4c, 0x4c, 0x4a, 0x47, 0x43, 0x3e, 0x3a, 0x38, 0x3a, 0x3e, 0x45, 0x4e, 0x5a, 0x64, 0x6d,
0x77, 0x82, 0x8d, 0x98, 0xa2, 0xa9, 0xac, 0xad, 0xad, 0xab, 0xa9, 0xaa, 0xad, 0xb1, 0xb4, 0xb4,
0xaf, 0xa4, 0x97, 0x8b, 0x82, 0x7c, 0x76, 0x75, 0x76, 0x74, 0x73, 0x71, 0x71, 0x73, 0x77, 0x7c,
0x81, 0x87, 0x8b, 0x8c, 0x8b, 0x89, 0x86, 0x85, 0x86, 0x86, 0x87, 0x85, 0x80, 0x7b, 0x74, 0x6f,
0x6e, 0x71, 0x75, 0x7b, 0x80, 0x86, 0x8a, 0x8a, 0x89, 0x8c, 0x8e, 0x92, 0x96, 0x99, 0x99, 0x96,
0x91, 0x8b, 0x84, 0x7f, 0x79, 0x75, 0x71, 0x6d, 0x69, 0x64, 0x5d, 0x58, 0x59, 0x5b, 0x60, 0x67,
0x6d, 0x70, 0x73, 0x75, 0x77, 0x7a, 0x7f, 0x85, 0x8d, 0x93, 0x97, 0x97, 0x94, 0x8f, 0x88, 0x81,
0x7f, 0x7e, 0x7d, 0x7b, 0x77, 0x72, 0x6d, 0x67, 0x65, 0x65, 0x68, 0x6d, 0x73, 0x7b, 0x82, 0x87,
0x8a, 0x8f, 0x93, 0x97, 0x9a, 0x9c, 0x9d, 0x9c, 0x99, 0x96, 0x93, 0x93, 0x93, 0x96, 0x98, 0x96,
0x93, 0x8e, 0x83, 0x7a, 0x72, 0x6b, 0x6a, 0x6f, 0x73, 0x79, 0x80, 0x85, 0x88, 0x89, 0x8a, 0x8c,
0x8d, 0x8e, 0x90, 0x8f, 0x8c, 0x8a, 0x87, 0x84, 0x82, 0x82, 0x81, 0x7e, 0x77, 0x70, 0x67, 0x5c,
0x54, 0x52, 0x54, 0x58, 0x5f, 0x68, 0x70, 0x77, 0x7c, 0x7e, 0x7f, 0x80, 0x82, 0x82, 0x82, 0x83,
0x81, 0x7f, 0x7e, 0x7d, 0x7d, 0x80, 0x83, 0x87, 0x8b, 0x8b, 0x8b, 0x88, 0x84, 0x83, 0x84, 0x89,
0x8d, 0x90, 0x92, 0x94, 0x94, 0x93, 0x93, 0x94, 0x94, 0x93, 0x90, 0x8c, 0x85, 0x7c, 0x71, 0x69,
0x62, 0x5f, 0x60, 0x61, 0x63, 0x66, 0x66, 0x63, 0x60, 0x5f, 0x61, 0x64, 0x69, 0x71, 0x7b, 0x83,
0x8b, 0x93, 0x9c, 0xa5, 0xae, 0xb5, 0xb7, 0xb6, 0xb4, 0xb0, 0xab, 0xa6, 0xa3, 0xa2, 0xa4, 0xa4,
0xa3, 0x9f, 0x98, 0x8e, 0x80, 0x74, 0x69, 0x61, 0x5b, 0x58, 0x57, 0x57, 0x56, 0x56, 0x58, 0x5c,
0x5f, 0x65, 0x6a, 0x6c, 0x6e, 0x70, 0x71, 0x70, 0x71, 0x74, 0x76, 0x77, 0x7a, 0x7a, 0x78, 0x77,
0x76, 0x76, 0x78, 0x7a, 0x7e, 0x82, 0x87, 0x8c, 0x8e, 0x8e, 0x8d, 0x8d, 0x8e, 0x8e, 0x8f, 0x8f,
0x8e, 0x8b, 0x87, 0x82, 0x80, 0x7d, 0x7a, 0x78, 0x78, 0x79, 0x78, 0x78, 0x7a, 0x7c, 0x7f, 0x83,
0x88, 0x8d, 0x91, 0x95, 0x97, 0x97, 0x99, 0x9c, 0x9d, 0xa0, 0xa3, 0xa4, 0xa4, 0xa2, 0x9d, 0x97,
0x8f, 0x88, 0x81, 0x7c, 0x76, 0x71, 0x6c, 0x67, 0x62, 0x5e, 0x5c, 0x5a, 0x5c, 0x60, 0x63, 0x68,
0x6d, 0x72, 0x75, 0x79, 0x7e, 0x80, 0x80, 0x81, 0x81, 0x81, 0x81, 0x82, 0x83, 0x83, 0x84, 0x86,
0x87, 0x88, 0x86, 0x81, 0x7c, 0x76, 0x70, 0x6c, 0x6b, 0x6c, 0x72, 0x78, 0x7c, 0x7f, 0x81, 0x84,
0x85, 0x84, 0x83, 0x81, 0x80, 0x80, 0x81, 0x82, 0x84, 0x85, 0x85, 0x85, 0x84, 0x83, 0x81, 0x7e,
0x7a, 0x78, 0x76, 0x74, 0x76, 0x7b, 0x81, 0x8a, 0x92, 0x97, 0x99, 0x9a, 0x98, 0x96, 0x93, 0x8e,
0x8a, 0x87, 0x85, 0x84, 0x85, 0x86, 0x88, 0x8a, 0x8c, 0x8e, 0x90, 0x92, 0x92, 0x91, 0x8f, 0x8d,
0x8b, 0x89, 0x88, 0x88, 0x89, 0x89, 0x87, 0x86, 0x83, 0x7f, 0x7a, 0x73, 0x6c, 0x65, 0x5f, 0x5a,
0x55, 0x50, 0x4c, 0x4c, 0x4e, 0x52, 0x56, 0x59, 0x5c, 0x5f, 0x63, 0x64, 0x64, 0x66, 0x6a, 0x6f,
0x77, 0x82, 0x8d, 0x96, 0x9f, 0xa6, 0xa9, 0xa9, 0xa8, 0xa5, 0xa3, 0xa2, 0xa1, 0xa0, 0x9e, 0x9c,
0x9d, 0xa0, 0xa1, 0x9f, 0x9b, 0x95, 0x8e, 0x83, 0x7b, 0x72, 0x6a, 0x66, 0x65, 0x65, 0x66, 0x6a,
0x6f, 0x71, 0x73, 0x75, 0x76, 0x76, 0x79, 0x7e, 0x82, 0x86, 0x89, 0x8a, 0x8c, 0x8e, 0x91, 0x95,
0x96, 0x96, 0x98, 0x96, 0x92, 0x8e, 0x8b, 0x88, 0x86, 0x84, 0x82, 0x7e, 0x7b, 0x79, 0x74, 0x6e,
0x6a, 0x66, 0x63, 0x64, 0x65, 0x65, 0x66, 0x65, 0x64, 0x66, 0x69, 0x6e, 0x75, 0x7d, 0x82, 0x89,
0x8c, 0x8c, 0x8a, 0x8b, 0x8d, 0x8d, 0x8d, 0x8e, 0x8f, 0x8f, 0x8d, 0x8c, 0x8a, 0x89, 0x89, 0x89,
0x88, 0x86, 0x82, 0x7e, 0x79, 0x73, 0x6e, 0x6b, 0x69, 0x6b, 0x6e, 0x6e, 0x6d, 0x6c, 0x6b, 0x6c,
0x6e, 0x72, 0x79, 0x80, 0x86, 0x8a, 0x8c, 0x8d, 0x8e, 0x90, 0x92, 0x95, 0x9a, 0xa0, 0xa3, 0xa3,
0xa3, 0xa2, 0xa0, 0x9d, 0x9b, 0x97, 0x93, 0x8e, 0x8a, 0x83, 0x7d, 0x79, 0x77, 0x76, 0x76, 0x76,
0x78, 0x76, 0x71, 0x6c, 0x66, 0x61, 0x61, 0x64, 0x6a, 0x71, 0x78, 0x7c, 0x7e, 0x7d, 0x7b, 0x7a,
0x78, 0x78, 0x79, 0x78, 0x79, 0x7b, 0x7b, 0x79, 0x7a, 0x7e, 0x82, 0x85, 0x86, 0x85, 0x80, 0x7a,
0x74, 0x6d, 0x69, 0x6a, 0x6d, 0x72, 0x79, 0x7e, 0x81, 0x83, 0x86, 0x8a, 0x8f, 0x94, 0x99, 0x9c,
0x9d, 0x9c, 0x99, 0x95, 0x92, 0x91, 0x90, 0x91, 0x92, 0x92, 0x8d, 0x85, 0x7f, 0x76, 0x6f, 0x6a,
0x68, 0x69, 0x6b, 0x6d, 0x6d, 0x6e, 0x6f, 0x71, 0x74, 0x78, 0x7b, 0x7f, 0x80, 0x80, 0x7f, 0x7d,
0x7c, 0x7f, 0x83, 0x8b, 0x94, 0x9b, 0x9f, 0xa0, 0x9e, 0x97, 0x93, 0x90, 0x8e, 0x8d, 0x8d, 0x8e,
0x8f, 0x8f, 0x8e, 0x8c, 0x8c, 0x8b, 0x89, 0x85, 0x80, 0x77, 0x6d, 0x63, 0x59, 0x52, 0x51, 0x52,
0x54, 0x59, 0x5c, 0x5e, 0x5e, 0x5f, 0x60, 0x63, 0x69, 0x71, 0x7a, 0x83, 0x8d, 0x93, 0x95, 0x97,
0x9b, 0x9f, 0xa1, 0xa3, 0xa3, 0xa2, 0x9c, 0x96, 0x8f, 0x85, 0x7e, 0x7a, 0x78, 0x76, 0x74, 0x71,
0x6c, 0x69, 0x67, 0x66, 0x67, 0x6a, 0x6f, 0x76, 0x79, 0x7e, 0x82, 0x85, 0x8a, 0x93, 0x9c, 0xa3,
0xaa, 0xaf, 0xb1, 0xae, 0xa8, 0xa0, 0x9a, 0x94, 0x92, 0x92, 0x91, 0x8e, 0x8a, 0x85, 0x80, 0x7c,
0x7a, 0x7a, 0x7a, 0x7a, 0x79, 0x75, 0x6e, 0x67, 0x62, 0x5f, 0x60, 0x60, 0x60, 0x61, 0x63, 0x61,
0x5f, 0x5e, 0x5d, 0x5e, 0x64, 0x6c, 0x73, 0x79, 0x7d, 0x80, 0x80, 0x82, 0x88, 0x8e, 0x96, 0x9d,
0xa2, 0xa5, 0xa4, 0x9f, 0x9a, 0x96, 0x93, 0x90, 0x8e, 0x8b, 0x88, 0x83, 0x7f, 0x78, 0x71, 0x6d,
0x6c, 0x6b, 0x6b, 0x6a, 0x68, 0x67, 0x66, 0x68, 0x6c, 0x73, 0x7e, 0x8b, 0x96, 0x9d, 0xa0, 0x9f,
0x9c, 0x98, 0x94, 0x94, 0x95, 0x96, 0x95, 0x94, 0x91, 0x8c, 0x86, 0x81, 0x7f, 0x7e, 0x7c, 0x79,
0x73, 0x6e, 0x68, 0x63, 0x62, 0x66, 0x6b, 0x72, 0x7b, 0x83, 0x87, 0x89, 0x89, 0x8a, 0x8c, 0x8d,
0x91, 0x94, 0x95, 0x95, 0x92, 0x8d, 0x86, 0x81, 0x7f, 0x7f, 0x7f, 0x7c, 0x78, 0x73, 0x6b, 0x63,
0x5e, 0x5d, 0x5f, 0x64, 0x6a, 0x70, 0x75, 0x78, 0x78, 0x76, 0x74, 0x74, 0x75, 0x76, 0x78, 0x7a,
0x79, 0x78, 0x78, 0x79, 0x7c, 0x82, 0x8b, 0x91, 0x96, 0x98, 0x96, 0x92, 0x8f, 0x8e, 0x90, 0x94,
0x9b, 0xa0, 0xa3, 0xa5, 0xa3, 0x9e, 0x99, 0x94, 0x8f, 0x8a, 0x85, 0x80, 0x79, 0x70, 0x66, 0x5f,
0x5a, 0x59, 0x5b, 0x5f, 0x62, 0x67, 0x69, 0x6a, 0x6b, 0x70, 0x79, 0x84, 0x90, 0x9b, 0xa4, 0xaa,
0xac, 0xab, 0xa7, 0xa4, 0xa2, 0x9f, 0x9c, 0x98, 0x8f, 0x84, 0x78, 0x6a, 0x5d, 0x56, 0x52, 0x50,
0x50, 0x53, 0x55, 0x55, 0x55, 0x57, 0x5b, 0x62, 0x6b, 0x76, 0x80, 0x86, 0x8c, 0x90, 0x93, 0x99,
0xa0, 0xa6, 0xac, 0xae, 0xad, 0xa7, 0x9d, 0x92, 0x89, 0x82, 0x7f, 0x7f, 0x80, 0x80, 0x81, 0x80,
0x7b, 0x78, 0x77, 0x79, 0x7b, 0x7d, 0x7e, 0x7f, 0x7c, 0x77, 0x71, 0x6e, 0x6d, 0x6e, 0x70, 0x72,
0x72, 0x70, 0x6d, 0x68, 0x65, 0x67, 0x6d, 0x74, 0x7d, 0x85, 0x8d, 0x93, 0x97, 0x9b, 0x9f, 0xa4,
0xab, 0xb2, 0xb5, 0xb5, 0xb2, 0xaa, 0xa0, 0x96, 0x8d, 0x89, 0x86, 0x81, 0x7e, 0x78, 0x6e, 0x64,
0x5b, 0x53, 0x4f, 0x4e, 0x50, 0x53, 0x56, 0x5b, 0x61, 0x66, 0x70, 0x7d, 0x8a, 0x95, 0xa0, 0xa8,
0xaa, 0xa7, 0xa1, 0x99, 0x93, 0x8f, 0x8d, 0x8c, 0x8c, 0x88, 0x80, 0x75, 0x69, 0x60, 0x59, 0x56,
0x54, 0x55, 0x58, 0x5a, 0x5c, 0x60, 0x65, 0x6d, 0x77, 0x80, 0x89, 0x91, 0x96, 0x98, 0x97, 0x98,
0x99, 0x9a, 0x9d, 0xa1, 0xa4, 0xa4, 0xa1, 0x9c, 0x94, 0x8b, 0x86, 0x83, 0x81, 0x81, 0x81, 0x7f,
0x7c, 0x7a, 0x77, 0x77, 0x79, 0x7d, 0x82, 0x86, 0x89, 0x89, 0x85, 0x81, 0x7e, 0x7a, 0x77, 0x76,
0x77, 0x75, 0x74, 0x73, 0x70, 0x6d, 0x6f, 0x74, 0x79, 0x7e, 0x82, 0x87, 0x89, 0x89, 0x88, 0x88,
0x8b, 0x91, 0x95, 0x9a, 0x9d, 0x9c, 0x96, 0x8f, 0x86, 0x7f, 0x79, 0x75, 0x70, 0x69, 0x64, 0x5d,
0x55, 0x4f, 0x4c, 0x4c, 0x4e, 0x52, 0x58, 0x5e, 0x65, 0x6d, 0x75, 0x7e, 0x88, 0x94, 0xa2, 0xae,
0xb6, 0xbb, 0xbd, 0xb9, 0xb3, 0xac, 0xa5, 0x9f, 0x99, 0x93, 0x8a, 0x7d, 0x6e, 0x61, 0x57, 0x51,
0x4e, 0x50, 0x54, 0x5b, 0x62, 0x68, 0x6d, 0x71, 0x78, 0x81, 0x8b, 0x93, 0x9b, 0xa2, 0xa7, 0xaa,
0xad, 0xad, 0xad, 0xaf, 0xaf, 0xad, 0xa7, 0x9e, 0x92, 0x85, 0x7b, 0x74, 0x70, 0x6f, 0x6f, 0x70,
0x72, 0x71, 0x6e, 0x6b, 0x6c, 0x6e, 0x70, 0x71, 0x72, 0x73, 0x72, 0x6f, 0x6d, 0x6a, 0x67, 0x67,
0x67, 0x66, 0x65, 0x62, 0x5e, 0x5c, 0x5b, 0x5d, 0x63, 0x6c, 0x77, 0x81, 0x8c, 0x96, 0x9e, 0xa2,
0xa5, 0xa9, 0xae, 0xb1, 0xb2, 0xb0, 0xab, 0xa4, 0x9c, 0x93, 0x8a, 0x83, 0x7e, 0x79, 0x73, 0x6e,
0x66, 0x5e, 0x58, 0x55, 0x55, 0x58, 0x5e, 0x66, 0x70, 0x7c, 0x86, 0x90, 0x9b, 0xa5, 0xaf, 0xb8,
0xbe, 0xc1, 0xbe, 0xb9, 0xb1, 0xa7, 0x9f, 0x97, 0x8e, 0x85, 0x7f, 0x74, 0x6a, 0x5e, 0x52, 0x48,
0x42, 0x3f, 0x41, 0x45, 0x4b, 0x54, 0x5e, 0x67, 0x6e, 0x74, 0x7b, 0x82, 0x88, 0x8c, 0x8f, 0x91,
0x91, 0x92, 0x91, 0x91, 0x92, 0x92, 0x92, 0x90, 0x8d, 0x88, 0x83, 0x80, 0x7d, 0x79, 0x79, 0x7b,
0x7c, 0x7f, 0x80, 0x80, 0x80, 0x80, 0x80, 0x81, 0x83, 0x84, 0x84, 0x83, 0x80, 0x7e, 0x7b, 0x78,
0x74, 0x71, 0x72, 0x71, 0x70, 0x70, 0x71, 0x72, 0x74, 0x7a, 0x80, 0x86, 0x8d, 0x94, 0x9a, 0x9d,
0xa1, 0xa4, 0xa5, 0xa5, 0xa6, 0xa5, 0xa3, 0x9f, 0x99, 0x90, 0x86, 0x7e, 0x75, 0x6e, 0x67, 0x60,
0x5e, 0x5c, 0x57, 0x53, 0x52, 0x53, 0x57, 0x5d, 0x63, 0x6c, 0x74, 0x7e, 0x87, 0x8f, 0x96, 0x9f,
0xa7, 0xae, 0xb0, 0xb0, 0xaf, 0xaa, 0xa3, 0x9a, 0x90, 0x85, 0x7c, 0x72, 0x69, 0x60, 0x57, 0x4f,
0x47, 0x44, 0x46, 0x4c, 0x52, 0x5a, 0x65, 0x70, 0x79, 0x80, 0x88, 0x8e, 0x95, 0x9c, 0xa1, 0xa5,
0xa8, 0xa9, 0xaa, 0xa9, 0xa6, 0xa3, 0x9c, 0x95, 0x91, 0x8c, 0x85, 0x80, 0x7c, 0x77, 0x74, 0x73,
0x74, 0x75, 0x76, 0x78, 0x79, 0x7a, 0x7a, 0x7a, 0x7c, 0x7c, 0x7d, 0x80, 0x80, 0x7f, 0x7d, 0x7a,
0x75, 0x6f, 0x6b, 0x68, 0x66, 0x65, 0x67, 0x69, 0x6c, 0x6f, 0x76, 0x7d, 0x83, 0x8c, 0x96, 0x9e,
0xa5, 0xa9, 0xab, 0xaa, 0xa8, 0xa5, 0xa2, 0x9d, 0x98, 0x91, 0x87, 0x7e, 0x73, 0x6a, 0x61, 0x59,
0x54, 0x53, 0x54, 0x54, 0x55, 0x57, 0x5b, 0x61, 0x69, 0x73, 0x7e, 0x88, 0x96, 0xa3, 0xac, 0xb2,
0xb7, 0xb7, 0xb8, 0xb9, 0xb6, 0xb0, 0xa9, 0xa0, 0x93, 0x86, 0x79, 0x6c, 0x60, 0x56, 0x50, 0x49,
0x43, 0x40, 0x40, 0x42, 0x47, 0x4f, 0x5b, 0x66, 0x71, 0x7d, 0x84, 0x88, 0x8c, 0x91, 0x94, 0x96,
0x9b, 0x9f, 0xa1, 0xa1, 0x9f, 0x9c, 0x97, 0x92, 0x8f, 0x8e, 0x8c, 0x8b, 0x8b, 0x8a, 0x88, 0x87,
0x88, 0x89, 0x89, 0x89, 0x8a, 0x89, 0x89, 0x87, 0x84, 0x7f, 0x7c, 0x7c, 0x7c, 0x7a, 0x78, 0x76,
0x71, 0x6c, 0x68, 0x63, 0x5f, 0x5f, 0x62, 0x67, 0x6b, 0x6f, 0x75, 0x7a, 0x7f, 0x85, 0x8d, 0x94,
0x9b, 0xa0, 0xa4, 0xa4, 0xa0, 0x9d, 0x99, 0x93, 0x8e, 0x89, 0x82, 0x7b, 0x73, 0x6c, 0x63, 0x5b,
0x56, 0x54, 0x54, 0x57, 0x5a, 0x5d, 0x60, 0x63, 0x67, 0x6c, 0x74, 0x7e, 0x89, 0x94, 0x9f, 0xa7,
0xab, 0xad, 0xad, 0xad, 0xae, 0xad, 0xa9, 0xa4, 0x9c, 0x92, 0x89, 0x80, 0x76, 0x6d, 0x67, 0x62,
0x5f, 0x5c, 0x5a, 0x5a, 0x5b, 0x5d, 0x65, 0x70, 0x7b, 0x84, 0x8e, 0x96, 0x9a, 0x9b, 0x9b, 0x9b,
0x9c, 0x9e, 0xa0, 0x9e, 0x9b, 0x96, 0x90, 0x89, 0x82, 0x7f, 0x7d, 0x7b, 0x7a, 0x7a, 0x78, 0x75,
0x71, 0x6e, 0x6d, 0x6d, 0x6f, 0x71, 0x73, 0x74, 0x77, 0x76, 0x74, 0x74, 0x76, 0x78, 0x79, 0x78,
0x75, 0x71, 0x6b, 0x67, 0x64, 0x63, 0x65, 0x6a, 0x72, 0x79, 0x80, 0x83, 0x86, 0x88, 0x8b, 0x91,
0x96, 0x9a, 0x9f, 0xa3, 0xa4, 0xa3, 0xa0, 0x9b, 0x96, 0x91, 0x8c, 0x85, 0x7d, 0x74, 0x6b, 0x63,
0x5d, 0x5b, 0x5d, 0x62, 0x69, 0x73, 0x7b, 0x80, 0x84, 0x86, 0x8a, 0x91, 0x9a, 0xa3, 0xab, 0xb2,
0xb7, 0xb9, 0xb8, 0xb5, 0xb2, 0xae, 0xa8, 0xa0, 0x96, 0x89, 0x7b, 0x6d, 0x5e, 0x52, 0x48, 0x41,
0x3e, 0x3f, 0x40, 0x42, 0x44, 0x48, 0x4d, 0x54, 0x5f, 0x69, 0x73, 0x7b, 0x80, 0x84, 0x86, 0x88,
0x8b, 0x90, 0x94, 0x98, 0x9a, 0x99, 0x96, 0x90, 0x8c, 0x87, 0x84, 0x83, 0x85, 0x89, 0x8e, 0x91,
0x91, 0x91, 0x8f, 0x8f, 0x8e, 0x8c, 0x8a, 0x88, 0x83, 0x80, 0x7c, 0x77, 0x76, 0x77, 0x78, 0x7a,
0x7c, 0x7a, 0x76, 0x71, 0x6d, 0x6b, 0x6b, 0x6d, 0x74, 0x7d, 0x86, 0x8e, 0x93, 0x97, 0x9b, 0x9f,
0xa3, 0xa6, 0xa8, 0xa8, 0xa6, 0xa1, 0x9a, 0x91, 0x89, 0x82, 0x7d, 0x76, 0x71, 0x6b, 0x64, 0x5b,
0x54, 0x51, 0x4f, 0x51, 0x57, 0x5e, 0x63, 0x69, 0x6e, 0x70, 0x72, 0x76, 0x7f, 0x87, 0x8f, 0x96,
0x9c, 0xa0, 0xa0, 0x9c, 0x99, 0x95, 0x92, 0x8e, 0x8b, 0x87, 0x83, 0x7d, 0x76, 0x71, 0x6d, 0x69,
0x69, 0x69, 0x69, 0x68, 0x68, 0x69, 0x6d, 0x71, 0x78, 0x80, 0x8b, 0x95, 0x9b, 0x9f, 0x9f, 0x9b,
0x97, 0x93, 0x91, 0x91, 0x91, 0x90, 0x8e, 0x8b, 0x86, 0x82, 0x80, 0x7f, 0x7f, 0x80, 0x81, 0x81,
0x81, 0x80, 0x7d, 0x7c, 0x7c, 0x7c, 0x7c, 0x7e, 0x7f, 0x80, 0x7f, 0x7c, 0x79, 0x79, 0x78, 0x79,
0x7a, 0x79, 0x77, 0x74, 0x6f, 0x6c, 0x6b, 0x6d, 0x73, 0x7b, 0x82, 0x8a, 0x8d, 0x8e, 0x8c, 0x8a,
0x89, 0x89, 0x89, 0x89, 0x8a, 0x8a, 0x89, 0x87, 0x83, 0x7f, 0x7a, 0x76, 0x71, 0x6c, 0x66, 0x60,
0x5a, 0x58, 0x57, 0x5d, 0x67, 0x73, 0x80, 0x8a, 0x92, 0x97, 0x99, 0x99, 0x9b, 0x9d, 0xa0, 0xa6,
0xab, 0xaf, 0xb0, 0xae, 0xac, 0xa7, 0xa1, 0x9a, 0x92, 0x88, 0x7e, 0x72, 0x67, 0x5d, 0x55, 0x50,
0x4e, 0x50, 0x52, 0x56, 0x5a, 0x5c, 0x5e, 0x63, 0x66, 0x6b, 0x74, 0x7c, 0x82, 0x89, 0x8e, 0x90,
0x90, 0x8f, 0x90, 0x92, 0x96, 0x98, 0x99, 0x97, 0x92, 0x8c, 0x87, 0x83, 0x80, 0x81, 0x85, 0x89,
0x8c, 0x8e, 0x8e, 0x8e, 0x8d, 0x8a, 0x87, 0x83, 0x7f, 0x7b, 0x76, 0x71, 0x6c, 0x69, 0x69, 0x6c,
0x71, 0x75, 0x77, 0x78, 0x76, 0x73, 0x70, 0x6e, 0x6e, 0x74, 0x7d, 0x85, 0x90, 0x96, 0x99, 0x9c,
0x9f, 0x9e, 0x9d, 0x9c, 0x97, 0x92, 0x8d, 0x87, 0x80, 0x7c, 0x76, 0x73, 0x72, 0x70, 0x6e, 0x6a,
0x64, 0x5e, 0x5a, 0x58, 0x58, 0x5d, 0x65, 0x6f, 0x77, 0x7d, 0x81, 0x84, 0x86, 0x89, 0x8d, 0x90,
0x95, 0x9a, 0x9c, 0x9c, 0x9b, 0x97, 0x93, 0x90, 0x8e, 0x8c, 0x89, 0x85, 0x81, 0x7f, 0x7c, 0x79,
0x76, 0x76, 0x77, 0x78, 0x78, 0x79, 0x79, 0x78, 0x7a, 0x7f, 0x83, 0x8b, 0x91, 0x95, 0x98, 0x97,
0x93, 0x8c, 0x85, 0x80, 0x7f, 0x7f, 0x7f, 0x7f, 0x7d, 0x7c, 0x79, 0x77, 0x75, 0x73, 0x74, 0x76,
0x77, 0x77, 0x77, 0x77, 0x77, 0x78, 0x7a, 0x7c, 0x7c, 0x7e, 0x7f, 0x7e, 0x7c, 0x79, 0x75, 0x74,
0x76, 0x7a, 0x7d, 0x7f, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x83, 0x88, 0x8d, 0x90, 0x90, 0x90,
0x8d, 0x89, 0x87, 0x85, 0x83, 0x81, 0x80, 0x7e, 0x7d, 0x79, 0x74, 0x72, 0x71, 0x72, 0x73, 0x73,
0x70, 0x6d, 0x6d, 0x6e, 0x71, 0x77, 0x80, 0x8b, 0x95, 0x9f, 0xa4, 0xa6, 0xa4, 0xa1, 0x9f, 0x9e,
0x9d, 0x9d, 0x9f, 0xa0, 0xa2, 0xa0, 0x99, 0x92, 0x8b, 0x85, 0x7f, 0x77, 0x6e, 0x64, 0x5c, 0x58,
0x55, 0x53, 0x54, 0x59, 0x5e, 0x62, 0x64, 0x64, 0x64, 0x65, 0x67, 0x6b, 0x70, 0x77, 0x7d, 0x82,
0x87, 0x88, 0x87, 0x86, 0x87, 0x89, 0x8d, 0x8e, 0x8e, 0x8d, 0x8b, 0x87, 0x85, 0x83, 0x82, 0x85,
0x87, 0x8a, 0x8b, 0x8c, 0x8a, 0x89, 0x87, 0x85, 0x81, 0x80, 0x7e, 0x7a, 0x77, 0x74, 0x71, 0x71,
0x73, 0x78, 0x7f, 0x84, 0x89, 0x8b, 0x8b, 0x89, 0x88, 0x87, 0x87, 0x8a, 0x90, 0x97, 0x9c, 0x9e,
0xa0, 0xa0, 0x9d, 0x99, 0x94, 0x8e, 0x86, 0x80, 0x79, 0x73, 0x6d, 0x6b, 0x6a, 0x6b, 0x6e, 0x71,
0x71, 0x6d, 0x69, 0x64, 0x60, 0x5e, 0x5f, 0x62, 0x69, 0x71, 0x77, 0x7c, 0x80, 0x81, 0x82, 0x81,
0x81, 0x81, 0x81, 0x82, 0x85, 0x87, 0x87, 0x86, 0x84, 0x83, 0x84, 0x87, 0x87, 0x84, 0x82, 0x82,
0x80, 0x7f, 0x7e, 0x7f, 0x81, 0x84, 0x85, 0x86, 0x86, 0x85, 0x86, 0x88, 0x8a, 0x8e, 0x91, 0x92,
0x92, 0x90, 0x8b, 0x86, 0x80, 0x7e, 0x7d, 0x7f, 0x80, 0x80, 0x80, 0x80, 0x7f, 0x7e, 0x7b, 0x79,
0x78, 0x79, 0x79, 0x78, 0x78, 0x79, 0x79, 0x7b, 0x7e, 0x80, 0x80, 0x82, 0x81, 0x7f, 0x7d, 0x7b,
0x79, 0x79, 0x7d, 0x83, 0x8a, 0x8d, 0x8d, 0x8f, 0x90, 0x8e, 0x8a, 0x87, 0x86, 0x85, 0x85, 0x84,
0x82, 0x80, 0x7d, 0x7a, 0x76, 0x72, 0x70, 0x6d, 0x69, 0x66, 0x63, 0x60, 0x60, 0x63, 0x68, 0x6e,
0x74, 0x77, 0x78, 0x78, 0x79, 0x7b, 0x7f, 0x81, 0x89, 0x91, 0x96, 0x9c, 0xa0, 0xa0, 0x9f, 0x9e,
0x9b, 0x9a, 0x9a, 0x98, 0x97, 0x98, 0x98, 0x95, 0x91, 0x8d, 0x89, 0x85, 0x81, 0x7b, 0x74, 0x6e,
0x6a, 0x67, 0x65, 0x67, 0x6a, 0x6e, 0x71, 0x73, 0x73, 0x71, 0x71, 0x72, 0x74, 0x77, 0x7b, 0x7f,
0x82, 0x86, 0x88, 0x88, 0x88, 0x88, 0x88, 0x89, 0x8a, 0x8b, 0x8a, 0x88, 0x87, 0x85, 0x83, 0x82,
0x81, 0x80, 0x80, 0x7e, 0x7b, 0x78, 0x75, 0x74, 0x74, 0x74, 0x74, 0x74, 0x73, 0x73, 0x73, 0x73,
0x73, 0x75, 0x7a, 0x80, 0x86, 0x8b, 0x8f, 0x91, 0x91, 0x90, 0x8e, 0x8d, 0x8d, 0x8e, 0x8f, 0x91,
0x93, 0x93, 0x91, 0x90, 0x8e, 0x8a, 0x85, 0x80, 0x7c, 0x76, 0x72, 0x6e, 0x6c, 0x6d, 0x70, 0x76,
0x7b, 0x7e, 0x7d, 0x7a, 0x76, 0x71, 0x6d, 0x6a, 0x6b, 0x6e, 0x72, 0x76, 0x7a, 0x7e, 0x80, 0x80,
0x80, 0x80, 0x80, 0x80, 0x81, 0x84, 0x86, 0x87, 0x8a, 0x8c, 0x8e, 0x91, 0x93, 0x94, 0x92, 0x8e,
0x89, 0x86, 0x82, 0x80, 0x80, 0x83, 0x85, 0x86, 0x87, 0x86, 0x84, 0x82, 0x83, 0x84, 0x84, 0x84,
0x85, 0x85, 0x85, 0x84, 0x80, 0x7c, 0x7a, 0x79, 0x79, 0x7a, 0x7b, 0x7a, 0x78, 0x76, 0x73, 0x70,
0x6e, 0x6c, 0x6a, 0x6a, 0x6a, 0x69, 0x69, 0x6c, 0x71, 0x76, 0x7c, 0x81, 0x85, 0x88, 0x8b, 0x8c,
0x8a, 0x8a, 0x8b, 0x8e, 0x93, 0x98, 0x9b, 0x9d, 0x9d, 0x9c, 0x97, 0x90, 0x8b, 0x88, 0x84, 0x80,
0x7f, 0x7b, 0x77, 0x76, 0x76, 0x75, 0x73, 0x72, 0x70, 0x6e, 0x6a, 0x67, 0x66, 0x67, 0x6a, 0x6f,
0x77, 0x7d, 0x81, 0x84, 0x85, 0x85, 0x83, 0x81, 0x82, 0x83, 0x85, 0x8a, 0x8e, 0x91, 0x92, 0x92,
0x93, 0x93, 0x92, 0x92, 0x92, 0x92, 0x93, 0x92, 0x90, 0x8f, 0x8e, 0x8c, 0x8a, 0x86, 0x80, 0x7a,
0x74, 0x6e, 0x6a, 0x65, 0x64, 0x66, 0x68, 0x6a, 0x6d, 0x6e, 0x6f, 0x70, 0x6f, 0x6f, 0x71, 0x74,
0x79, 0x7e, 0x81, 0x85, 0x89, 0x88, 0x88, 0x89, 0x8a, 0x8b, 0x8a, 0x89, 0x88, 0x85, 0x82, 0x80,
0x7f, 0x7c, 0x7b, 0x79, 0x77, 0x75, 0x72, 0x71, 0x71, 0x71, 0x73, 0x76, 0x79, 0x7d, 0x82, 0x87,
0x88, 0x89, 0x8b, 0x8d, 0x90, 0x95, 0x99, 0x9b, 0x9d, 0x9d, 0x9b, 0x97, 0x93, 0x8f, 0x8c, 0x8a,
0x89, 0x89, 0x87, 0x85, 0x84, 0x83, 0x81, 0x80, 0x7e, 0x7b, 0x78, 0x75, 0x74, 0x73, 0x72, 0x73,
0x77, 0x7b, 0x7e, 0x7f, 0x7e, 0x7a, 0x76, 0x6f, 0x67, 0x62, 0x60, 0x61, 0x64, 0x67, 0x6a, 0x6d,
0x70, 0x73, 0x74, 0x75, 0x78, 0x7c, 0x80, 0x83, 0x87, 0x8b, 0x8e, 0x92, 0x96, 0x9a, 0x9b, 0x99,
0x96, 0x92, 0x8d, 0x87, 0x81, 0x7f, 0x7e, 0x7f, 0x81, 0x84, 0x86, 0x88, 0x88, 0x88, 0x87, 0x87,
0x87, 0x89, 0x8b, 0x8c, 0x8c, 0x8b, 0x88, 0x85, 0x85, 0x84, 0x83, 0x80, 0x7d, 0x7a, 0x77, 0x72,
0x6b, 0x67, 0x65, 0x63, 0x63, 0x64, 0x65, 0x68, 0x6b, 0x70, 0x76, 0x7c, 0x81, 0x88, 0x8f, 0x96,
0x9a, 0x9b, 0x9c, 0x9c, 0x9b, 0x9d, 0x9e, 0x9d, 0x9d, 0x9b, 0x96, 0x90, 0x89, 0x80, 0x78, 0x73,
0x70, 0x6c, 0x6a, 0x69, 0x68, 0x69, 0x6b, 0x6b, 0x6b, 0x6d, 0x6d, 0x6d, 0x6d, 0x6d, 0x6d, 0x6f,
0x71, 0x75, 0x7a, 0x7d, 0x80, 0x83, 0x84, 0x82, 0x7f, 0x7b, 0x78, 0x78, 0x7a, 0x7f, 0x83, 0x88,
0x8c, 0x8f, 0x93, 0x96, 0x99, 0x9c, 0x9d, 0x9d, 0x9f, 0x9f, 0x9e, 0x9c, 0x99, 0x98, 0x95, 0x8f,
0x89, 0x83, 0x7d, 0x76, 0x70, 0x6a, 0x66, 0x65, 0x67, 0x6a, 0x6e, 0x73, 0x76, 0x77, 0x78, 0x79,
0x7a, 0x7d, 0x80, 0x84, 0x88, 0x8a, 0x8b, 0x8a, 0x88, 0x87, 0x86, 0x84, 0x81, 0x80, 0x7c, 0x78,
0x74, 0x6f, 0x6b, 0x68, 0x65, 0x66, 0x67, 0x69, 0x69, 0x6b, 0x6e, 0x71, 0x74, 0x7b, 0x80, 0x86,
0x8d, 0x93, 0x95, 0x96, 0x97, 0x97, 0x98, 0x99, 0x9a, 0x9a, 0x99, 0x95, 0x90, 0x8b, 0x85, 0x81,
0x7f, 0x7f, 0x7f, 0x7f, 0x80, 0x80, 0x80, 0x80, 0x81, 0x81, 0x82, 0x83, 0x83, 0x83, 0x82, 0x81,
0x80, 0x80, 0x80, 0x81, 0x80, 0x80, 0x7e, 0x79, 0x72, 0x6a, 0x63, 0x5d, 0x59, 0x5a, 0x5e, 0x63,
0x67, 0x6c, 0x73, 0x78, 0x7c, 0x80, 0x85, 0x8a, 0x8e, 0x93, 0x97, 0x9a, 0x9c, 0x9f, 0xa1, 0xa0,
0x9d, 0x99, 0x94, 0x8d, 0x86, 0x80, 0x7a, 0x75, 0x74, 0x76, 0x78, 0x7b, 0x80, 0x82, 0x84, 0x85,
0x85, 0x85, 0x85, 0x87, 0x88, 0x89, 0x8a, 0x88, 0x85, 0x83, 0x80, 0x7e, 0x7a, 0x76, 0x71, 0x6c,
0x67, 0x61, 0x5b, 0x57, 0x55, 0x55, 0x59, 0x5e, 0x64, 0x6a, 0x72, 0x7a, 0x80, 0x86, 0x8f, 0x96,
0x9d, 0xa4, 0xaa, 0xac, 0xac, 0xac, 0xac, 0xaa, 0xa6, 0xa1, 0x9c, 0x96, 0x8e, 0x84, 0x7d, 0x75,
0x6e, 0x6a, 0x69, 0x68, 0x6a, 0x6b, 0x6c, 0x6e, 0x71, 0x72, 0x73, 0x75, 0x77, 0x79, 0x7b, 0x7c,
0x7c, 0x7b, 0x7c, 0x7d, 0x7c, 0x7d, 0x7c, 0x7b, 0x79, 0x76, 0x72, 0x6f, 0x6c, 0x6c, 0x70, 0x77,
0x7d, 0x83, 0x8a, 0x90, 0x96, 0x9a, 0x9d, 0xa0, 0xa2, 0xa4, 0xa5, 0xa5, 0xa3, 0xa0, 0x9c, 0x98,
0x92, 0x8b, 0x84, 0x7d, 0x74, 0x6d, 0x66, 0x62, 0x60, 0x5e, 0x61, 0x67, 0x6c, 0x71, 0x76, 0x78,
0x7c, 0x80, 0x81, 0x82, 0x85, 0x87, 0x88, 0x89, 0x88, 0x86, 0x85, 0x84, 0x81, 0x7f, 0x7b, 0x77,
0x73, 0x6e, 0x68, 0x64, 0x62, 0x61, 0x61, 0x65, 0x6a, 0x6f, 0x74, 0x7a, 0x7f, 0x83, 0x88, 0x8d,
0x92, 0x97, 0x9b, 0x9f, 0xa1, 0xa3, 0xa4, 0xa4, 0xa1, 0x9f, 0x9b, 0x94, 0x8f, 0x8a, 0x84, 0x80,
0x7c, 0x7a, 0x79, 0x7a, 0x7c, 0x7f, 0x80, 0x82, 0x85, 0x86, 0x86, 0x86, 0x87, 0x87, 0x86, 0x85,
0x84, 0x83, 0x81, 0x80, 0x7e, 0x7b, 0x76, 0x71, 0x6b, 0x65, 0x5e, 0x59, 0x55, 0x53, 0x52, 0x53,
0x57, 0x5e, 0x65, 0x6c, 0x74, 0x7c, 0x81, 0x86, 0x8a, 0x90, 0x96, 0x9a, 0x9e, 0xa0, 0xa2, 0xa2,
0x9f, 0x9d, 0x99, 0x92, 0x8c, 0x87, 0x82, 0x7e, 0x79, 0x77, 0x77, 0x78, 0x7b, 0x7e, 0x82, 0x86,
0x89, 0x8d, 0x8e, 0x8e, 0x8e, 0x8e, 0x8d, 0x8c, 0x8b, 0x88, 0x85, 0x81, 0x7d, 0x79, 0x75, 0x6f,
0x6a, 0x66, 0x61, 0x5b, 0x58, 0x57, 0x57, 0x5a, 0x5f, 0x67, 0x70, 0x79, 0x80, 0x89, 0x92, 0x9a,
0xa0, 0xa6, 0xac, 0xaf, 0xb1, 0xb2, 0xb1, 0xae, 0xaa, 0xa5, 0x9e, 0x94, 0x8a, 0x82, 0x7a, 0x72,
0x6a, 0x64, 0x61, 0x60, 0x5f, 0x63, 0x67, 0x6a, 0x6d, 0x70, 0x73, 0x76, 0x77, 0x78, 0x79, 0x7a,
0x7a, 0x7b, 0x7a, 0x79, 0x78, 0x76, 0x73, 0x70, 0x6c, 0x68, 0x65, 0x65, 0x65, 0x67, 0x6b, 0x71,
0x78, 0x7e, 0x85, 0x8d, 0x95, 0x9c, 0xa3, 0xa8, 0xab, 0xae, 0xb0, 0xb1, 0xb0, 0xae, 0xaa, 0xa6,
0xa0, 0x99, 0x91, 0x87, 0x7f, 0x77, 0x70, 0x69, 0x65, 0x65, 0x66, 0x67, 0x6b, 0x6f, 0x74, 0x79,
0x7e, 0x80, 0x84, 0x87, 0x89, 0x89, 0x87, 0x84, 0x84, 0x83, 0x81, 0x7f, 0x7e, 0x7a, 0x76, 0x72,
0x6c, 0x67, 0x63, 0x5e, 0x5c, 0x5b, 0x5c, 0x60, 0x63, 0x68, 0x6f, 0x75, 0x7c, 0x82, 0x89, 0x8f,
0x93, 0x96, 0x98, 0x98, 0x99, 0x9b, 0x9c, 0x9d, 0x9d, 0x9c, 0x99, 0x94, 0x8c, 0x85, 0x80, 0x7a,
0x76, 0x74, 0x74, 0x74, 0x76, 0x7b, 0x81, 0x86, 0x8b, 0x90, 0x92, 0x94, 0x94, 0x92, 0x8f, 0x8d,
0x8a, 0x87, 0x85, 0x84, 0x82, 0x80, 0x7b, 0x74, 0x6d, 0x65, 0x5d, 0x57, 0x53, 0x51, 0x52, 0x55,
0x5a, 0x5e, 0x63, 0x6a, 0x72, 0x7a, 0x81, 0x88, 0x8f, 0x94, 0x9a, 0x9e, 0xa0, 0xa2, 0xa3, 0xa3,
0xa3, 0xa0, 0x9d, 0x99, 0x92, 0x8c, 0x88, 0x81, 0x7e, 0x7b, 0x79, 0x79, 0x79, 0x79, 0x7c, 0x7f,
0x81, 0x84, 0x88, 0x8c, 0x8e, 0x8d, 0x8d, 0x8a, 0x84, 0x80, 0x7c, 0x78, 0x74, 0x71, 0x6e, 0x6b,
0x66, 0x61, 0x5d, 0x5a, 0x57, 0x56, 0x57, 0x59, 0x5d, 0x62, 0x68, 0x71, 0x7c, 0x85, 0x90, 0x9c,
0xa6, 0xac, 0xb1, 0xb5, 0xb5, 0xb3, 0xb0, 0xae, 0xac, 0xa9, 0xa3, 0x9c, 0x93, 0x87, 0x7e, 0x74,
0x6b, 0x64, 0x60, 0x5f, 0x60, 0x61, 0x65, 0x6a, 0x6f, 0x74, 0x78, 0x7c, 0x80, 0x82, 0x82, 0x82,
0x80, 0x7f, 0x7c, 0x7b, 0x7b, 0x7b, 0x7a, 0x77, 0x71, 0x6a, 0x65, 0x60, 0x5e, 0x5f, 0x64, 0x6c,
0x74, 0x7b, 0x82, 0x89, 0x8e, 0x94, 0x9b, 0xa1, 0xa6, 0xab, 0xae, 0xb0, 0xb2, 0xae, 0xa9, 0xa6,
0xa0, 0x9b, 0x96, 0x8e, 0x84, 0x7b, 0x71, 0x69, 0x64, 0x62, 0x62, 0x64, 0x69, 0x6d, 0x70, 0x72,
0x75, 0x79, 0x7d, 0x81, 0x85, 0x88, 0x89, 0x88, 0x86, 0x83, 0x7e, 0x7a, 0x78, 0x77, 0x76, 0x72,
0x6e, 0x6a, 0x64, 0x5f, 0x5c, 0x5a, 0x5c, 0x5e, 0x64, 0x6a, 0x6f, 0x74, 0x7b, 0x81, 0x89, 0x90,
0x98, 0x9e, 0xa0, 0xa2, 0xa2, 0x9e, 0x9b, 0x98, 0x97, 0x99, 0x9a, 0x98, 0x95, 0x90, 0x8a, 0x82,
0x7b, 0x76, 0x74, 0x75, 0x79, 0x7e, 0x83, 0x88, 0x8e, 0x93, 0x96, 0x99, 0x9b, 0x9b, 0x98, 0x93,
0x8d, 0x88, 0x81, 0x7b, 0x79, 0x78, 0x78, 0x75, 0x6f, 0x68, 0x5f, 0x55, 0x4e, 0x49, 0x48, 0x4a,
0x4e, 0x55, 0x5e, 0x65, 0x6b, 0x71, 0x77, 0x7e, 0x83, 0x8a, 0x90, 0x95, 0x9a, 0x9d, 0x9d, 0x9c,
0x9c, 0x9d, 0xa1, 0xa1, 0x9c, 0x97, 0x92, 0x8c, 0x86, 0x80, 0x7d, 0x7d, 0x7f, 0x80, 0x80, 0x80,
0x80, 0x80, 0x81, 0x83, 0x85, 0x87, 0x89, 0x88, 0x87, 0x84, 0x7f, 0x78, 0x71, 0x6d, 0x6c, 0x6b,
0x6a, 0x69, 0x66, 0x64, 0x61, 0x5f, 0x5f, 0x62, 0x67, 0x6d, 0x74, 0x79, 0x7f, 0x86, 0x8f, 0x98,
0xa2, 0xac, 0xb3, 0xb9, 0xbc, 0xb9, 0xb4, 0xac, 0xa4, 0xa0, 0x9c, 0x97, 0x93, 0x8d, 0x84, 0x7d,
0x73, 0x68, 0x60, 0x5c, 0x5a, 0x5b, 0x5e, 0x62, 0x65, 0x69, 0x6d, 0x72, 0x76, 0x7b, 0x7f, 0x81,
0x80, 0x7d, 0x7a, 0x75, 0x71, 0x71, 0x71, 0x72, 0x73, 0x71, 0x6d, 0x69, 0x65, 0x61, 0x61, 0x64,
0x6a, 0x72, 0x7b, 0x83, 0x8a, 0x8f, 0x94, 0x98, 0x9c, 0xa2, 0xa6, 0xab, 0xaf, 0xaf, 0xaf, 0xaa,
0xa4, 0xa0, 0x9c, 0x98, 0x93, 0x8c, 0x84, 0x7c, 0x74, 0x6d, 0x6b, 0x6b, 0x6e, 0x73, 0x79, 0x7d,
0x7f, 0x7f, 0x7f, 0x80, 0x82, 0x84, 0x86, 0x89, 0x8b, 0x8a, 0x86, 0x80, 0x7a, 0x75, 0x6f, 0x6c,
0x6a, 0x67, 0x63, 0x60, 0x5c, 0x59, 0x58, 0x59, 0x5e, 0x64, 0x6c, 0x73, 0x78, 0x7d, 0x81, 0x86,
0x8d, 0x94, 0x9a, 0x9e, 0xa1, 0xa1, 0x9d, 0x98, 0x93, 0x8f, 0x8d, 0x8c, 0x8b, 0x8a, 0x88, 0x85,
0x80, 0x7c, 0x77, 0x75, 0x76, 0x79, 0x7f, 0x84, 0x8a, 0x90, 0x94, 0x96, 0x98, 0x9a, 0x9a, 0x98,
0x95, 0x8e, 0x88, 0x80, 0x79, 0x74, 0x71, 0x6f, 0x6f, 0x6e, 0x6b, 0x65, 0x5d, 0x57, 0x51, 0x4e,
0x4f, 0x53, 0x5a, 0x62, 0x6b, 0x73, 0x7b, 0x80, 0x86, 0x8a, 0x90, 0x95, 0x99, 0x9d, 0xa0, 0xa0,
0x9f, 0xa0, 0x9f, 0x9f, 0xa0, 0xa0, 0x9e, 0x98, 0x91, 0x8a, 0x84, 0x80, 0x7e, 0x7e, 0x7f, 0x80,
0x80, 0x80, 0x7f, 0x7e, 0x7d, 0x7d, 0x7f, 0x7f, 0x7e, 0x7d, 0x7b, 0x76, 0x71, 0x6a, 0x63, 0x60,
0x5f, 0x60, 0x61, 0x63, 0x63, 0x63, 0x62, 0x62, 0x66, 0x6b, 0x72, 0x79, 0x80, 0x86, 0x8d, 0x93,
0x9a, 0xa3, 0xac, 0xb3, 0xb7, 0xb9, 0xb7, 0xb1, 0xa9, 0xa1, 0x99, 0x91, 0x8b, 0x88, 0x84, 0x80,
0x7c, 0x75, 0x6d, 0x65, 0x60, 0x5c, 0x5c, 0x5f, 0x62, 0x65, 0x69, 0x6c, 0x71, 0x77, 0x7e, 0x82,
0x86, 0x88, 0x87, 0x85, 0x80, 0x7c, 0x79, 0x78, 0x78, 0x79, 0x79, 0x78, 0x76, 0x72, 0x6e, 0x6c,
0x6c, 0x6d, 0x72, 0x79, 0x7f, 0x85, 0x8d, 0x91, 0x94, 0x97, 0x9b, 0x9f, 0xa3, 0xa6, 0xa7, 0xa6,
0xa3, 0x9f, 0x9a, 0x96, 0x92, 0x8e, 0x8a, 0x84, 0x7f, 0x78, 0x73, 0x6f, 0x6e, 0x70, 0x72, 0x76,
0x79, 0x7c, 0x7e, 0x7e, 0x7e, 0x7e, 0x7f, 0x80, 0x81, 0x81, 0x81, 0x80, 0x7d, 0x77, 0x71, 0x6a,
0x64, 0x61, 0x5f, 0x5d, 0x5b, 0x58, 0x58, 0x59, 0x5b, 0x60, 0x68, 0x6f, 0x76, 0x7d, 0x82, 0x86,
0x8b, 0x91, 0x96, 0x9b, 0xa0, 0xa3, 0xa4, 0xa4, 0xa3, 0x9f, 0x9b, 0x96, 0x93, 0x92, 0x91, 0x90,
0x8f, 0x8c, 0x89, 0x86, 0x82, 0x80, 0x80, 0x82, 0x85, 0x88, 0x8b, 0x8e, 0x8f, 0x90, 0x91, 0x92,
0x90, 0x8d, 0x89, 0x85, 0x80, 0x79, 0x74, 0x70, 0x6c, 0x6a, 0x68, 0x67, 0x65, 0x61, 0x5d, 0x58,
0x54, 0x52, 0x53, 0x58, 0x5f, 0x67, 0x6f, 0x77, 0x7c, 0x80, 0x85, 0x8a, 0x8e, 0x91, 0x94, 0x96,
0x98, 0x98, 0x97, 0x98, 0x99, 0x99, 0x99, 0x99, 0x97, 0x93, 0x8d, 0x88, 0x83, 0x7f, 0x7c, 0x7b,
0x7c, 0x7c, 0x7c, 0x7d, 0x7c, 0x7c, 0x7a, 0x7a, 0x7a, 0x79, 0x78, 0x77, 0x76, 0x73, 0x6e, 0x6b,
0x6a, 0x69, 0x6b, 0x6d, 0x6f, 0x72, 0x72, 0x74, 0x76, 0x79, 0x7d, 0x81, 0x85, 0x8b, 0x91, 0x95,
0x9a, 0xa0, 0xa5, 0xaa, 0xae, 0xaf, 0xb0, 0xaf, 0xac, 0xa6, 0x9f, 0x97, 0x8e, 0x88, 0x84, 0x81,
0x7e, 0x7a, 0x75, 0x6f, 0x6a, 0x64, 0x5f, 0x5c, 0x59, 0x58, 0x59, 0x5a, 0x5e, 0x63, 0x69, 0x70,
0x77, 0x7b, 0x7e, 0x80, 0x80, 0x7f, 0x7d, 0x7c, 0x7a, 0x78, 0x78, 0x79, 0x79, 0x78, 0x77, 0x76,
0x76, 0x75, 0x74, 0x75, 0x76, 0x7a, 0x7e, 0x81, 0x85, 0x8a, 0x8f, 0x94, 0x97, 0x9a, 0x9d, 0x9f,
0xa0, 0xa0, 0x9d, 0x9b, 0x9a, 0x98, 0x95, 0x92, 0x8f, 0x8d, 0x8a, 0x88, 0x87, 0x85, 0x84, 0x83,
0x83, 0x83, 0x82, 0x81, 0x81, 0x80, 0x80, 0x80, 0x80, 0x7f, 0x7e, 0x7d, 0x7c, 0x79, 0x74, 0x6f,
0x6b, 0x66, 0x62, 0x5e, 0x5a, 0x57, 0x57, 0x58, 0x5a, 0x5d, 0x62, 0x67, 0x6d, 0x73, 0x77, 0x7d,
0x80, 0x85, 0x8b, 0x8f, 0x93, 0x96, 0x99, 0x9c, 0x9e, 0x9f, 0x9e, 0x9b, 0x9a, 0x99, 0x96, 0x93,
0x90, 0x8c, 0x8a, 0x88, 0x86, 0x84, 0x83, 0x80, 0x80, 0x80, 0x7f, 0x7e, 0x7e, 0x7e, 0x7f, 0x80,
0x80, 0x80, 0x7f, 0x7f, 0x7f, 0x7d, 0x7a, 0x77, 0x76, 0x76, 0x74, 0x72, 0x6f, 0x6b, 0x6a, 0x68,
0x68, 0x68, 0x69, 0x6c, 0x6e, 0x71, 0x74, 0x79, 0x7e, 0x82, 0x87, 0x8c, 0x8f, 0x92, 0x93, 0x94,
0x95, 0x95, 0x95, 0x96, 0x97, 0x99, 0x9a, 0x9a, 0x98, 0x95, 0x92, 0x8f, 0x8c, 0x88, 0x85, 0x81,
0x7f, 0x7c, 0x79, 0x76, 0x74, 0x73, 0x73, 0x73, 0x72, 0x71, 0x6f, 0x6d, 0x6c, 0x6a, 0x69, 0x68,
0x6a, 0x6c, 0x6d, 0x6e, 0x6f, 0x70, 0x71, 0x74, 0x77, 0x7a, 0x7e, 0x80, 0x83, 0x86, 0x88, 0x8b,
0x8e, 0x92, 0x96, 0x9c, 0x9f, 0xa1, 0xa2, 0xa2, 0xa0, 0x9c, 0x99, 0x95, 0x92, 0x8f, 0x8c, 0x89,
0x85, 0x80, 0x7d, 0x7a, 0x76, 0x72, 0x6f, 0x6b, 0x67, 0x63, 0x60, 0x5e, 0x5d, 0x5f, 0x63, 0x69,
0x6f, 0x74, 0x79, 0x7e, 0x81, 0x84, 0x84, 0x84, 0x85, 0x85, 0x86, 0x86, 0x86, 0x84, 0x81, 0x80,
0x80, 0x80, 0x80, 0x81, 0x80, 0x80, 0x7f, 0x7d, 0x7d, 0x7e, 0x7f, 0x84, 0x89, 0x8c, 0x8f, 0x92,
0x94, 0x95, 0x95, 0x95, 0x93, 0x93, 0x94, 0x95, 0x94, 0x92, 0x8f, 0x8e, 0x8c, 0x8b, 0x8b, 0x8a,
0x89, 0x87, 0x83, 0x7e, 0x78, 0x74, 0x71, 0x71, 0x71, 0x72, 0x71, 0x70, 0x70, 0x6e, 0x6b, 0x69,
0x67, 0x65, 0x64, 0x61, 0x60, 0x5d, 0x59, 0x57, 0x59, 0x5c, 0x61, 0x67, 0x6d, 0x73, 0x78, 0x7b,
0x7d, 0x80, 0x84, 0x89, 0x8f, 0x97, 0x9d, 0xa1, 0xa5, 0xa7, 0xa8, 0xa9, 0xa9, 0xaa, 0xab, 0xab,
0xa8, 0xa4, 0x9f, 0x99, 0x94, 0x91, 0x8e, 0x8c, 0x8a, 0x88, 0x84, 0x80, 0x7a, 0x75, 0x74, 0x72,
0x71, 0x72, 0x73, 0x74, 0x74, 0x73, 0x73, 0x73, 0x74, 0x75, 0x77, 0x77, 0x76, 0x74, 0x70, 0x6c,
0x69, 0x69, 0x6b, 0x6e, 0x72, 0x76, 0x78, 0x78, 0x78, 0x78, 0x79, 0x7b, 0x7e, 0x80, 0x82, 0x85,
0x86, 0x86, 0x86, 0x87, 0x88, 0x8a, 0x8c, 0x91, 0x93, 0x92, 0x90, 0x8e, 0x8a, 0x87, 0x86, 0x84,
0x83, 0x82, 0x80, 0x7e, 0x78, 0x74, 0x71, 0x70, 0x6f, 0x71, 0x72, 0x73, 0x74, 0x74, 0x74, 0x74,
0x76, 0x79, 0x7c, 0x80, 0x84, 0x87, 0x86, 0x85, 0x86, 0x85, 0x86, 0x89, 0x8c, 0x8e, 0x91, 0x92,
0x91, 0x90, 0x90, 0x91, 0x93, 0x96, 0x98, 0x99, 0x99, 0x97, 0x93, 0x90, 0x8d, 0x8b, 0x8a, 0x8b,
0x8b, 0x89, 0x86, 0x82, 0x7c, 0x77, 0x72, 0x6e, 0x6b, 0x68, 0x65, 0x63, 0x5f, 0x5a, 0x58, 0x58,
0x5a, 0x5e, 0x63, 0x67, 0x6c, 0x72, 0x74, 0x76, 0x78, 0x7a, 0x7f, 0x81, 0x84, 0x85, 0x85, 0x84,
0x82, 0x80, 0x7f, 0x80, 0x81, 0x82, 0x83, 0x82, 0x80, 0x7e, 0x7c, 0x7c, 0x7e, 0x80, 0x84, 0x8a,
0x8f, 0x92, 0x94, 0x94, 0x94, 0x96, 0x99, 0x9c, 0xa0, 0xa2, 0xa3, 0xa3, 0xa1, 0x9f, 0x9e, 0x9c,
0x9a, 0x99, 0x96, 0x91, 0x8a, 0x82, 0x7b, 0x73, 0x6e, 0x6d, 0x6c, 0x6d, 0x6e, 0x6d, 0x6b, 0x68,
0x65, 0x63, 0x62, 0x62, 0x62, 0x63, 0x62, 0x61, 0x5f, 0x5e, 0x5f, 0x63, 0x67, 0x6c, 0x71, 0x76,
0x78, 0x7a, 0x7b, 0x7c, 0x80, 0x85, 0x8b, 0x92, 0x99, 0x9c, 0x9e, 0x9f, 0x9f, 0xa0, 0xa0, 0xa1,
0xa4, 0xa4, 0xa2, 0x9e, 0x98, 0x92, 0x8d, 0x89, 0x84, 0x83, 0x82, 0x7f, 0x7b, 0x76, 0x6f, 0x68,
0x64, 0x63, 0x63, 0x65, 0x67, 0x6b, 0x6e, 0x6f, 0x71, 0x74, 0x77, 0x7b, 0x80, 0x82, 0x83, 0x83,
0x82, 0x80, 0x80, 0x80, 0x84, 0x87, 0x8a, 0x8c, 0x8d, 0x8b, 0x87, 0x84, 0x82, 0x80, 0x81, 0x83,
0x84, 0x85, 0x86, 0x85, 0x85, 0x85, 0x85, 0x87, 0x89, 0x8c, 0x8d, 0x8d, 0x8b, 0x88, 0x86, 0x84,
0x82, 0x82, 0x82, 0x81, 0x7f, 0x7b, 0x77, 0x71, 0x6d, 0x6b, 0x6b, 0x6d, 0x6f, 0x70, 0x71, 0x72,
0x72, 0x72, 0x74, 0x76, 0x79, 0x7e, 0x81, 0x83, 0x84, 0x84, 0x83, 0x82, 0x83, 0x84, 0x86, 0x88,
0x89, 0x8a, 0x8a, 0x88, 0x86, 0x85, 0x86, 0x8a, 0x8c, 0x8c, 0x8c, 0x8b, 0x89, 0x86, 0x85, 0x86,
0x88, 0x8a, 0x8c, 0x8d, 0x8b, 0x87, 0x83, 0x80, 0x7d, 0x79, 0x77, 0x75, 0x73, 0x71, 0x6e, 0x69,
0x66, 0x64, 0x63, 0x66, 0x6a, 0x6e, 0x72, 0x75, 0x77, 0x79, 0x7a, 0x7d, 0x81, 0x86, 0x8a, 0x8c,
0x8d, 0x8d, 0x8b, 0x89, 0x89, 0x89, 0x8a, 0x8a, 0x8a, 0x88, 0x85, 0x81, 0x7e, 0x7d, 0x7d, 0x7e,
0x82, 0x86, 0x89, 0x8b, 0x8c, 0x8d, 0x8e, 0x8f, 0x91, 0x94, 0x97, 0x99, 0x9b, 0x9a, 0x98, 0x95,
0x93, 0x91, 0x8f, 0x8c, 0x87, 0x82, 0x7c, 0x74, 0x6b, 0x64, 0x60, 0x5f, 0x5e, 0x5f, 0x60, 0x5f,
0x5d, 0x5b, 0x5a, 0x59, 0x5b, 0x5e, 0x60, 0x63, 0x67, 0x6a, 0x6c, 0x6d, 0x6f, 0x73, 0x78, 0x7b,
0x80, 0x84, 0x87, 0x88, 0x8a, 0x8d, 0x90, 0x94, 0x9b, 0xa1, 0xa5, 0xa8, 0xa9, 0xa9, 0xa8, 0xa8,
0xa8, 0xa8, 0xa8, 0xa6, 0xa4, 0x9f, 0x9a, 0x95, 0x8f, 0x89, 0x85, 0x81, 0x7e, 0x7a, 0x76, 0x70,
0x6a, 0x65, 0x60, 0x5f, 0x60, 0x62, 0x65, 0x69, 0x6b, 0x6e, 0x71, 0x73, 0x76, 0x7b, 0x7f, 0x82,
0x85, 0x85, 0x85, 0x83, 0x81, 0x81, 0x81, 0x82, 0x82, 0x82, 0x81, 0x7f, 0x7b, 0x77, 0x74, 0x73,
0x73, 0x74, 0x76, 0x78, 0x79, 0x7a, 0x7a, 0x7a, 0x7b, 0x7c, 0x7e, 0x80, 0x82, 0x84, 0x84, 0x83,
0x82, 0x80, 0x80, 0x82, 0x84, 0x84, 0x83, 0x81, 0x7f, 0x7d, 0x7a, 0x7a, 0x7b, 0x7d, 0x80, 0x82,
0x82, 0x82, 0x82, 0x82, 0x83, 0x84, 0x86, 0x8a, 0x8d, 0x8f, 0x91, 0x91, 0x8e, 0x8b, 0x8a, 0x8a,
0x8b, 0x8b, 0x8c, 0x8b, 0x89, 0x88, 0x86, 0x86, 0x87, 0x89, 0x8b, 0x8c, 0x8b, 0x88, 0x87, 0x85,
0x83, 0x83, 0x84, 0x86, 0x86, 0x85, 0x83, 0x80, 0x7d, 0x77, 0x72, 0x6f, 0x6c, 0x6a, 0x67, 0x64,
0x61, 0x5e, 0x5b, 0x59, 0x5a, 0x5c, 0x60, 0x64, 0x67, 0x6a, 0x6c, 0x6d, 0x70, 0x74, 0x7a, 0x7f,
0x84, 0x88, 0x8a, 0x8b, 0x8b, 0x89, 0x89, 0x8a, 0x8a, 0x8b, 0x8c, 0x8a, 0x88, 0x88, 0x88, 0x87,
0x89, 0x8c, 0x90, 0x94, 0x98, 0x9a, 0x9b, 0x9c, 0x9c, 0x9c, 0x9d, 0xa0, 0xa1, 0xa2, 0xa2, 0xa0,
0x9e, 0x99, 0x95, 0x91, 0x8e, 0x89, 0x85, 0x80, 0x79, 0x72, 0x6b, 0x65, 0x62, 0x60, 0x60, 0x61,
0x61, 0x60, 0x5f, 0x5d, 0x5c, 0x5c, 0x5d, 0x60, 0x64, 0x67, 0x6b, 0x6f, 0x71, 0x72, 0x73, 0x74,
0x76, 0x79, 0x7d, 0x80, 0x81, 0x82, 0x84, 0x86, 0x89, 0x8c, 0x91, 0x97, 0x9b, 0x9d, 0x9e, 0x9c,
0x9b, 0x9a, 0x99, 0x98, 0x97, 0x95, 0x93, 0x91, 0x8c, 0x86, 0x80, 0x7c, 0x78, 0x75, 0x71, 0x6f,
0x6e, 0x6b, 0x68, 0x67, 0x66, 0x67, 0x6a, 0x6e, 0x73, 0x78, 0x7b, 0x7f, 0x80, 0x83, 0x88, 0x8c,
0x91, 0x95, 0x98, 0x98, 0x97, 0x94, 0x91, 0x8e, 0x8c, 0x8b, 0x8a, 0x88, 0x87, 0x83, 0x80, 0x7c,
0x79, 0x77, 0x77, 0x77, 0x79, 0x7a, 0x7a, 0x79, 0x78, 0x77, 0x77, 0x78, 0x7a, 0x7c, 0x7e, 0x7f,
0x7f, 0x7f, 0x7e, 0x7d, 0x7e, 0x7f, 0x80, 0x80, 0x80, 0x80, 0x7e, 0x7c, 0x7a, 0x79, 0x7a, 0x7b,
0x7c, 0x7d, 0x7d, 0x7c, 0x7a, 0x79, 0x79, 0x7a, 0x7d, 0x80, 0x82, 0x83, 0x84, 0x83, 0x82, 0x82,
0x80, 0x80, 0x81, 0x82, 0x83, 0x84, 0x84, 0x84, 0x85, 0x86, 0x89, 0x8c, 0x8e, 0x90, 0x91, 0x8f,
0x8d, 0x8c, 0x8a, 0x89, 0x8b, 0x8d, 0x8d, 0x8b, 0x8a, 0x87, 0x81, 0x7d, 0x7a, 0x77, 0x73, 0x71,
0x6f, 0x6d, 0x6b, 0x69, 0x68, 0x67, 0x67, 0x69, 0x6a, 0x6b, 0x6d, 0x6f, 0x70, 0x73, 0x76, 0x7a,
0x7f, 0x83, 0x88, 0x8b, 0x8d, 0x8d, 0x8e, 0x8d, 0x8b, 0x8b, 0x8c, 0x8d, 0x8f, 0x8f, 0x8d, 0x8d,
0x8b, 0x8a, 0x8b, 0x8c, 0x8c, 0x8d, 0x8f, 0x8f, 0x8e, 0x8d, 0x8d, 0x8c, 0x8c, 0x8c, 0x8c, 0x8b,
0x8a, 0x89, 0x86, 0x83, 0x80, 0x7e, 0x7c, 0x7a, 0x77, 0x74, 0x70, 0x6b, 0x66, 0x62, 0x5f, 0x5f,
0x5f, 0x60, 0x61, 0x61, 0x61, 0x61, 0x61, 0x64, 0x67, 0x6c, 0x71, 0x75, 0x7a, 0x7e, 0x80, 0x82,
0x85, 0x86, 0x88, 0x8a, 0x8d, 0x90, 0x93, 0x95, 0x96, 0x98, 0x9a, 0x9c, 0x9e, 0xa0, 0xa2, 0xa0,
0x9e, 0x9b, 0x97, 0x93, 0x92, 0x91, 0x91, 0x91, 0x8d, 0x8a, 0x86, 0x81, 0x7d, 0x79, 0x75, 0x73,
0x72, 0x71, 0x71, 0x70, 0x70, 0x70, 0x70, 0x71, 0x72, 0x73, 0x74, 0x76, 0x78, 0x79, 0x7b, 0x7d,
0x80, 0x82, 0x87, 0x8a, 0x8a, 0x89, 0x87, 0x84, 0x81, 0x7f, 0x7c, 0x7c, 0x7c, 0x7b, 0x7a, 0x79,
0x77, 0x75, 0x73, 0x72, 0x72, 0x71, 0x71, 0x71, 0x71, 0x71, 0x72, 0x72, 0x74, 0x76, 0x79, 0x7c,
0x7e, 0x80, 0x81, 0x83, 0x85, 0x88, 0x89, 0x8b, 0x8f, 0x90, 0x91, 0x90, 0x90, 0x8d, 0x8b, 0x8a,
0x89, 0x89, 0x88, 0x88, 0x87, 0x85, 0x83, 0x81, 0x81, 0x80, 0x80, 0x81, 0x83, 0x83, 0x84, 0x85,
0x86, 0x87, 0x86, 0x85, 0x86, 0x88, 0x89, 0x89, 0x8a, 0x8b, 0x8c, 0x8c, 0x8e, 0x8f, 0x90, 0x8e,
0x8d, 0x8a, 0x86, 0x81, 0x7e, 0x7b, 0x7a, 0x78, 0x78, 0x76, 0x74, 0x72, 0x70, 0x6d, 0x6a, 0x67,
0x64, 0x63, 0x63, 0x63, 0x63, 0x63, 0x62, 0x61, 0x61, 0x62, 0x62, 0x64, 0x67, 0x6a, 0x6c, 0x6f,
0x72, 0x77, 0x7c, 0x80, 0x86, 0x8b, 0x8d, 0x90, 0x92, 0x94, 0x95, 0x97, 0x99, 0x9d, 0x9f, 0xa1,
0xa3, 0xa3, 0xa1, 0x9f, 0x9d, 0x9b, 0x9a, 0x98, 0x97, 0x97, 0x96, 0x94, 0x92, 0x8f, 0x8d, 0x8b,
0x89, 0x88, 0x86, 0x84, 0x83, 0x82, 0x80, 0x80, 0x7e, 0x7e, 0x7d, 0x79, 0x75, 0x71, 0x6c, 0x67,
0x64, 0x62, 0x60, 0x60, 0x61, 0x62, 0x64, 0x64, 0x64, 0x64, 0x66, 0x68, 0x6a, 0x6e, 0x71, 0x75,
0x78, 0x7a, 0x7d, 0x7f, 0x80, 0x83, 0x87, 0x89, 0x8c, 0x8e, 0x8f, 0x8f, 0x90, 0x91, 0x91, 0x90,
0x8f, 0x8f, 0x8d, 0x89, 0x87, 0x84, 0x82, 0x81, 0x81, 0x80, 0x80, 0x80, 0x80, 0x80, 0x7e, 0x7d,
0x7c, 0x7c, 0x7d, 0x7f, 0x80, 0x81, 0x82, 0x82, 0x83, 0x83, 0x83, 0x82, 0x82, 0x83, 0x84, 0x86,
0x88, 0x88, 0x89, 0x8c, 0x8d, 0x8d, 0x8d, 0x8b, 0x89, 0x87, 0x84, 0x81, 0x80, 0x7f, 0x80, 0x80,
0x80, 0x80, 0x80, 0x7d, 0x7b, 0x79, 0x75, 0x72, 0x71, 0x70, 0x71, 0x72, 0x73, 0x72, 0x74, 0x75,
0x75, 0x77, 0x79, 0x7b, 0x7e, 0x80, 0x81, 0x84, 0x85, 0x86, 0x87, 0x88, 0x88, 0x86, 0x84, 0x82,
0x80, 0x7d, 0x7c, 0x7a, 0x78, 0x78, 0x79, 0x79, 0x77, 0x75, 0x74, 0x74, 0x74, 0x75, 0x77, 0x7b,
0x7f, 0x81, 0x85, 0x87, 0x88, 0x8b, 0x8c, 0x8e, 0x91, 0x94, 0x96, 0x98, 0x99, 0x98, 0x99, 0x98,
0x96, 0x94, 0x91, 0x8e, 0x89, 0x84, 0x80, 0x7c, 0x79, 0x78, 0x77, 0x77, 0x79, 0x7a, 0x78, 0x76,
0x74, 0x71, 0x6f, 0x6e, 0x6d, 0x6d, 0x6c, 0x6c, 0x6c, 0x6c, 0x6c, 0x6c, 0x6c, 0x6e, 0x70, 0x73,
0x74, 0x75, 0x78, 0x7b, 0x7e, 0x82, 0x87, 0x8a, 0x8e, 0x91, 0x94, 0x96, 0x96, 0x97, 0x98, 0x99,
0x9b, 0x9b, 0x9b, 0x99, 0x96, 0x93, 0x8f, 0x8b, 0x88, 0x85, 0x83, 0x81, 0x80, 0x7e, 0x7b, 0x7a,
0x77, 0x76, 0x75, 0x75, 0x76, 0x78, 0x7a, 0x7b, 0x7c, 0x7d, 0x7e, 0x7c, 0x79, 0x77, 0x74, 0x71,
0x6e, 0x6b, 0x69, 0x68, 0x67, 0x68, 0x6b, 0x6d, 0x6f, 0x71, 0x72, 0x73, 0x74, 0x76, 0x77, 0x7a,
0x7e, 0x82, 0x87, 0x8b, 0x8f, 0x92, 0x95, 0x97, 0x98, 0x99, 0x99, 0x99, 0x99, 0x97, 0x95, 0x93,
0x91, 0x8f, 0x8d, 0x8a, 0x88, 0x85, 0x83, 0x81, 0x80, 0x7e, 0x7d, 0x7d, 0x7f, 0x81, 0x83, 0x84,
0x85, 0x84, 0x83, 0x83, 0x81, 0x81, 0x80, 0x80, 0x80, 0x7f, 0x7c, 0x7b, 0x79, 0x77, 0x77, 0x77,
0x77, 0x78, 0x78, 0x78, 0x79, 0x79, 0x79, 0x79, 0x79, 0x7a, 0x7a, 0x79, 0x79, 0x78, 0x77, 0x77,
0x77, 0x79, 0x7b, 0x7d, 0x7e, 0x7d, 0x7a, 0x77, 0x74, 0x71, 0x70, 0x71, 0x71, 0x74, 0x76, 0x79,
0x7c, 0x7f, 0x80, 0x83, 0x86, 0x8a, 0x8e, 0x91, 0x93, 0x94, 0x95, 0x95, 0x94, 0x92, 0x91, 0x90,
0x8e, 0x8b, 0x88, 0x85, 0x81, 0x7f, 0x7e, 0x7d, 0x7d, 0x7d, 0x7c, 0x7c, 0x7c, 0x7c, 0x7d, 0x7e,
0x81, 0x85, 0x88, 0x8c, 0x8f, 0x91, 0x92, 0x94, 0x94, 0x94, 0x95, 0x96, 0x97, 0x97, 0x96, 0x93,
0x8f, 0x8b, 0x85, 0x80, 0x7a, 0x74, 0x6e, 0x6a, 0x67, 0x64, 0x62, 0x61, 0x62, 0x65, 0x68, 0x6a,
0x6b, 0x6a, 0x69, 0x68, 0x66, 0x64, 0x63, 0x63, 0x64, 0x67, 0x69, 0x6c, 0x6d, 0x6e, 0x70, 0x71,
0x73, 0x75, 0x77, 0x7a, 0x7f, 0x82, 0x87, 0x8c, 0x91, 0x96, 0x9c, 0xa1, 0xa5, 0xa7, 0xa7, 0xa7,
0xa6, 0xa5, 0xa4, 0xa3, 0xa3, 0xa2, 0xa1, 0x9e, 0x9a, 0x94, 0x8e, 0x8a, 0x85, 0x82, 0x80, 0x7f,
0x7e, 0x7d, 0x7b, 0x7b, 0x7c, 0x7d, 0x7f, 0x82, 0x85, 0x85, 0x85, 0x84, 0x81, 0x7e, 0x79, 0x74,
0x71, 0x6e, 0x6c, 0x6b, 0x68, 0x65, 0x64, 0x64, 0x65, 0x66, 0x68, 0x69, 0x6a, 0x6b, 0x6b, 0x6b,
0x6b, 0x6f, 0x74, 0x7a, 0x80, 0x84, 0x87, 0x8a, 0x8b, 0x8a, 0x8a, 0x89, 0x88, 0x89, 0x88, 0x87,
0x86, 0x83, 0x80, 0x80, 0x7e, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7e, 0x80, 0x84, 0x8a,
0x8f, 0x93, 0x96, 0x97, 0x96, 0x94, 0x91, 0x8f, 0x8d, 0x8c, 0x8b, 0x8a, 0x88, 0x85, 0x83, 0x82,
0x82, 0x82, 0x81, 0x81, 0x82, 0x83, 0x83, 0x81, 0x80, 0x80, 0x80, 0x7f, 0x80, 0x80, 0x80, 0x7f,
0x7f, 0x7e, 0x7d, 0x7c, 0x7c, 0x7d, 0x7e, 0x7d, 0x7b, 0x76, 0x71, 0x6f, 0x6e, 0x6e, 0x6f, 0x71,
0x75, 0x78, 0x79, 0x7a, 0x7d, 0x7f, 0x7f, 0x80, 0x81, 0x83, 0x83, 0x84, 0x84, 0x83, 0x81, 0x80,
0x7d, 0x7b, 0x7a, 0x78, 0x76, 0x73, 0x6f, 0x6e, 0x6e, 0x6e, 0x71, 0x74, 0x78, 0x7c, 0x80, 0x82,
0x84, 0x86, 0x89, 0x8d, 0x91, 0x95, 0x98, 0x9a, 0x9c, 0x9e, 0xa0, 0xa0, 0x9f, 0x9e, 0x9f, 0x9e,
0x9c, 0x98, 0x91, 0x8a, 0x83, 0x7d, 0x76, 0x72, 0x70, 0x6f, 0x6f, 0x6f, 0x6e, 0x6d, 0x6e, 0x70,
0x73, 0x76, 0x77, 0x76, 0x75, 0x73, 0x71, 0x6e, 0x6c, 0x6d, 0x70, 0x74, 0x76, 0x77, 0x77, 0x77,
0x77, 0x76, 0x76, 0x78, 0x7b, 0x7f, 0x84, 0x89, 0x8d, 0x90, 0x92, 0x95, 0x97, 0x99, 0x99, 0x98,
0x95, 0x92, 0x8e, 0x8a, 0x88, 0x88, 0x89, 0x8a, 0x8a, 0x87, 0x83, 0x7e, 0x78, 0x73, 0x70, 0x6e,
0x6e, 0x6f, 0x71, 0x75, 0x77, 0x79, 0x7c, 0x7f, 0x83, 0x85, 0x87, 0x88, 0x87, 0x84, 0x80, 0x7c,
0x77, 0x74, 0x72, 0x73, 0x76, 0x75, 0x73, 0x72, 0x71, 0x71, 0x72, 0x73, 0x76, 0x79, 0x7c, 0x7f,
0x80, 0x80, 0x83, 0x86, 0x8a, 0x8f, 0x92, 0x94, 0x96, 0x94, 0x92, 0x90, 0x8c, 0x8a, 0x89, 0x89,
0x89, 0x89, 0x87, 0x85, 0x82, 0x80, 0x7e, 0x7d, 0x7c, 0x7c, 0x7d, 0x7f, 0x7f, 0x7f, 0x80, 0x83,
0x88, 0x8c, 0x90, 0x93, 0x93, 0x91, 0x8e, 0x88, 0x80, 0x7c, 0x79, 0x77, 0x76, 0x75, 0x74, 0x74,
0x73, 0x72, 0x71, 0x71, 0x71, 0x73, 0x76, 0x78, 0x78, 0x77, 0x77, 0x76, 0x76, 0x77, 0x79, 0x7c,
0x7e, 0x7e, 0x7c, 0x7b, 0x79, 0x77, 0x77, 0x78, 0x7a, 0x7c, 0x7c, 0x7b, 0x7a, 0x79, 0x78, 0x7a,
0x7d, 0x80, 0x84, 0x89, 0x8c, 0x8d, 0x8e, 0x8e, 0x8d, 0x8e, 0x91, 0x93, 0x94, 0x95, 0x94, 0x90,
0x8b, 0x86, 0x81, 0x7f, 0x7e, 0x7d, 0x7b, 0x79, 0x77, 0x76, 0x76, 0x78, 0x7b, 0x80, 0x87, 0x8d,
0x91, 0x93, 0x92, 0x91, 0x90, 0x91, 0x93, 0x96, 0x98, 0x99, 0x99, 0x97, 0x94, 0x8f, 0x8c, 0x89,
0x86, 0x83, 0x80, 0x79, 0x71, 0x69, 0x62, 0x5d, 0x5a, 0x59, 0x5c, 0x5f, 0x61, 0x61, 0x61, 0x61,
0x62, 0x64, 0x66, 0x6b, 0x6e, 0x70, 0x71, 0x70, 0x6e, 0x6d, 0x6d, 0x70, 0x75, 0x79, 0x7b, 0x7d,
0x7d, 0x7d, 0x7e, 0x7f, 0x82, 0x87, 0x8e, 0x97, 0x9e, 0xa2, 0xa4, 0xa5, 0xa4, 0xa4, 0xa4, 0xa3,
0xa2, 0xa0, 0x9d, 0x99, 0x95, 0x91, 0x8e, 0x8d, 0x8e, 0x8f, 0x8e, 0x8a, 0x84, 0x80, 0x7a, 0x76,
0x74, 0x74, 0x77, 0x7b, 0x80, 0x83, 0x85, 0x86, 0x86, 0x88, 0x88, 0x88, 0x89, 0x87, 0x84, 0x80,
0x7a, 0x74, 0x6f, 0x6c, 0x6b, 0x6b, 0x6b, 0x69, 0x67, 0x64, 0x62, 0x60, 0x60, 0x62, 0x66, 0x6b,
0x71, 0x75, 0x77, 0x79, 0x7a, 0x7d, 0x7f, 0x80, 0x84, 0x85, 0x85, 0x82, 0x80, 0x7d, 0x7b, 0x7a,
0x7c, 0x7e, 0x80, 0x81, 0x81, 0x80, 0x80, 0x7e, 0x7c, 0x7d, 0x7f, 0x81, 0x86, 0x8a, 0x8e, 0x90,
0x93, 0x96, 0x9a, 0x9e, 0xa1, 0xa2, 0xa2, 0x9e, 0x98, 0x90, 0x8a, 0x86, 0x83, 0x83, 0x83, 0x83,
0x81, 0x80, 0x80, 0x7e, 0x7e, 0x7e, 0x7f, 0x80, 0x83, 0x84, 0x83, 0x82, 0x80, 0x7f, 0x7d, 0x7d,
0x7f, 0x80, 0x80, 0x7f, 0x7b, 0x77, 0x73, 0x70, 0x6f, 0x71, 0x73, 0x74, 0x74, 0x74, 0x72, 0x71,
0x71, 0x72, 0x74, 0x77, 0x7a, 0x7c, 0x7c, 0x7b, 0x7a, 0x7a, 0x7b, 0x7d, 0x7f, 0x80, 0x82, 0x81,
0x7f, 0x7a, 0x75, 0x71, 0x6e, 0x6e, 0x6f, 0x70, 0x71, 0x72, 0x75, 0x78, 0x7b, 0x80, 0x85, 0x8d,
0x95, 0x9a, 0x9c, 0x9c, 0x9b, 0x9b, 0x9c, 0x9d, 0x9f, 0xa2, 0xa3, 0xa3, 0xa0, 0x9c, 0x97, 0x92,
0x8e, 0x8b, 0x88, 0x84, 0x80, 0x7b, 0x75, 0x70, 0x6c, 0x68, 0x67, 0x69, 0x6c, 0x6e, 0x70, 0x70,
0x6f, 0x6e, 0x6f, 0x71, 0x74, 0x77, 0x7a, 0x7d, 0x7c, 0x7b, 0x79, 0x79, 0x79, 0x7b, 0x7d, 0x7d,
0x7d, 0x7c, 0x7c, 0x7d, 0x7e, 0x80, 0x84, 0x89, 0x8f, 0x93, 0x96, 0x96, 0x94, 0x90, 0x8c, 0x89,
0x88, 0x86, 0x85, 0x83, 0x81, 0x7f, 0x7c, 0x79, 0x78, 0x79, 0x7a, 0x7a, 0x79, 0x76, 0x73, 0x71,
0x6f, 0x6f, 0x72, 0x76, 0x7b, 0x80, 0x82, 0x85, 0x87, 0x88, 0x89, 0x8a, 0x8b, 0x8b, 0x8b, 0x8a,
0x88, 0x84, 0x80, 0x7d, 0x7a, 0x78, 0x77, 0x77, 0x77, 0x76, 0x75, 0x74, 0x73, 0x74, 0x77, 0x7b,
0x80, 0x84, 0x88, 0x89, 0x8a, 0x8b, 0x8b, 0x8c, 0x8e, 0x8e, 0x8d, 0x8c, 0x89, 0x85, 0x82, 0x80,
0x7e, 0x7f, 0x80, 0x81, 0x82, 0x82, 0x81, 0x80, 0x7e, 0x7c, 0x7b, 0x7c, 0x80, 0x82, 0x85, 0x89,
0x8a, 0x8b, 0x8d, 0x8e, 0x8f, 0x8f, 0x8e, 0x8b, 0x87, 0x81, 0x7d, 0x77, 0x73, 0x71, 0x71, 0x70,
0x70, 0x70, 0x6f, 0x70, 0x70, 0x70, 0x72, 0x74, 0x77, 0x7a, 0x7c, 0x7d, 0x7e, 0x7d, 0x7c, 0x7c,
0x7b, 0x7c, 0x7e, 0x7f, 0x7e, 0x7d, 0x7a, 0x79, 0x78, 0x78, 0x7a, 0x7d, 0x7f, 0x80, 0x81, 0x82,
0x82, 0x82, 0x84, 0x86, 0x87, 0x89, 0x8a, 0x8a, 0x89, 0x88, 0x87, 0x88, 0x89, 0x8a, 0x8b, 0x8b,
0x8b, 0x89, 0x85, 0x80, 0x7d, 0x7a, 0x79, 0x78, 0x7a, 0x7c, 0x7f, 0x80, 0x84, 0x87, 0x8b, 0x90,
0x94, 0x98, 0x9b, 0x9b, 0x99, 0x98, 0x96, 0x95, 0x95, 0x95, 0x94, 0x92, 0x8f, 0x8b, 0x85, 0x80,
0x79, 0x74, 0x71, 0x6d, 0x69, 0x67, 0x64, 0x60, 0x5c, 0x58, 0x56, 0x57, 0x59, 0x5b, 0x5e, 0x60,
0x62, 0x63, 0x65, 0x67, 0x6b, 0x6f, 0x74, 0x78, 0x7c, 0x7e, 0x7f, 0x7f, 0x7f, 0x80, 0x82, 0x84,
0x86, 0x87, 0x88, 0x89, 0x8a, 0x8d, 0x90, 0x94, 0x99, 0x9e, 0xa2, 0xa3, 0xa1, 0x9f, 0x9c, 0x98,
0x96, 0x93, 0x91, 0x90, 0x8f, 0x8e, 0x8d, 0x8a, 0x87, 0x86, 0x86, 0x86, 0x84, 0x81, 0x80, 0x7d,
0x7a, 0x79, 0x7a, 0x7d, 0x80, 0x83, 0x86, 0x87, 0x87, 0x86, 0x86, 0x85, 0x85, 0x85, 0x84, 0x83,
0x83, 0x80, 0x7d, 0x77, 0x72, 0x6e, 0x6a, 0x67, 0x65, 0x63, 0x61, 0x60, 0x61, 0x61, 0x63, 0x67,
0x6c, 0x71, 0x75, 0x78, 0x79, 0x7a, 0x7b, 0x7c, 0x7e, 0x7f, 0x80, 0x80, 0x80, 0x7e, 0x7c, 0x7b,
0x7a, 0x7a, 0x7c, 0x7e, 0x80, 0x81, 0x83, 0x83, 0x83, 0x83, 0x83, 0x84, 0x87, 0x8a, 0x8e, 0x92,
0x95, 0x97, 0x9a, 0x9c, 0x9e, 0x9e, 0x9e, 0x9c, 0x9a, 0x97, 0x92, 0x8d, 0x89, 0x86, 0x84, 0x83,
0x83, 0x81, 0x80, 0x7f, 0x7e, 0x7d, 0x7c, 0x7d, 0x7e, 0x80, 0x82, 0x84, 0x84, 0x83, 0x82, 0x81,
0x80, 0x7e, 0x7c, 0x7a, 0x7a, 0x79, 0x77, 0x75, 0x74, 0x73, 0x73, 0x73, 0x74, 0x74, 0x74, 0x72,
0x71, 0x70, 0x70, 0x70, 0x72, 0x73, 0x75, 0x76, 0x75, 0x73, 0x72, 0x72, 0x73, 0x75, 0x77, 0x79,
0x7a, 0x7b, 0x7b, 0x7a, 0x78, 0x75, 0x74, 0x74, 0x76, 0x79, 0x7d, 0x80, 0x84, 0x89, 0x8e, 0x92,
0x98, 0x9d, 0xa1, 0xa3, 0xa4, 0xa2, 0xa1, 0xa0, 0xa1, 0xa2, 0xa1, 0x9f, 0x9e, 0x9b, 0x96, 0x90,
0x8b, 0x85, 0x80, 0x7d, 0x7a, 0x77, 0x74, 0x71, 0x6e, 0x6c, 0x69, 0x66, 0x65, 0x66, 0x67, 0x69,
0x6a, 0x6b, 0x6d, 0x70, 0x73, 0x77, 0x7b, 0x7f, 0x81, 0x82, 0x83, 0x83, 0x82, 0x82, 0x82, 0x84,
0x85, 0x85, 0x85, 0x83, 0x81, 0x81, 0x81, 0x81, 0x82, 0x85, 0x87, 0x88, 0x89, 0x88, 0x85, 0x83,
0x81, 0x80, 0x7e, 0x7c, 0x7b, 0x7b, 0x7b, 0x7a, 0x79, 0x79, 0x79, 0x79, 0x7a, 0x7a, 0x79, 0x76,
0x74, 0x73, 0x75, 0x77, 0x7b, 0x80, 0x83, 0x86, 0x88, 0x89, 0x8a, 0x8a, 0x8a, 0x8c, 0x8d, 0x8e,
0x8e, 0x8e, 0x8d, 0x8c, 0x88, 0x84, 0x80, 0x7d, 0x7a, 0x78, 0x76, 0x74, 0x73, 0x73, 0x75, 0x77,
0x7b, 0x7f, 0x81, 0x84, 0x85, 0x85, 0x85, 0x85, 0x87, 0x88, 0x8a, 0x8a, 0x8a, 0x89, 0x86, 0x84,
0x82, 0x80, 0x7f, 0x7e, 0x7e, 0x7f, 0x7e, 0x7f, 0x7e, 0x7d, 0x7d, 0x7c, 0x7c, 0x7c, 0x7d, 0x7e,
0x80, 0x81, 0x82, 0x84, 0x86, 0x88, 0x8a, 0x8b, 0x8a, 0x87, 0x83, 0x80, 0x7d, 0x7a, 0x78, 0x77,
0x76, 0x76, 0x74, 0x73, 0x72, 0x71, 0x71, 0x71, 0x72, 0x74, 0x77, 0x7a, 0x7d, 0x80, 0x80, 0x81,
0x81, 0x82, 0x83, 0x83, 0x83, 0x82, 0x81, 0x81, 0x81, 0x82, 0x83, 0x83, 0x83, 0x84, 0x83, 0x81,
0x80, 0x7e, 0x7d, 0x7d, 0x7e, 0x7f, 0x80, 0x80, 0x81, 0x81, 0x80, 0x80, 0x81, 0x81, 0x82, 0x84,
0x85, 0x86, 0x86, 0x87, 0x86, 0x85, 0x84, 0x83, 0x82, 0x82, 0x83, 0x84, 0x85, 0x87, 0x8b, 0x8e,
0x92, 0x96, 0x98, 0x98, 0x99, 0x98, 0x95, 0x93, 0x90, 0x8e, 0x8e, 0x8d, 0x8b, 0x88, 0x84, 0x80,
0x7b, 0x75, 0x70, 0x6b, 0x67, 0x64, 0x61, 0x5f, 0x5d, 0x5b, 0x59, 0x57, 0x57, 0x56, 0x58, 0x59,
0x5b, 0x5f, 0x63, 0x68, 0x6d, 0x73, 0x79, 0x7f, 0x84, 0x87, 0x8a, 0x8b, 0x8c, 0x8f, 0x91, 0x92,
0x94, 0x95, 0x96, 0x95, 0x93, 0x91, 0x8f, 0x8e, 0x8d, 0x8e, 0x8f, 0x91, 0x92, 0x92, 0x92, 0x92,
0x91, 0x8f, 0x8e, 0x8d, 0x8d, 0x8c, 0x8a, 0x89, 0x88, 0x88, 0x88, 0x88, 0x87, 0x86, 0x85, 0x83,
0x80, 0x7c, 0x78, 0x77, 0x78, 0x7a, 0x7c, 0x7e, 0x80, 0x80, 0x80, 0x81, 0x80, 0x80, 0x82, 0x83,
0x83, 0x83, 0x80, 0x7f, 0x7d, 0x7a, 0x77, 0x74, 0x70, 0x6d, 0x6a, 0x67, 0x65, 0x63, 0x62, 0x64,
0x66, 0x69, 0x6d, 0x6f, 0x72, 0x73, 0x74, 0x76, 0x78, 0x7b, 0x7e, 0x82, 0x86, 0x88, 0x88, 0x86,
0x84, 0x83, 0x83, 0x82, 0x82, 0x83, 0x84, 0x85, 0x86, 0x86, 0x86, 0x87, 0x87, 0x88, 0x88, 0x88,
0x89, 0x8a, 0x8c, 0x90, 0x93, 0x96, 0x99, 0x9c, 0x9d, 0x9d, 0x9b, 0x96, 0x91, 0x8d, 0x8a, 0x88,
0x87, 0x85, 0x84, 0x82, 0x80, 0x7d, 0x7b, 0x7a, 0x79, 0x78, 0x79, 0x79, 0x7a, 0x7c, 0x7e, 0x7f,
0x7f, 0x80, 0x80, 0x80, 0x80, 0x80, 0x7e, 0x7d, 0x7b, 0x79, 0x78, 0x77, 0x76, 0x75, 0x74, 0x72,
0x70, 0x6d, 0x6a, 0x68, 0x67, 0x68, 0x69, 0x6a, 0x6b, 0x6c, 0x6d, 0x6e, 0x6f, 0x70, 0x71, 0x74,
0x77, 0x7b, 0x7e, 0x80, 0x82, 0x83, 0x84, 0x86, 0x87, 0x87, 0x88, 0x8a, 0x8b, 0x8d, 0x90, 0x93,
0x97, 0x9a, 0x9e, 0xa0, 0xa2, 0xa2, 0xa0, 0x9d, 0x9a, 0x99, 0x98, 0x97, 0x97, 0x98, 0x95, 0x91,
0x8c, 0x87, 0x82, 0x7e, 0x79, 0x76, 0x73, 0x70, 0x6e, 0x6c, 0x6a, 0x67, 0x66, 0x65, 0x65, 0x65,
0x65, 0x67, 0x69, 0x6c, 0x6f, 0x74, 0x79, 0x7e, 0x82, 0x87, 0x8a, 0x8b, 0x8b, 0x89, 0x89, 0x89,
0x8a, 0x8b, 0x8b, 0x8a, 0x87, 0x83, 0x80, 0x7c, 0x79, 0x77, 0x76, 0x77, 0x78, 0x78, 0x79, 0x7a,
0x7b, 0x7b, 0x7c, 0x7d, 0x7f, 0x80, 0x80, 0x80, 0x7f, 0x7f, 0x80, 0x80, 0x82, 0x83, 0x83, 0x83,
0x81, 0x80, 0x7d, 0x7a, 0x78, 0x79, 0x7b, 0x7d, 0x80, 0x82, 0x85, 0x86, 0x88, 0x89, 0x8b, 0x8c,
0x8e, 0x8f, 0x8f, 0x8f, 0x8e, 0x8b, 0x88, 0x86, 0x85, 0x83, 0x80, 0x7f, 0x7b, 0x78, 0x76, 0x75,
0x75, 0x75, 0x76, 0x78, 0x7a, 0x7c, 0x7d, 0x7e, 0x7f, 0x80, 0x83, 0x85, 0x88, 0x8a, 0x8c, 0x8c,
0x8a, 0x88, 0x84, 0x80, 0x7f, 0x7e, 0x7d, 0x7d, 0x7d, 0x7d, 0x7c, 0x7c, 0x7a, 0x79, 0x78, 0x77,
0x77, 0x78, 0x79, 0x7a, 0x7d, 0x80, 0x83, 0x87, 0x89, 0x8b, 0x8c, 0x8a, 0x87, 0x83, 0x80, 0x7c,
0x7a, 0x79, 0x79, 0x79, 0x79, 0x79, 0x78, 0x76, 0x75, 0x73, 0x73, 0x73, 0x75, 0x78, 0x7b, 0x7f,
0x81, 0x83, 0x86, 0x89, 0x8b, 0x8d, 0x8e, 0x8e, 0x8e, 0x8c, 0x8a, 0x87, 0x86, 0x85, 0x84, 0x83,
0x81, 0x80, 0x7e, 0x7c, 0x79, 0x77, 0x75, 0x75, 0x75, 0x77, 0x78, 0x7a, 0x7a, 0x7a, 0x7b, 0x7c,
0x7e, 0x80, 0x83, 0x87, 0x8a, 0x8b, 0x8b, 0x8a, 0x8a, 0x8a, 0x8b, 0x8c, 0x8c, 0x8b, 0x8c, 0x8c,
0x8c, 0x8e, 0x8f, 0x90, 0x90, 0x90, 0x90, 0x8e, 0x8a, 0x87, 0x83, 0x81, 0x80, 0x80, 0x80, 0x80,
0x7f, 0x7d, 0x78, 0x72, 0x6c, 0x68, 0x65, 0x62, 0x61, 0x60, 0x5f, 0x5f, 0x5e, 0x5f, 0x5f, 0x60,
0x61, 0x64, 0x68, 0x6c, 0x70, 0x74, 0x79, 0x7f, 0x85, 0x8b, 0x90, 0x95, 0x98, 0x9a, 0x9a, 0x98,
0x97, 0x96, 0x96, 0x96, 0x96, 0x95, 0x91, 0x8d, 0x89, 0x85, 0x80, 0x7f, 0x7e, 0x7e, 0x80, 0x82,
0x83, 0x84, 0x85, 0x86, 0x88, 0x89, 0x8a, 0x8c, 0x8d, 0x8e, 0x8e, 0x8d, 0x8c, 0x8c, 0x8c, 0x8b,
0x8a, 0x88, 0x83, 0x80, 0x7b, 0x78, 0x75, 0x72, 0x72, 0x74, 0x76, 0x79, 0x7c, 0x7c, 0x7c, 0x7d,
0x7c, 0x7c, 0x7c, 0x7c, 0x7d, 0x7e, 0x7d, 0x7b, 0x78, 0x75, 0x74, 0x73, 0x71, 0x6f, 0x6d, 0x6b,
0x69, 0x68, 0x68, 0x68, 0x69, 0x6d, 0x71, 0x76, 0x79, 0x7b, 0x7d, 0x80, 0x82, 0x86, 0x89, 0x8d,
0x90, 0x92, 0x93, 0x90, 0x8d, 0x8a, 0x88, 0x87, 0x87, 0x88, 0x88, 0x87, 0x86, 0x85, 0x83, 0x81,
0x80, 0x81, 0x83, 0x85, 0x88, 0x8a, 0x8d, 0x90, 0x94, 0x96, 0x97, 0x99, 0x99, 0x97, 0x93, 0x8f,
0x89, 0x84, 0x81, 0x80, 0x80, 0x80, 0x80, 0x7e, 0x7b, 0x78, 0x74, 0x70, 0x6e, 0x6d, 0x6f, 0x72,
0x75, 0x79, 0x7c, 0x7d, 0x7f, 0x81, 0x83, 0x84, 0x85, 0x85, 0x84, 0x81, 0x7e, 0x7b, 0x78, 0x76,
0x75, 0x75, 0x73, 0x6f, 0x6c, 0x69, 0x66, 0x63, 0x61, 0x62, 0x64, 0x67, 0x6b, 0x6d, 0x70, 0x72,
0x75, 0x78, 0x7c, 0x80, 0x85, 0x89, 0x8d, 0x90, 0x91, 0x92, 0x93, 0x95, 0x97, 0x99, 0x9a, 0x9b,
0x9a, 0x99, 0x99, 0x98, 0x97, 0x96, 0x96, 0x96, 0x95, 0x92, 0x90, 0x8d, 0x89, 0x88, 0x87, 0x87,
0x87, 0x87, 0x87, 0x84, 0x80, 0x7c, 0x76, 0x71, 0x6e, 0x6c, 0x6a, 0x69, 0x68, 0x67, 0x67, 0x67,
0x67, 0x68, 0x6a, 0x6d, 0x71, 0x74, 0x76, 0x7a, 0x7e, 0x81, 0x86, 0x8b, 0x8f, 0x92, 0x94, 0x93,
0x92, 0x8f, 0x8b, 0x88, 0x86, 0x85, 0x83, 0x80, 0x7d, 0x79, 0x74, 0x6f, 0x6b, 0x67, 0x66, 0x69,
0x6c, 0x6f, 0x72, 0x75, 0x78, 0x7b, 0x7f, 0x82, 0x86, 0x8a, 0x8e, 0x91, 0x92, 0x92, 0x91, 0x90,
0x90, 0x8f, 0x8d, 0x8a, 0x86, 0x82, 0x7f, 0x7b, 0x78, 0x77, 0x78, 0x7a, 0x7e, 0x81, 0x83, 0x84,
0x85, 0x85, 0x85, 0x86, 0x88, 0x89, 0x8a, 0x8b, 0x8a, 0x89, 0x87, 0x84, 0x83, 0x82, 0x80, 0x7f,
0x7c, 0x78, 0x74, 0x72, 0x70, 0x6f, 0x70, 0x73, 0x77, 0x7a, 0x7d, 0x7f, 0x80, 0x81, 0x82, 0x84,
0x87, 0x8a, 0x8d, 0x8e, 0x8d, 0x8b, 0x88, 0x84, 0x81, 0x7f, 0x7d, 0x7b, 0x79, 0x77, 0x75, 0x72,
0x70, 0x6f, 0x70, 0x72, 0x75, 0x79, 0x7c, 0x7f, 0x81, 0x84, 0x87, 0x8a, 0x8d, 0x8e, 0x8d, 0x8c,
0x8a, 0x86, 0x83, 0x80, 0x7e, 0x7d, 0x7d, 0x7d, 0x7d, 0x7c, 0x79, 0x76, 0x73, 0x70, 0x6f, 0x71,
0x73, 0x78, 0x7d, 0x81, 0x86, 0x89, 0x8c, 0x8f, 0x92, 0x94, 0x96, 0x96, 0x94, 0x92, 0x8e, 0x8a,
0x87, 0x84, 0x82, 0x80, 0x7d, 0x79, 0x76, 0x72, 0x6d, 0x6a, 0x68, 0x69, 0x6b, 0x6f, 0x73, 0x76,
0x79, 0x7d, 0x80, 0x82, 0x84, 0x87, 0x8b, 0x8e, 0x90, 0x91, 0x91, 0x91, 0x91, 0x92, 0x93, 0x94,
0x93, 0x92, 0x90, 0x8c, 0x88, 0x85, 0x81, 0x80, 0x7f, 0x7e, 0x7e, 0x7c, 0x7a, 0x78, 0x76, 0x74,
0x75, 0x77, 0x79, 0x7a, 0x7a, 0x78, 0x76, 0x72, 0x6e, 0x6b, 0x68, 0x66, 0x65, 0x65, 0x65, 0x65,
0x66, 0x68, 0x6a, 0x6d, 0x71, 0x76, 0x7c, 0x80, 0x84, 0x89, 0x8d, 0x92, 0x97, 0x9d, 0xa1, 0xa3,
0xa4, 0xa3, 0xa0, 0x9c, 0x97, 0x93, 0x90, 0x8d, 0x8a, 0x86, 0x81, 0x7e, 0x79, 0x74, 0x70, 0x6e,
0x6d, 0x6f, 0x71, 0x75, 0x78, 0x7c, 0x80, 0x84, 0x89, 0x8e, 0x92, 0x96, 0x9a, 0x9a, 0x9a, 0x98,
0x94, 0x90, 0x8c, 0x88, 0x85, 0x81, 0x7c, 0x76, 0x71, 0x6c, 0x69, 0x68, 0x69, 0x6b, 0x6e, 0x71,
0x74, 0x76, 0x77, 0x79, 0x7a, 0x7b, 0x7d, 0x7e, 0x7f, 0x7f, 0x80, 0x7f, 0x7e, 0x7d, 0x7c, 0x7b,
0x7a, 0x78, 0x75, 0x72, 0x70, 0x6d, 0x6b, 0x6b, 0x6e, 0x72, 0x77, 0x7c, 0x80, 0x82, 0x85, 0x87,
0x8a, 0x8d, 0x91, 0x95, 0x97, 0x98, 0x99, 0x98, 0x96, 0x92, 0x8d, 0x89, 0x84, 0x81, 0x7f, 0x7c,
0x7a, 0x78, 0x78, 0x78, 0x7a, 0x7e, 0x82, 0x86, 0x8a, 0x8e, 0x90, 0x92, 0x94, 0x95, 0x96, 0x96,
0x96, 0x93, 0x90, 0x8b, 0x86, 0x82, 0x7f, 0x7c, 0x79, 0x76, 0x73, 0x70, 0x6d, 0x6a, 0x67, 0x65,
0x65, 0x66, 0x69, 0x6d, 0x72, 0x78, 0x7d, 0x80, 0x84, 0x87, 0x8a, 0x8c, 0x8d, 0x8c, 0x8a, 0x87,
0x83, 0x80, 0x7c, 0x78, 0x75, 0x72, 0x6f, 0x6c, 0x68, 0x64, 0x61, 0x60, 0x60, 0x62, 0x67, 0x6c,
0x72, 0x79, 0x7f, 0x84, 0x88, 0x8c, 0x8f, 0x92, 0x95, 0x97, 0x9a, 0x9b, 0x9c, 0x9d, 0x9d, 0x9e,
0x9e, 0x9e, 0x9d, 0x9b, 0x97, 0x92, 0x8c, 0x87, 0x84, 0x82, 0x81, 0x81, 0x81, 0x80, 0x80, 0x80,
0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x7f, 0x7c, 0x79, 0x75, 0x70, 0x6c, 0x69, 0x66, 0x65,
0x64, 0x65, 0x65, 0x66, 0x68, 0x6b, 0x6f, 0x75, 0x7b, 0x80, 0x84, 0x88, 0x8c, 0x90, 0x93, 0x97,
0x99, 0x99, 0x97, 0x95, 0x91, 0x8c, 0x87, 0x82, 0x7e, 0x7a, 0x76, 0x72, 0x6f, 0x6d, 0x69, 0x66,
0x64, 0x63, 0x64, 0x66, 0x6b, 0x70, 0x76, 0x7c, 0x82, 0x89, 0x90, 0x96, 0x9b, 0x9f, 0xa2, 0xa2,
0xa1, 0x9e, 0x99, 0x94, 0x90, 0x8b, 0x87, 0x82, 0x7e, 0x79, 0x75, 0x71, 0x6f, 0x6f, 0x6f, 0x72,
0x76, 0x79, 0x7e, 0x82, 0x85, 0x87, 0x89, 0x8a, 0x8b, 0x8b, 0x8a, 0x8b, 0x8b, 0x8a, 0x88, 0x86,
0x83, 0x80, 0x7f, 0x7c, 0x78, 0x74, 0x70, 0x6d, 0x6b, 0x6a, 0x6c, 0x70, 0x74, 0x77, 0x7b, 0x7e,
0x81, 0x83, 0x85, 0x88, 0x8b, 0x8d, 0x8e, 0x8e, 0x8f, 0x8d, 0x8a, 0x85, 0x80, 0x7a, 0x74, 0x70,
0x6c, 0x6a, 0x6a, 0x6a, 0x6b, 0x6e, 0x72, 0x78, 0x7e, 0x83, 0x89, 0x8d, 0x90, 0x91, 0x93, 0x95,
0x95, 0x95, 0x93, 0x91, 0x8e, 0x8a, 0x86, 0x82, 0x7e, 0x7a, 0x77, 0x74, 0x71, 0x70, 0x6f, 0x6e,
0x6d, 0x6d, 0x6e, 0x70, 0x75, 0x79, 0x7f, 0x85, 0x8b, 0x90, 0x94, 0x98, 0x9a, 0x9c, 0x9c, 0x9a,
0x98, 0x94, 0x8e, 0x88, 0x83, 0x7f, 0x7a, 0x75, 0x71, 0x6d, 0x69, 0x66, 0x63, 0x61, 0x61, 0x64,
0x69, 0x6e, 0x75, 0x7b, 0x81, 0x87, 0x8a, 0x8d, 0x8f, 0x8f, 0x90, 0x91, 0x92, 0x93, 0x93, 0x93,
0x93, 0x92, 0x91, 0x8f, 0x8d, 0x8a, 0x85, 0x80, 0x7c, 0x77, 0x73, 0x72, 0x73, 0x74, 0x75, 0x77,
0x79, 0x7a, 0x7a, 0x7b, 0x7c, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7b, 0x77, 0x74, 0x70, 0x6c, 0x69,
0x67, 0x66, 0x68, 0x69, 0x6b, 0x6f, 0x73, 0x78, 0x7f, 0x84, 0x8b, 0x91, 0x97, 0x9b, 0x9e, 0xa0,
0xa2, 0xa4, 0xa3, 0xa2, 0xa0, 0x9d, 0x98, 0x91, 0x8b, 0x85, 0x80, 0x7b, 0x77, 0x74, 0x71, 0x70,
0x6e, 0x6b, 0x6a, 0x69, 0x6a, 0x6c, 0x70, 0x75, 0x7c, 0x81, 0x87, 0x8e, 0x93, 0x98, 0x9b, 0x9d,
0x9d, 0x9c, 0x98, 0x92, 0x8c, 0x86, 0x80, 0x7b, 0x76, 0x72, 0x6e, 0x6a, 0x67, 0x64, 0x62, 0x62,
0x63, 0x67, 0x6c, 0x71, 0x77, 0x7c, 0x80, 0x83, 0x85, 0x86, 0x86, 0x86, 0x87, 0x87, 0x87, 0x86,
0x85, 0x83, 0x81, 0x7f, 0x7c, 0x7a, 0x78, 0x75, 0x73, 0x71, 0x70, 0x71, 0x73, 0x77, 0x7b, 0x80,
0x85, 0x8a, 0x8d, 0x90, 0x92, 0x94, 0x96, 0x97, 0x98, 0x98, 0x97, 0x94, 0x90, 0x8a, 0x84, 0x7e,
0x78, 0x74, 0x71, 0x70, 0x71, 0x72, 0x74, 0x79, 0x7e, 0x82, 0x88, 0x8e, 0x92, 0x95, 0x96, 0x97,
0x96, 0x95, 0x93, 0x90, 0x8d, 0x8a, 0x86, 0x80, 0x7c, 0x76, 0x70, 0x6a, 0x67, 0x64, 0x63, 0x63,
0x63, 0x64, 0x65, 0x66, 0x68, 0x6b, 0x6f, 0x75, 0x7b, 0x81, 0x86, 0x8b, 0x90, 0x93, 0x95, 0x96,
0x96, 0x94, 0x91, 0x8d, 0x87, 0x81, 0x7c, 0x76, 0x71, 0x6d, 0x6a, 0x68, 0x66, 0x65, 0x64, 0x64,
0x66, 0x6b, 0x71, 0x78, 0x80, 0x87, 0x8e, 0x93, 0x97, 0x99, 0x9a, 0x9a, 0x9a, 0x9a, 0x9b, 0x9b,
0x9b, 0x9a, 0x99, 0x97, 0x95, 0x93, 0x91, 0x8d, 0x89, 0x85, 0x80, 0x7c, 0x79, 0x78, 0x78, 0x79,
0x7b, 0x7d, 0x7f, 0x80, 0x7f, 0x7e, 0x7d, 0x7c, 0x7a, 0x79, 0x79, 0x77, 0x74, 0x71, 0x6d, 0x69,
0x65, 0x62, 0x60, 0x60, 0x62, 0x65, 0x69, 0x6d, 0x72, 0x78, 0x7f, 0x85, 0x8b, 0x91, 0x95, 0x98,
0x9a, 0x9a, 0x9a, 0x98, 0x96, 0x94, 0x91, 0x8d, 0x88, 0x83, 0x7f, 0x79, 0x74, 0x71, 0x6e, 0x6d,
0x6c, 0x6c, 0x6c, 0x6c, 0x6c, 0x6d, 0x6f, 0x72, 0x77, 0x7e, 0x84, 0x8c, 0x92, 0x98, 0x9d, 0x9f,
0xa1, 0xa3, 0xa2, 0xa0, 0x9c, 0x96, 0x8f, 0x88, 0x81, 0x7d, 0x78, 0x74, 0x71, 0x6f, 0x6d, 0x6b,
0x6b, 0x6c, 0x6e, 0x71, 0x76, 0x7c, 0x81, 0x86, 0x8a, 0x8b, 0x8c, 0x8c, 0x8b, 0x8a, 0x88, 0x87,
0x86, 0x83, 0x81, 0x7f, 0x7b, 0x79, 0x77, 0x74, 0x73, 0x71, 0x6f, 0x6d, 0x6d, 0x6d, 0x70, 0x73,
0x78, 0x7d, 0x82, 0x85, 0x88, 0x8b, 0x8c, 0x8c, 0x8c, 0x8d, 0x8c, 0x8c, 0x8a, 0x86, 0x81, 0x7d,
0x77, 0x72, 0x6e, 0x6c, 0x6c, 0x6d, 0x70, 0x73, 0x78, 0x7d, 0x81, 0x87, 0x8d, 0x93, 0x97, 0x9a,
0x9b, 0x9a, 0x98, 0x96, 0x92, 0x8f, 0x8c, 0x88, 0x84, 0x80, 0x7b, 0x77, 0x72, 0x6e, 0x6b, 0x6a,
0x6b, 0x6c, 0x6d, 0x6f, 0x71, 0x74, 0x77, 0x7b, 0x7f, 0x83, 0x89, 0x8e, 0x92, 0x96, 0x98, 0x9a,
0x9a, 0x9a, 0x98, 0x96, 0x91, 0x8b, 0x84, 0x7e, 0x77, 0x70, 0x6a, 0x66, 0x63, 0x62, 0x62, 0x62,
0x63, 0x65, 0x68, 0x6c, 0x71, 0x78, 0x7e, 0x84, 0x8a, 0x8e, 0x91, 0x92, 0x91, 0x91, 0x91, 0x91,
0x91, 0x91, 0x90, 0x8f, 0x8d, 0x8c, 0x8a, 0x88, 0x85, 0x83, 0x80, 0x7f, 0x7d, 0x7a, 0x79, 0x78,
0x79, 0x7a, 0x7c, 0x7e, 0x7f, 0x80, 0x80, 0x7e, 0x7c, 0x7b, 0x7a, 0x79, 0x78, 0x77, 0x75, 0x72,
0x6e, 0x6b, 0x69, 0x68, 0x68, 0x6b, 0x6f, 0x73, 0x78, 0x7e, 0x84, 0x8a, 0x91, 0x96, 0x9c, 0xa0,
0xa3, 0xa4, 0xa3, 0xa1, 0x9d, 0x9a, 0x96, 0x93, 0x8f, 0x8b, 0x86, 0x81, 0x7c, 0x76, 0x72, 0x6e,
0x6c, 0x6a, 0x6a, 0x6a, 0x6b, 0x6c, 0x6d, 0x6f, 0x71, 0x74, 0x78, 0x7e, 0x82, 0x87, 0x8c, 0x91,
0x93, 0x95, 0x97, 0x97, 0x95, 0x92, 0x8d, 0x87, 0x80, 0x7b, 0x75, 0x70, 0x6d, 0x6b, 0x69, 0x69,
0x69, 0x6a, 0x6b, 0x6d, 0x71, 0x75, 0x79, 0x7e, 0x82, 0x87, 0x8a, 0x8b, 0x8b, 0x8b, 0x8a, 0x88,
0x87, 0x86, 0x85, 0x84, 0x82, 0x80, 0x80, 0x7f, 0x7d, 0x7c, 0x7b, 0x7a, 0x7a, 0x79, 0x79, 0x7b,
0x7d, 0x80, 0x82, 0x86, 0x89, 0x8d, 0x90, 0x92, 0x92, 0x92, 0x90, 0x8e, 0x8d, 0x8b, 0x88, 0x85,
0x80, 0x7c, 0x78, 0x74, 0x71, 0x70, 0x71, 0x73, 0x76, 0x7a, 0x7d, 0x81, 0x86, 0x8b, 0x8e, 0x91,
0x93, 0x93, 0x93, 0x90, 0x8d, 0x88, 0x84, 0x80, 0x7d, 0x79, 0x75, 0x71, 0x6e, 0x6a, 0x67, 0x65,
0x64, 0x64, 0x65, 0x68, 0x6b, 0x6e, 0x71, 0x75, 0x78, 0x7d, 0x80, 0x84, 0x89, 0x8d, 0x91, 0x95,
0x97, 0x98, 0x99, 0x98, 0x96, 0x93, 0x8e, 0x88, 0x81, 0x7b, 0x74, 0x6f, 0x6c, 0x69, 0x68, 0x69,
0x6b, 0x6d, 0x6f, 0x72, 0x76, 0x7b, 0x7f, 0x84, 0x89, 0x8e, 0x93, 0x96, 0x98, 0x98, 0x98, 0x97,
0x96, 0x96, 0x95, 0x93, 0x91, 0x8f, 0x8d, 0x8c, 0x8a, 0x89, 0x87, 0x85, 0x83, 0x80, 0x7e, 0x7c,
0x7b, 0x7a, 0x79, 0x78, 0x78, 0x78, 0x78, 0x77, 0x76, 0x75, 0x73, 0x72, 0x71, 0x6f, 0x6d, 0x6b,
0x69, 0x66, 0x65, 0x64, 0x63, 0x65, 0x67, 0x6b, 0x6f, 0x75, 0x7b, 0x80, 0x87, 0x8e, 0x94, 0x98,
0x9c, 0x9e, 0x9e, 0x9d, 0x9b, 0x99, 0x96, 0x93, 0x8f, 0x8c, 0x88, 0x84, 0x81, 0x7e, 0x7a, 0x77,
0x73, 0x70, 0x6f, 0x6f, 0x70, 0x72, 0x74, 0x75, 0x78, 0x7b, 0x7e, 0x80, 0x83, 0x86, 0x89, 0x8c,
0x90, 0x93, 0x96, 0x99, 0x99, 0x99, 0x97, 0x93, 0x8e, 0x88, 0x82, 0x7e, 0x79, 0x75, 0x72, 0x70,
0x70, 0x70, 0x71, 0x73, 0x75, 0x76, 0x79, 0x7c, 0x7e, 0x80, 0x82, 0x83, 0x83, 0x83, 0x83, 0x82,
0x81, 0x80, 0x7e, 0x7c, 0x7a, 0x79, 0x78, 0x77, 0x76, 0x76, 0x77, 0x76, 0x76, 0x77, 0x77, 0x78,
0x79, 0x7a, 0x7c, 0x7f, 0x81, 0x83, 0x86, 0x89, 0x8a, 0x8b, 0x8b, 0x8b, 0x8a, 0x89, 0x87, 0x85,
0x82, 0x80, 0x7d, 0x7a, 0x78, 0x78, 0x77, 0x78, 0x79, 0x7b, 0x7d, 0x80, 0x84, 0x89, 0x8d, 0x91,
0x93, 0x95, 0x96, 0x94, 0x92, 0x8f, 0x8b, 0x88, 0x84, 0x81, 0x7e, 0x7b, 0x78, 0x75, 0x73, 0x71,
0x70, 0x6f, 0x70, 0x71, 0x73, 0x75, 0x77, 0x79, 0x7c, 0x7f, 0x81, 0x84, 0x87, 0x89, 0x8b, 0x8c,
0x8e, 0x90, 0x91, 0x91, 0x90, 0x8f, 0x8c, 0x88, 0x82, 0x7c, 0x76, 0x6f, 0x6b, 0x68, 0x66, 0x65,
0x66, 0x67, 0x69, 0x6b, 0x6e, 0x72, 0x76, 0x7a, 0x7f, 0x81, 0x84, 0x87, 0x8a, 0x8c, 0x8e, 0x8f,
0x90, 0x90, 0x90, 0x8e, 0x8c, 0x8a, 0x89, 0x88, 0x89, 0x89, 0x8a, 0x8b, 0x8b, 0x8a, 0x88, 0x86,
0x83, 0x81, 0x80, 0x80, 0x7e, 0x7c, 0x7b, 0x7a, 0x7a, 0x7a, 0x79, 0x77, 0x76, 0x76, 0x75, 0x73,
0x71, 0x6f, 0x6d, 0x6c, 0x6c, 0x6e, 0x70, 0x73, 0x77, 0x7b, 0x7f, 0x82, 0x88, 0x8d, 0x93, 0x98,
0x9b, 0x9d, 0x9e, 0x9e, 0x9b, 0x99, 0x95, 0x91, 0x8e, 0x8a, 0x87, 0x83, 0x80, 0x7c, 0x78, 0x74,
0x71, 0x6f, 0x6d, 0x6d, 0x6d, 0x6d, 0x6e, 0x6f, 0x71, 0x72, 0x74, 0x77, 0x79, 0x7b, 0x7d, 0x7f,
0x80, 0x83, 0x85, 0x88, 0x8a, 0x8d, 0x8e, 0x8e, 0x8d, 0x8b, 0x86, 0x81, 0x7e, 0x7b, 0x79, 0x77,
0x77, 0x77, 0x77, 0x78, 0x7a, 0x7c, 0x7e, 0x80, 0x81, 0x82, 0x83, 0x83, 0x83, 0x83, 0x82, 0x82,
0x82, 0x82, 0x82, 0x82, 0x81, 0x80, 0x7e, 0x7d, 0x7d, 0x7d, 0x7f, 0x80, 0x82, 0x83, 0x84, 0x84,
0x84, 0x84, 0x84, 0x85, 0x86, 0x87, 0x88, 0x89, 0x89, 0x88, 0x88, 0x88, 0x87, 0x85, 0x84, 0x83,
0x81, 0x7f, 0x7c, 0x79, 0x76, 0x75, 0x75, 0x75, 0x76, 0x77, 0x78, 0x79, 0x7a, 0x7d, 0x80, 0x83,
0x87, 0x89, 0x8a, 0x8a, 0x89, 0x86, 0x83, 0x80, 0x7d, 0x7a, 0x78, 0x76, 0x75, 0x73, 0x71, 0x70,
0x6f, 0x70, 0x71, 0x73, 0x76, 0x78, 0x7a, 0x7c, 0x7e, 0x80, 0x81, 0x84, 0x87, 0x89, 0x8b, 0x8d,
0x8d, 0x8e, 0x8e, 0x8e, 0x8e, 0x8e, 0x8e, 0x8d, 0x8b, 0x87, 0x83, 0x7f, 0x7b, 0x77, 0x75, 0x73,
0x73, 0x74, 0x75, 0x76, 0x78, 0x79, 0x7c, 0x7e, 0x80, 0x83, 0x85, 0x87, 0x89, 0x8b, 0x8b, 0x8c,
0x8c, 0x8d, 0x8e, 0x8e, 0x8d, 0x8c, 0x89, 0x86, 0x85, 0x85, 0x86, 0x87, 0x88, 0x89, 0x88, 0x87,
0x84, 0x81, 0x7e, 0x7b, 0x78, 0x76, 0x74, 0x72, 0x71, 0x6f, 0x6e, 0x6c, 0x6b, 0x6a, 0x6a, 0x6a,
0x6a, 0x69, 0x68, 0x67, 0x67, 0x68, 0x6b, 0x6f, 0x74, 0x79, 0x7f, 0x83, 0x87, 0x8c, 0x90, 0x94,
0x98, 0x9b, 0x9d, 0x9e, 0x9e, 0x9c, 0x9a, 0x97, 0x93, 0x8f, 0x8d, 0x8a, 0x87, 0x84, 0x80, 0x7d,
0x79, 0x77, 0x75, 0x75, 0x76, 0x78, 0x79, 0x79, 0x7a, 0x7b, 0x7b, 0x7b, 0x7c, 0x7e, 0x80, 0x80,
0x81, 0x82, 0x83, 0x84, 0x85, 0x87, 0x89, 0x8b, 0x8d, 0x8d, 0x8b, 0x88, 0x85, 0x81, 0x7e, 0x7c,
0x7b, 0x7a, 0x7b, 0x7c, 0x7c, 0x7c, 0x7c, 0x7c, 0x7c, 0x7d, 0x7d, 0x7c, 0x7a, 0x79, 0x77, 0x76,
0x75, 0x75, 0x75, 0x76, 0x77, 0x77, 0x77, 0x76, 0x76, 0x76, 0x77, 0x79, 0x7d, 0x80, 0x82, 0x85,
0x87, 0x88, 0x88, 0x88, 0x88, 0x88, 0x89, 0x89, 0x89, 0x88, 0x87, 0x85, 0x85, 0x83, 0x83, 0x83,
0x84, 0x83, 0x81, 0x80, 0x7f, 0x7d, 0x7c, 0x7c, 0x7d, 0x7f, 0x80, 0x82, 0x83, 0x84, 0x86, 0x88,
0x8a, 0x8b, 0x8d, 0x8d, 0x8d, 0x8b, 0x88, 0x85, 0x81, 0x7f, 0x7c, 0x7b, 0x7a, 0x79, 0x78, 0x77,
0x76, 0x76, 0x76, 0x78, 0x7a, 0x7c, 0x7f, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x81, 0x81, 0x82,
0x82, 0x83, 0x82, 0x80, 0x80, 0x7f, 0x7e, 0x7f, 0x7f, 0x7e, 0x7c, 0x7b, 0x78, 0x76, 0x74, 0x71,
0x71, 0x72, 0x72, 0x74, 0x76, 0x77, 0x79, 0x7a, 0x7c, 0x7e, 0x80, 0x83, 0x85, 0x87, 0x88, 0x89,
0x89, 0x89, 0x8a, 0x8b, 0x8c, 0x8c, 0x8c, 0x8c, 0x8b, 0x8a, 0x8b, 0x8c, 0x8d, 0x8f, 0x90, 0x91,
0x91, 0x8f, 0x8b, 0x88, 0x83, 0x80, 0x7e, 0x7b, 0x79, 0x77, 0x74, 0x72, 0x6f, 0x6e, 0x6d, 0x6c,
0x6d, 0x6e, 0x6e, 0x6e, 0x6e, 0x6e, 0x70, 0x73, 0x76, 0x7b, 0x80, 0x85, 0x8a, 0x8e, 0x90, 0x92,
0x94, 0x95, 0x96, 0x97, 0x97, 0x96, 0x94, 0x91, 0x8d, 0x8a, 0x86, 0x82, 0x80, 0x7d, 0x7a, 0x76,
0x72, 0x70, 0x6e, 0x6e, 0x6f, 0x70, 0x73, 0x75, 0x77, 0x78, 0x78, 0x77, 0x76, 0x76, 0x76, 0x78,
0x79, 0x7b, 0x7d, 0x7d, 0x7f, 0x81, 0x83, 0x85, 0x88, 0x8a, 0x8b, 0x8b, 0x8a, 0x88, 0x87, 0x85,
0x84, 0x85, 0x85, 0x86, 0x87, 0x88, 0x87, 0x86, 0x84, 0x83, 0x82, 0x81, 0x80, 0x80, 0x7f, 0x7d,
0x7a, 0x79, 0x78, 0x78, 0x79, 0x7a, 0x7a, 0x7b, 0x7b, 0x7c, 0x7d, 0x7e, 0x80, 0x83, 0x87, 0x8b,
0x8e, 0x8f, 0x8f, 0x8e, 0x8c, 0x8b, 0x89, 0x88, 0x86, 0x84, 0x82, 0x80, 0x7d, 0x7b, 0x7a, 0x79,
0x79, 0x79, 0x79, 0x78, 0x77, 0x75, 0x74, 0x73, 0x73, 0x74, 0x76, 0x78, 0x7b, 0x7e, 0x7f, 0x80,
0x80, 0x81, 0x82, 0x82, 0x82, 0x82, 0x81, 0x80, 0x7d, 0x7c, 0x7a, 0x7a, 0x7a, 0x7a, 0x7b, 0x7c,
0x7c, 0x7d, 0x7d, 0x7f, 0x80, 0x81, 0x85, 0x88, 0x8a, 0x8b, 0x8c, 0x8b, 0x8a, 0x88, 0x87, 0x87,
0x87, 0x86, 0x86, 0x85, 0x84, 0x83, 0x82, 0x81, 0x82, 0x82, 0x82, 0x82, 0x80, 0x7f, 0x7d, 0x7c,
0x7b, 0x7b, 0x7b, 0x7c, 0x7d, 0x7e, 0x7e, 0x7e, 0x7e, 0x7f, 0x7f, 0x80, 0x81, 0x83, 0x84, 0x84,
0x84, 0x83, 0x82, 0x82, 0x82, 0x82, 0x82, 0x82, 0x82, 0x82, 0x83, 0x84, 0x85, 0x86, 0x87, 0x88,
0x88, 0x87, 0x85, 0x81, 0x7e, 0x7a, 0x76, 0x74, 0x72, 0x70, 0x6e, 0x6c, 0x6a, 0x68, 0x66, 0x66,
0x67, 0x68, 0x6a, 0x6c, 0x6e, 0x70, 0x73, 0x75, 0x79, 0x7d, 0x81, 0x86, 0x8b, 0x90, 0x94, 0x96,
0x98, 0x99, 0x9a, 0x9b, 0x9c, 0x9b, 0x9b, 0x99, 0x96, 0x92, 0x8f, 0x8c, 0x89, 0x87, 0x85, 0x81,
0x7f, 0x7d, 0x7b, 0x79, 0x79, 0x79, 0x7a, 0x7b, 0x7d, 0x7f, 0x7f, 0x7e, 0x7c, 0x7a, 0x78, 0x77,
0x77, 0x78, 0x79, 0x7a, 0x7b, 0x7c, 0x7e, 0x80, 0x81, 0x83, 0x83, 0x84, 0x83, 0x82, 0x81, 0x80,
0x80, 0x80, 0x80, 0x80, 0x81, 0x81, 0x81, 0x80, 0x7e, 0x7b, 0x79, 0x77, 0x76, 0x75, 0x75, 0x74,
0x74, 0x72, 0x71, 0x71, 0x71, 0x72, 0x73, 0x74, 0x75, 0x77, 0x79, 0x7c, 0x80, 0x83, 0x87, 0x8c,
0x90, 0x93, 0x95, 0x95, 0x94, 0x92, 0x90, 0x8e, 0x8d, 0x8b, 0x8a, 0x88, 0x86, 0x83, 0x81, 0x80,
0x80, 0x7f, 0x7f, 0x7f, 0x7f, 0x7f, 0x7e, 0x7d, 0x7d, 0x7d, 0x7e, 0x7f, 0x80, 0x82, 0x84, 0x84,
0x85, 0x85, 0x84, 0x84, 0x83, 0x83, 0x81, 0x80, 0x7f, 0x7c, 0x7b, 0x7a, 0x7a, 0x7a, 0x7b, 0x7c,
0x7c, 0x7c, 0x7c, 0x7d, 0x7e, 0x7f, 0x80, 0x81, 0x82, 0x83, 0x85, 0x85, 0x84, 0x81, 0x80, 0x7d,
0x7b, 0x7a, 0x78, 0x78, 0x76, 0x76, 0x76, 0x77, 0x78, 0x79, 0x7a, 0x7b, 0x7b, 0x7a, 0x7a, 0x7a,
0x79, 0x7a, 0x7b, 0x7c, 0x7e, 0x80, 0x81, 0x82, 0x82, 0x82, 0x82, 0x83, 0x84, 0x85, 0x86, 0x87,
0x88, 0x89, 0x89, 0x89, 0x89, 0x89, 0x8a, 0x8a, 0x8a, 0x8b, 0x8b, 0x8c, 0x8d, 0x8f, 0x90, 0x90,
0x90, 0x90, 0x8e, 0x8c, 0x89, 0x84, 0x80, 0x7d, 0x7a, 0x77, 0x75, 0x73, 0x70, 0x6e, 0x6c, 0x6b,
0x6a, 0x6a, 0x6b, 0x6d, 0x6e, 0x70, 0x72, 0x75, 0x78, 0x7b, 0x7d, 0x80, 0x82, 0x85, 0x89, 0x8b,
0x8d, 0x8d, 0x8e, 0x8f, 0x8f, 0x8f, 0x8f, 0x8d, 0x8b, 0x89, 0x86, 0x82, 0x80, 0x7e, 0x7d, 0x7b,
0x7a, 0x78, 0x77, 0x76, 0x74, 0x74, 0x74, 0x75, 0x76, 0x77, 0x79, 0x7a, 0x7a, 0x7a, 0x7a, 0x79,
0x79, 0x79, 0x7b, 0x7c, 0x7e, 0x7f, 0x80, 0x82, 0x84, 0x87, 0x89, 0x8a, 0x8b, 0x8b, 0x8b, 0x8b,
0x8a, 0x8a, 0x8a, 0x8a, 0x8b, 0x8b, 0x8c, 0x8b, 0x8a, 0x88, 0x84, 0x81, 0x80, 0x7f, 0x7d, 0x7c,
0x7b, 0x79, 0x78, 0x78, 0x77, 0x76, 0x77, 0x77, 0x77, 0x78, 0x7a, 0x7b, 0x7e, 0x81, 0x84, 0x87,
0x8b, 0x8e, 0x90, 0x91, 0x90, 0x8f, 0x8c, 0x89, 0x86, 0x83, 0x82, 0x80, 0x7f, 0x7d, 0x7b, 0x78,
0x76, 0x74, 0x73, 0x72, 0x71, 0x70, 0x71, 0x71, 0x72, 0x73, 0x74, 0x75, 0x76, 0x78, 0x7a, 0x7c,
0x7d, 0x7e, 0x7e, 0x7f, 0x7f, 0x7f, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x81, 0x82, 0x83,
0x84, 0x86, 0x87, 0x88, 0x89, 0x8a, 0x8b, 0x8b, 0x8c, 0x8c, 0x8e, 0x8e, 0x8e, 0x8d, 0x8c, 0x89,
0x86, 0x83, 0x81, 0x80, 0x7f, 0x7e, 0x7d, 0x7d, 0x7d, 0x7d, 0x7e, 0x7e, 0x7e, 0x7f, 0x7e, 0x7d,
0x7d, 0x7d, 0x7c, 0x7c, 0x7d, 0x7e, 0x7f, 0x80, 0x81, 0x81, 0x81, 0x80, 0x80, 0x7f, 0x7f, 0x7f,
0x7f, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80,
0x80, 0x80, 0x80, 0x80, 0x80, 0x7f, 0x7c, 0x78, 0x75, 0x73, 0x71, 0x70, 0x70, 0x70, 0x70, 0x70,
0x70, 0x71, 0x71, 0x73, 0x75, 0x77, 0x79, 0x7c, 0x7f, 0x81, 0x84, 0x86, 0x88, 0x8b, 0x8e, 0x91,
0x94, 0x96, 0x96, 0x97, 0x97, 0x97, 0x97, 0x96, 0x96, 0x95, 0x93, 0x90, 0x8d, 0x8a, 0x87, 0x85,
0x84, 0x82, 0x80, 0x80, 0x7e, 0x7d, 0x7b, 0x79, 0x78, 0x78, 0x77, 0x77, 0x78, 0x79, 0x79, 0x78,
0x77, 0x77, 0x77, 0x78, 0x78, 0x79, 0x7a, 0x7b, 0x7c, 0x7d, 0x7e, 0x7e, 0x7f, 0x80, 0x80, 0x7f,
0x7f, 0x7f, 0x7e, 0x7d, 0x7c, 0x7b, 0x7c, 0x7c, 0x7d, 0x7e, 0x7c, 0x7b, 0x79, 0x78, 0x76, 0x76,
0x76, 0x75, 0x76, 0x77, 0x77, 0x78, 0x79, 0x79, 0x7b, 0x7c, 0x7e, 0x80, 0x82, 0x85, 0x88, 0x8a,
0x8d, 0x90, 0x93, 0x95, 0x96, 0x98, 0x97, 0x95, 0x93, 0x90, 0x8e, 0x8c, 0x8b, 0x8a, 0x88, 0x87,
0x85, 0x82, 0x80, 0x7f, 0x7c, 0x7b, 0x7a, 0x78, 0x78, 0x79, 0x79, 0x79, 0x79, 0x78, 0x79, 0x7a,
0x7c, 0x7e, 0x7e, 0x7e, 0x7d, 0x7d, 0x7e, 0x7e, 0x7f, 0x7f, 0x80, 0x80, 0x80, 0x80, 0x80, 0x7f,
0x7f, 0x80, 0x80, 0x80, 0x81, 0x82, 0x82, 0x81, 0x81, 0x80, 0x80, 0x80, 0x80, 0x7f, 0x7f, 0x7d,
0x7b, 0x79, 0x77, 0x75, 0x74, 0x75, 0x75, 0x76, 0x77, 0x77, 0x78, 0x78, 0x79, 0x7a, 0x7c, 0x7d,
0x7e, 0x7f, 0x80, 0x80, 0x80, 0x80, 0x81, 0x83, 0x86, 0x89, 0x8a, 0x8a, 0x8a, 0x89, 0x89, 0x89,
0x89, 0x89, 0x8a, 0x8c, 0x8d, 0x8d, 0x8e, 0x8d, 0x8d, 0x8c, 0x8b, 0x8b, 0x8a, 0x89, 0x88, 0x87,
0x85, 0x83, 0x82, 0x81, 0x81, 0x80, 0x80, 0x7e, 0x7b, 0x77, 0x75, 0x73, 0x71, 0x71, 0x72, 0x72,
0x73, 0x74, 0x74, 0x74, 0x75, 0x75, 0x76, 0x77, 0x79, 0x7a, 0x7b, 0x7c, 0x7e, 0x7e, 0x7f, 0x80,
0x82, 0x84, 0x86, 0x86, 0x87, 0x86, 0x85, 0x85, 0x85, 0x85, 0x84, 0x84, 0x84, 0x83, 0x82, 0x81,
0x80, 0x7e, 0x7d, 0x7d, 0x7d, 0x7d, 0x7c, 0x7a, 0x79, 0x78, 0x78, 0x78, 0x78, 0x7a, 0x7c, 0x7d,
0x7e, 0x80, 0x80, 0x80, 0x80, 0x82, 0x84, 0x85, 0x87, 0x88, 0x89, 0x89, 0x89, 0x8a, 0x8a, 0x8a,
0x89, 0x89, 0x88, 0x87, 0x85, 0x82, 0x81, 0x80, 0x80, 0x81, 0x82, 0x82, 0x81, 0x80, 0x7f, 0x7d,
0x7c, 0x7b, 0x7b, 0x7b, 0x7c, 0x7d, 0x7e, 0x7e, 0x7e, 0x7e, 0x7f, 0x7f, 0x80, 0x81, 0x82, 0x82,
0x82, 0x83, 0x83, 0x84, 0x85, 0x86, 0x87, 0x88, 0x87, 0x85, 0x83, 0x80, 0x7e, 0x7d, 0x7c, 0x7c,
0x7b, 0x7a, 0x79, 0x77, 0x75, 0x73, 0x71, 0x71, 0x71, 0x71, 0x71, 0x72, 0x72, 0x73, 0x73, 0x74,
0x76, 0x79, 0x7b, 0x7d, 0x7f, 0x80, 0x80, 0x81, 0x81, 0x83, 0x85, 0x88, 0x8a, 0x8c, 0x8d, 0x8e,
0x8e, 0x8f, 0x8f, 0x8f, 0x90, 0x90, 0x90, 0x90, 0x8f, 0x8d, 0x8b, 0x8a, 0x88, 0x87, 0x86, 0x85,
0x84, 0x82, 0x80, 0x7e, 0x7c, 0x7b, 0x7a, 0x7a, 0x7b, 0x7a, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b,
0x7c, 0x7d, 0x7d, 0x7d, 0x7c, 0x7c, 0x7b, 0x7b, 0x7c, 0x7e, 0x80, 0x80, 0x81, 0x81, 0x80, 0x80,
0x7e, 0x7e, 0x7e, 0x7f, 0x80, 0x80, 0x81, 0x82, 0x83, 0x82, 0x82, 0x81, 0x80, 0x80, 0x7e, 0x7c,
0x7b, 0x79, 0x78, 0x77, 0x76, 0x76, 0x77, 0x78, 0x77, 0x77, 0x76, 0x75, 0x74, 0x75, 0x76, 0x79,
0x7b, 0x7e, 0x80, 0x82, 0x84, 0x84, 0x85, 0x85, 0x86, 0x87, 0x88, 0x88, 0x89, 0x89, 0x8a, 0x8a,
0x8b, 0x8c, 0x8e, 0x8f, 0x90, 0x90, 0x8f, 0x8e, 0x8c, 0x8b, 0x8b, 0x8a, 0x8b, 0x8b, 0x8b, 0x8b,
0x8a, 0x88, 0x85, 0x83, 0x82, 0x80, 0x80, 0x7e, 0x7c, 0x79, 0x77, 0x75, 0x74, 0x73, 0x73, 0x74,
0x76, 0x76, 0x76, 0x76, 0x76, 0x76, 0x77, 0x78, 0x79, 0x7a, 0x7b, 0x7c, 0x7d, 0x7d, 0x7c, 0x7b,
0x7b, 0x7a, 0x7a, 0x79, 0x77, 0x76, 0x75, 0x74, 0x73, 0x74, 0x76, 0x78, 0x7a, 0x7c, 0x7c, 0x7c,
0x7c, 0x7c, 0x7d, 0x7e, 0x80, 0x81, 0x84, 0x86, 0x88, 0x89, 0x8a, 0x8b, 0x8c, 0x8d, 0x8e, 0x8e,
0x8e, 0x8e, 0x8e, 0x8e, 0x8e, 0x8e, 0x8f, 0x90, 0x91, 0x91, 0x90, 0x8e, 0x8c, 0x89, 0x87, 0x86,
0x85, 0x85, 0x84, 0x83, 0x81, 0x80, 0x7d, 0x7b, 0x78, 0x76, 0x75, 0x75, 0x74, 0x73, 0x73, 0x72,
0x73, 0x74, 0x75, 0x77, 0x79, 0x7a, 0x7a, 0x7a, 0x7a, 0x7a, 0x7b, 0x7c, 0x7e, 0x80, 0x81, 0x83,
0x85, 0x85, 0x85, 0x84, 0x84, 0x84, 0x83, 0x83, 0x83, 0x81, 0x80, 0x7f, 0x7c, 0x7a, 0x79, 0x78,
0x77, 0x77, 0x77, 0x76, 0x75, 0x74, 0x74, 0x75, 0x76, 0x77, 0x79, 0x7b, 0x7e, 0x7f, 0x80, 0x80,
0x81, 0x82, 0x83, 0x84, 0x86, 0x86, 0x87, 0x87, 0x87, 0x88, 0x8a, 0x8c, 0x8e, 0x8f, 0x8f, 0x8f,
0x8e, 0x8c, 0x8b, 0x8b, 0x8b, 0x8b, 0x8d, 0x8e, 0x8f, 0x8f, 0x8e, 0x8c, 0x8a, 0x88, 0x85, 0x83,
0x81, 0x80, 0x7d, 0x7b, 0x78, 0x76, 0x74, 0x74, 0x74, 0x74, 0x74, 0x73, 0x72, 0x71, 0x70, 0x71,
0x73, 0x75, 0x78, 0x7b, 0x7d, 0x7e, 0x7f, 0x7e, 0x7d, 0x7c, 0x7c, 0x7c, 0x7c, 0x7c, 0x7c, 0x7c,
0x7c, 0x7c, 0x7d, 0x7e, 0x7f, 0x80, 0x80, 0x80, 0x7f, 0x7e, 0x7e, 0x7f, 0x80, 0x80, 0x82, 0x85,
0x87, 0x88, 0x89, 0x88, 0x88, 0x87, 0x86, 0x85, 0x84, 0x83, 0x82, 0x81, 0x80, 0x7f, 0x7f, 0x7f,
0x80, 0x80, 0x81, 0x82, 0x82, 0x82, 0x83, 0x83, 0x84, 0x85, 0x87, 0x88, 0x89, 0x8a, 0x89, 0x88,
0x87, 0x85, 0x83, 0x81, 0x80, 0x7f, 0x7e, 0x7c, 0x7b, 0x79, 0x79, 0x7a, 0x7b, 0x7c, 0x7e, 0x7e,
0x7e, 0x7e, 0x7d, 0x7c, 0x7d, 0x7e, 0x7f, 0x80, 0x83, 0x84, 0x85, 0x86, 0x87, 0x86, 0x84, 0x83,
0x82, 0x82, 0x81, 0x81, 0x80, 0x7e, 0x7e, 0x7f, 0x7e, 0x7e, 0x7f, 0x7e, 0x7d, 0x7c, 0x7a, 0x79,
0x78, 0x77, 0x78, 0x78, 0x79, 0x79, 0x78, 0x77, 0x76, 0x74, 0x73, 0x73, 0x73, 0x74, 0x75, 0x76,
0x77, 0x79, 0x7a, 0x7d, 0x7f, 0x80, 0x83, 0x85, 0x86, 0x87, 0x88, 0x88, 0x89, 0x8c, 0x8d, 0x90,
0x93, 0x95, 0x96, 0x96, 0x95, 0x94, 0x93, 0x92, 0x91, 0x90, 0x8e, 0x8c, 0x89, 0x85, 0x82, 0x80,
0x7e, 0x7d, 0x7b, 0x7a, 0x79, 0x78, 0x77, 0x76, 0x75, 0x75, 0x77, 0x78, 0x79, 0x7b, 0x7c, 0x7c,
0x7b, 0x7b, 0x7b, 0x7b, 0x7c, 0x7d, 0x7d, 0x7e, 0x7e, 0x7e, 0x7d, 0x7d, 0x7e, 0x7f, 0x7f, 0x7f,
0x80, 0x7f, 0x7d, 0x7c, 0x7c, 0x7c, 0x7c, 0x7e, 0x7f, 0x80, 0x81, 0x81, 0x80, 0x80, 0x7f, 0x7d,
0x7c, 0x7c, 0x7b, 0x7b, 0x7a, 0x78, 0x77, 0x77, 0x77, 0x77, 0x79, 0x7a, 0x7b, 0x7b, 0x7c, 0x7d,
0x7e, 0x80, 0x83, 0x86, 0x89, 0x8c, 0x8e, 0x8f, 0x8e, 0x8d, 0x8d, 0x8b, 0x8b, 0x8b, 0x8b, 0x8b,
0x8b, 0x8a, 0x8a, 0x88, 0x87, 0x87, 0x87, 0x86, 0x86, 0x85, 0x83, 0x81, 0x81, 0x81, 0x82, 0x84,
0x86, 0x87, 0x89, 0x89, 0x88, 0x86, 0x84, 0x82, 0x80, 0x80, 0x7e, 0x7d, 0x7b, 0x79, 0x76, 0x75,
0x73, 0x72, 0x72, 0x72, 0x72, 0x73, 0x72, 0x72, 0x72, 0x73, 0x74, 0x76, 0x77, 0x79, 0x7a, 0x7b,
0x7a, 0x79, 0x77, 0x75, 0x74, 0x74, 0x74, 0x75, 0x75, 0x76, 0x77, 0x78, 0x79, 0x7b, 0x7d, 0x80,
0x81, 0x82, 0x83, 0x84, 0x84, 0x86, 0x88, 0x8a, 0x8e, 0x91, 0x94, 0x96, 0x97, 0x97, 0x96, 0x95,
0x94, 0x92, 0x92, 0x92, 0x90, 0x8f, 0x8d, 0x8b, 0x8a, 0x89, 0x89, 0x88, 0x87, 0x86, 0x84, 0x82,
0x80, 0x7e, 0x7c, 0x7c, 0x7c, 0x7c, 0x7c, 0x7b, 0x7a, 0x77, 0x75, 0x73, 0x72, 0x71, 0x71, 0x72,
0x73, 0x73, 0x74, 0x74, 0x75, 0x76, 0x78, 0x7a, 0x7b, 0x7d, 0x7d, 0x7d, 0x7c, 0x7c, 0x7d, 0x7e,
0x80, 0x82, 0x84, 0x85, 0x86, 0x86, 0x85, 0x84, 0x83, 0x83, 0x82, 0x81, 0x80, 0x7f, 0x7c, 0x79,
0x77, 0x75, 0x75, 0x75, 0x76, 0x77, 0x77, 0x77, 0x78, 0x78, 0x79, 0x7b, 0x7e, 0x80, 0x82, 0x85,
0x87, 0x87, 0x87, 0x88, 0x89, 0x8a, 0x8c, 0x8d, 0x8f, 0x8f, 0x8f, 0x8e, 0x8d, 0x8d, 0x8d, 0x8d,
0x8d, 0x8d, 0x8c, 0x8b, 0x89, 0x86, 0x85, 0x85, 0x85, 0x85, 0x86, 0x86, 0x85, 0x84, 0x83, 0x81,
0x80, 0x7f, 0x7e, 0x7d, 0x7c, 0x7a, 0x78, 0x75, 0x73, 0x72, 0x71, 0x71, 0x71, 0x72, 0x73, 0x73,
0x73, 0x73, 0x74, 0x76, 0x78, 0x7a, 0x7d, 0x7e, 0x7e, 0x7e, 0x7d, 0x7c, 0x7c, 0x7c, 0x7c, 0x7d,
0x7e, 0x7e, 0x7e, 0x7e, 0x7d, 0x7c, 0x7b, 0x7c, 0x7c, 0x7d, 0x7e, 0x7e, 0x7e, 0x7e, 0x7f, 0x80,
0x83, 0x87, 0x8a, 0x8d, 0x8e, 0x8f, 0x8f, 0x8e, 0x8e, 0x8d, 0x8d, 0x8c, 0x8c, 0x8b, 0x89, 0x87,
0x84, 0x83, 0x82, 0x81, 0x81, 0x81, 0x81, 0x80, 0x80, 0x80, 0x7f, 0x80, 0x81, 0x82, 0x83, 0x84,
0x84, 0x83, 0x80, 0x7e, 0x7c, 0x7b, 0x7a, 0x7a, 0x7a, 0x7a, 0x7a, 0x7a, 0x7a, 0x79, 0x7a, 0x7b,
0x7c, 0x7d, 0x7e, 0x7e, 0x7e, 0x7d, 0x7d, 0x7e, 0x7f, 0x81, 0x84, 0x86, 0x88, 0x88, 0x88, 0x87,
0x86, 0x84, 0x83, 0x83, 0x82, 0x81, 0x80, 0x7f, 0x7d, 0x7b, 0x7a, 0x7a, 0x7a, 0x7a, 0x7a, 0x78,
0x76, 0x74, 0x73, 0x72, 0x73, 0x74, 0x76, 0x78, 0x7a, 0x7a, 0x7a, 0x7a, 0x79, 0x7a, 0x7a, 0x7c,
0x7e, 0x80, 0x80, 0x81, 0x82, 0x83, 0x85, 0x87, 0x89, 0x8b, 0x8d, 0x8d, 0x8d, 0x8c, 0x8b, 0x8b,
0x8c, 0x8e, 0x90, 0x92, 0x93, 0x94, 0x93, 0x93, 0x91, 0x90, 0x8e, 0x8c, 0x8a, 0x87, 0x84, 0x81,
0x7e, 0x7b, 0x78, 0x77, 0x76, 0x76, 0x76, 0x75, 0x74, 0x73, 0x72, 0x72, 0x72, 0x74, 0x76, 0x78,
0x7a, 0x7b, 0x7c, 0x7c, 0x7c, 0x7c, 0x7e, 0x7f, 0x80, 0x80, 0x80, 0x80, 0x7f, 0x7e, 0x7d, 0x7d,
0x7d, 0x7d, 0x7d, 0x7d, 0x7c, 0x7a, 0x79, 0x78, 0x77, 0x77, 0x79, 0x7b, 0x7d, 0x7d, 0x7e, 0x7f,
0x80, 0x80, 0x81, 0x81, 0x82, 0x83, 0x83, 0x82, 0x81, 0x80, 0x7f, 0x7f, 0x80, 0x80, 0x81, 0x82,
0x83, 0x83, 0x83, 0x83, 0x85, 0x86, 0x88, 0x8a, 0x8c, 0x8c, 0x8c, 0x8b, 0x8b, 0x8a, 0x8a, 0x8b,
0x8b, 0x8b, 0x8a, 0x89, 0x88, 0x85, 0x82, 0x81, 0x80, 0x80, 0x7f, 0x7f, 0x7f, 0x7e, 0x7d, 0x7d,
0x7d, 0x7e, 0x80, 0x82, 0x84, 0x85, 0x86, 0x85, 0x85, 0x84, 0x83, 0x82, 0x80, 0x80, 0x7f, 0x7d,
0x7a, 0x77, 0x74, 0x72, 0x70, 0x70, 0x6f, 0x6f, 0x6e, 0x6e, 0x6d, 0x6d, 0x6e, 0x70, 0x72, 0x74,
0x76, 0x77, 0x77, 0x77, 0x76, 0x76, 0x76, 0x77, 0x79, 0x7c, 0x7e, 0x7f, 0x80, 0x80, 0x81, 0x82,
0x84, 0x85, 0x87, 0x88, 0x89, 0x8a, 0x8a, 0x8a, 0x8b, 0x8d, 0x90, 0x93, 0x96, 0x98, 0x99, 0x99,
0x98, 0x97, 0x97, 0x96, 0x94, 0x94, 0x92, 0x91, 0x8e, 0x8c, 0x88, 0x85, 0x83, 0x82, 0x80, 0x7f,
0x7d, 0x7a, 0x76, 0x73, 0x71, 0x71, 0x72, 0x72, 0x73, 0x74, 0x74, 0x73, 0x73, 0x72, 0x72, 0x73,
0x73, 0x75, 0x76, 0x77, 0x78, 0x78, 0x77, 0x78, 0x79, 0x7b, 0x7c, 0x7d, 0x7d, 0x7c, 0x7b, 0x7b,
0x7a, 0x7a, 0x7c, 0x7f, 0x80, 0x83, 0x85, 0x85, 0x85, 0x85, 0x85, 0x84, 0x83, 0x82, 0x81, 0x80,
0x7e, 0x7d, 0x7b, 0x7a, 0x7a, 0x7a, 0x7c, 0x7c, 0x7c, 0x7c, 0x7c, 0x7c, 0x7c, 0x7d, 0x7f, 0x81,
0x84, 0x88, 0x8a, 0x8b, 0x8d, 0x8e, 0x8f, 0x91, 0x92, 0x92, 0x93, 0x93, 0x93, 0x91, 0x8f, 0x8d,
0x8c, 0x8b, 0x8a, 0x88, 0x86, 0x84, 0x81, 0x7f, 0x7d, 0x7b, 0x79, 0x79, 0x7a, 0x7b, 0x7c, 0x7c,
0x7d, 0x7d, 0x7e, 0x7f, 0x7f, 0x7f, 0x7f, 0x7e, 0x7d, 0x7b, 0x78, 0x77, 0x76, 0x75, 0x75, 0x75,
0x75, 0x75, 0x74, 0x73, 0x73, 0x72, 0x73, 0x74, 0x76, 0x78, 0x78, 0x79, 0x7a, 0x7b, 0x7c, 0x7d,
0x7f, 0x80, 0x80, 0x81, 0x80, 0x80, 0x7f, 0x7e, 0x7d, 0x7d, 0x7e, 0x7f, 0x80, 0x81, 0x81, 0x82,
0x82, 0x84, 0x86, 0x88, 0x8b, 0x8e, 0x91, 0x92, 0x93, 0x94, 0x94, 0x94, 0x94, 0x94, 0x93, 0x91,
0x8f, 0x8c, 0x89, 0x86, 0x83, 0x81, 0x80, 0x7f, 0x7d, 0x7c, 0x7a, 0x79, 0x78, 0x78, 0x77, 0x78,
0x79, 0x7a, 0x7a, 0x7a, 0x79, 0x79, 0x78, 0x79, 0x7a, 0x7b, 0x7c, 0x7e, 0x7f, 0x7f, 0x7e, 0x7d,
0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7c, 0x7c, 0x7b, 0x7b, 0x7c, 0x7d, 0x7f, 0x80, 0x82, 0x84, 0x84,
0x85, 0x84, 0x85, 0x85, 0x85, 0x85, 0x85, 0x85, 0x84, 0x82, 0x80, 0x7f, 0x7e, 0x7c, 0x7b, 0x79,
0x78, 0x76, 0x74, 0x73, 0x72, 0x71, 0x72, 0x73, 0x75, 0x77, 0x79, 0x7a, 0x7a, 0x7b, 0x7d, 0x7f,
0x80, 0x83, 0x84, 0x86, 0x87, 0x89, 0x8a, 0x8a, 0x8b, 0x8c, 0x8d, 0x8e, 0x8e, 0x8e, 0x8d, 0x8c,
0x8b, 0x8a, 0x89, 0x8a, 0x8b, 0x8d, 0x8e, 0x8e, 0x8e, 0x8e, 0x8d, 0x8c, 0x8a, 0x88, 0x85, 0x83,
0x81, 0x7f, 0x7d, 0x7b, 0x79, 0x78, 0x77, 0x76, 0x74, 0x73, 0x71, 0x6f, 0x6e, 0x6e, 0x6e, 0x70,
0x72, 0x74, 0x77, 0x79, 0x7a, 0x7b, 0x7d, 0x7f, 0x80, 0x81, 0x82, 0x83, 0x83, 0x82, 0x82, 0x81,
0x80, 0x80, 0x80, 0x7f, 0x7d, 0x7c, 0x7a, 0x78, 0x75, 0x74, 0x73, 0x73, 0x75, 0x77, 0x79, 0x7b,
0x7d, 0x7f, 0x81, 0x84, 0x86, 0x88, 0x8a, 0x8a, 0x8b, 0x8b, 0x8a, 0x8a, 0x89, 0x89, 0x89, 0x89,
0x89, 0x88, 0x87, 0x86, 0x84, 0x83, 0x83, 0x83, 0x83, 0x84, 0x84, 0x85, 0x85, 0x85, 0x86, 0x87,
0x88, 0x88, 0x88, 0x88, 0x86, 0x85, 0x82, 0x80, 0x7f, 0x7f, 0x7f, 0x7e, 0x7f, 0x7e, 0x7e, 0x7d,
0x7d, 0x7c, 0x7c, 0x7d, 0x7e, 0x80, 0x80, 0x81, 0x82, 0x82, 0x83, 0x83, 0x84, 0x84, 0x83, 0x81,
0x80, 0x7e, 0x7b, 0x78, 0x75, 0x73, 0x72, 0x71, 0x70, 0x6e, 0x6d, 0x6c, 0x6b, 0x6a, 0x6a, 0x6c,
0x6d, 0x6f, 0x71, 0x73, 0x74, 0x75, 0x77, 0x7a, 0x7d, 0x80, 0x82, 0x85, 0x88, 0x89, 0x8a, 0x8b,
0x8b, 0x8c, 0x8d, 0x8e, 0x8e, 0x8e, 0x8e, 0x8e, 0x8d, 0x8d, 0x8d, 0x8e, 0x90, 0x91, 0x92, 0x93,
0x92, 0x92, 0x92, 0x93, 0x93, 0x94, 0x94, 0x93, 0x92, 0x90, 0x8d, 0x8b, 0x88, 0x84, 0x81, 0x80,
0x7d, 0x7a, 0x77, 0x74, 0x70, 0x6e, 0x6d, 0x6c, 0x6c, 0x6c, 0x6d, 0x6d, 0x6d, 0x6c, 0x6d, 0x6e,
0x70, 0x72, 0x74, 0x76, 0x77, 0x78, 0x79, 0x79, 0x7a, 0x7a, 0x7c, 0x7d, 0x7e, 0x7e, 0x7e, 0x7e,
0x7e, 0x7e, 0x7e, 0x7e, 0x7f, 0x80, 0x81, 0x83, 0x84, 0x84, 0x84, 0x84, 0x84, 0x85, 0x85, 0x85,
0x84, 0x83, 0x83, 0x82, 0x80, 0x81, 0x81, 0x81, 0x81, 0x81, 0x80, 0x80, 0x7f, 0x7e, 0x7e, 0x7f,
0x80, 0x83, 0x85, 0x88, 0x8a, 0x8b, 0x8d, 0x8e, 0x90, 0x91, 0x93, 0x93, 0x94, 0x94, 0x93, 0x91,
0x8f, 0x8d, 0x8b, 0x89, 0x87, 0x84, 0x81, 0x7e, 0x7a, 0x77, 0x74, 0x72, 0x71, 0x71, 0x72, 0x73,
0x74, 0x74, 0x75, 0x77, 0x78, 0x7b, 0x7d, 0x7e, 0x7f, 0x7f, 0x7f, 0x7e, 0x7d, 0x7c, 0x7c, 0x7b,
0x7b, 0x7a, 0x79, 0x78, 0x76, 0x74, 0x73, 0x72, 0x72, 0x73, 0x74, 0x75, 0x76, 0x77, 0x78, 0x79,
0x7b, 0x7d, 0x7f, 0x80, 0x81, 0x81, 0x80, 0x80, 0x80, 0x80, 0x81, 0x82, 0x84, 0x86, 0x87, 0x88,
0x87, 0x88, 0x89, 0x89, 0x8a, 0x8c, 0x8f, 0x91, 0x92, 0x92, 0x93, 0x94, 0x94, 0x95, 0x95, 0x95,
0x94, 0x92, 0x8f, 0x8c, 0x88, 0x85, 0x83, 0x81, 0x80, 0x7e, 0x7c, 0x79, 0x75, 0x72, 0x70, 0x6f,
0x6f, 0x6e, 0x6f, 0x70, 0x71, 0x72, 0x72, 0x73, 0x75, 0x77, 0x7a, 0x7d, 0x7f, 0x80, 0x80, 0x81,
0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x7f, 0x7e, 0x7d, 0x7c, 0x7c, 0x7c, 0x7d, 0x7e, 0x7f,
0x80, 0x80, 0x80, 0x80, 0x81, 0x83, 0x85, 0x87, 0x88, 0x88, 0x88, 0x86, 0x85, 0x82, 0x81, 0x80,
0x7f, 0x7e, 0x7c, 0x7b, 0x79, 0x77, 0x75, 0x74, 0x74, 0x75, 0x76, 0x78, 0x7a, 0x7b, 0x7c, 0x7d,
0x7e, 0x80, 0x82, 0x84, 0x87, 0x89, 0x8a, 0x8b, 0x8c, 0x8c, 0x8c, 0x8d, 0x8d, 0x8e, 0x8f, 0x8f,
0x8e, 0x8c, 0x8b, 0x8a, 0x89, 0x89, 0x89, 0x89, 0x89, 0x89, 0x88, 0x86, 0x85, 0x84, 0x83, 0x83,
0x82, 0x81, 0x80, 0x7f, 0x7d, 0x7a, 0x78, 0x77, 0x76, 0x75, 0x73, 0x71, 0x6f, 0x6d, 0x6b, 0x6b,
0x6b, 0x6c, 0x6e, 0x71, 0x74, 0x77, 0x79, 0x7a, 0x7c, 0x7e, 0x80, 0x82, 0x84, 0x86, 0x87, 0x88,
0x88, 0x86, 0x85, 0x83, 0x82, 0x81, 0x80, 0x7e, 0x7b, 0x79, 0x77, 0x75, 0x75, 0x75, 0x76, 0x78,
0x7a, 0x7c, 0x7e, 0x7f, 0x81, 0x84, 0x87, 0x8a, 0x8d, 0x8f, 0x91, 0x92, 0x92, 0x91, 0x90, 0x90,
0x8f, 0x8f, 0x8e, 0x8d, 0x8b, 0x88, 0x86, 0x83, 0x81, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80,
0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x7f, 0x7d, 0x7c, 0x7c, 0x7c, 0x7c, 0x7d, 0x7e,
0x7d, 0x7d, 0x7c, 0x7c, 0x7b, 0x7b, 0x7c, 0x7d, 0x7f, 0x80, 0x80, 0x81, 0x81, 0x81, 0x81, 0x82,
0x82, 0x81, 0x80, 0x7f, 0x7e, 0x7b, 0x79, 0x76, 0x75, 0x73, 0x72, 0x71, 0x6f, 0x6d, 0x6b, 0x6a,
0x6a, 0x6a, 0x6b, 0x6d, 0x71, 0x74, 0x76, 0x79, 0x7c, 0x7e, 0x81, 0x84, 0x88, 0x8c, 0x8f, 0x91,
0x93, 0x94, 0x94, 0x93, 0x93, 0x93, 0x93, 0x93, 0x92, 0x91, 0x90, 0x8f, 0x8e, 0x8d, 0x8d, 0x8d,
0x8d, 0x8d, 0x8d, 0x8d, 0x8c, 0x8c, 0x8c, 0x8d, 0x8e, 0x8e, 0x8d, 0x8c, 0x8a, 0x87, 0x84, 0x81,
0x7f, 0x7c, 0x7a, 0x77, 0x75, 0x72, 0x70, 0x6d, 0x6b, 0x69, 0x68, 0x68, 0x68, 0x69, 0x6a, 0x6a,
0x6b, 0x6c, 0x6d, 0x6f, 0x71, 0x74, 0x76, 0x77, 0x79, 0x7a, 0x7b, 0x7c, 0x7d, 0x7f, 0x80, 0x80,
0x81, 0x81, 0x81, 0x81, 0x81, 0x81, 0x81, 0x81, 0x83, 0x84, 0x85, 0x86, 0x86, 0x86, 0x86, 0x87,
0x88, 0x89, 0x89, 0x8a, 0x8b, 0x8a, 0x89, 0x88, 0x87, 0x86, 0x85, 0x84, 0x82, 0x81, 0x80, 0x7f,
0x7e, 0x7e, 0x7e, 0x80, 0x82, 0x84, 0x87, 0x89, 0x8a, 0x8b, 0x8c, 0x8d, 0x8f, 0x91, 0x92, 0x92,
0x93, 0x92, 0x8f, 0x8c, 0x89, 0x86, 0x83, 0x80, 0x7d, 0x7a, 0x76, 0x73, 0x70, 0x6e, 0x6d, 0x6c,
0x6c, 0x6d, 0x6e, 0x6f, 0x70, 0x71, 0x73, 0x75, 0x78, 0x7b, 0x7d, 0x7f, 0x80, 0x80, 0x80, 0x80,
0x80, 0x7f, 0x7e, 0x7e, 0x7d, 0x7d, 0x7b, 0x79, 0x77, 0x76, 0x75, 0x75, 0x75, 0x76, 0x78, 0x79,
0x7a, 0x7b, 0x7b, 0x7d, 0x7e, 0x80, 0x81, 0x82, 0x83, 0x84, 0x84, 0x85, 0x85, 0x87, 0x88, 0x8a,
0x8b, 0x8c, 0x8d, 0x8d, 0x8d, 0x8c, 0x8d, 0x8d, 0x8e, 0x90, 0x91, 0x92, 0x93, 0x93, 0x93, 0x93,
0x92, 0x92, 0x91, 0x90, 0x8e, 0x8c, 0x89, 0x86, 0x83, 0x80, 0x7e, 0x7a, 0x77, 0x74, 0x70, 0x6d,
0x6a, 0x68, 0x66, 0x67, 0x68, 0x69, 0x6c, 0x6e, 0x70, 0x72, 0x73, 0x75, 0x78, 0x7a, 0x7d, 0x80,
0x82, 0x83, 0x84, 0x84, 0x83, 0x83, 0x82, 0x82, 0x81, 0x81, 0x80, 0x80, 0x7f, 0x7e, 0x7d, 0x7d,
0x7d, 0x7d, 0x7e, 0x7f, 0x80, 0x80, 0x80, 0x81, 0x82, 0x84, 0x86, 0x88, 0x88, 0x88, 0x88, 0x87,
0x85, 0x84, 0x82, 0x81, 0x80, 0x80, 0x7f, 0x7d, 0x7c, 0x7b, 0x7a, 0x79, 0x79, 0x79, 0x7b, 0x7c,
0x7d, 0x7e, 0x80, 0x80, 0x81, 0x84, 0x86, 0x88, 0x8a, 0x8b, 0x8b, 0x8b, 0x8b, 0x8b, 0x8b, 0x8c,
0x8c, 0x8c, 0x8c, 0x8a, 0x88, 0x87, 0x85, 0x83, 0x82, 0x82, 0x82, 0x82, 0x81, 0x81, 0x80, 0x80,
0x80, 0x80, 0x80, 0x80, 0x80, 0x7f, 0x7e, 0x7d, 0x7b, 0x79, 0x77, 0x76, 0x74, 0x72, 0x70, 0x6e,
0x6c, 0x6b, 0x6a, 0x6b, 0x6c, 0x6f, 0x72, 0x75, 0x78, 0x7a, 0x7c, 0x7e, 0x80, 0x82, 0x84, 0x86,
0x89, 0x8a, 0x8b, 0x8a, 0x89, 0x88, 0x85, 0x83, 0x82, 0x80, 0x7f, 0x7e, 0x7c, 0x7b, 0x7a, 0x79,
0x79, 0x7a, 0x7b, 0x7d, 0x7e, 0x80, 0x81, 0x83, 0x85, 0x88, 0x8b, 0x8e, 0x91, 0x93, 0x94, 0x94,
0x93, 0x92, 0x91, 0x90, 0x8f, 0x8d, 0x8c, 0x8a, 0x87, 0x85, 0x82, 0x80, 0x7f, 0x7e, 0x7d, 0x7d,
0x7d, 0x7c, 0x7b, 0x7a, 0x7a, 0x7b, 0x7b, 0x7c, 0x7d, 0x7d, 0x7c, 0x7b, 0x7a, 0x7a, 0x79, 0x79,
0x7a, 0x7b, 0x7b, 0x7b, 0x7b, 0x7a, 0x79, 0x79, 0x79, 0x7a, 0x7b, 0x7d, 0x7e, 0x7f, 0x80, 0x80,
0x80, 0x80, 0x81, 0x82, 0x82, 0x82, 0x81, 0x80, 0x7f, 0x7d, 0x7a, 0x78, 0x76, 0x75, 0x73, 0x71,
0x6e, 0x6c, 0x6b, 0x6b, 0x6c, 0x6f, 0x72, 0x75, 0x79, 0x7c, 0x7f, 0x81, 0x84, 0x87, 0x8a, 0x8e,
0x92, 0x95, 0x97, 0x99, 0x99, 0x99, 0x98, 0x97, 0x96, 0x96, 0x95, 0x94, 0x92, 0x90, 0x8e, 0x8c,
0x8a, 0x89, 0x89, 0x88, 0x88, 0x86, 0x85, 0x84, 0x83, 0x83, 0x84, 0x84, 0x85, 0x85, 0x84, 0x82,
0x80, 0x7f, 0x7c, 0x79, 0x77, 0x75, 0x73, 0x72, 0x6f, 0x6c, 0x6a, 0x68, 0x67, 0x66, 0x67, 0x68,
0x69, 0x6a, 0x6a, 0x6c, 0x6d, 0x6f, 0x72, 0x75, 0x78, 0x7b, 0x7c, 0x7d, 0x7e, 0x7e, 0x7f, 0x80,
0x81, 0x82, 0x84, 0x85, 0x85, 0x84, 0x83, 0x82, 0x81, 0x82, 0x82, 0x84, 0x85, 0x87, 0x89, 0x8a,
0x8b, 0x8c, 0x8d, 0x8e, 0x90, 0x91, 0x91, 0x91, 0x8f, 0x8e, 0x8c, 0x8a, 0x89, 0x88, 0x86, 0x84,
0x82, 0x80, 0x7d, 0x7c, 0x7b, 0x7c, 0x7d, 0x7f, 0x81, 0x83, 0x85, 0x86, 0x87, 0x88, 0x89, 0x8b,
0x8d, 0x8e, 0x8e, 0x8d, 0x8b, 0x89, 0x86, 0x82, 0x80, 0x7e, 0x7c, 0x79, 0x76, 0x72, 0x6f, 0x6c,
0x6b, 0x69, 0x69, 0x6a, 0x6b, 0x6c, 0x6d, 0x6e, 0x6f, 0x71, 0x74, 0x77, 0x7b, 0x7e, 0x80, 0x82,
0x82, 0x82, 0x82, 0x81, 0x80, 0x80, 0x80, 0x80, 0x80, 0x7e, 0x7c, 0x7a, 0x79, 0x79, 0x79, 0x7a,
0x7c, 0x7c, 0x7d, 0x7e, 0x7f, 0x80, 0x81, 0x84, 0x86, 0x89, 0x8a, 0x8a, 0x8a, 0x89, 0x89, 0x89,
0x89, 0x8a, 0x8b, 0x8c, 0x8d, 0x8c, 0x8b, 0x8b, 0x8a, 0x8a, 0x8a, 0x8b, 0x8d, 0x8e, 0x8f, 0x8f,
0x8f, 0x8f, 0x90, 0x90, 0x90, 0x90, 0x8e, 0x8c, 0x89, 0x85, 0x82, 0x7f, 0x7b, 0x78, 0x75, 0x72,
0x6e, 0x6a, 0x66, 0x63, 0x61, 0x61, 0x62, 0x64, 0x67, 0x6a, 0x6d, 0x6f, 0x71, 0x73, 0x76, 0x79,
0x7c, 0x80, 0x82, 0x84, 0x85, 0x86, 0x86, 0x86, 0x85, 0x85, 0x85, 0x85, 0x84, 0x83, 0x81, 0x80,
0x7f, 0x7e, 0x7f, 0x7f, 0x80, 0x80, 0x81, 0x80, 0x80, 0x80, 0x81, 0x83, 0x85, 0x87, 0x89, 0x8a,
0x8a, 0x8a, 0x89, 0x87, 0x85, 0x84, 0x83, 0x82, 0x81, 0x80, 0x7e, 0x7c, 0x7a, 0x79, 0x7a, 0x7b,
0x7c, 0x7e, 0x7f, 0x80, 0x80, 0x81, 0x82, 0x84, 0x87, 0x89, 0x8b, 0x8c, 0x8b, 0x8a, 0x89, 0x88,
0x88, 0x87, 0x87, 0x87, 0x87, 0x86, 0x84, 0x81, 0x7f, 0x7d, 0x7c, 0x7b, 0x7c, 0x7c, 0x7d, 0x7e,
0x7e, 0x7f, 0x7f, 0x80, 0x80, 0x81, 0x81, 0x81, 0x80, 0x7e, 0x7c, 0x7a, 0x78, 0x77, 0x76, 0x75,
0x73, 0x71, 0x6e, 0x6b, 0x69, 0x69, 0x6a, 0x6c, 0x6f, 0x73, 0x77, 0x7a, 0x7d, 0x7f, 0x81, 0x83,
0x86, 0x89, 0x8b, 0x8d, 0x8e, 0x8d, 0x8d, 0x8b, 0x8a, 0x88, 0x87, 0x86, 0x85, 0x83, 0x81, 0x7f,
0x7d, 0x7b, 0x7b, 0x7b, 0x7c, 0x7e, 0x7f, 0x80, 0x81, 0x82, 0x84, 0x86, 0x89, 0x8d, 0x91, 0x92,
0x94, 0x94, 0x93, 0x91, 0x8f, 0x8d, 0x8c, 0x8b, 0x89, 0x88, 0x85, 0x82, 0x80, 0x7d, 0x7b, 0x7a,
0x7a, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x7b, 0x7d, 0x7e, 0x7e, 0x7d, 0x7c, 0x7a, 0x78,
0x76, 0x75, 0x75, 0x76, 0x78, 0x78, 0x78, 0x77, 0x76, 0x76, 0x76, 0x76, 0x78, 0x7a, 0x7c, 0x7e,
0x80, 0x81, 0x82, 0x84, 0x85, 0x87, 0x89, 0x89, 0x88, 0x86, 0x84, 0x81, 0x7f, 0x7d, 0x7b, 0x79,
0x78, 0x76, 0x73, 0x70, 0x6e, 0x6c, 0x6c, 0x6e, 0x71, 0x75, 0x78, 0x7c, 0x80, 0x82, 0x85, 0x88,
0x8b, 0x8f, 0x93, 0x96, 0x98, 0x9a, 0x9b, 0x9a, 0x99, 0x99, 0x97, 0x97, 0x96, 0x94, 0x92, 0x8f,
0x8b, 0x89, 0x86, 0x85, 0x85, 0x84, 0x84, 0x83, 0x81, 0x80, 0x7e, 0x7d, 0x7d, 0x7e, 0x7f, 0x80,
0x80, 0x80, 0x7f, 0x7d, 0x7a, 0x77, 0x75, 0x73, 0x71, 0x6f, 0x6d, 0x6b, 0x68, 0x66, 0x65, 0x65,
0x67, 0x68, 0x6a, 0x6d, 0x6f, 0x70, 0x72, 0x74, 0x76, 0x7a, 0x7d, 0x80, 0x81, 0x82, 0x82, 0x82,
0x82, 0x81, 0x81, 0x82, 0x83, 0x84, 0x84, 0x84, 0x83, 0x81, 0x81, 0x80, 0x82, 0x84, 0x86, 0x88,
0x8a, 0x8c, 0x8e, 0x8f, 0x91, 0x93, 0x95, 0x96, 0x96, 0x96, 0x94, 0x92, 0x90, 0x8e, 0x8c, 0x8a,
0x89, 0x86, 0x84, 0x80, 0x7d, 0x79, 0x77, 0x76, 0x76, 0x78, 0x7b, 0x7e, 0x80, 0x82, 0x83, 0x84,
0x85, 0x86, 0x87, 0x88, 0x88, 0x87, 0x86, 0x85, 0x82, 0x80, 0x7e, 0x7c, 0x7a, 0x78, 0x75, 0x72,
0x6e, 0x6b, 0x68, 0x67, 0x67, 0x68, 0x6a, 0x6c, 0x6d, 0x6f, 0x71, 0x72, 0x75, 0x78, 0x7b, 0x7f,
0x81, 0x83, 0x85, 0x85, 0x84, 0x84, 0x83, 0x82, 0x82, 0x82, 0x82, 0x81, 0x80, 0x7e, 0x7d, 0x7c,
0x7d, 0x7e, 0x7f, 0x80, 0x81, 0x83, 0x85, 0x86, 0x87, 0x8a, 0x8c, 0x8e, 0x8f, 0x8f, 0x8d, 0x8c,
0x8a, 0x88, 0x87, 0x86, 0x86, 0x87, 0x88, 0x88, 0x87, 0x86, 0x85, 0x85, 0x85, 0x85, 0x86, 0x88,
0x8a, 0x8b, 0x8d, 0x8d, 0x8e, 0x8f, 0x8f, 0x8f, 0x8e, 0x8c, 0x89, 0x86, 0x81, 0x7d, 0x79, 0x76,
0x73, 0x70, 0x6d, 0x69, 0x65, 0x62, 0x5f, 0x5d, 0x5d, 0x5e, 0x61, 0x64, 0x68, 0x6b, 0x6f, 0x72,
0x75, 0x78, 0x7c, 0x80, 0x82, 0x85, 0x87, 0x88, 0x89, 0x89, 0x89, 0x89, 0x89, 0x89, 0x88, 0x88,
0x87, 0x85, 0x83, 0x82, 0x82, 0x82, 0x82, 0x83, 0x84, 0x84, 0x83, 0x83, 0x83, 0x84, 0x86, 0x87,
0x89, 0x8b, 0x8b, 0x8b, 0x8a, 0x88, 0x86, 0x84, 0x82, 0x81, 0x80, 0x80, 0x7e, 0x7c, 0x7a, 0x7a,
0x7a, 0x7b, 0x7c, 0x7e, 0x80, 0x81, 0x82, 0x83, 0x84, 0x86, 0x87, 0x89, 0x8b, 0x8c, 0x8b, 0x8a,
0x88, 0x86, 0x83, 0x81, 0x80, 0x7f, 0x7e, 0x7d, 0x7c, 0x7a, 0x78, 0x77, 0x75, 0x75, 0x76, 0x78,
0x79, 0x7b, 0x7c, 0x7e, 0x7f, 0x80, 0x81, 0x82, 0x83, 0x83, 0x83, 0x82, 0x81, 0x80, 0x7e, 0x7c,
0x7a, 0x78, 0x77, 0x75, 0x72, 0x70, 0x6d, 0x6c, 0x6c, 0x6d, 0x6f, 0x73, 0x77, 0x7b, 0x7f, 0x81,
0x83, 0x85, 0x88, 0x8a, 0x8c, 0x8d, 0x8e, 0x8f, 0x8e, 0x8d, 0x8c, 0x8a, 0x89, 0x88, 0x86, 0x85,
0x82, 0x80, 0x7f, 0x7d, 0x7c, 0x7c, 0x7d, 0x7f, 0x80, 0x81, 0x83, 0x84, 0x85, 0x86, 0x88, 0x8a,
0x8c, 0x8e, 0x8f, 0x90, 0x90, 0x8e, 0x8d, 0x8b, 0x88, 0x87, 0x85, 0x83, 0x81, 0x7f, 0x7c, 0x7a,
0x78, 0x77, 0x77, 0x78, 0x79, 0x79, 0x7b, 0x7b, 0x7b, 0x7c, 0x7c, 0x7d, 0x7d, 0x7d, 0x7c, 0x7b,
0x78, 0x76, 0x74, 0x72, 0x71, 0x71, 0x72, 0x73, 0x74, 0x74, 0x74, 0x74, 0x74, 0x75, 0x78, 0x7a,
0x7d, 0x80, 0x83, 0x85, 0x88, 0x89, 0x8b, 0x8d, 0x8e, 0x8f, 0x8e, 0x8d, 0x8b, 0x88, 0x84, 0x81,
0x80, 0x7e, 0x7c, 0x7a, 0x78, 0x75, 0x73, 0x70, 0x6f, 0x70, 0x71, 0x73, 0x77, 0x7b, 0x7f, 0x82,
0x85, 0x88, 0x8b, 0x8f, 0x92, 0x94, 0x96, 0x97, 0x97, 0x97, 0x95, 0x95, 0x94, 0x93, 0x93, 0x92,
0x90, 0x8e, 0x8b, 0x89, 0x86, 0x84, 0x83, 0x82, 0x81, 0x80, 0x80, 0x7e, 0x7d, 0x7b, 0x7a, 0x7a,
0x7a, 0x7a, 0x7a, 0x79, 0x78, 0x76, 0x74, 0x71, 0x6f, 0x6d, 0x6c, 0x6b, 0x6b, 0x6a, 0x69, 0x69,
0x68, 0x69, 0x6b, 0x6d, 0x70, 0x72, 0x75, 0x78, 0x7b, 0x7d, 0x80, 0x81, 0x84, 0x86, 0x88, 0x88,
0x88, 0x86, 0x85, 0x83, 0x82, 0x81, 0x81, 0x82, 0x82, 0x82, 0x82, 0x81, 0x81, 0x81, 0x82, 0x84,
0x86, 0x88, 0x8b, 0x8d, 0x8f, 0x91, 0x92, 0x94, 0x96, 0x97, 0x98, 0x98, 0x97, 0x95, 0x92, 0x8f,
0x8c, 0x89, 0x86, 0x84, 0x81, 0x7f, 0x7c, 0x78, 0x76, 0x74, 0x74, 0x75, 0x76, 0x79, 0x7b, 0x7d,
0x7f, 0x80, 0x80, 0x81, 0x82, 0x82, 0x82, 0x82, 0x81, 0x80, 0x7f, 0x7c, 0x7a, 0x77, 0x76, 0x74,
0x72, 0x6f, 0x6d, 0x6c, 0x6b, 0x6a, 0x6a, 0x6c, 0x6e, 0x70, 0x72, 0x74, 0x76, 0x78, 0x79, 0x7b,
0x7e, 0x80, 0x82, 0x84, 0x86, 0x87, 0x87, 0x86, 0x85, 0x84, 0x84, 0x83, 0x83, 0x82, 0x81, 0x80,
0x80, 0x80, 0x81, 0x82, 0x84, 0x86, 0x88, 0x8a, 0x8c, 0x8d, 0x8e, 0x8f, 0x8f, 0x8f, 0x8f, 0x8e,
0x8d, 0x8b, 0x88, 0x86, 0x84, 0x82, 0x81, 0x80, 0x80, 0x80, 0x80, 0x80, 0x7f, 0x7f, 0x80, 0x81,
0x83, 0x85, 0x88, 0x8a, 0x8c, 0x8d, 0x8d, 0x8e, 0x8d, 0x8d, 0x8c, 0x89, 0x87, 0x84, 0x80, 0x7d,
0x79, 0x76, 0x72, 0x6f, 0x6c, 0x69, 0x65, 0x62, 0x5f, 0x5d, 0x5d, 0x5e, 0x60, 0x63, 0x67, 0x6b,
0x70, 0x74, 0x77, 0x7b, 0x7e, 0x80, 0x82, 0x85, 0x87, 0x88, 0x89, 0x8a, 0x8b, 0x8b, 0x8c, 0x8c,
0x8c, 0x8c, 0x8b, 0x8a, 0x89, 0x87, 0x87, 0x87, 0x87, 0x88, 0x88, 0x89, 0x89, 0x88, 0x88, 0x87,
0x87, 0x88, 0x88, 0x87, 0x87, 0x86, 0x85, 0x83, 0x81, 0x80, 0x7f, 0x7e, 0x7d, 0x7d, 0x7c, 0x7b,
0x7b, 0x7a, 0x7a, 0x7c, 0x7d, 0x7f, 0x80, 0x82, 0x85, 0x87, 0x88, 0x8a, 0x8b, 0x8c, 0x8c, 0x8b,
0x8a, 0x88, 0x85, 0x82, 0x80, 0x7c, 0x7a, 0x78, 0x77, 0x76, 0x74, 0x73, 0x72, 0x71, 0x71, 0x71,
0x72, 0x74, 0x75, 0x78, 0x7a, 0x7c, 0x7e, 0x80, 0x81, 0x83, 0x85, 0x86, 0x86, 0x86, 0x85, 0x83,
0x81, 0x80, 0x7e, 0x7c, 0x7a, 0x79, 0x77, 0x75, 0x74, 0x72, 0x71, 0x72, 0x74, 0x77, 0x79, 0x7c,
0x80, 0x82, 0x84, 0x86, 0x89, 0x8a, 0x8c, 0x8d, 0x8e, 0x8e, 0x8d, 0x8c, 0x8b, 0x89, 0x88, 0x86,
0x84, 0x82, 0x81, 0x80, 0x80, 0x7f, 0x7f, 0x80, 0x80, 0x81, 0x83, 0x84, 0x85, 0x86, 0x86, 0x87,
0x87, 0x89, 0x8a, 0x8b, 0x8c, 0x8c, 0x8c, 0x8b, 0x89, 0x86, 0x83, 0x81, 0x7f, 0x7e, 0x7c, 0x7a,
0x78, 0x77, 0x76, 0x76, 0x77, 0x78, 0x79, 0x7a, 0x7b, 0x7c, 0x7c, 0x7d, 0x7d, 0x7d, 0x7c, 0x7c,
0x7c, 0x7a, 0x78, 0x76, 0x74, 0x72, 0x71, 0x70, 0x70, 0x70, 0x70, 0x71, 0x72, 0x73, 0x74, 0x77,
0x7a, 0x7d, 0x80, 0x84, 0x87, 0x8a, 0x8d, 0x8e, 0x90, 0x91, 0x91, 0x91, 0x91, 0x90, 0x8f, 0x8d,
0x8a, 0x87, 0x84, 0x81, 0x80, 0x7d, 0x7a, 0x78, 0x75, 0x72, 0x70, 0x70, 0x72, 0x74, 0x77, 0x7b,
0x7f, 0x81, 0x84, 0x87, 0x89, 0x8b, 0x8d, 0x8f, 0x91, 0x92, 0x93, 0x93, 0x93, 0x93, 0x92, 0x91,
0x90, 0x8f, 0x8d, 0x8b, 0x88, 0x86, 0x84, 0x82, 0x81, 0x80, 0x80, 0x80, 0x7f, 0x7e, 0x7c, 0x7a,
0x78, 0x77, 0x75, 0x75, 0x74, 0x73, 0x72, 0x70, 0x6f, 0x6d, 0x6c, 0x6b, 0x6a, 0x6a, 0x6a, 0x6a,
0x6a, 0x6a, 0x6b, 0x6d, 0x6f, 0x72, 0x75, 0x79, 0x7c, 0x80, 0x83, 0x85, 0x88, 0x8a, 0x8b, 0x8d,
0x8d, 0x8e, 0x8c, 0x8b, 0x89, 0x86, 0x84, 0x83, 0x82, 0x81, 0x81, 0x80, 0x80, 0x80, 0x80, 0x80,
0x80, 0x81, 0x84, 0x86, 0x89, 0x8c, 0x8e, 0x91, 0x93, 0x95, 0x96, 0x97, 0x97, 0x97, 0x96, 0x94,
0x92, 0x8f, 0x8c, 0x8a, 0x87, 0x84, 0x82, 0x80, 0x7d, 0x7a, 0x77, 0x74, 0x73, 0x73, 0x73, 0x75,
0x76, 0x78, 0x79, 0x7a, 0x7b, 0x7b, 0x7c, 0x7d, 0x7d, 0x7d, 0x7d, 0x7b, 0x7a, 0x78, 0x76, 0x74,
0x72, 0x70, 0x6f, 0x6e, 0x6d, 0x6d, 0x6d, 0x6e, 0x6f, 0x71, 0x73, 0x76, 0x78, 0x7a, 0x7b, 0x7d,
0x7e, 0x80, 0x81, 0x84, 0x86, 0x89, 0x8a, 0x8b, 0x8b, 0x89, 0x88, 0x86, 0x85, 0x84, 0x83, 0x83,
0x83, 0x83, 0x83, 0x83, 0x84, 0x85, 0x87, 0x89, 0x8a, 0x8c, 0x8d, 0x8d, 0x8d, 0x8d, 0x8e, 0x8e,
0x8e, 0x8e, 0x8d, 0x8b, 0x88, 0x85, 0x82, 0x80, 0x7e, 0x7d, 0x7c, 0x7c, 0x7c, 0x7b, 0x7c, 0x7c,
0x7e, 0x80, 0x81, 0x83, 0x86, 0x88, 0x89, 0x89, 0x89, 0x89, 0x89, 0x88, 0x87, 0x86, 0x84, 0x82,
0x80, 0x7d, 0x79, 0x75, 0x71, 0x6e, 0x6b, 0x68, 0x65, 0x62, 0x5f, 0x5e, 0x5e, 0x5f, 0x62, 0x66,
0x6a, 0x6e, 0x72, 0x75, 0x78, 0x7a, 0x7d, 0x80, 0x82, 0x86, 0x89, 0x8b, 0x8d, 0x8f, 0x90, 0x91,
0x91, 0x91, 0x90, 0x90, 0x8e, 0x8d, 0x8c, 0x8b, 0x8b, 0x8b, 0x8b, 0x8c, 0x8d, 0x8c, 0x8c, 0x8a,
0x89, 0x87, 0x85, 0x84, 0x84, 0x83, 0x83, 0x83, 0x82, 0x81, 0x80, 0x7f, 0x7d, 0x7d, 0x7c, 0x7b,
0x7b, 0x7a, 0x7a, 0x7a, 0x7a, 0x7b, 0x7d, 0x80, 0x81, 0x84, 0x86, 0x87, 0x88, 0x89, 0x89, 0x89,
0x89, 0x89, 0x87, 0x85, 0x82, 0x80, 0x7d, 0x79, 0x77, 0x75, 0x73, 0x72, 0x71, 0x6f, 0x6e, 0x6d,
0x6c, 0x6c, 0x6e, 0x70, 0x73, 0x76, 0x79, 0x7c, 0x7f, 0x80, 0x83, 0x85, 0x86, 0x87, 0x88, 0x88,
0x87, 0x87, 0x86, 0x84, 0x83, 0x82, 0x81, 0x80, 0x80, 0x7e, 0x7c, 0x7a, 0x78, 0x78, 0x78, 0x7a,
0x7c, 0x7f, 0x81, 0x83, 0x84, 0x85, 0x86, 0x88, 0x89, 0x8a, 0x8b, 0x8b, 0x8b, 0x8a, 0x88, 0x86,
0x85, 0x84, 0x82, 0x81, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x82, 0x83, 0x85, 0x86, 0x86,
0x86, 0x86, 0x86, 0x87, 0x88, 0x89, 0x8a, 0x8a, 0x89, 0x87, 0x85, 0x82, 0x80, 0x7d, 0x7b, 0x7a,
0x79, 0x77, 0x76, 0x76, 0x75, 0x75, 0x76, 0x76, 0x77, 0x78, 0x78, 0x78, 0x79, 0x79, 0x79, 0x7a,
0x7b, 0x7c, 0x7c, 0x7c, 0x7b, 0x79, 0x77, 0x75, 0x73, 0x72, 0x72, 0x73, 0x74, 0x75, 0x77, 0x78,
0x7a, 0x7c, 0x80, 0x82, 0x85, 0x88, 0x8b, 0x8d, 0x8e, 0x90, 0x90, 0x91, 0x92, 0x92, 0x91, 0x91,
0x8f, 0x8d, 0x8b, 0x88, 0x85, 0x82, 0x80, 0x7e, 0x7b, 0x78, 0x76, 0x74, 0x73, 0x73, 0x74, 0x76,
0x79, 0x7c, 0x7f, 0x80, 0x82, 0x84, 0x85, 0x87, 0x89, 0x8b, 0x8d, 0x8f, 0x91, 0x92, 0x92, 0x92,
0x91, 0x90, 0x8e, 0x8b, 0x89, 0x87, 0x84, 0x82, 0x80, 0x7f, 0x7e, 0x7e, 0x7e, 0x7d, 0x7c, 0x7a,
0x78, 0x75, 0x73, 0x71, 0x70, 0x6f, 0x6f, 0x6f, 0x6f, 0x6f, 0x6f, 0x6e, 0x6e, 0x6d, 0x6d, 0x6e,
0x6e, 0x6f, 0x70, 0x71, 0x72, 0x74, 0x77, 0x7a, 0x7d, 0x80, 0x83, 0x86, 0x88, 0x8a, 0x8c, 0x8d,
0x8e, 0x8f, 0x8f, 0x8f, 0x8e, 0x8d, 0x8b, 0x89, 0x86, 0x84, 0x83, 0x82, 0x81, 0x80, 0x80, 0x7f,
0x7e, 0x7e, 0x7e, 0x7f, 0x80, 0x83, 0x86, 0x89, 0x8b, 0x8e, 0x90, 0x91, 0x92, 0x93, 0x94, 0x93,
0x93, 0x91, 0x90, 0x8e, 0x8c, 0x8a, 0x88, 0x86, 0x83, 0x81, 0x7f, 0x7b, 0x77, 0x74, 0x72, 0x71,
0x71, 0x72, 0x73, 0x74, 0x75, 0x75, 0x75, 0x75, 0x76, 0x76, 0x77, 0x77, 0x77, 0x76, 0x75, 0x74,
0x73, 0x72, 0x72, 0x71, 0x71, 0x71, 0x71, 0x71, 0x72, 0x73, 0x74, 0x76, 0x79, 0x7b, 0x7e, 0x80,
0x81, 0x83, 0x84, 0x86, 0x88, 0x8a, 0x8c, 0x8e, 0x8f, 0x8e, 0x8d, 0x8b, 0x8a, 0x88, 0x86, 0x85,
0x85, 0x85, 0x84, 0x84, 0x84, 0x84, 0x84, 0x85, 0x86, 0x87, 0x87, 0x87, 0x88, 0x89, 0x89, 0x8a,
0x8b, 0x8b, 0x8c, 0x8c, 0x8b, 0x89, 0x87, 0x84, 0x81, 0x7f, 0x7e, 0x7c, 0x7c, 0x7c, 0x7c, 0x7c,
0x7c, 0x7d, 0x7d, 0x7e, 0x7f, 0x80, 0x81, 0x82, 0x83, 0x83, 0x84, 0x84, 0x83, 0x83, 0x83, 0x81,
0x80, 0x7e, 0x7c, 0x79, 0x75, 0x72, 0x6f, 0x6d, 0x6a, 0x69, 0x67, 0x66, 0x65, 0x64, 0x65, 0x66,
0x69, 0x6c, 0x70, 0x73, 0x77, 0x79, 0x7c, 0x7e, 0x80, 0x83, 0x87, 0x8a, 0x8d, 0x90, 0x92, 0x94,
0x94, 0x94, 0x94, 0x93, 0x92, 0x90, 0x8f, 0x8e, 0x8c, 0x8b, 0x8b, 0x8b, 0x8a, 0x8a, 0x8b, 0x8a,
0x88, 0x87, 0x84, 0x82, 0x81, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x7e, 0x7d, 0x7c, 0x7c,
0x7c, 0x7b, 0x7b, 0x7c, 0x7c, 0x7c, 0x7d, 0x7e, 0x80, 0x81, 0x83, 0x85, 0x85, 0x86, 0x86, 0x86,
0x85, 0x85, 0x85, 0x84, 0x83, 0x81, 0x80, 0x7d, 0x7a, 0x78, 0x75, 0x73, 0x71, 0x70, 0x6f, 0x6d,
0x6b, 0x6a, 0x6a, 0x6a, 0x6c, 0x6f, 0x72, 0x76, 0x79, 0x7d, 0x80, 0x82, 0x84, 0x86, 0x88, 0x8a,
0x8b, 0x8b, 0x8c, 0x8b, 0x8b, 0x8b, 0x8b, 0x8a, 0x89, 0x88, 0x86, 0x84, 0x81, 0x7f, 0x7d, 0x7c,
0x7b, 0x7c, 0x7e, 0x7f, 0x80, 0x81, 0x81, 0x82, 0x82, 0x83, 0x83, 0x84, 0x85, 0x85, 0x85, 0x84,
0x84, 0x84, 0x83, 0x83, 0x82, 0x82, 0x82, 0x81, 0x80, 0x80, 0x80, 0x80, 0x80, 0x82, 0x83, 0x84,
0x85, 0x86, 0x87, 0x87, 0x88, 0x88, 0x88, 0x88, 0x88, 0x87, 0x86, 0x83, 0x80, 0x7e, 0x7c, 0x7a,
0x78, 0x77, 0x76, 0x75, 0x74, 0x73, 0x73, 0x72, 0x72, 0x72, 0x73, 0x74, 0x75, 0x76, 0x77, 0x78,
0x7a, 0x7c, 0x7e, 0x7f, 0x80, 0x80, 0x80, 0x7e, 0x7d, 0x7b, 0x7b, 0x7a, 0x7a, 0x7b, 0x7d, 0x7e,
0x7f, 0x80, 0x80, 0x81, 0x82, 0x83, 0x85, 0x87, 0x89, 0x8b, 0x8c, 0x8d, 0x8e, 0x8e, 0x8f, 0x8f,
0x8e, 0x8d, 0x8c, 0x89, 0x87, 0x84, 0x82, 0x80, 0x7e, 0x7d, 0x7b, 0x7a, 0x79, 0x78, 0x78, 0x78,
0x78, 0x7a, 0x7b, 0x7d, 0x7f, 0x80, 0x81, 0x82, 0x83, 0x85, 0x87, 0x89, 0x8c, 0x8e, 0x8f, 0x90,
0x8f, 0x8e, 0x8d, 0x8b, 0x89, 0x87, 0x85, 0x83, 0x81, 0x80, 0x7e, 0x7c, 0x7b, 0x7a, 0x79, 0x78,
0x76, 0x75, 0x73, 0x70, 0x6f, 0x6e, 0x6e, 0x6e, 0x6f, 0x71, 0x72, 0x73, 0x73, 0x72, 0x72, 0x72,
0x73, 0x74, 0x75, 0x77, 0x79, 0x7a, 0x7c, 0x7e, 0x80, 0x81, 0x84, 0x87, 0x88, 0x8a, 0x8a, 0x8b,
0x8b, 0x8c, 0x8c, 0x8d, 0x8e, 0x8e, 0x8d, 0x8c, 0x8a, 0x88, 0x86, 0x84, 0x82, 0x81, 0x80, 0x80,
0x7f, 0x7d, 0x7c, 0x7c, 0x7c, 0x7d, 0x7f, 0x81, 0x84, 0x86, 0x88, 0x8a, 0x8c, 0x8d, 0x8e, 0x8f,
0x90, 0x90, 0x91, 0x90, 0x8f, 0x8e, 0x8d, 0x8b, 0x8a, 0x87, 0x85, 0x82, 0x7f, 0x7b, 0x78, 0x74,
0x72, 0x70, 0x70, 0x70, 0x70, 0x70, 0x70, 0x70, 0x6f, 0x6f, 0x6f, 0x6f, 0x70, 0x71, 0x73, 0x74,
0x74, 0x75, 0x75, 0x76, 0x77, 0x77, 0x78, 0x78, 0x79, 0x79, 0x79, 0x7a, 0x7b, 0x7d, 0x7f, 0x81,
0x83, 0x85, 0x87, 0x88, 0x8a, 0x8b, 0x8c, 0x8c, 0x8d, 0x8d, 0x8e, 0x8d, 0x8c, 0x8a, 0x89, 0x87,
0x86, 0x85, 0x84, 0x84, 0x84, 0x84, 0x83, 0x82, 0x81, 0x81, 0x81, 0x82, 0x82, 0x84, 0x84, 0x85,
0x86, 0x87, 0x88, 0x89, 0x8a, 0x8a, 0x8a, 0x89, 0x87, 0x85, 0x83, 0x81, 0x80, 0x80, 0x80, 0x80,
0x80, 0x80, 0x7f, 0x7e, 0x7c, 0x7b, 0x7b, 0x7b, 0x7c, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7c,
0x7c, 0x7b, 0x7a, 0x79, 0x76, 0x74, 0x72, 0x70, 0x6f, 0x6f, 0x6f, 0x6f, 0x6f, 0x6f, 0x6f, 0x6f,
0x6f, 0x70, 0x72, 0x74, 0x76, 0x79, 0x7b, 0x7e, 0x80, 0x81, 0x83, 0x86, 0x89, 0x8d, 0x90, 0x93,
0x94, 0x94, 0x94, 0x94, 0x93, 0x92, 0x91, 0x91, 0x90, 0x8f, 0x8d, 0x8c, 0x8a, 0x89, 0x87, 0x86,
0x85, 0x84, 0x82, 0x80, 0x7f, 0x7e, 0x7d, 0x7e, 0x7e, 0x7f, 0x80, 0x80, 0x80, 0x80, 0x7f, 0x7e,
0x7d, 0x7d, 0x7d, 0x7e, 0x7f, 0x80, 0x80, 0x81, 0x81, 0x81, 0x82, 0x83, 0x83, 0x82, 0x82, 0x81,
0x80, 0x80, 0x7f, 0x7f, 0x7f, 0x7f, 0x7f, 0x7e, 0x7c, 0x7a, 0x78, 0x75, 0x73, 0x71, 0x70, 0x6f,
0x6e, 0x6e, 0x6e, 0x6e, 0x6e, 0x6f, 0x70, 0x72, 0x75, 0x78, 0x7b, 0x7e, 0x80, 0x82, 0x84, 0x86,
0x89, 0x8b, 0x8d, 0x8f, 0x90, 0x91, 0x90, 0x90, 0x8f, 0x8f, 0x8d, 0x8c, 0x8a, 0x88, 0x86, 0x83,
0x80, 0x80, 0x7f, 0x7e, 0x7e, 0x7e, 0x7e, 0x7e, 0x7d, 0x7d, 0x7c, 0x7c, 0x7c, 0x7e, 0x7f, 0x81,
0x83, 0x84, 0x84, 0x84, 0x84, 0x84, 0x84, 0x84, 0x84, 0x83, 0x83, 0x82, 0x82, 0x82, 0x82, 0x83,
0x84, 0x85, 0x86, 0x86, 0x86, 0x85, 0x84, 0x83, 0x82, 0x82, 0x82, 0x81, 0x80, 0x7f, 0x7d, 0x7a,
0x78, 0x76, 0x75, 0x74, 0x74, 0x74, 0x74, 0x73, 0x72, 0x71, 0x71, 0x72, 0x73, 0x74, 0x76, 0x77,
0x79, 0x7a, 0x7b, 0x7d, 0x7f, 0x80, 0x81, 0x82, 0x83, 0x83, 0x82, 0x82, 0x82, 0x82, 0x83, 0x84,
0x85, 0x86, 0x86, 0x86, 0x85, 0x84, 0x83, 0x83, 0x83, 0x84, 0x86, 0x87, 0x88, 0x89, 0x89, 0x88,
0x88, 0x87, 0x87, 0x86, 0x85, 0x83, 0x82, 0x80, 0x80, 0x80, 0x7f, 0x7f, 0x80, 0x80, 0x7f, 0x7f,
0x7e, 0x7d, 0x7c, 0x7c, 0x7d, 0x7e, 0x7f, 0x80, 0x81, 0x81, 0x82, 0x83, 0x84, 0x86, 0x87, 0x89,
0x8a, 0x8b, 0x8a, 0x89, 0x87, 0x86, 0x84, 0x83, 0x82, 0x81, 0x80, 0x7f, 0x7d, 0x7a, 0x78, 0x76,
0x75, 0x73, 0x72, 0x70, 0x6f, 0x6f, 0x6f, 0x6f, 0x6f, 0x71, 0x73, 0x74, 0x76, 0x77, 0x77, 0x77,
0x77, 0x78, 0x79, 0x7b, 0x7e, 0x80, 0x82, 0x85, 0x86, 0x87, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88,
0x88, 0x88, 0x88, 0x88, 0x88, 0x89, 0x89, 0x8a, 0x8a, 0x89, 0x87, 0x85, 0x83, 0x81, 0x80, 0x80,
0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x7f, 0x7f, 0x80, 0x80, 0x81, 0x83, 0x85, 0x86, 0x87, 0x88,
0x89, 0x8a, 0x8c, 0x8d, 0x8e, 0x8e, 0x8e, 0x8c, 0x8a, 0x89, 0x87, 0x85, 0x82, 0x80, 0x7f, 0x7c,
0x79, 0x77, 0x73, 0x71, 0x6f, 0x6e, 0x6d, 0x6c, 0x6b, 0x6a, 0x6a, 0x69, 0x69, 0x6a, 0x6c, 0x6f,
0x73, 0x76, 0x78, 0x7a, 0x7b, 0x7c, 0x7c, 0x7d, 0x7e, 0x80, 0x80, 0x81, 0x82, 0x83, 0x83, 0x85,
0x86, 0x87, 0x89, 0x8a, 0x8a, 0x8a, 0x8a, 0x89, 0x89, 0x89, 0x89, 0x8a, 0x8a, 0x8a, 0x8a, 0x88,
0x87, 0x85, 0x83, 0x82, 0x81, 0x82, 0x82, 0x82, 0x82, 0x81, 0x81, 0x80, 0x80, 0x81, 0x81, 0x82,
0x83, 0x83, 0x84, 0x84, 0x84, 0x85, 0x86, 0x86, 0x87, 0x87, 0x87, 0x86, 0x85, 0x84, 0x83, 0x83,
0x82, 0x82, 0x82, 0x81, 0x80, 0x7e, 0x7c, 0x79, 0x78, 0x77, 0x77, 0x77, 0x77, 0x77, 0x77, 0x77,
0x76, 0x75, 0x74, 0x74, 0x74, 0x74, 0x73, 0x72, 0x73, 0x73, 0x73, 0x75, 0x76, 0x77, 0x78, 0x79,
0x79, 0x7a, 0x79, 0x79, 0x7a, 0x7b, 0x7d, 0x7f, 0x80, 0x83, 0x84, 0x85, 0x86, 0x88, 0x89, 0x8b,
0x8d, 0x8f, 0x90, 0x91, 0x91, 0x90, 0x90, 0x90, 0x8f, 0x8f, 0x8f, 0x8e, 0x8c, 0x8a, 0x88, 0x86,
0x83, 0x82, 0x80, 0x80, 0x7f, 0x7e, 0x7d, 0x7d, 0x7c, 0x7c, 0x7d, 0x7e, 0x7f, 0x7f, 0x80, 0x80,
0x7f, 0x7f, 0x7e, 0x7f, 0x80, 0x81, 0x83, 0x84, 0x86, 0x86, 0x84, 0x83, 0x81, 0x80, 0x7f, 0x7e,
0x7d, 0x7c, 0x7a, 0x79, 0x79, 0x78, 0x79, 0x79, 0x79, 0x78, 0x78, 0x76, 0x75, 0x73, 0x72, 0x72,
0x72, 0x73, 0x75, 0x76, 0x77, 0x78, 0x78, 0x78, 0x78, 0x78, 0x7a, 0x7c, 0x7e, 0x80, 0x82, 0x84,
0x86, 0x88, 0x8a, 0x8d, 0x8f, 0x90, 0x91, 0x91, 0x91, 0x90, 0x8f, 0x8d, 0x8d, 0x8c, 0x8b, 0x8a,
0x88, 0x86, 0x84, 0x81, 0x80, 0x7d, 0x7c, 0x7b, 0x7a, 0x79, 0x78, 0x78, 0x77, 0x77, 0x78, 0x7a,
0x7d, 0x80, 0x81, 0x83, 0x84, 0x85, 0x85, 0x85, 0x85, 0x86, 0x87, 0x87, 0x88, 0x88, 0x87, 0x87,
0x86, 0x86, 0x85, 0x85, 0x84, 0x83, 0x81, 0x80, 0x7e, 0x7d, 0x7c, 0x7b, 0x7b, 0x7b, 0x7b, 0x7a,
0x79, 0x77, 0x75, 0x74, 0x73, 0x72, 0x73, 0x74, 0x75, 0x76, 0x75, 0x75, 0x76, 0x76, 0x77, 0x79,
0x7a, 0x7b, 0x7c, 0x7c, 0x7d, 0x7e, 0x80, 0x81, 0x83, 0x85, 0x87, 0x88, 0x88, 0x88, 0x88, 0x88,
0x88, 0x89, 0x89, 0x89, 0x8a, 0x89, 0x87, 0x86, 0x84, 0x83, 0x82, 0x82, 0x82, 0x82, 0x82, 0x81,
0x81, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x81, 0x82, 0x83, 0x84,
0x84, 0x83, 0x82, 0x81, 0x80, 0x80, 0x80, 0x80, 0x81, 0x81, 0x82, 0x81, 0x81, 0x80, 0x80, 0x80,
0x80, 0x81, 0x82, 0x82, 0x82, 0x82, 0x81, 0x80, 0x80, 0x80, 0x80, 0x80, 0x7f, 0x7d, 0x7b, 0x79,
0x77, 0x75, 0x74, 0x73, 0x73, 0x73, 0x73, 0x73, 0x72, 0x73, 0x73, 0x74, 0x76, 0x77, 0x79, 0x7b,
0x7c, 0x7d, 0x7e, 0x7f, 0x81, 0x83, 0x85, 0x88, 0x8a, 0x8c, 0x8c, 0x8b, 0x8a, 0x89, 0x88, 0x87,
0x86, 0x86, 0x85, 0x84, 0x83, 0x83, 0x82, 0x82, 0x83, 0x83, 0x82, 0x82, 0x81, 0x80, 0x80, 0x80,
0x80, 0x80, 0x81, 0x82, 0x84, 0x84, 0x85, 0x84, 0x83, 0x82, 0x81, 0x81, 0x82, 0x83, 0x84, 0x85,
0x85, 0x86, 0x87, 0x87, 0x88, 0x89, 0x89, 0x89, 0x88, 0x86, 0x84, 0x83, 0x82, 0x81, 0x80, 0x80,
0x7f, 0x7d, 0x7a, 0x77, 0x74, 0x71, 0x6f, 0x6d, 0x6b, 0x6a, 0x6a, 0x69, 0x6a, 0x6a, 0x6b, 0x6d,
0x6f, 0x72, 0x75, 0x78, 0x7b, 0x7d, 0x7e, 0x80, 0x80, 0x82, 0x84, 0x87, 0x88, 0x8a, 0x8b, 0x8b,
0x8b, 0x8b, 0x8b, 0x8b, 0x8b, 0x8a, 0x89, 0x88, 0x86, 0x85, 0x84, 0x84, 0x84, 0x84, 0x85, 0x84,
0x84, 0x83, 0x82, 0x80, 0x80, 0x80, 0x80, 0x80, 0x82, 0x83, 0x83, 0x83, 0x83, 0x83, 0x83, 0x83,
0x83, 0x84, 0x83, 0x83, 0x83, 0x82, 0x82, 0x83, 0x84, 0x85, 0x86, 0x87, 0x87, 0x86, 0x85, 0x84,
0x83, 0x82, 0x81, 0x81, 0x80, 0x80, 0x7f, 0x7d, 0x7b, 0x79, 0x78, 0x76, 0x75, 0x74, 0x73, 0x72,
0x71, 0x70, 0x6f, 0x6f, 0x6f, 0x70, 0x72, 0x73, 0x74, 0x74, 0x76, 0x77, 0x79, 0x7b, 0x7d, 0x7f,
0x80, 0x81, 0x82, 0x82, 0x82, 0x82, 0x82, 0x83, 0x84, 0x85, 0x86, 0x86, 0x87, 0x86, 0x86, 0x86,
0x86, 0x87, 0x88, 0x89, 0x8a, 0x8a, 0x8a, 0x8a, 0x8a, 0x8a, 0x8a, 0x8a, 0x8a, 0x8a, 0x89, 0x87,
0x85, 0x83, 0x82, 0x81, 0x81, 0x80, 0x80, 0x80, 0x7f, 0x7e, 0x7e, 0x7d, 0x7d, 0x7d, 0x7e, 0x7e,
0x7f, 0x80, 0x80, 0x80, 0x80, 0x81, 0x83, 0x84, 0x85, 0x86, 0x86, 0x85, 0x83, 0x80, 0x80, 0x7e,
0x7c, 0x7b, 0x7a, 0x79, 0x77, 0x76, 0x74, 0x73, 0x73, 0x73, 0x73, 0x73, 0x73, 0x72, 0x72, 0x73,
0x73, 0x75, 0x76, 0x78, 0x7b, 0x7d, 0x7e, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x82, 0x83, 0x84,
0x86, 0x87, 0x87, 0x88, 0x89, 0x8b, 0x8c, 0x8d, 0x8c, 0x8c, 0x8b, 0x8a, 0x8a, 0x8a, 0x8a, 0x8a,
0x8a, 0x8a, 0x89, 0x87, 0x84, 0x81, 0x7f, 0x7d, 0x7b, 0x7a, 0x79, 0x79, 0x78, 0x78, 0x78, 0x79,
0x7a, 0x7c, 0x7e, 0x80, 0x80, 0x81, 0x82, 0x82, 0x83, 0x84, 0x85, 0x87, 0x89, 0x8a, 0x8a, 0x8a,
0x8a, 0x88, 0x87, 0x85, 0x84, 0x83, 0x81, 0x80, 0x7e, 0x7c, 0x7a, 0x79, 0x78, 0x77, 0x77, 0x76,
0x75, 0x74, 0x73, 0x72, 0x72, 0x72, 0x72, 0x73, 0x75, 0x77, 0x78, 0x79, 0x7a, 0x7b, 0x7b, 0x7c,
0x7d, 0x7e, 0x7f, 0x80, 0x80, 0x80, 0x81, 0x82, 0x83, 0x85, 0x87, 0x88, 0x89, 0x8a, 0x89, 0x89,
0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x87, 0x86, 0x84, 0x83, 0x82, 0x81, 0x80, 0x7f,
0x7e, 0x7d, 0x7c, 0x7b, 0x7a, 0x7b, 0x7c, 0x7d, 0x7e, 0x7f, 0x7f, 0x80, 0x80, 0x81, 0x82, 0x83,
0x85, 0x86, 0x87, 0x88, 0x87, 0x87, 0x86, 0x85, 0x85, 0x84, 0x84, 0x83, 0x82, 0x81, 0x80, 0x7f,
0x7e, 0x7d, 0x7c, 0x7c, 0x7c, 0x7c, 0x7c, 0x7b, 0x7a, 0x7a, 0x7a, 0x7a, 0x7a, 0x7b, 0x7b, 0x7b,
0x7b, 0x79, 0x78, 0x78, 0x78, 0x79, 0x79, 0x7a, 0x7a, 0x7a, 0x79, 0x79, 0x79, 0x7a, 0x7b, 0x7c,
0x7e, 0x7f, 0x80, 0x80, 0x82, 0x83, 0x85, 0x87, 0x89, 0x8a, 0x8b, 0x8b, 0x8b, 0x8a, 0x88, 0x87,
0x86, 0x86, 0x86, 0x85, 0x84, 0x82, 0x81, 0x80, 0x7f, 0x7e, 0x7e, 0x7e, 0x7e, 0x7e, 0x7f, 0x7f,
0x7f, 0x7f, 0x80, 0x81, 0x83, 0x84, 0x85, 0x86, 0x86, 0x86, 0x85, 0x85, 0x85, 0x85, 0x85, 0x86,
0x86, 0x85, 0x85, 0x84, 0x83, 0x83, 0x83, 0x82, 0x82, 0x82, 0x81, 0x80, 0x7f, 0x7e, 0x7d, 0x7d,
0x7d, 0x7d, 0x7d, 0x7b, 0x79, 0x77, 0x74, 0x72, 0x71, 0x70, 0x6f, 0x6f, 0x70, 0x70, 0x70, 0x71,
0x72, 0x74, 0x76, 0x77, 0x79, 0x7b, 0x7d, 0x7f, 0x80, 0x80, 0x82, 0x84, 0x87, 0x8a, 0x8c, 0x8d,
0x8d, 0x8d, 0x8c, 0x8b, 0x8a, 0x8a, 0x89, 0x88, 0x88, 0x86, 0x85, 0x84, 0x83, 0x82, 0x81, 0x80,
0x80, 0x80, 0x7f, 0x7e, 0x7d, 0x7d, 0x7d, 0x7e, 0x7f, 0x80, 0x82, 0x83, 0x84, 0x84, 0x84, 0x84,
0x84, 0x85, 0x85, 0x86, 0x86, 0x86, 0x86, 0x85, 0x85, 0x85, 0x85, 0x85, 0x85, 0x85, 0x84, 0x83,
0x82, 0x80, 0x80, 0x7f, 0x7e, 0x7e, 0x7d, 0x7d, 0x7c, 0x7b, 0x79, 0x78, 0x76, 0x75, 0x74, 0x74,
0x73, 0x72, 0x71, 0x70, 0x70, 0x70, 0x71, 0x72, 0x74, 0x76, 0x78, 0x79, 0x7a, 0x7b, 0x7c, 0x7e,
0x80, 0x82, 0x84, 0x86, 0x88, 0x88, 0x88, 0x89, 0x89, 0x89, 0x89, 0x89, 0x89, 0x88, 0x87, 0x86,
0x85, 0x84, 0x84, 0x84, 0x84, 0x84, 0x84, 0x84, 0x83, 0x83, 0x82, 0x82, 0x83, 0x83, 0x84, 0x85,
0x85, 0x84, 0x84, 0x83, 0x83, 0x83, 0x84, 0x84, 0x84, 0x83, 0x82, 0x81, 0x80, 0x80, 0x7f, 0x7f,
0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x81, 0x81, 0x81, 0x81, 0x81, 0x80, 0x7f, 0x7d,
0x7c, 0x7c, 0x7b, 0x7a, 0x7a, 0x79, 0x77, 0x75, 0x74, 0x72, 0x72, 0x72, 0x72, 0x73, 0x74, 0x75,
0x75, 0x76, 0x77, 0x79, 0x7b, 0x7d, 0x7f, 0x80, 0x81, 0x83, 0x83, 0x84, 0x85, 0x86, 0x87, 0x88,
0x88, 0x89, 0x89, 0x88, 0x88, 0x87, 0x87, 0x87, 0x87, 0x87, 0x87, 0x87, 0x87, 0x86, 0x85, 0x85,
0x85, 0x85, 0x85, 0x85, 0x84, 0x83, 0x81, 0x80, 0x7f, 0x7e, 0x7e, 0x7e, 0x7e, 0x7e, 0x7e, 0x7e,
0x7e, 0x7e, 0x7e, 0x7f, 0x7f, 0x80, 0x80, 0x81, 0x81, 0x81, 0x82, 0x83, 0x84, 0x85, 0x86, 0x87,
0x87, 0x87, 0x86, 0x84, 0x83, 0x82, 0x81, 0x80, 0x80, 0x7f, 0x7d, 0x7c, 0x7a, 0x78, 0x76, 0x75,
0x74, 0x73, 0x72, 0x72, 0x71, 0x71, 0x71, 0x72, 0x74, 0x76, 0x78, 0x7a, 0x7c, 0x7d, 0x7f, 0x7f,
0x80, 0x81, 0x82, 0x84, 0x86, 0x87, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88,
0x87, 0x86, 0x85, 0x84, 0x84, 0x84, 0x84, 0x84, 0x84, 0x83, 0x83, 0x81, 0x80, 0x80, 0x80, 0x7f,
0x7f, 0x7f, 0x7e, 0x7d, 0x7d, 0x7c, 0x7c, 0x7c, 0x7d, 0x7e, 0x7f, 0x80, 0x80, 0x80, 0x80, 0x81,
0x82, 0x84, 0x85, 0x86, 0x87, 0x88, 0x88, 0x87, 0x86, 0x86, 0x85, 0x85, 0x84, 0x83, 0x81, 0x80,
0x7f, 0x7d, 0x7c, 0x7b, 0x7a, 0x79, 0x78, 0x77, 0x76, 0x75, 0x74, 0x74, 0x74, 0x76, 0x77, 0x78,
0x79, 0x7b, 0x7c, 0x7c, 0x7d, 0x7d, 0x7f, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80,
0x80, 0x80, 0x80, 0x81, 0x81, 0x81, 0x81, 0x82, 0x82, 0x83, 0x84, 0x85, 0x85, 0x86, 0x86, 0x85,
0x85, 0x85, 0x85, 0x85, 0x84, 0x84, 0x83, 0x82, 0x81, 0x80, 0x7f, 0x7e, 0x7e, 0x7e, 0x7f, 0x7f,
0x7f, 0x80, 0x80, 0x80, 0x81, 0x82, 0x83, 0x84, 0x85, 0x85, 0x86, 0x86, 0x86, 0x86, 0x86, 0x87,
0x87, 0x86, 0x85, 0x84, 0x83, 0x81, 0x80, 0x7f, 0x7e, 0x7e, 0x7d, 0x7d, 0x7c, 0x7c, 0x7b, 0x7a,
0x7a, 0x79, 0x79, 0x79, 0x79, 0x79, 0x78, 0x77, 0x77, 0x77, 0x77, 0x78, 0x79, 0x79, 0x79, 0x7a,
0x7a, 0x7a, 0x7a, 0x7b, 0x7b, 0x7c, 0x7d, 0x7e, 0x7f, 0x80, 0x80, 0x81, 0x82, 0x83, 0x85, 0x86,
0x87, 0x89, 0x89, 0x89, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x87, 0x85, 0x83, 0x82, 0x80,
0x7f, 0x7e, 0x7e, 0x7d, 0x7c, 0x7c, 0x7c, 0x7c, 0x7d, 0x7e, 0x7f, 0x80, 0x81, 0x83, 0x84, 0x84,
0x85, 0x85, 0x86, 0x87, 0x89, 0x89, 0x8a, 0x8a, 0x8a, 0x89, 0x87, 0x86, 0x84, 0x83, 0x82, 0x81,
0x80, 0x7f, 0x7e, 0x7c, 0x7b, 0x7b, 0x7a, 0x7a, 0x79, 0x78, 0x78, 0x77, 0x76, 0x75, 0x75, 0x75,
0x75, 0x76, 0x76, 0x76, 0x76, 0x75, 0x75, 0x75, 0x76, 0x77, 0x79, 0x7a, 0x7b, 0x7c, 0x7d, 0x7f,
0x80, 0x81, 0x82, 0x84, 0x85, 0x87, 0x88, 0x89, 0x89, 0x89, 0x8a, 0x8a, 0x8a, 0x8a, 0x89, 0x89,
0x88, 0x86, 0x85, 0x83, 0x82, 0x81, 0x80, 0x80, 0x80, 0x7f, 0x7d, 0x7d, 0x7d, 0x7e, 0x7e, 0x80,
0x80, 0x81, 0x83, 0x84, 0x85, 0x86, 0x86, 0x87, 0x88, 0x88, 0x89, 0x88, 0x88, 0x86, 0x85, 0x84,
0x83, 0x82, 0x81, 0x80, 0x80, 0x7f, 0x7d, 0x7c, 0x7c, 0x7c, 0x7c, 0x7c, 0x7c, 0x7c, 0x7c, 0x7b,
0x7b, 0x7b, 0x7a, 0x7a, 0x7a, 0x7a, 0x7a, 0x7a, 0x79, 0x79, 0x78, 0x77, 0x76, 0x76, 0x76, 0x77,
0x77, 0x78, 0x79, 0x79, 0x7b, 0x7c, 0x7e, 0x7f, 0x80, 0x81, 0x83, 0x83, 0x84, 0x85, 0x86, 0x87,
0x88, 0x88, 0x88, 0x88, 0x87, 0x87, 0x86, 0x84, 0x83, 0x83, 0x83, 0x83, 0x82, 0x82, 0x82, 0x82,
0x81, 0x81, 0x81, 0x81, 0x81, 0x82, 0x82, 0x82, 0x82, 0x82, 0x82, 0x83, 0x83, 0x84, 0x84, 0x84,
0x84, 0x84, 0x83, 0x82, 0x81, 0x81, 0x80, 0x80, 0x80, 0x80, 0x80, 0x7f, 0x7f, 0x7f, 0x7f, 0x80,
0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x7f, 0x7e, 0x7d, 0x7c, 0x7a, 0x78,
0x77, 0x76, 0x75, 0x75, 0x74, 0x74, 0x74, 0x74, 0x74, 0x75, 0x77, 0x79, 0x7b, 0x7c, 0x7e, 0x80,
0x80, 0x81, 0x82, 0x84, 0x85, 0x87, 0x89, 0x8a, 0x8a, 0x8a, 0x8a, 0x8a, 0x89, 0x88, 0x87, 0x86,
0x85, 0x84, 0x83, 0x82, 0x81, 0x81, 0x81, 0x81, 0x81, 0x81, 0x80, 0x80, 0x80, 0x80, 0x7f, 0x7f,
0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x7f, 0x80, 0x80, 0x80, 0x80, 0x81, 0x81, 0x81,
0x81, 0x81, 0x82, 0x83, 0x84, 0x84, 0x84, 0x85, 0x85, 0x84, 0x84, 0x84, 0x84, 0x84, 0x83, 0x82,
0x81, 0x80, 0x7f, 0x7d, 0x7b, 0x79, 0x78, 0x77, 0x76, 0x75, 0x73, 0x72, 0x72, 0x72, 0x73, 0x74,
0x76, 0x78, 0x79, 0x7b, 0x7d, 0x7f, 0x80, 0x81, 0x82, 0x84, 0x85, 0x85, 0x85, 0x85, 0x85, 0x84,
0x84, 0x84, 0x83, 0x83, 0x83, 0x82, 0x81, 0x80, 0x80, 0x80, 0x80, 0x80, 0x81, 0x81, 0x82, 0x82,
0x82, 0x82, 0x82, 0x82, 0x82, 0x83, 0x83, 0x83, 0x83, 0x83, 0x83, 0x82, 0x81, 0x81, 0x80, 0x80,
0x80, 0x80, 0x80, 0x80, 0x80, 0x81, 0x81, 0x82, 0x83, 0x84, 0x84, 0x84, 0x84, 0x84, 0x83, 0x83,
0x84, 0x84, 0x84, 0x83, 0x82, 0x81, 0x80, 0x7f, 0x7e, 0x7c, 0x7b, 0x7b, 0x7a, 0x7a, 0x79, 0x78,
0x78, 0x78, 0x78, 0x78, 0x79, 0x79, 0x7a, 0x7a, 0x7a, 0x7b, 0x7b, 0x7c, 0x7d, 0x7f, 0x80, 0x80,
0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x7f, 0x7f, 0x7f, 0x7f, 0x7f, 0x80,
0x80, 0x81, 0x82, 0x83, 0x84, 0x84, 0x84, 0x85, 0x86, 0x86, 0x86, 0x86, 0x86, 0x85, 0x84, 0x83,
0x82, 0x81, 0x80, 0x80, 0x80, 0x80, 0x7f, 0x7e, 0x7e, 0x7e, 0x7f, 0x80, 0x80, 0x82, 0x83, 0x83,
0x84, 0x84, 0x84, 0x84, 0x85, 0x86, 0x87, 0x88, 0x88, 0x88, 0x87, 0x87, 0x85, 0x84, 0x82, 0x81,
0x80, 0x7f, 0x7e, 0x7c, 0x7a, 0x79, 0x79, 0x79, 0x79, 0x79, 0x78, 0x78, 0x77, 0x76, 0x76, 0x76,
0x76, 0x77, 0x77, 0x78, 0x79, 0x79, 0x79, 0x7a, 0x7a, 0x7b, 0x7b, 0x7c, 0x7d, 0x7d, 0x7e, 0x7f,
0x7f, 0x80, 0x80, 0x82, 0x83, 0x84, 0x85, 0x86, 0x86, 0x87, 0x87, 0x87, 0x88, 0x88, 0x89, 0x89,
0x89, 0x88, 0x87, 0x86, 0x84, 0x83, 0x81, 0x80, 0x80, 0x80, 0x7e, 0x7d, 0x7c, 0x7c, 0x7c, 0x7d,
0x7e, 0x7f, 0x80, 0x81, 0x83, 0x84, 0x84, 0x85, 0x86, 0x87, 0x88, 0x88, 0x88, 0x88, 0x87, 0x86,
0x85, 0x84, 0x83, 0x82, 0x81, 0x80, 0x80, 0x7e, 0x7c, 0x7b, 0x7a, 0x79, 0x7a, 0x7a, 0x7a, 0x7a,
0x79, 0x79, 0x78, 0x78, 0x78, 0x78, 0x79, 0x7a, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b,
0x7c, 0x7c, 0x7d, 0x7d, 0x7d, 0x7d, 0x7e, 0x7f, 0x80, 0x81, 0x82, 0x83, 0x83, 0x83, 0x83, 0x83,
0x83, 0x84, 0x85, 0x86, 0x86, 0x85, 0x85, 0x84, 0x83, 0x82, 0x82, 0x81, 0x81, 0x81, 0x81, 0x81,
0x80, 0x80, 0x80, 0x81, 0x81, 0x82, 0x83, 0x83, 0x83, 0x83, 0x83, 0x83, 0x84, 0x85, 0x86, 0x87,
0x88, 0x87, 0x87, 0x86, 0x85, 0x83, 0x82, 0x81, 0x80, 0x80, 0x7f, 0x7e, 0x7d, 0x7c, 0x7b, 0x7b,
0x7b, 0x7c, 0x7c, 0x7d, 0x7d, 0x7c, 0x7c, 0x7c, 0x7c, 0x7c, 0x7d, 0x7d, 0x7d, 0x7d, 0x7c, 0x7c,
0x7b, 0x7a, 0x7a, 0x7a, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x7b, 0x7c, 0x7d, 0x7f, 0x80,
0x80, 0x80, 0x80, 0x81, 0x81, 0x82, 0x84, 0x85, 0x86, 0x87, 0x88, 0x88, 0x88, 0x88, 0x87, 0x86,
0x86, 0x85, 0x84, 0x83, 0x82, 0x81, 0x81, 0x80, 0x81, 0x81, 0x82, 0x81, 0x81, 0x80, 0x80, 0x80,
0x7f, 0x7f, 0x80, 0x80, 0x80, 0x81, 0x81, 0x81, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80,
0x80, 0x80, 0x80, 0x81, 0x81, 0x82, 0x83, 0x83, 0x83, 0x82, 0x81, 0x81, 0x80, 0x80, 0x80, 0x81,
0x81, 0x80, 0x80, 0x7f, 0x7e, 0x7c, 0x7b, 0x7a, 0x79, 0x78, 0x77, 0x76, 0x75, 0x74, 0x74, 0x74,
0x75, 0x77, 0x79, 0x7a, 0x7c, 0x7d, 0x7e, 0x7f, 0x80, 0x81, 0x82, 0x83, 0x85, 0x85, 0x85, 0x85,
0x85, 0x85, 0x84, 0x84, 0x84, 0x84, 0x84, 0x83, 0x82, 0x81, 0x80, 0x80, 0x80, 0x80, 0x81, 0x81,
0x81, 0x81, 0x80, 0x80, 0x80, 0x80, 0x80, 0x81, 0x82, 0x83, 0x83, 0x83, 0x83, 0x83, 0x83, 0x83,
0x84, 0x84, 0x84, 0x83, 0x82, 0x82, 0x81, 0x81, 0x82, 0x82, 0x83, 0x83, 0x83, 0x82, 0x82, 0x80,
0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x7f, 0x7e, 0x7d, 0x7c, 0x7b, 0x7a, 0x7a, 0x7a, 0x7a, 0x79,
0x79, 0x79, 0x79, 0x79, 0x7a, 0x7b, 0x7c, 0x7d, 0x7e, 0x7e, 0x7e, 0x7e, 0x7e, 0x7f, 0x80, 0x81,
0x82, 0x83, 0x83, 0x83, 0x82, 0x82, 0x81, 0x81, 0x80, 0x80, 0x80, 0x80, 0x7f, 0x7e, 0x7e, 0x7e,
0x7e, 0x7f, 0x80, 0x80, 0x80, 0x81, 0x81, 0x81, 0x82, 0x82, 0x83, 0x83, 0x84, 0x84, 0x84, 0x84,
0x83, 0x83, 0x82, 0x82, 0x82, 0x82, 0x82, 0x82, 0x82, 0x81, 0x81, 0x81, 0x81, 0x82, 0x83, 0x83,
0x83, 0x83, 0x82, 0x81, 0x81, 0x81, 0x81, 0x81, 0x82, 0x82, 0x83, 0x83, 0x83, 0x82, 0x81, 0x81,
0x80, 0x80, 0x80, 0x7f, 0x7e, 0x7c, 0x7b, 0x7b, 0x7a, 0x7a, 0x7b, 0x7b, 0x7b, 0x7a, 0x7a, 0x79,
0x78, 0x78, 0x78, 0x79, 0x7a, 0x7b, 0x7b, 0x7c, 0x7c, 0x7c, 0x7c, 0x7c, 0x7d, 0x7d, 0x7e, 0x7f,
0x7f, 0x80, 0x80, 0x80, 0x81, 0x82, 0x84, 0x85, 0x86, 0x86, 0x85, 0x85, 0x85, 0x84, 0x85, 0x86,
0x86, 0x87, 0x87, 0x87, 0x86, 0x85, 0x84, 0x83, 0x82, 0x82, 0x81, 0x80, 0x80, 0x7f, 0x7e, 0x7d,
0x7e, 0x7f, 0x7f, 0x80, 0x81, 0x81, 0x82, 0x82, 0x82, 0x82, 0x83, 0x83, 0x84, 0x85, 0x85, 0x84,
0x84, 0x83, 0x82, 0x82, 0x81, 0x81, 0x81, 0x80, 0x80, 0x7e, 0x7d, 0x7c, 0x7c, 0x7c, 0x7c, 0x7c,
0x7c, 0x7b, 0x7a, 0x79, 0x78, 0x77, 0x78, 0x78, 0x79, 0x7a, 0x7b, 0x7b, 0x7c, 0x7d, 0x7d, 0x7e,
0x7f, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x81, 0x82, 0x82, 0x83, 0x82, 0x81,
0x81, 0x80, 0x80, 0x81, 0x81, 0x82, 0x82, 0x81, 0x81, 0x81, 0x80, 0x80, 0x80, 0x81, 0x81, 0x81,
0x81, 0x81, 0x81, 0x82, 0x82, 0x83, 0x84, 0x85, 0x85, 0x86, 0x85, 0x85, 0x84, 0x84, 0x85, 0x85,
0x86, 0x86, 0x86, 0x85, 0x85, 0x84, 0x83, 0x82, 0x81, 0x81, 0x80, 0x80, 0x7f, 0x7d, 0x7c, 0x7b,
0x7b, 0x7a, 0x7a, 0x7b, 0x7b, 0x7b, 0x7b, 0x7a, 0x7a, 0x7a, 0x7a, 0x7b, 0x7b, 0x7c, 0x7c, 0x7c,
0x7c, 0x7c, 0x7c, 0x7c, 0x7c, 0x7d, 0x7e, 0x7e, 0x7e, 0x7e, 0x7e, 0x7e, 0x7f, 0x7f, 0x80, 0x80,
0x80, 0x81, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x81, 0x82, 0x83, 0x83, 0x83, 0x84, 0x84,
0x85, 0x85, 0x86, 0x86, 0x85, 0x85, 0x84, 0x83, 0x83, 0x83, 0x84, 0x84, 0x84, 0x84, 0x83, 0x82,
0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x7f, 0x7f, 0x7f, 0x7f, 0x80, 0x80,
0x80, 0x80, 0x80, 0x80, 0x80, 0x81, 0x81, 0x82, 0x82, 0x82, 0x82, 0x81, 0x80, 0x7f, 0x7f, 0x7f,
0x80, 0x80, 0x80, 0x7f, 0x7f, 0x7e, 0x7d, 0x7d, 0x7c, 0x7c, 0x7c, 0x7c, 0x7b, 0x7a, 0x7a, 0x79,
0x79, 0x79, 0x7a, 0x7b, 0x7c, 0x7d, 0x7d, 0x7d, 0x7e, 0x7e, 0x7e, 0x7f, 0x80, 0x81, 0x81, 0x82,
0x82, 0x82, 0x82, 0x83, 0x83, 0x84, 0x84, 0x85, 0x85, 0x84, 0x84, 0x83, 0x83, 0x83, 0x83, 0x84,
0x84, 0x84, 0x83, 0x82, 0x80, 0x80, 0x80, 0x7f, 0x80, 0x80, 0x80, 0x80, 0x81, 0x81, 0x82, 0x82,
0x83, 0x83, 0x84, 0x84, 0x84, 0x83, 0x83, 0x82, 0x82, 0x82, 0x82, 0x83, 0x83, 0x82, 0x81, 0x80,
0x7f, 0x7e, 0x7d, 0x7c, 0x7c, 0x7c, 0x7c, 0x7c, 0x7b, 0x7b, 0x7a, 0x7a, 0x79, 0x7a, 0x7a, 0x7b,
0x7b, 0x7b, 0x7c, 0x7c, 0x7d, 0x7e, 0x7f, 0x80, 0x80, 0x80, 0x81, 0x80, 0x80, 0x80, 0x80, 0x80,
0x80, 0x81, 0x81, 0x82, 0x82, 0x81, 0x81, 0x81, 0x81, 0x81, 0x81, 0x81, 0x81, 0x80, 0x80, 0x80,
0x80, 0x7f, 0x80, 0x80, 0x80, 0x80, 0x81, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x81, 0x81, 0x82,
0x82, 0x82, 0x81, 0x81, 0x82, 0x82, 0x83, 0x84, 0x84, 0x84, 0x84, 0x83, 0x83, 0x83, 0x83, 0x83,
0x84, 0x83, 0x83, 0x81, 0x80, 0x80, 0x7e, 0x7d, 0x7d, 0x7e, 0x7e, 0x7e, 0x7e, 0x7e, 0x7e, 0x7f,
0x7f, 0x7f, 0x80, 0x80, 0x80, 0x80, 0x80, 0x7f, 0x7f, 0x7f, 0x7f, 0x7f, 0x80, 0x80, 0x7f, 0x7e,
0x7d, 0x7c, 0x7a, 0x7a, 0x7a, 0x7a, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7c, 0x7c, 0x7d, 0x7e,
0x7f, 0x80, 0x80, 0x80, 0x80, 0x81, 0x83, 0x84, 0x85, 0x85, 0x86, 0x85, 0x85, 0x84, 0x83, 0x83,
0x83, 0x84, 0x85, 0x85, 0x84, 0x84, 0x84, 0x83, 0x83, 0x83, 0x83, 0x83, 0x82, 0x82, 0x81, 0x80,
0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x7f, 0x7f, 0x7f, 0x80, 0x80, 0x80,
0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x81, 0x80, 0x80, 0x80, 0x80, 0x7f, 0x7f, 0x7f,
0x7f, 0x7f, 0x7f, 0x7e, 0x7d, 0x7c, 0x7b, 0x7a, 0x79, 0x79, 0x7a, 0x7a, 0x7b, 0x7c, 0x7d, 0x7d,
0x7e, 0x7f, 0x80, 0x80, 0x80, 0x81, 0x81, 0x81, 0x81, 0x81, 0x82, 0x82, 0x83, 0x83, 0x83, 0x83,
0x82, 0x81, 0x80, 0x80, 0x7f, 0x7f, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80,
0x81, 0x82, 0x82, 0x82, 0x83, 0x83, 0x84, 0x84, 0x85, 0x86, 0x86, 0x85, 0x85, 0x84, 0x83, 0x83,
0x82, 0x82, 0x83, 0x83, 0x83, 0x82, 0x82, 0x81, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x7f, 0x7e,
0x7e, 0x7d, 0x7d, 0x7c, 0x7c, 0x7c, 0x7c, 0x7c, 0x7c, 0x7b, 0x7a, 0x7a, 0x7a, 0x7a, 0x7b, 0x7b,
0x7c, 0x7c, 0x7c, 0x7c, 0x7d, 0x7e, 0x7f, 0x80, 0x80, 0x80, 0x81, 0x81, 0x81, 0x81, 0x81, 0x81,
0x81, 0x82, 0x81, 0x81, 0x80, 0x80, 0x7f, 0x7e, 0x7e, 0x7e, 0x7e, 0x7f, 0x80, 0x80, 0x80, 0x81,
0x82, 0x83, 0x83, 0x84, 0x85, 0x85, 0x86, 0x85, 0x85, 0x85, 0x85, 0x85, 0x86, 0x86, 0x85, 0x85,
0x83, 0x82, 0x80, 0x80, 0x7f, 0x7f, 0x7f, 0x7e, 0x7e, 0x7e, 0x7e, 0x7e, 0x7e, 0x7e, 0x7e, 0x7e,
0x7f, 0x7f, 0x7f, 0x80, 0x80, 0x80, 0x80, 0x80, 0x81, 0x81, 0x81, 0x81, 0x80, 0x80, 0x80, 0x7f,
0x7e, 0x7e, 0x7f, 0x7f, 0x7f, 0x7f, 0x7f, 0x7f, 0x7e, 0x7e, 0x7e, 0x7e, 0x7f, 0x7f, 0x7f, 0x7e,
0x7e, 0x7e, 0x7d, 0x7d, 0x7e, 0x7e, 0x7e, 0x7e, 0x7e, 0x7e, 0x7d, 0x7d, 0x7c, 0x7d, 0x7e, 0x7e,
0x7f, 0x80, 0x80, 0x80, 0x80, 0x81, 0x82, 0x83, 0x84, 0x85, 0x86, 0x86, 0x85, 0x85, 0x85, 0x85,
0x85, 0x85, 0x85, 0x85, 0x84, 0x83, 0x81, 0x80, 0x80, 0x7f, 0x7f, 0x7f, 0x7f, 0x80, 0x80, 0x80,
0x80, 0x80, 0x81, 0x81, 0x82, 0x83, 0x83, 0x82, 0x82, 0x82, 0x81, 0x81, 0x82, 0x82, 0x82, 0x81,
0x80, 0x80, 0x7e, 0x7d, 0x7c, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7c,
0x7c, 0x7d, 0x7e, 0x7e, 0x7f, 0x7f, 0x80, 0x80, 0x80, 0x81, 0x81, 0x81, 0x81, 0x80, 0x80, 0x80,
0x80, 0x80, 0x80, 0x80, 0x80, 0x81, 0x81, 0x81, 0x80, 0x80, 0x80, 0x81, 0x81, 0x81, 0x82, 0x82,
0x82, 0x81, 0x81, 0x81, 0x81, 0x81, 0x81, 0x81, 0x81, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80,
0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x81, 0x82, 0x83, 0x83, 0x84, 0x84, 0x84, 0x84, 0x83, 0x83,
0x83, 0x82, 0x82, 0x81, 0x80, 0x80, 0x7f, 0x7e, 0x7d, 0x7c, 0x7b, 0x7b, 0x7c, 0x7c, 0x7d, 0x7d,
0x7d, 0x7e, 0x7e, 0x7f, 0x80, 0x80, 0x80, 0x81, 0x81, 0x81, 0x81, 0x81, 0x81, 0x81, 0x81, 0x81,
0x80, 0x80, 0x7f, 0x7e, 0x7c, 0x7b, 0x7b, 0x7b, 0x7b, 0x7b, 0x7c, 0x7c, 0x7c, 0x7c, 0x7c, 0x7d,
0x7d, 0x7e, 0x7f, 0x80, 0x80, 0x80, 0x81, 0x82, 0x82, 0x83, 0x84, 0x84, 0x84, 0x84, 0x84, 0x83,
0x83, 0x83, 0x83, 0x83, 0x84, 0x84, 0x84, 0x84, 0x83, 0x83, 0x83, 0x83, 0x83, 0x83, 0x83, 0x82,
0x82, 0x82, 0x81, 0x81, 0x80, 0x80, 0x80, 0x80, 0x80, 0x7f, 0x7e, 0x7d, 0x7c, 0x7c, 0x7c, 0x7c,
0x7c, 0x7d, 0x7d, 0x7e, 0x7e, 0x7e, 0x7f, 0x7f, 0x80, 0x80, 0x81, 0x81, 0x82, 0x82, 0x81, 0x81,
0x81, 0x81, 0x81, 0x80, 0x80, 0x80, 0x7f, 0x7e, 0x7d, 0x7c, 0x7b, 0x7b, 0x7b, 0x7c, 0x7c, 0x7d,
0x7d, 0x7e, 0x7e, 0x7f, 0x80, 0x80, 0x80, 0x81, 0x81, 0x81, 0x81, 0x81, 0x82, 0x82, 0x82, 0x82,
0x82, 0x82, 0x81, 0x80, 0x80, 0x7f, 0x7f, 0x7f, 0x7f, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80,
0x80, 0x81, 0x81, 0x82, 0x82, 0x83, 0x83, 0x83, 0x83, 0x84, 0x84, 0x84, 0x83, 0x83, 0x82, 0x81,
0x81, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80,
0x80, 0x80, 0x80, 0x80, 0x7f, 0x7f, 0x7f, 0x7e, 0x7e, 0x7e, 0x7d, 0x7d, 0x7c, 0x7b, 0x7b, 0x7b,
0x7c, 0x7c, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7e, 0x7f, 0x80, 0x80, 0x81, 0x81, 0x81, 0x81, 0x81,
0x81, 0x81, 0x81, 0x81, 0x81, 0x80, 0x80, 0x80, 0x7f, 0x7f, 0x7f, 0x7e, 0x7f, 0x7f, 0x80, 0x80,
0x80, 0x81, 0x81, 0x82, 0x83, 0x84, 0x84, 0x85, 0x85, 0x85, 0x85, 0x85, 0x85, 0x85, 0x85, 0x85,
0x84, 0x83, 0x82, 0x81, 0x80, 0x7f, 0x7e, 0x7e, 0x7e, 0x7d, 0x7e, 0x7e, 0x7e, 0x7d, 0x7d, 0x7d,
0x7d, 0x7d, 0x7e, 0x7e, 0x7f, 0x7f, 0x80, 0x80, 0x80, 0x80, 0x80, 0x81, 0x81, 0x81, 0x80, 0x80,
0x80, 0x7f, 0x7f, 0x7f, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80,
0x80, 0x80, 0x80, 0x80, 0x80, 0x7f, 0x7f, 0x7e, 0x7e, 0x7e, 0x7d, 0x7d, 0x7c, 0x7c, 0x7c, 0x7c,
0x7d, 0x7e, 0x7e, 0x7f, 0x80, 0x80, 0x80, 0x80, 0x81, 0x82, 0x84, 0x84, 0x85, 0x86, 0x86, 0x86,
0x86, 0x85, 0x85, 0x85, 0x84, 0x84, 0x83, 0x82, 0x81, 0x80, 0x80, 0x80, 0x7f, 0x7f, 0x7f, 0x80,
0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x81, 0x81, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80,
0x80, 0x80, 0x80, 0x7f, 0x7e, 0x7d, 0x7d, 0x7c, 0x7c, 0x7d, 0x7d, 0x7d, 0x7e, 0x7e, 0x7e, 0x7e,
0x7d, 0x7e, 0x7e, 0x7f, 0x7f, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80,
0x80, 0x7f, 0x7f, 0x7f, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x81, 0x82,
0x82, 0x83, 0x83, 0x83, 0x83, 0x83, 0x82, 0x82, 0x82, 0x81, 0x81, 0x81, 0x80, 0x80, 0x80, 0x80,
0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x81, 0x82, 0x82, 0x82, 0x82,
0x81, 0x81, 0x80, 0x80, 0x80, 0x80, 0x80, 0x7f, 0x7f, 0x7f, 0x7e, 0x7e, 0x7e, 0x7e, 0x7e, 0x7e,
0x7f, 0x7f, 0x7f, 0x80, 0x80, 0x80, 0x80, 0x81, 0x81, 0x81, 0x81, 0x81, 0x81, 0x81, 0x81, 0x80,
0x80, 0x80, 0x80, 0x80, 0x7f, 0x7e, 0x7d, 0x7c, 0x7c, 0x7c, 0x7c, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d,
0x7d, 0x7d, 0x7e, 0x7f, 0x7f, 0x80, 0x80, 0x81, 0x81, 0x82, 0x82, 0x82, 0x82, 0x83, 0x83, 0x83,
0x83, 0x82, 0x82, 0x82, 0x82, 0x83, 0x83, 0x83, 0x84, 0x83, 0x83, 0x82, 0x81, 0x81, 0x81, 0x81,
0x81, 0x81, 0x81, 0x81, 0x81, 0x80, 0x80, 0x80, 0x7f, 0x7f, 0x7e, 0x7e, 0x7d, 0x7d, 0x7c, 0x7c,
0x7c, 0x7c, 0x7d, 0x7e, 0x7e, 0x7e, 0x7e, 0x7f, 0x7f, 0x7f, 0x80, 0x80, 0x81, 0x82, 0x82, 0x83,
0x82, 0x82, 0x82, 0x81, 0x81, 0x80, 0x80, 0x80, 0x80, 0x80, 0x7f, 0x7e, 0x7e, 0x7e, 0x7e, 0x7e,
0x7e, 0x7f, 0x7f, 0x7f, 0x7f, 0x7f, 0x7f, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x81, 0x81,
0x81, 0x81, 0x81, 0x81, 0x81, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x81, 0x81, 0x81, 0x81,
0x81, 0x80, 0x80, 0x80, 0x81, 0x81, 0x81, 0x81, 0x82, 0x81, 0x81, 0x81, 0x81, 0x81, 0x80, 0x80,
0x80, 0x80, 0x80, 0x7f, 0x7f, 0x7f, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80,
0x80, 0x80, 0x81, 0x81, 0x81, 0x81, 0x80, 0x80, 0x80, 0x80, 0x80, 0x7f, 0x7f, 0x7f, 0x7e, 0x7e,
0x7d, 0x7d, 0x7d, 0x7e, 0x7e, 0x7e, 0x7e, 0x7e, 0x7e, 0x7e, 0x7f, 0x7f, 0x80, 0x80, 0x80, 0x80,
0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x81,
0x81, 0x82, 0x82, 0x82, 0x83, 0x83, 0x83, 0x83, 0x84, 0x84, 0x84, 0x84, 0x83, 0x83, 0x82, 0x82,
0x81, 0x81, 0x81, 0x80, 0x80, 0x80, 0x7f, 0x7f, 0x7e, 0x7e, 0x7d, 0x7d, 0x7e, 0x7e, 0x7e, 0x7e,
0x7e, 0x7d, 0x7e, 0x7e, 0x7f, 0x7f, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80,
0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x81, 0x81, 0x81, 0x81, 0x81, 0x80, 0x80, 0x80, 0x80, 0x80,
0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x7f, 0x7e, 0x7e, 0x7e, 0x7e, 0x7e, 0x7e, 0x7d, 0x7d, 0x7d,
0x7d, 0x7e, 0x7e, 0x7f, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x81, 0x82, 0x83, 0x84, 0x84, 0x84,
0x84, 0x84, 0x84, 0x83, 0x82, 0x82, 0x82, 0x81, 0x81, 0x81, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80,
0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80,
0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x7f, 0x7f, 0x7f, 0x7f, 0x7f, 0x7f, 0x80, 0x80, 0x80, 0x80,
0x80, 0x80, 0x7f, 0x7f, 0x7f, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x7f, 0x7f, 0x7f,
0x7f, 0x7f, 0x7f, 0x7f, 0x7f, 0x7f, 0x7f, 0x80, 0x80, 0x81, 0x81, 0x81, 0x81, 0x81, 0x81, 0x81,
0x81, 0x82, 0x82, 0x83, 0x83, 0x83, 0x82, 0x82, 0x82, 0x82, 0x81, 0x81, 0x81, 0x80, 0x80, 0x80,
0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80,
0x80, 0x80, 0x80, 0x7f, 0x7f, 0x7f, 0x7f, 0x7f, 0x7f, 0x7f, 0x7f, 0x7f, 0x7f, 0x7f, 0x80, 0x80,
0x80, 0x81, 0x81, 0x81, 0x81, 0x81, 0x81, 0x81, 0x82, 0x82, 0x82, 0x81, 0x81, 0x80, 0x80, 0x80,
0x80, 0x80, 0x7f, 0x7f, 0x7f, 0x7f, 0x7e, 0x7e, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7e, 0x7e, 0x7e,
0x7f, 0x7f, 0x7f, 0x7f, 0x80, 0x80, 0x80, 0x81, 0x81, 0x81, 0x81, 0x81, 0x81, 0x81, 0x81, 0x82,
0x82, 0x82, 0x82, 0x82, 0x82, 0x82, 0x82, 0x82, 0x82, 0x83, 0x83, 0x82, 0x82, 0x81, 0x81, 0x80,
0x81, 0x81, 0x81, 0x81, 0x80, 0x80, 0x80, 0x7f, 0x7e, 0x7e, 0x7e, 0x7e, 0x7e, 0x7e, 0x7e, 0x7d,
0x7d, 0x7d, 0x7e, 0x7e, 0x7f, 0x7f, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x81, 0x81, 0x82, 0x82,
0x82, 0x82, 0x81, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x7f, 0x7f, 0x7f, 0x7f, 0x7f, 0x7f,
0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80,
0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x81, 0x81, 0x81, 0x81,
0x81, 0x81, 0x81, 0x80, 0x81, 0x81, 0x81, 0x81, 0x81, 0x81, 0x80, 0x80, 0x80, 0x80, 0x80, 0x7f,
0x7f, 0x7f, 0x7f, 0x7e, 0x7e, 0x7e, 0x7f, 0x7f, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80,
0x81, 0x81, 0x81, 0x82, 0x82, 0x82, 0x81, 0x81, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x7f, 0x7f,
0x7f, 0x7e, 0x7e, 0x7e, 0x7f, 0x7f, 0x7f, 0x80, 0x7f, 0x7f, 0x7f, 0x7f, 0x7f, 0x80, 0x80, 0x80,
0x80, 0x80, 0x80, 0x7f, 0x7f, 0x7f, 0x7f, 0x7f, 0x7f, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x81,
0x81, 0x82, 0x83, 0x83, 0x83, 0x83, 0x83, 0x83, 0x83, 0x83, 0x83, 0x83, 0x83, 0x82, 0x81, 0x80,
0x80, 0x80, 0x80, 0x80, 0x80, 0x7f, 0x7f, 0x7e, 0x7e, 0x7d, 0x7d, 0x7d, 0x7d, 0x7e, 0x7e, 0x7f,
0x7f, 0x7f, 0x7f, 0x80, 0x80, 0x80, 0x80, 0x81, 0x81, 0x81, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80,
0x81, 0x81, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x81, 0x81, 0x81, 0x80, 0x80, 0x80, 0x80,
0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x7f, 0x7e, 0x7e, 0x7e, 0x7e, 0x7e, 0x7e, 0x7e, 0x7e, 0x7e,
0x7e, 0x7e, 0x7e, 0x7f, 0x7f, 0x80, 0x80, 0x80, 0x80, 0x81, 0x81, 0x81, 0x82, 0x83, 0x83, 0x84,
0x83, 0x83, 0x82, 0x82, 0x81, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80,
0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x81, 0x81, 0x80, 0x80, 0x80,
0x80, 0x7f, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x7f, 0x7f, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80,
0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x81, 0x81, 0x80, 0x80, 0x80, 0x80, 0x7f, 0x7f, 0x7f,
0x7e, 0x7e, 0x7e, 0x7e, 0x7e, 0x7e, 0x7e, 0x7f, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x81, 0x81,
0x81, 0x82, 0x82, 0x83, 0x83, 0x83, 0x82, 0x82, 0x81, 0x81, 0x81, 0x80, 0x80, 0x80, 0x80, 0x80,
0x80, 0x80, 0x7f, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80,
0x80, 0x80, 0x80, 0x80, 0x7f, 0x7f, 0x7f, 0x7f, 0x7f, 0x7f, 0x7f, 0x7f, 0x7f, 0x7f, 0x80, 0x80,
0x80, 0x81, 0x82, 0x82, 0x82, 0x82, 0x82, 0x82, 0x82, 0x82, 0x82, 0x82, 0x82, 0x81, 0x81, 0x80,
0x80, 0x7f, 0x7f, 0x7f, 0x7f, 0x7e, 0x7e, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7e, 0x7e, 0x7e,
0x7f, 0x7f, 0x7f, 0x80, 0x80, 0x80, 0x81, 0x81, 0x81, 0x81, 0x81, 0x81, 0x81, 0x81, 0x81, 0x81,
0x81, 0x81, 0x81, 0x81, 0x80, 0x80, 0x80, 0x80, 0x81, 0x81, 0x81, 0x81, 0x81, 0x81, 0x81, 0x81,
0x81, 0x81, 0x81, 0x81, 0x80, 0x80, 0x80, 0x7f, 0x7f, 0x7e, 0x7e, 0x7e, 0x7e, 0x7e, 0x7e, 0x7e,
0x7e, 0x7e, 0x7e, 0x7f, 0x7f, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x81, 0x81, 0x82, 0x82, 0x82,
0x82, 0x82, 0x81, 0x81, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x7f, 0x7f, 0x7f, 0x7e, 0x7f, 0x7f,
0x7f, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x81, 0x81, 0x81, 0x81, 0x81, 0x80,
0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80,
0x80, 0x80, 0x80, 0x80, 0x80, 0x81, 0x81, 0x82, 0x82, 0x81, 0x81, 0x80, 0x80, 0x80, 0x80, 0x7f,
0x7f, 0x7f, 0x7e, 0x7e, 0x7e, 0x7e, 0x7e, 0x7f, 0x7f, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80,
0x81, 0x81, 0x82, 0x82, 0x83, 0x83, 0x82, 0x82, 0x81, 0x81, 0x80, 0x80, 0x80, 0x80, 0x80, 0x7f,
0x7f, 0x7f, 0x7f, 0x7f, 0x7f, 0x80, 0x80, 0x80, 0x80, 0x80, 0x7f, 0x7f, 0x80, 0x80, 0x80, 0x80,
0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x7f, 0x7f, 0x7f, 0x7f, 0x7f, 0x7f, 0x7f, 0x7f, 0x80,
0x80, 0x81, 0x81, 0x82, 0x82, 0x82, 0x82, 0x82, 0x82, 0x82, 0x83, 0x83, 0x83, 0x82, 0x82, 0x81,
0x80, 0x80, 0x80, 0x80, 0x7f, 0x7f, 0x7e, 0x7e, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7e, 0x7e, 0x7f,
0x7f, 0x7f, 0x7f, 0x80, 0x80, 0x80, 0x81, 0x81, 0x82, 0x82, 0x82, 0x81, 0x81, 0x82, 0x82, 0x82,
0x81, 0x81, 0x81, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80,
0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x7f, 0x7f, 0x7f, 0x7e, 0x7e, 0x7e, 0x7e, 0x7e,
0x7e, 0x7e, 0x7e, 0x7e, 0x7f, 0x7f, 0x80, 0x80, 0x80, 0x80, 0x81, 0x81, 0x81, 0x82, 0x82, 0x82,
0x83, 0x82, 0x82, 0x81, 0x81, 0x81, 0x80, 0x80, 0x80, 0x80, 0x80, 0x7f, 0x7f, 0x7f, 0x7f, 0x7f,
0x7f, 0x7f, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x81, 0x81, 0x82, 0x82, 0x82, 0x82, 0x81,
0x81, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x7f, 0x7f, 0x80, 0x80, 0x80, 0x80, 0x80,
0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x81, 0x81, 0x81, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80,
0x7f, 0x7f, 0x7f, 0x7e, 0x7e, 0x7e, 0x7e, 0x7e, 0x7f, 0x7f, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80,
0x80, 0x81, 0x82, 0x82, 0x83, 0x83, 0x82, 0x82, 0x81, 0x81, 0x81, 0x80, 0x80, 0x80, 0x80, 0x80,
0x7f, 0x7f, 0x7f, 0x7f, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80,
0x80, 0x81, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x7f, 0x7f, 0x7f, 0x7f, 0x7f, 0x7f,
0x80, 0x80, 0x80, 0x81, 0x81, 0x81, 0x81, 0x81, 0x81, 0x81, 0x81, 0x82, 0x82, 0x82, 0x82, 0x81,
0x81, 0x80, 0x80, 0x80, 0x80, 0x7f, 0x7e, 0x7e, 0x7d, 0x7d, 0x7d, 0x7d, 0x7d, 0x7e, 0x7e, 0x7e,
0x7f, 0x7f, 0x7f, 0x7f, 0x80, 0x80, 0x80, 0x80, 0x81, 0x81, 0x82, 0x82, 0x82, 0x82, 0x82, 0x82,
0x82, 0x81, 0x81, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80,
0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x7f, 0x7f, 0x7f, 0x7f, 0x7f,
0x7e, 0x7e, 0x7e, 0x7e, 0x7f, 0x7f, 0x80, 0x80, 0x80, 0x80, 0x80, 0x81, 0x81, 0x81, 0x81, 0x82,
0x82, 0x82, 0x81, 0x81, 0x81, 0x81, 0x80, 0x80, 0x80, 0x80, 0x80, 0x7f, 0x7f, 0x7e, 0x7e, 0x7e,
0x7e, 0x7e, 0x7f, 0x7f, 0x7f, 0x7f, 0x80, 0x80, 0x80, 0x80, 0x80, 0x81, 0x81, 0x82, 0x81, 0x81,
0x81, 0x81, 0x81, 0x81, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80,
0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x81, 0x81, 0x81, 0x81, 0x80, 0x80, 0x80, 0x80,
0x80, 0x80, 0x80, 0x7f, 0x7f, 0x7f, 0x7f, 0x7f, 0x7f, 0x7f, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80,
0x80, 0x81, 0x81, 0x82, 0x82, 0x82, 0x82, 0x82, 0x81, 0x81, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80,
0x7f, 0x7f, 0x7f, 0x7f, 0x7f, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80,
0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x7f, 0x7f, 0x7f, 0x7f, 0x7f, 0x7f,
0x7f, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x81, 0x81, 0x82, 0x82, 0x82, 0x82,
0x81, 0x81, 0x81, 0x80, 0x80, 0x80, 0x80, 0x7f, 0x7f, 0x7e, 0x7e, 0x7e, 0x7e, 0x7e, 0x7f, 0x7f,
0x7f, 0x7f, 0x7f, 0x7f, 0x80, 0x80, 0x80, 0x80, 0x81, 0x81, 0x82, 0x82, 0x82, 0x82, 0x82, 0x82,
0x82, 0x81, 0x81, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80,
0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x7f, 0x7f, 0x7f, 0x7f, 0x7e,
0x7e, 0x7e, 0x7e, 0x7e, 0x7e, 0x7f, 0x7f, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x81, 0x81,
0x81, 0x81, 0x81, 0x81, 0x81, 0x81, 0x81, 0x81, 0x80, 0x80, 0x80, 0x80, 0x80, 0x7f, 0x7f, 0x7f,
0x7e, 0x7f, 0x7f, 0x7f, 0x7f, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x81, 0x81, 0x82, 0x82, 0x82,
0x82, 0x82, 0x82, 0x81, 0x81, 0x81, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x7f, 0x7f, 0x80, 0x80,
0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80,
0x80, 0x80, 0x80, 0x7f, 0x7f, 0x7f, 0x7e, 0x7e, 0x7e, 0x7f, 0x7f, 0x7f, 0x80, 0x80, 0x80, 0x80,
0x80, 0x80, 0x80, 0x81, 0x81, 0x81, 0x81, 0x81, 0x81, 0x81, 0x81, 0x80, 0x80, 0x80, 0x80, 0x80,
0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80,
0x81, 0x81, 0x81, 0x81, 0x81, 0x81, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x7f, 0x7f,
0x7f, 0x7f, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x81, 0x81, 0x81, 0x81, 0x81,
0x81, 0x81, 0x81, 0x80, 0x80, 0x80, 0x80, 0x7f, 0x7f, 0x7e, 0x7e, 0x7e, 0x7e, 0x7e, 0x7e, 0x7e,
0x7f, 0x7f, 0x7f, 0x7f, 0x7f, 0x80, 0x80, 0x80, 0x80, 0x80, 0x81, 0x81, 0x81, 0x81, 0x81, 0x81,
0x81, 0x81, 0x81, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80,
0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x7f, 0x7f,
0x7f, 0x7f, 0x7f, 0x7f, 0x7f, 0x7f, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x81,
0x81, 0x81, 0x81, 0x81, 0x81, 0x81, 0x81, 0x81, 0x80, 0x80, 0x80, 0x80, 0x80, 0x7f, 0x7f, 0x7f,
0x7f, 0x7e, 0x7e, 0x7f, 0x7f, 0x7f, 0x7f, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x81, 0x81, 0x81,
0x81, 0x81, 0x81, 0x81, 0x81, 0x81, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x7f, 0x7f, 0x80,
0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80,
0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x7f, 0x7f, 0x7f, 0x7f, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80,
0x80, 0x80, 0x80, 0x80, 0x81, 0x81, 0x81, 0x81, 0x81, 0x81, 0x81, 0x80, 0x80, 0x80, 0x80, 0x80,
0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80,
0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x7f,
0x7f, 0x7f, 0x7f, 0x7f, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x81, 0x81, 0x81,
0x81, 0x81, 0x81, 0x81, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x7f, 0x7f, 0x7f, 0x7f, 0x7f, 0x7f,
0x7f, 0x7f, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x81, 0x81, 0x81, 0x81, 0x81, 0x81,
0x81, 0x81, 0x81, 0x81, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80,
0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x7f, 0x7f,
0x7f, 0x7f, 0x7f, 0x7f, 0x7f, 0x7f, 0x7f, 0x7f, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80,
0x80, 0x80, 0x81, 0x81, 0x81, 0x81, 0x81, 0x81, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80,
0x7f, 0x7f, 0x7f, 0x7f, 0x7f, 0x7f, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x81, 0x81, 0x81,
0x81, 0x81, 0x81, 0x81, 0x81, 0x81, 0x81, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80, 0x80,
};
