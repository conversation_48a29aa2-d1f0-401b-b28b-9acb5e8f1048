/* DejaVu 24
 original ttf url : https://dejavu-fonts.github.io/
 original license : https://dejavu-fonts.github.io/License.html
This data has been converted to AdafruitGFX font format from DejaVuSans.ttf.
*/
const uint8_t DejaVu24Bitmaps[] PROGMEM = {
0xff, 0xff, 0xff, 0x03, 0xf0, 0xcf, 0x3c, 0xf3, 0xcf, 0x3c, 0xc0, 0x03, 0x08, 0x03, 0x18, 0x03,
0x18, 0x03, 0x18, 0x02, 0x18, 0x7f, 0xff, 0x7f, 0xff, 0x06, 0x30, 0x04, 0x30, 0x0c, 0x20, 0x0c,
0x60, 0xff, 0xfe, 0xff, 0xfe, 0x18, 0x40, 0x18, 0xc0, 0x18, 0xc0, 0x18, 0xc0, 0x10, 0xc0, 0x04,
0x00, 0x80, 0x10, 0x0f, 0xc7, 0xfd, 0xc8, 0xb1, 0x06, 0x20, 0xe4, 0x0f, 0x80, 0xfe, 0x03, 0xe0,
0x4e, 0x08, 0xc1, 0x1e, 0x27, 0xff, 0xc7, 0xe0, 0x10, 0x02, 0x00, 0x40, 0x08, 0x00, 0x3c, 0x03,
0x06, 0x60, 0x60, 0xc3, 0x06, 0x0c, 0x30, 0xc0, 0xc3, 0x1c, 0x0c, 0x31, 0x80, 0xc3, 0x38, 0x0c,
0x33, 0x00, 0x66, 0x63, 0xc3, 0xc6, 0x66, 0x00, 0xcc, 0x30, 0x1c, 0xc3, 0x01, 0x8c, 0x30, 0x38,
0xc3, 0x03, 0x0c, 0x30, 0x60, 0xc3, 0x06, 0x06, 0x60, 0xc0, 0x3c, 0x07, 0xc0, 0x1f, 0xe0, 0x38,
0x20, 0x30, 0x00, 0x30, 0x00, 0x30, 0x00, 0x18, 0x00, 0x1c, 0x00, 0x3e, 0x00, 0x77, 0x06, 0xe3,
0x86, 0xc1, 0xcc, 0xc0, 0xfc, 0xc0, 0x78, 0xe0, 0x78, 0x70, 0xfc, 0x3f, 0xce, 0x0f, 0x87, 0xff,
0xfc, 0x19, 0x8c, 0xc6, 0x31, 0x18, 0xc6, 0x31, 0x8c, 0x61, 0x0c, 0x63, 0x0c, 0x61, 0x80, 0xc3,
0x18, 0x63, 0x18, 0x43, 0x18, 0xc6, 0x31, 0x8c, 0x46, 0x31, 0x98, 0xcc, 0x00, 0x04, 0x00, 0x83,
0x11, 0xba, 0xe1, 0xf0, 0x3e, 0x1d, 0x76, 0x23, 0x04, 0x00, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01,
0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0x80, 0xff, 0xff, 0xff, 0xff, 0x01, 0x80, 0x01,
0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0x80, 0x6d, 0xbd, 0x80, 0xff, 0xf0,
0xfc, 0x03, 0x07, 0x06, 0x06, 0x06, 0x0c, 0x0c, 0x0c, 0x18, 0x18, 0x18, 0x18, 0x30, 0x30, 0x30,
0x60, 0x60, 0x60, 0xe0, 0xc0, 0x0f, 0x03, 0xfc, 0x70, 0xe6, 0x06, 0x60, 0x6c, 0x03, 0xc0, 0x3c,
0x03, 0xc0, 0x3c, 0x03, 0xc0, 0x3c, 0x03, 0xc0, 0x36, 0x06, 0x60, 0x67, 0x0e, 0x3f, 0xc0, 0xf0,
0x3c, 0x3f, 0x0c, 0xc0, 0x30, 0x0c, 0x03, 0x00, 0xc0, 0x30, 0x0c, 0x03, 0x00, 0xc0, 0x30, 0x0c,
0x03, 0x00, 0xc0, 0x30, 0xff, 0xff, 0xf0, 0x3f, 0x0f, 0xf8, 0xc1, 0xc0, 0x0e, 0x00, 0x60, 0x06,
0x00, 0x60, 0x0c, 0x01, 0xc0, 0x18, 0x03, 0x00, 0x60, 0x0c, 0x01, 0x80, 0x30, 0x06, 0x00, 0xff,
0xef, 0xfe, 0x3f, 0x07, 0xfc, 0x41, 0xc0, 0x06, 0x00, 0x60, 0x06, 0x00, 0x60, 0x0c, 0x1f, 0x81,
0xfc, 0x00, 0xe0, 0x07, 0x00, 0x30, 0x03, 0x00, 0x78, 0x0e, 0xff, 0xc3, 0xf0, 0x01, 0xc0, 0x1e,
0x00, 0xb0, 0x0d, 0x80, 0xcc, 0x06, 0x60, 0x63, 0x03, 0x18, 0x30, 0xc3, 0x06, 0x18, 0x31, 0x81,
0x8f, 0xff, 0xff, 0xfc, 0x03, 0x00, 0x18, 0x00, 0xc0, 0x06, 0x00, 0x7f, 0xcf, 0xf9, 0x80, 0x30,
0x06, 0x00, 0xc0, 0x1f, 0xc3, 0xfc, 0x41, 0xc0, 0x1c, 0x01, 0x80, 0x30, 0x06, 0x00, 0xc0, 0x3c,
0x0e, 0xff, 0x8f, 0xc0, 0x07, 0xc1, 0xfe, 0x38, 0x27, 0x00, 0x60, 0x0c, 0x00, 0xcf, 0x8d, 0xfc,
0xf8, 0xef, 0x07, 0xe0, 0x3e, 0x03, 0xe0, 0x36, 0x03, 0x70, 0x77, 0x8e, 0x3f, 0xc0, 0xf8, 0xff,
0xff, 0xfc, 0x03, 0x00, 0x60, 0x1c, 0x03, 0x00, 0x60, 0x18, 0x03, 0x00, 0xe0, 0x18, 0x03, 0x00,
0xc0, 0x18, 0x07, 0x00, 0xc0, 0x18, 0x06, 0x00, 0x1f, 0x87, 0xfe, 0x70, 0xec, 0x03, 0xc0, 0x3c,
0x03, 0xc0, 0x37, 0x0e, 0x3f, 0xc3, 0xfc, 0x70, 0xec, 0x03, 0xc0, 0x3c, 0x03, 0xc0, 0x37, 0x0e,
0x7f, 0xe1, 0xf8, 0x1f, 0x03, 0xfc, 0x71, 0xce, 0x0e, 0xc0, 0x6c, 0x07, 0xc0, 0x7c, 0x07, 0xe0,
0xf7, 0x1f, 0x3f, 0xb1, 0xf3, 0x00, 0x30, 0x06, 0x00, 0xe4, 0x1c, 0x7f, 0x83, 0xe0, 0xfc, 0x00,
0x3f, 0x6d, 0x80, 0x00, 0x0d, 0xb7, 0xb0, 0x00, 0x02, 0x00, 0x3c, 0x03, 0xf0, 0x3f, 0x01, 0xf8,
0x1f, 0x80, 0x3c, 0x00, 0x7e, 0x00, 0x1f, 0x80, 0x0f, 0xc0, 0x03, 0xf0, 0x00, 0xf0, 0x00, 0x20,
0xff, 0xff, 0xff, 0xfc, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1f, 0xff, 0xff, 0xff, 0x80, 0x80, 0x01,
0xe0, 0x01, 0xf8, 0x00, 0x7e, 0x00, 0x3f, 0x00, 0x0f, 0xc0, 0x07, 0x80, 0x3f, 0x03, 0xf0, 0x1f,
0x81, 0xf8, 0x07, 0x80, 0x08, 0x00, 0x00, 0x3e, 0x3f, 0xb0, 0xf0, 0x30, 0x18, 0x0c, 0x0c, 0x0e,
0x0e, 0x0e, 0x06, 0x03, 0x01, 0x80, 0x00, 0x00, 0x30, 0x18, 0x0c, 0x00, 0x00, 0xfc, 0x00, 0x3f,
0xf8, 0x03, 0xc0, 0xf0, 0x38, 0x01, 0xc3, 0x80, 0x07, 0x38, 0x79, 0x99, 0x8f, 0xec, 0xfc, 0x71,
0xe3, 0xc7, 0x07, 0x1e, 0x30, 0x18, 0xf1, 0x80, 0xc7, 0x8c, 0x06, 0x3c, 0x70, 0x73, 0x71, 0xc7,
0xb9, 0x8f, 0xef, 0x8e, 0x1e, 0x70, 0x38, 0x00, 0x00, 0xe0, 0x04, 0x03, 0xc0, 0xe0, 0x0f, 0xfe,
0x00, 0x0f, 0x80, 0x00, 0x03, 0xc0, 0x03, 0xc0, 0x03, 0xc0, 0x07, 0xe0, 0x06, 0x60, 0x06, 0x60,
0x0c, 0x30, 0x0c, 0x30, 0x0c, 0x30, 0x18, 0x18, 0x18, 0x18, 0x38, 0x1c, 0x3f, 0xfc, 0x3f, 0xfc,
0x60, 0x06, 0x60, 0x06, 0x60, 0x06, 0xc0, 0x03, 0xff, 0x0f, 0xfc, 0xc0, 0xec, 0x06, 0xc0, 0x6c,
0x06, 0xc0, 0x6c, 0x0c, 0xff, 0x8f, 0xfc, 0xc0, 0x6c, 0x03, 0xc0, 0x3c, 0x03, 0xc0, 0x3c, 0x06,
0xff, 0xef, 0xf8, 0x07, 0xe0, 0x7f, 0xe3, 0xc1, 0xdc, 0x01, 0x60, 0x01, 0x80, 0x0c, 0x00, 0x30,
0x00, 0xc0, 0x03, 0x00, 0x0c, 0x00, 0x30, 0x00, 0x60, 0x01, 0x80, 0x07, 0x00, 0x4f, 0x07, 0x1f,
0xf8, 0x1f, 0x80, 0xff, 0x81, 0xff, 0xe3, 0x01, 0xe6, 0x00, 0xec, 0x00, 0xd8, 0x01, 0xf0, 0x01,
0xe0, 0x03, 0xc0, 0x07, 0x80, 0x0f, 0x00, 0x1e, 0x00, 0x3c, 0x00, 0xf8, 0x01, 0xb0, 0x07, 0x60,
0x3c, 0xff, 0xf1, 0xff, 0x00, 0xff, 0xff, 0xff, 0x00, 0x60, 0x0c, 0x01, 0x80, 0x30, 0x06, 0x00,
0xff, 0xdf, 0xfb, 0x00, 0x60, 0x0c, 0x01, 0x80, 0x30, 0x06, 0x00, 0xff, 0xff, 0xfc, 0xff, 0xff,
0xfc, 0x03, 0x00, 0xc0, 0x30, 0x0c, 0x03, 0x00, 0xff, 0xbf, 0xec, 0x03, 0x00, 0xc0, 0x30, 0x0c,
0x03, 0x00, 0xc0, 0x30, 0x00, 0x07, 0xe0, 0x3f, 0xf0, 0xe0, 0x73, 0x80, 0x26, 0x00, 0x1c, 0x00,
0x30, 0x00, 0x60, 0x00, 0xc0, 0x7f, 0x80, 0xff, 0x00, 0x1e, 0x00, 0x36, 0x00, 0x6c, 0x00, 0xdc,
0x01, 0x9e, 0x07, 0x1f, 0xfc, 0x0f, 0xe0, 0xc0, 0x1e, 0x00, 0xf0, 0x07, 0x80, 0x3c, 0x01, 0xe0,
0x0f, 0x00, 0x78, 0x03, 0xff, 0xff, 0xff, 0xf0, 0x07, 0x80, 0x3c, 0x01, 0xe0, 0x0f, 0x00, 0x78,
0x03, 0xc0, 0x1e, 0x00, 0xc0, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x0c, 0x30, 0xc3, 0x0c, 0x30, 0xc3,
0x0c, 0x30, 0xc3, 0x0c, 0x30, 0xc3, 0x0c, 0x30, 0xc3, 0x1b, 0xef, 0x00, 0xc0, 0x71, 0x81, 0xc3,
0x07, 0x06, 0x1c, 0x0c, 0x70, 0x19, 0xc0, 0x37, 0x00, 0x7c, 0x00, 0xf8, 0x01, 0xb0, 0x03, 0x38,
0x06, 0x38, 0x0c, 0x38, 0x18, 0x38, 0x30, 0x38, 0x60, 0x38, 0xc0, 0x39, 0x80, 0x38, 0xc0, 0x18,
0x03, 0x00, 0x60, 0x0c, 0x01, 0x80, 0x30, 0x06, 0x00, 0xc0, 0x18, 0x03, 0x00, 0x60, 0x0c, 0x01,
0x80, 0x30, 0x06, 0x00, 0xff, 0xff, 0xfc, 0xe0, 0x07, 0xf0, 0x0f, 0xf0, 0x0f, 0xf8, 0x1f, 0xd8,
0x1b, 0xd8, 0x1b, 0xcc, 0x33, 0xcc, 0x33, 0xcc, 0x33, 0xc6, 0x63, 0xc6, 0x63, 0xc7, 0xe3, 0xc3,
0xc3, 0xc3, 0xc3, 0xc1, 0x83, 0xc0, 0x03, 0xc0, 0x03, 0xc0, 0x03, 0xe0, 0x1f, 0x80, 0xfc, 0x07,
0xf0, 0x3d, 0x81, 0xe6, 0x0f, 0x30, 0x78, 0xc3, 0xc6, 0x1e, 0x18, 0xf0, 0xc7, 0x83, 0x3c, 0x19,
0xe0, 0x6f, 0x03, 0x78, 0x0f, 0xc0, 0x7e, 0x01, 0xc0, 0x07, 0xe0, 0x1f, 0xf8, 0x3c, 0x3c, 0x70,
0x0e, 0x60, 0x06, 0x60, 0x06, 0xc0, 0x03, 0xc0, 0x03, 0xc0, 0x03, 0xc0, 0x03, 0xc0, 0x03, 0xc0,
0x03, 0x60, 0x06, 0x60, 0x06, 0x70, 0x0e, 0x3c, 0x3c, 0x1f, 0xf8, 0x07, 0xe0, 0xff, 0x1f, 0xfb,
0x07, 0x60, 0x3c, 0x07, 0x80, 0xf0, 0x1e, 0x0e, 0xff, 0xdf, 0xe3, 0x00, 0x60, 0x0c, 0x01, 0x80,
0x30, 0x06, 0x00, 0xc0, 0x18, 0x00, 0x07, 0xe0, 0x1f, 0xf8, 0x3c, 0x3c, 0x70, 0x0e, 0x60, 0x06,
0x60, 0x06, 0xc0, 0x03, 0xc0, 0x03, 0xc0, 0x03, 0xc0, 0x03, 0xc0, 0x03, 0xc0, 0x03, 0x60, 0x07,
0x60, 0x06, 0x70, 0x0e, 0x3c, 0x3c, 0x1f, 0xf8, 0x07, 0xf0, 0x00, 0x38, 0x00, 0x18, 0x00, 0x0c,
0xff, 0x07, 0xfe, 0x30, 0x31, 0x80, 0xcc, 0x06, 0x60, 0x33, 0x01, 0x98, 0x18, 0xff, 0xc7, 0xfc,
0x30, 0x71, 0x81, 0x8c, 0x06, 0x60, 0x33, 0x01, 0xd8, 0x06, 0xc0, 0x36, 0x00, 0xc0, 0x1f, 0x87,
0xfe, 0x70, 0x6c, 0x00, 0xc0, 0x0c, 0x00, 0xc0, 0x07, 0x00, 0x7f, 0x01, 0xfc, 0x00, 0xe0, 0x07,
0x00, 0x30, 0x03, 0x00, 0x3c, 0x0e, 0xff, 0xe3, 0xf8, 0xff, 0xff, 0xff, 0xf0, 0x30, 0x00, 0xc0,
0x03, 0x00, 0x0c, 0x00, 0x30, 0x00, 0xc0, 0x03, 0x00, 0x0c, 0x00, 0x30, 0x00, 0xc0, 0x03, 0x00,
0x0c, 0x00, 0x30, 0x00, 0xc0, 0x03, 0x00, 0x0c, 0x00, 0xc0, 0x1e, 0x00, 0xf0, 0x07, 0x80, 0x3c,
0x01, 0xe0, 0x0f, 0x00, 0x78, 0x03, 0xc0, 0x1e, 0x00, 0xf0, 0x07, 0x80, 0x3c, 0x01, 0xe0, 0x0d,
0x80, 0xce, 0x0e, 0x3f, 0xe0, 0x7c, 0x00, 0xc0, 0x03, 0x60, 0x06, 0x60, 0x06, 0x60, 0x06, 0x30,
0x0c, 0x30, 0x0c, 0x38, 0x1c, 0x18, 0x18, 0x18, 0x18, 0x0c, 0x30, 0x0c, 0x30, 0x0c, 0x30, 0x06,
0x60, 0x06, 0x60, 0x07, 0x60, 0x03, 0xc0, 0x03, 0xc0, 0x03, 0xc0, 0xc0, 0x78, 0x0f, 0x01, 0xe0,
0x36, 0x07, 0x81, 0x98, 0x1e, 0x06, 0x60, 0xec, 0x19, 0x83, 0x30, 0x63, 0x0c, 0xc3, 0x0c, 0x33,
0x0c, 0x30, 0xce, 0x30, 0xc6, 0x18, 0xc1, 0x98, 0x66, 0x06, 0x61, 0x98, 0x19, 0x86, 0x60, 0x6c,
0x0d, 0x80, 0xf0, 0x3c, 0x03, 0xc0, 0xf0, 0x0f, 0x03, 0xc0, 0x38, 0x07, 0x00, 0x70, 0x0e, 0x60,
0x18, 0x60, 0x60, 0xe1, 0xc0, 0xc7, 0x00, 0xcc, 0x01, 0xf0, 0x01, 0xe0, 0x03, 0x80, 0x07, 0x80,
0x1f, 0x00, 0x37, 0x00, 0xc6, 0x03, 0x86, 0x0e, 0x0e, 0x18, 0x0c, 0x60, 0x0d, 0xc0, 0x1c, 0xe0,
0x1d, 0x80, 0x63, 0x03, 0x0e, 0x1c, 0x18, 0x60, 0x33, 0x00, 0xfc, 0x01, 0xe0, 0x07, 0x80, 0x0c,
0x00, 0x30, 0x00, 0xc0, 0x03, 0x00, 0x0c, 0x00, 0x30, 0x00, 0xc0, 0x03, 0x00, 0x0c, 0x00, 0xff,
0xff, 0xff, 0xf0, 0x01, 0x80, 0x0e, 0x00, 0x70, 0x01, 0x80, 0x0c, 0x00, 0x60, 0x03, 0x80, 0x1c,
0x00, 0x60, 0x03, 0x00, 0x18, 0x00, 0xe0, 0x07, 0x00, 0x18, 0x00, 0xff, 0xff, 0xff, 0xf0, 0xff,
0xf1, 0x8c, 0x63, 0x18, 0xc6, 0x31, 0x8c, 0x63, 0x18, 0xc6, 0x31, 0xff, 0x80, 0xc0, 0xe0, 0x60,
0x60, 0x60, 0x30, 0x30, 0x30, 0x18, 0x18, 0x18, 0x18, 0x0c, 0x0c, 0x0c, 0x06, 0x06, 0x06, 0x07,
0x03, 0xff, 0xc6, 0x31, 0x8c, 0x63, 0x18, 0xc6, 0x31, 0x8c, 0x63, 0x18, 0xc7, 0xff, 0x80, 0x03,
0x80, 0x0f, 0x80, 0x3b, 0x80, 0xe3, 0x83, 0x83, 0x8e, 0x03, 0xb8, 0x03, 0x80, 0xff, 0xff, 0xff,
0x60, 0xc1, 0x83, 0x3f, 0x0f, 0xf9, 0x03, 0x00, 0x30, 0x06, 0x3f, 0xdf, 0xff, 0x03, 0xc0, 0x78,
0x1f, 0x87, 0xbf, 0xf3, 0xe6, 0xc0, 0x0c, 0x00, 0xc0, 0x0c, 0x00, 0xc0, 0x0c, 0xf8, 0xff, 0xcf,
0x0e, 0xe0, 0x6c, 0x03, 0xc0, 0x3c, 0x03, 0xc0, 0x3c, 0x03, 0xe0, 0x6f, 0x0e, 0xff, 0xcc, 0xf8,
0x0f, 0x8f, 0xf7, 0x05, 0x80, 0xc0, 0x30, 0x0c, 0x03, 0x00, 0xc0, 0x18, 0x07, 0x04, 0xff, 0x0f,
0x80, 0x00, 0x30, 0x03, 0x00, 0x30, 0x03, 0x00, 0x31, 0xf3, 0x3f, 0xf7, 0x0f, 0x60, 0x7c, 0x03,
0xc0, 0x3c, 0x03, 0xc0, 0x3c, 0x03, 0x60, 0x77, 0x0f, 0x3f, 0xf1, 0xf3, 0x0f, 0x83, 0xfc, 0x70,
0xe6, 0x07, 0xc0, 0x3f, 0xff, 0xff, 0xfc, 0x00, 0xc0, 0x06, 0x00, 0x70, 0x23, 0xfe, 0x0f, 0xc0,
0x0f, 0x1f, 0x38, 0x30, 0x30, 0xff, 0xff, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30,
0x30, 0x30, 0x1f, 0x33, 0xff, 0x70, 0xf6, 0x07, 0xc0, 0x3c, 0x03, 0xc0, 0x3c, 0x03, 0xc0, 0x36,
0x07, 0x70, 0xf3, 0xff, 0x1f, 0x30, 0x03, 0x00, 0x72, 0x0e, 0x3f, 0xc1, 0xf8, 0xc0, 0x18, 0x03,
0x00, 0x60, 0x0c, 0x01, 0x9f, 0x3f, 0xf7, 0x87, 0xe0, 0x78, 0x0f, 0x01, 0xe0, 0x3c, 0x07, 0x80,
0xf0, 0x1e, 0x03, 0xc0, 0x78, 0x0c, 0xfc, 0x3f, 0xff, 0xff, 0xf0, 0x18, 0xc6, 0x00, 0x0c, 0x63,
0x18, 0xc6, 0x31, 0x8c, 0x63, 0x18, 0xc6, 0x33, 0xfb, 0x80, 0xc0, 0x0c, 0x00, 0xc0, 0x0c, 0x00,
0xc0, 0x0c, 0x1c, 0xc3, 0x8c, 0x70, 0xce, 0x0d, 0xc0, 0xf8, 0x0f, 0x80, 0xdc, 0x0c, 0xe0, 0xc7,
0x0c, 0x38, 0xc1, 0xcc, 0x0e, 0xff, 0xff, 0xff, 0xff, 0xf0, 0xcf, 0x87, 0xcf, 0xfc, 0xfe, 0xf0,
0xf8, 0x7e, 0x07, 0x03, 0xc0, 0x60, 0x3c, 0x06, 0x03, 0xc0, 0x60, 0x3c, 0x06, 0x03, 0xc0, 0x60,
0x3c, 0x06, 0x03, 0xc0, 0x60, 0x3c, 0x06, 0x03, 0xc0, 0x60, 0x30, 0xcf, 0x9f, 0xfb, 0xc3, 0xf0,
0x3c, 0x07, 0x80, 0xf0, 0x1e, 0x03, 0xc0, 0x78, 0x0f, 0x01, 0xe0, 0x3c, 0x06, 0x1f, 0x83, 0xfc,
0x70, 0xe6, 0x06, 0xc0, 0x3c, 0x03, 0xc0, 0x3c, 0x03, 0xc0, 0x36, 0x06, 0x70, 0xe3, 0xfc, 0x1f,
0x80, 0xcf, 0x8f, 0xfc, 0xf0, 0xee, 0x06, 0xc0, 0x3c, 0x03, 0xc0, 0x3c, 0x03, 0xc0, 0x3e, 0x06,
0xf0, 0xef, 0xfc, 0xcf, 0x8c, 0x00, 0xc0, 0x0c, 0x00, 0xc0, 0x0c, 0x00, 0x1f, 0x33, 0xff, 0x70,
0xf6, 0x07, 0xc0, 0x3c, 0x03, 0xc0, 0x3c, 0x03, 0xc0, 0x36, 0x07, 0x70, 0xf3, 0xff, 0x1f, 0x30,
0x03, 0x00, 0x30, 0x03, 0x00, 0x30, 0x03, 0xcf, 0xff, 0xf0, 0xe0, 0xc0, 0xc0, 0xc0, 0xc0, 0xc0,
0xc0, 0xc0, 0xc0, 0xc0, 0x3f, 0x0f, 0xf3, 0x82, 0x60, 0x0c, 0x00, 0xf0, 0x0f, 0xc0, 0x3c, 0x00,
0xc0, 0x1a, 0x07, 0x7f, 0xc7, 0xf0, 0x30, 0x30, 0x30, 0x30, 0xff, 0xff, 0x30, 0x30, 0x30, 0x30,
0x30, 0x30, 0x30, 0x30, 0x30, 0x1f, 0x0f, 0xc0, 0x78, 0x0f, 0x01, 0xe0, 0x3c, 0x07, 0x80, 0xf0,
0x1e, 0x03, 0xc0, 0x78, 0x1f, 0x87, 0xbf, 0xf3, 0xe6, 0xc0, 0x1b, 0x01, 0x98, 0x0c, 0xc0, 0x63,
0x06, 0x18, 0x30, 0x63, 0x03, 0x18, 0x18, 0xc0, 0x6c, 0x03, 0x60, 0x1f, 0x00, 0x70, 0x00, 0xc1,
0xe0, 0xf0, 0x78, 0x36, 0x1e, 0x19, 0x87, 0x86, 0x63, 0x31, 0x9c, 0xcc, 0xe3, 0x33, 0x30, 0xcc,
0xcc, 0x36, 0x1b, 0x07, 0x87, 0x81, 0xe1, 0xe0, 0x78, 0x78, 0x1c, 0x0e, 0x00, 0xe0, 0x3b, 0x83,
0x8e, 0x38, 0x31, 0x80, 0xd8, 0x07, 0xc0, 0x1c, 0x01, 0xf0, 0x1d, 0xc0, 0xc6, 0x0c, 0x18, 0xe0,
0xee, 0x03, 0x80, 0xc0, 0x1b, 0x01, 0x98, 0x0c, 0xe0, 0xe3, 0x06, 0x18, 0x70, 0x63, 0x03, 0x18,
0x0d, 0x80, 0x6c, 0x03, 0xe0, 0x0e, 0x00, 0x70, 0x03, 0x00, 0x18, 0x01, 0x80, 0x7c, 0x03, 0xc0,
0x00, 0xff, 0xff, 0xfc, 0x03, 0x00, 0xe0, 0x38, 0x0e, 0x03, 0x80, 0xe0, 0x38, 0x0e, 0x01, 0x80,
0x7f, 0xff, 0xfe, 0x03, 0x83, 0xc3, 0x81, 0x80, 0xc0, 0x60, 0x30, 0x18, 0x0c, 0x0e, 0x3e, 0x1f,
0x01, 0xc0, 0x60, 0x30, 0x18, 0x0c, 0x06, 0x03, 0x01, 0xc0, 0x78, 0x1c, 0xff, 0xff, 0xff, 0xff,
0xff, 0xff, 0xe0, 0x78, 0x0e, 0x03, 0x01, 0x80, 0xc0, 0x60, 0x30, 0x18, 0x0e, 0x03, 0xe1, 0xf1,
0xc0, 0xc0, 0x60, 0x30, 0x18, 0x0c, 0x06, 0x07, 0x0f, 0x07, 0x00, 0x00, 0x00, 0x7c, 0x05, 0xfe,
0x1e, 0x1f, 0xe0, 0x0f, 0x80, };

const GFXglyph DejaVu24Glyphs[] PROGMEM = {
 {    0,   0,   0,   9,   0,   1 },  // 0x20 ' '
 {    0,   2,  18,  11,   4, -17 },  // 0x21 '!'
 {    5,   6,   7,  12,   2, -17 },  // 0x22 '"'
 {   11,  16,  18,  21,   2, -17 },  // 0x23 '#'
 {   47,  11,  22,  16,   2, -17 },  // 0x24 '$'
 {   78,  20,  18,  24,   1, -17 },  // 0x25 '%'
 {  123,  16,  18,  20,   1, -17 },  // 0x26 '&'
 {  159,   2,   7,   8,   2, -17 },  // 0x27 '''
 {  161,   5,  21,  10,   2, -17 },  // 0x28 '('
 {  175,   5,  21,  10,   2, -17 },  // 0x29 ')'
 {  189,  11,  10,  13,   0, -17 },  // 0x2a '*'
 {  203,  16,  16,  21,   3, -15 },  // 0x2b '+'
 {  235,   3,   6,   9,   2,  -2 },  // 0x2c ','
 {  238,   6,   2,  10,   1,  -7 },  // 0x2d '-'
 {  240,   2,   3,   9,   3,  -2 },  // 0x2e '.'
 {  241,   8,  20,   9,   0, -17 },  // 0x2f '/'
 {  261,  12,  18,  16,   2, -17 },  // 0x30 '0'
 {  288,  10,  18,  16,   3, -17 },  // 0x31 '1'
 {  311,  12,  18,  16,   2, -17 },  // 0x32 '2'
 {  338,  12,  18,  16,   2, -17 },  // 0x33 '3'
 {  365,  13,  18,  16,   1, -17 },  // 0x34 '4'
 {  395,  11,  18,  16,   2, -17 },  // 0x35 '5'
 {  420,  12,  18,  16,   2, -17 },  // 0x36 '6'
 {  447,  11,  18,  16,   2, -17 },  // 0x37 '7'
 {  472,  12,  18,  16,   2, -17 },  // 0x38 '8'
 {  499,  12,  18,  16,   2, -17 },  // 0x39 '9'
 {  526,   2,  12,   9,   3, -11 },  // 0x3a ':'
 {  529,   3,  15,   9,   2, -11 },  // 0x3b ';'
 {  535,  15,  13,  21,   3, -13 },  // 0x3c '<'
 {  560,  15,   7,  21,   3, -10 },  // 0x3d '='
 {  574,  15,  13,  21,   3, -13 },  // 0x3e '>'
 {  599,   9,  18,  14,   2, -17 },  // 0x3f '?'
 {  620,  21,  21,  25,   2, -16 },  // 0x40 '@'
 {  676,  16,  18,  17,   0, -17 },  // 0x41 'A'
 {  712,  12,  18,  17,   2, -17 },  // 0x42 'B'
 {  739,  14,  18,  18,   1, -17 },  // 0x43 'C'
 {  771,  15,  18,  19,   2, -17 },  // 0x44 'D'
 {  805,  11,  18,  16,   2, -17 },  // 0x45 'E'
 {  830,  10,  18,  15,   2, -17 },  // 0x46 'F'
 {  853,  15,  18,  20,   1, -17 },  // 0x47 'G'
 {  887,  13,  18,  19,   2, -17 },  // 0x48 'H'
 {  917,   2,  18,   8,   2, -17 },  // 0x49 'I'
 {  922,   6,  23,   8,  -2, -17 },  // 0x4a 'J'
 {  940,  15,  18,  17,   2, -17 },  // 0x4b 'K'
 {  974,  11,  18,  14,   2, -17 },  // 0x4c 'L'
 {  999,  16,  18,  22,   2, -17 },  // 0x4d 'M'
 { 1035,  13,  18,  19,   2, -17 },  // 0x4e 'N'
 { 1065,  16,  18,  20,   1, -17 },  // 0x4f 'O'
 { 1101,  11,  18,  15,   2, -17 },  // 0x50 'P'
 { 1126,  16,  21,  20,   1, -17 },  // 0x51 'Q'
 { 1168,  13,  18,  18,   2, -17 },  // 0x52 'R'
 { 1198,  12,  18,  16,   2, -17 },  // 0x53 'S'
 { 1225,  14,  18,  16,   0, -17 },  // 0x54 'T'
 { 1257,  13,  18,  19,   2, -17 },  // 0x55 'U'
 { 1287,  16,  18,  17,   0, -17 },  // 0x56 'V'
 { 1323,  22,  18,  25,   1, -17 },  // 0x57 'W'
 { 1373,  15,  18,  18,   1, -17 },  // 0x58 'X'
 { 1407,  14,  18,  16,   0, -17 },  // 0x59 'Y'
 { 1439,  14,  18,  17,   1, -17 },  // 0x5a 'Z'
 { 1471,   5,  21,  10,   2, -17 },  // 0x5b '['
 { 1485,   8,  20,   9,   0, -17 },  // 0x5c '\'
 { 1505,   5,  21,  10,   2, -17 },  // 0x5d ']'
 { 1519,  15,   7,  21,   3, -17 },  // 0x5e '^'
 { 1533,  12,   2,  13,   0,   5 },  // 0x5f '_'
 { 1536,   6,   4,  13,   2, -18 },  // 0x60 '`'
 { 1539,  11,  13,  15,   1, -12 },  // 0x61 'a'
 { 1557,  12,  18,  16,   2, -17 },  // 0x62 'b'
 { 1584,  10,  13,  14,   1, -12 },  // 0x63 'c'
 { 1601,  12,  18,  16,   1, -17 },  // 0x64 'd'
 { 1628,  12,  13,  15,   1, -12 },  // 0x65 'e'
 { 1648,   8,  18,   9,   1, -17 },  // 0x66 'f'
 { 1666,  12,  18,  16,   1, -12 },  // 0x67 'g'
 { 1693,  11,  18,  16,   2, -17 },  // 0x68 'h'
 { 1718,   2,  18,   8,   2, -17 },  // 0x69 'i'
 { 1723,   5,  23,   8,  -1, -17 },  // 0x6a 'j'
 { 1738,  12,  18,  15,   2, -17 },  // 0x6b 'k'
 { 1765,   2,  18,   7,   2, -17 },  // 0x6c 'l'
 { 1770,  20,  13,  25,   2, -12 },  // 0x6d 'm'
 { 1803,  11,  13,  16,   2, -12 },  // 0x6e 'n'
 { 1821,  12,  13,  15,   1, -12 },  // 0x6f 'o'
 { 1841,  12,  18,  16,   2, -12 },  // 0x70 'p'
 { 1868,  12,  18,  16,   1, -12 },  // 0x71 'q'
 { 1895,   8,  13,  11,   2, -12 },  // 0x72 'r'
 { 1908,  11,  13,  13,   1, -12 },  // 0x73 's'
 { 1926,   8,  17,  10,   0, -16 },  // 0x74 't'
 { 1943,  11,  13,  16,   2, -12 },  // 0x75 'u'
 { 1961,  13,  13,  16,   1, -12 },  // 0x76 'v'
 { 1983,  18,  13,  21,   1, -12 },  // 0x77 'w'
 { 2013,  13,  13,  16,   1, -12 },  // 0x78 'x'
 { 2035,  13,  18,  16,   1, -12 },  // 0x79 'y'
 { 2065,  11,  13,  14,   1, -12 },  // 0x7a 'z'
 { 2083,   9,  22,  16,   3, -17 },  // 0x7b '{'
 { 2108,   2,  24,   9,   3, -17 },  // 0x7c '|'
 { 2114,   9,  22,  16,   3, -17 },  // 0x7d '}'
 { 2139,  15,   5,  21,   3,  -9 },  // 0x7e '~'
};

const GFXfont DejaVu24 PROGMEM = {
  (uint8_t*)DejaVu24Bitmaps,
  (GFXglyph*)DejaVu24Glyphs,
  0x20, 0x7e, 25 };

