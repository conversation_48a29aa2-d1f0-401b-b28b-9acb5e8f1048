/* DejaVu 9
 original ttf url : https://dejavu-fonts.github.io/
 original license : https://dejavu-fonts.github.io/License.html
This data has been converted to AdafruitGFX font format from DejaVuSans.ttf.
*/
const uint8_t DejaVu9Bitmaps[] PROGMEM = {
0xfa, 0xb4, 0x28, 0xaf, 0xca, 0xfd, 0x45, 0x00, 0x21, 0xea, 0x38, 0x38, 0xaf, 0x08, 0x44, 0xa4,
0xa8, 0x5a, 0x15, 0x25, 0x22, 0x31, 0x04, 0x19, 0x9e, 0x66, 0xc0, 0xc0, 0x4a, 0xa1, 0x85, 0x52,
0xab, 0x9d, 0x50, 0x21, 0x3e, 0x42, 0x00, 0xc0, 0xc0, 0x80, 0x25, 0x25, 0x20, 0x69, 0x99, 0x99,
0x60, 0xc9, 0x24, 0xb8, 0x64, 0x84, 0x44, 0x43, 0xc0, 0x69, 0x16, 0x11, 0x60, 0x11, 0x94, 0xa9,
0x7c, 0x40, 0xf8, 0x8e, 0x11, 0xe0, 0x7c, 0x8e, 0x99, 0x60, 0xf1, 0x22, 0x24, 0x40, 0x69, 0x96,
0x99, 0x60, 0x69, 0x97, 0x13, 0xe0, 0x88, 0x8c, 0x04, 0xee, 0x0e, 0x04, 0xfc, 0x0f, 0xc0, 0x81,
0xc1, 0xdc, 0x80, 0xe1, 0x24, 0x40, 0x40, 0x3c, 0x42, 0x9d, 0xa5, 0xa5, 0x9e, 0x40, 0x38, 0x30,
0xc4, 0x92, 0x7a, 0x18, 0x40, 0xf4, 0x63, 0xe8, 0xc7, 0xc0, 0x72, 0x61, 0x08, 0x25, 0xc0, 0xf4,
0xe3, 0x18, 0xcf, 0xc0, 0xf8, 0x8f, 0x88, 0xf0, 0xf8, 0x8f, 0x88, 0x80, 0x76, 0x61, 0x38, 0xe5,
0xc0, 0x8c, 0x63, 0xf8, 0xc6, 0x20, 0xfe, 0x55, 0x55, 0x80, 0x8c, 0xa9, 0x8a, 0x4a, 0x20, 0x88,
0x88, 0x88, 0xf0, 0x87, 0x3c, 0xed, 0xb6, 0x18, 0x40, 0x8e, 0x73, 0x59, 0xce, 0x20, 0x76, 0xe3,
0x18, 0xed, 0xc0, 0xe9, 0x9e, 0x88, 0x80, 0x76, 0xe3, 0x18, 0xe9, 0xc2, 0xe4, 0xa5, 0xca, 0x4a,
0x20, 0x72, 0x28, 0x1c, 0x0a, 0x27, 0x00, 0xf9, 0x08, 0x42, 0x10, 0x80, 0x8c, 0x63, 0x18, 0xc5,
0xc0, 0x86, 0x14, 0x92, 0x48, 0xc3, 0x00, 0x49, 0x24, 0x8a, 0x85, 0x43, 0xe0, 0xa0, 0x50, 0xcd,
0x23, 0x0c, 0x31, 0x28, 0xc0, 0x8a, 0x9c, 0x42, 0x10, 0x80, 0xf8, 0x44, 0x44, 0x43, 0xe0, 0xea,
0xab, 0x91, 0x24, 0x48, 0xd5, 0x57, 0x31, 0x20, 0xf8, 0x90, 0x61, 0x79, 0xf0, 0x88, 0x8e, 0x99,
0x9e, 0x78, 0x88, 0x70, 0x11, 0x17, 0x99, 0x97, 0x69, 0xf8, 0x70, 0x34, 0x4e, 0x44, 0x44, 0x79,
0x99, 0x71, 0x60, 0x88, 0x8e, 0x99, 0x99, 0xbe, 0x45, 0x55, 0x80, 0x88, 0x89, 0xac, 0xa9, 0xff,
0xed, 0x26, 0x4c, 0x99, 0x20, 0xe9, 0x99, 0x90, 0x69, 0x99, 0x60, 0xe9, 0x99, 0xe8, 0x80, 0x79,
0x99, 0x71, 0x10, 0xf2, 0x48, 0x68, 0x62, 0xe0, 0x4f, 0x44, 0x47, 0x99, 0x99, 0x70, 0x44, 0x98,
0xa1, 0xc1, 0x00, 0x93, 0x76, 0xba, 0x24, 0x40, 0x8a, 0x88, 0xa8, 0x80, 0x44, 0x88, 0xa1, 0xc1,
0x02, 0x18, 0x00, 0xf1, 0x24, 0xf0, 0x69, 0x64, 0x93, 0xff, 0x80, 0xc9, 0x34, 0x96, 0x01, 0x91,
0x80, };

const GFXglyph DejaVu9Glyphs[] PROGMEM = {
 {    0,   0,   0,   4,   0,   1 },  // 0x20 ' '
 {    0,   1,   7,   4,   1,  -6 },  // 0x21 '!'
 {    1,   3,   2,   5,   1,  -6 },  // 0x22 '"'
 {    2,   6,   7,   9,   1,  -6 },  // 0x23 '#'
 {    8,   6,   8,   7,   0,  -6 },  // 0x24 '$'
 {   14,   8,   7,  10,   0,  -6 },  // 0x25 '%'
 {   21,   6,   7,   9,   1,  -6 },  // 0x26 '&'
 {   27,   1,   2,   3,   1,  -6 },  // 0x27 '''
 {   28,   2,   8,   5,   1,  -7 },  // 0x28 '('
 {   30,   2,   8,   5,   1,  -7 },  // 0x29 ')'
 {   32,   5,   4,   6,   0,  -6 },  // 0x2a '*'
 {   35,   5,   5,   9,   1,  -4 },  // 0x2b '+'
 {   39,   1,   2,   4,   1,   0 },  // 0x2c ','
 {   40,   2,   1,   4,   1,  -2 },  // 0x2d '-'
 {   41,   1,   1,   4,   1,   0 },  // 0x2e '.'
 {   42,   3,   7,   4,   0,  -6 },  // 0x2f '/'
 {   45,   4,   7,   7,   1,  -6 },  // 0x30 '0'
 {   49,   3,   7,   7,   2,  -6 },  // 0x31 '1'
 {   52,   5,   7,   7,   1,  -6 },  // 0x32 '2'
 {   57,   4,   7,   7,   1,  -6 },  // 0x33 '3'
 {   61,   5,   7,   7,   1,  -6 },  // 0x34 '4'
 {   66,   4,   7,   7,   1,  -6 },  // 0x35 '5'
 {   70,   4,   7,   7,   1,  -6 },  // 0x36 '6'
 {   74,   4,   7,   7,   1,  -6 },  // 0x37 '7'
 {   78,   4,   7,   7,   1,  -6 },  // 0x38 '8'
 {   82,   4,   7,   7,   1,  -6 },  // 0x39 '9'
 {   86,   1,   5,   4,   1,  -4 },  // 0x3a ':'
 {   87,   1,   6,   4,   1,  -4 },  // 0x3b ';'
 {   88,   6,   5,   9,   1,  -4 },  // 0x3c '<'
 {   92,   6,   3,   9,   1,  -3 },  // 0x3d '='
 {   95,   6,   5,   9,   1,  -4 },  // 0x3e '>'
 {   99,   4,   7,   6,   1,  -6 },  // 0x3f '?'
 {  103,   8,   8,  11,   1,  -6 },  // 0x40 '@'
 {  111,   6,   7,   7,   0,  -6 },  // 0x41 'A'
 {  117,   5,   7,   8,   1,  -6 },  // 0x42 'B'
 {  122,   5,   7,   8,   1,  -6 },  // 0x43 'C'
 {  127,   5,   7,   8,   1,  -6 },  // 0x44 'D'
 {  132,   4,   7,   7,   1,  -6 },  // 0x45 'E'
 {  136,   4,   7,   7,   1,  -6 },  // 0x46 'F'
 {  140,   5,   7,   8,   1,  -6 },  // 0x47 'G'
 {  145,   5,   7,   8,   1,  -6 },  // 0x48 'H'
 {  150,   1,   7,   4,   1,  -6 },  // 0x49 'I'
 {  151,   2,   9,   4,   0,  -6 },  // 0x4a 'J'
 {  154,   5,   7,   7,   1,  -6 },  // 0x4b 'K'
 {  159,   4,   7,   6,   1,  -6 },  // 0x4c 'L'
 {  163,   6,   7,   9,   1,  -6 },  // 0x4d 'M'
 {  169,   5,   7,   8,   1,  -6 },  // 0x4e 'N'
 {  174,   5,   7,   8,   1,  -6 },  // 0x4f 'O'
 {  179,   4,   7,   7,   1,  -6 },  // 0x50 'P'
 {  183,   5,   8,   8,   1,  -6 },  // 0x51 'Q'
 {  188,   5,   7,   7,   1,  -6 },  // 0x52 'R'
 {  193,   6,   7,   8,   1,  -6 },  // 0x53 'S'
 {  199,   5,   7,   6,   0,  -6 },  // 0x54 'T'
 {  204,   5,   7,   8,   1,  -6 },  // 0x55 'U'
 {  209,   6,   7,   7,   0,  -6 },  // 0x56 'V'
 {  215,   9,   7,   8,  -1,  -6 },  // 0x57 'W'
 {  223,   6,   7,   7,   0,  -6 },  // 0x58 'X'
 {  229,   5,   7,   6,   0,  -6 },  // 0x59 'Y'
 {  234,   5,   7,   6,   0,  -6 },  // 0x5a 'Z'
 {  239,   2,   8,   5,   1,  -6 },  // 0x5b '['
 {  241,   3,   7,   4,   0,  -6 },  // 0x5c '\'
 {  244,   2,   8,   5,   1,  -6 },  // 0x5d ']'
 {  246,   6,   2,   9,   1,  -6 },  // 0x5e '^'
 {  248,   5,   1,   6,   0,   2 },  // 0x5f '_'
 {  249,   2,   2,   6,   1,  -7 },  // 0x60 '`'
 {  250,   4,   5,   7,   1,  -4 },  // 0x61 'a'
 {  253,   4,   8,   7,   1,  -7 },  // 0x62 'b'
 {  257,   4,   5,   7,   1,  -4 },  // 0x63 'c'
 {  260,   4,   8,   7,   1,  -7 },  // 0x64 'd'
 {  264,   4,   5,   7,   1,  -4 },  // 0x65 'e'
 {  267,   4,   8,   4,   0,  -7 },  // 0x66 'f'
 {  271,   4,   7,   7,   1,  -4 },  // 0x67 'g'
 {  275,   4,   8,   7,   1,  -7 },  // 0x68 'h'
 {  279,   1,   7,   4,   1,  -6 },  // 0x69 'i'
 {  280,   2,   9,   4,   0,  -6 },  // 0x6a 'j'
 {  283,   4,   8,   6,   1,  -7 },  // 0x6b 'k'
 {  287,   1,   8,   4,   1,  -7 },  // 0x6c 'l'
 {  288,   7,   5,  10,   1,  -4 },  // 0x6d 'm'
 {  293,   4,   5,   7,   1,  -4 },  // 0x6e 'n'
 {  296,   4,   5,   7,   1,  -4 },  // 0x6f 'o'
 {  299,   4,   7,   7,   1,  -4 },  // 0x70 'p'
 {  303,   4,   7,   7,   1,  -4 },  // 0x71 'q'
 {  307,   3,   5,   5,   1,  -4 },  // 0x72 'r'
 {  309,   4,   5,   6,   1,  -4 },  // 0x73 's'
 {  312,   4,   6,   5,   0,  -5 },  // 0x74 't'
 {  315,   4,   5,   7,   1,  -4 },  // 0x75 'u'
 {  318,   7,   5,   6,  -1,  -4 },  // 0x76 'v'
 {  323,   7,   5,   8,   0,  -4 },  // 0x77 'w'
 {  328,   5,   5,   6,   0,  -4 },  // 0x78 'x'
 {  332,   7,   7,   6,  -1,  -4 },  // 0x79 'y'
 {  339,   4,   5,   7,   1,  -4 },  // 0x7a 'z'
 {  342,   3,   8,   6,   1,  -6 },  // 0x7b '{'
 {  345,   1,   9,   4,   1,  -6 },  // 0x7c '|'
 {  347,   3,   8,   6,   1,  -6 },  // 0x7d '}'
 {  350,   6,   3,   9,   1,  -4 },  // 0x7e '~'
};

const GFXfont DejaVu9 PROGMEM = {
  (uint8_t*)DejaVu9Bitmaps,
  (GFXglyph*)DejaVu9Glyphs,
  0x20, 0x7e, 10 };

