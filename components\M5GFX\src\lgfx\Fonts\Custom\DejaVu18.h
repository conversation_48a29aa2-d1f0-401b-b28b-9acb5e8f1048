/* DejaVu 18
 original ttf url : https://dejavu-fonts.github.io/
 original license : https://dejavu-fonts.github.io/License.html
This data has been converted to AdafruitGFX font format from DejaVuSans.ttf.
*/
const uint8_t DejaVu18Bitmaps[] PROGMEM = {
0xff, 0xff, 0xc3, 0xc0, 0xcf, 0x3c, 0xf3, 0xcc, 0x04, 0x40, 0x44, 0x0c, 0xc0, 0xc8, 0x7f, 0xf7,
0xff, 0x09, 0x81, 0x90, 0xff, 0xef, 0xfe, 0x13, 0x03, 0x30, 0x32, 0x02, 0x20, 0x08, 0x02, 0x03,
0xe1, 0xfc, 0xe9, 0x32, 0x0f, 0x81, 0xf8, 0x0f, 0x02, 0x60, 0x9a, 0x2e, 0xff, 0x1f, 0x80, 0x80,
0x20, 0x08, 0x00, 0x78, 0x10, 0x90, 0x43, 0x31, 0x86, 0x62, 0x0c, 0xc8, 0x19, 0x10, 0x1e, 0x4f,
0x01, 0x12, 0x02, 0x66, 0x08, 0xcc, 0x31, 0x98, 0x41, 0x21, 0x03, 0xc0, 0x0f, 0x01, 0xf8, 0x30,
0x83, 0x00, 0x38, 0x03, 0xc0, 0x7e, 0x6c, 0x76, 0xc3, 0xcc, 0x18, 0xe1, 0xc7, 0xfe, 0x3e, 0x70,
0xff, 0xc0, 0x32, 0x66, 0x4c, 0xcc, 0xcc, 0xc4, 0x66, 0x23, 0xc4, 0x66, 0x23, 0x33, 0x33, 0x32,
0x66, 0x4c, 0x11, 0x25, 0x51, 0xc3, 0x8a, 0xa4, 0x88, 0x06, 0x00, 0x60, 0x06, 0x00, 0x60, 0x06,
0x0f, 0xff, 0xff, 0xf0, 0x60, 0x06, 0x00, 0x60, 0x06, 0x00, 0x60, 0x6d, 0x40, 0xff, 0xc0, 0xf0,
0x0c, 0x31, 0x86, 0x18, 0xe3, 0x0c, 0x31, 0xc6, 0x18, 0x63, 0x0c, 0x00, 0x3e, 0x3f, 0x98, 0xd8,
0x3c, 0x1e, 0x0f, 0x07, 0x83, 0xc1, 0xe0, 0xd8, 0xcf, 0xe3, 0xe0, 0x38, 0xf8, 0xd8, 0x18, 0x18,
0x18, 0x18, 0x18, 0x18, 0x18, 0x18, 0xff, 0xff, 0x7c, 0x7f, 0x21, 0xc0, 0x60, 0x30, 0x30, 0x18,
0x18, 0x18, 0x18, 0x18, 0x1f, 0xef, 0xf0, 0x7e, 0x7f, 0xa0, 0xe0, 0x30, 0x39, 0xf0, 0xfc, 0x07,
0x01, 0x80, 0xe0, 0xff, 0xe7, 0xe0, 0x07, 0x01, 0xc0, 0xb0, 0x6c, 0x13, 0x08, 0xc6, 0x31, 0x0c,
0xff, 0xff, 0xf0, 0x30, 0x0c, 0x03, 0x00, 0x7e, 0x7e, 0x60, 0x60, 0x7c, 0x7e, 0x47, 0x03, 0x03,
0x03, 0x87, 0xfe, 0x7c, 0x1e, 0x1f, 0x9c, 0x5c, 0x0c, 0x06, 0xf3, 0xfd, 0xc7, 0xc1, 0xe0, 0xd8,
0xef, 0xe1, 0xe0, 0xff, 0xff, 0x06, 0x06, 0x06, 0x0e, 0x0c, 0x0c, 0x1c, 0x18, 0x18, 0x38, 0x30,
0x3e, 0x3f, 0xb8, 0xf8, 0x3e, 0x39, 0xf1, 0xfd, 0xc7, 0xc1, 0xe0, 0xf8, 0xef, 0xe3, 0xe0, 0x3c,
0x3f, 0xb8, 0xd8, 0x3c, 0x1f, 0x1d, 0xfe, 0x7b, 0x01, 0x81, 0xd1, 0xcf, 0xc3, 0xc0, 0xf0, 0x03,
0xc0, 0x6c, 0x00, 0x03, 0x6a, 0x00, 0x00, 0x20, 0x3c, 0x1f, 0x1f, 0x0f, 0x81, 0xf0, 0x0f, 0x80,
0x3e, 0x01, 0xe0, 0x04, 0xff, 0xff, 0xfc, 0x00, 0x00, 0x0f, 0xff, 0xff, 0xc0, 0x80, 0x1e, 0x01,
0xf0, 0x07, 0xc0, 0x3e, 0x07, 0xc3, 0xe3, 0xe0, 0xf0, 0x10, 0x00, 0x79, 0xfa, 0x38, 0x30, 0x61,
0x86, 0x18, 0x30, 0x60, 0x01, 0x83, 0x00, 0x07, 0xe0, 0x1f, 0xf8, 0x3c, 0x1c, 0x70, 0x06, 0x60,
0x07, 0xe3, 0x63, 0xc7, 0xe3, 0xc6, 0x63, 0xc6, 0x66, 0xc7, 0xfc, 0xe3, 0x70, 0x60, 0x00, 0x70,
0x00, 0x3c, 0x30, 0x1f, 0xf0, 0x07, 0xc0, 0x06, 0x00, 0x60, 0x0f, 0x00, 0xf0, 0x19, 0x81, 0x98,
0x19, 0x83, 0x0c, 0x3f, 0xc7, 0xfe, 0x60, 0x66, 0x06, 0xc0, 0x30, 0xfc, 0x7f, 0xb0, 0xd8, 0x6c,
0x37, 0xf3, 0xf9, 0x86, 0xc1, 0xe0, 0xf0, 0xff, 0xef, 0xe0, 0x0f, 0xc7, 0xfd, 0xc0, 0xb0, 0x0c,
0x01, 0x80, 0x30, 0x06, 0x00, 0xc0, 0x0c, 0x01, 0xc0, 0x9f, 0xf0, 0xfc, 0xfe, 0x1f, 0xf3, 0x07,
0x60, 0x6c, 0x07, 0x80, 0xf0, 0x1e, 0x03, 0xc0, 0x78, 0x1b, 0x07, 0x7f, 0xcf, 0xe0, 0xff, 0xff,
0xc0, 0xc0, 0xc0, 0xff, 0xff, 0xc0, 0xc0, 0xc0, 0xc0, 0xff, 0xff, 0xff, 0xff, 0xc0, 0xc0, 0xc0,
0xfe, 0xfe, 0xc0, 0xc0, 0xc0, 0xc0, 0xc0, 0xc0, 0x0f, 0xc7, 0xfd, 0xc0, 0xb0, 0x0c, 0x01, 0x87,
0xf0, 0xfe, 0x03, 0xc0, 0x6c, 0x0d, 0xc1, 0x9f, 0xe0, 0xf8, 0xc0, 0xf0, 0x3c, 0x0f, 0x03, 0xc0,
0xff, 0xff, 0xff, 0x03, 0xc0, 0xf0, 0x3c, 0x0f, 0x03, 0xc0, 0xc0, 0xff, 0xff, 0xff, 0xc0, 0x18,
0xc6, 0x31, 0x8c, 0x63, 0x18, 0xc6, 0x31, 0x8c, 0xfe, 0xe0, 0xc1, 0x98, 0x63, 0x18, 0x66, 0x0d,
0x81, 0xe0, 0x3c, 0x06, 0xc0, 0xcc, 0x18, 0xc3, 0x0c, 0x60, 0xcc, 0x0c, 0xc0, 0xc0, 0xc0, 0xc0,
0xc0, 0xc0, 0xc0, 0xc0, 0xc0, 0xc0, 0xc0, 0xff, 0xff, 0xe0, 0x7f, 0x0f, 0xf0, 0xfd, 0x8b, 0xd9,
0xbd, 0x9b, 0xcf, 0x3c, 0xf3, 0xc6, 0x3c, 0x63, 0xc0, 0x3c, 0x03, 0xc0, 0x30, 0xe0, 0xf8, 0x3f,
0x0f, 0xc3, 0xd8, 0xf6, 0x3c, 0xcf, 0x1b, 0xc6, 0xf0, 0xfc, 0x3f, 0x07, 0xc1, 0xc0, 0x1f, 0x83,
0xfc, 0x70, 0xe6, 0x06, 0xc0, 0x3c, 0x03, 0xc0, 0x3c, 0x03, 0xc0, 0x36, 0x06, 0x70, 0xe3, 0xfc,
0x1f, 0x80, 0xfc, 0xfe, 0xc7, 0xc3, 0xc3, 0xc7, 0xfe, 0xfc, 0xc0, 0xc0, 0xc0, 0xc0, 0xc0, 0x1f,
0x83, 0xfc, 0x70, 0xe6, 0x06, 0xc0, 0x3c, 0x03, 0xc0, 0x3c, 0x03, 0xc0, 0x36, 0x06, 0x70, 0xe3,
0xfc, 0x1f, 0x80, 0x18, 0x00, 0xc0, 0xfc, 0x3f, 0x8c, 0x73, 0x0c, 0xc3, 0x31, 0xcf, 0xe3, 0xf0,
0xc6, 0x30, 0xcc, 0x33, 0x06, 0xc1, 0xc0, 0x3e, 0x1f, 0xce, 0x13, 0x00, 0xc0, 0x1f, 0x03, 0xf0,
0x0e, 0x01, 0x80, 0x68, 0x3b, 0xfc, 0x7e, 0x00, 0xff, 0xff, 0xff, 0x06, 0x00, 0x60, 0x06, 0x00,
0x60, 0x06, 0x00, 0x60, 0x06, 0x00, 0x60, 0x06, 0x00, 0x60, 0x06, 0x00, 0xc0, 0xf0, 0x3c, 0x0f,
0x03, 0xc0, 0xf0, 0x3c, 0x0f, 0x03, 0xc0, 0xf0, 0x36, 0x19, 0xfe, 0x1e, 0x00, 0xc0, 0x36, 0x06,
0x60, 0x66, 0x06, 0x30, 0xc3, 0x0c, 0x19, 0x81, 0x98, 0x19, 0x80, 0xf0, 0x0f, 0x00, 0x60, 0x06,
0x00, 0xc1, 0xc1, 0xe0, 0xe0, 0xd8, 0xf8, 0xcc, 0x6c, 0x66, 0x36, 0x33, 0x1b, 0x18, 0xd8, 0xd8,
0x6c, 0x6c, 0x36, 0x36, 0x1f, 0x1f, 0x07, 0x07, 0x03, 0x83, 0x81, 0xc1, 0xc0, 0x70, 0xe6, 0x18,
0xe6, 0x0d, 0xc0, 0xf0, 0x1c, 0x03, 0x80, 0x78, 0x1b, 0x07, 0x30, 0xc7, 0x30, 0x6e, 0x0e, 0xe0,
0x76, 0x06, 0x30, 0xc1, 0x98, 0x19, 0x80, 0xf0, 0x06, 0x00, 0x60, 0x06, 0x00, 0x60, 0x06, 0x00,
0x60, 0x06, 0x00, 0xff, 0xff, 0xfc, 0x07, 0x01, 0xc0, 0x30, 0x0e, 0x03, 0x80, 0xe0, 0x18, 0x06,
0x01, 0xc0, 0x7f, 0xff, 0xfe, 0xff, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xff, 0xc3, 0x06, 0x18,
0x61, 0xc3, 0x0c, 0x30, 0xe1, 0x86, 0x18, 0x30, 0xc0, 0xff, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33,
0xff, 0x0e, 0x03, 0xe0, 0xc6, 0x30, 0x6c, 0x06, 0xff, 0xff, 0xc0, 0xc6, 0x30, 0x3c, 0x7e, 0x47,
0x03, 0x3f, 0xff, 0xc3, 0xc7, 0xff, 0x7b, 0xc0, 0x60, 0x30, 0x18, 0x0d, 0xe7, 0xfb, 0x8f, 0x83,
0xc1, 0xe0, 0xf0, 0x7c, 0x7f, 0xf6, 0xf0, 0x1e, 0x7f, 0x61, 0xc0, 0xc0, 0xc0, 0xc0, 0x61, 0x7f,
0x1e, 0x01, 0x80, 0xc0, 0x60, 0x33, 0xdb, 0xff, 0x8f, 0x83, 0xc1, 0xe0, 0xf0, 0x7c, 0x77, 0xf9,
0xec, 0x1f, 0x1f, 0xe6, 0x1f, 0x03, 0xff, 0xff, 0xfc, 0x01, 0x81, 0x7f, 0xc7, 0xe0, 0x1e, 0x7c,
0xc1, 0x8f, 0xff, 0xcc, 0x18, 0x30, 0x60, 0xc1, 0x83, 0x06, 0x00, 0x3d, 0xbf, 0xf8, 0xf8, 0x3c,
0x1e, 0x0f, 0x07, 0xc7, 0x7f, 0x9e, 0xc0, 0x68, 0x67, 0xf1, 0xf0, 0xc0, 0xc0, 0xc0, 0xc0, 0xde,
0xfe, 0xe7, 0xc3, 0xc3, 0xc3, 0xc3, 0xc3, 0xc3, 0xc3, 0xf0, 0xff, 0xff, 0xf0, 0x33, 0x00, 0x33,
0x33, 0x33, 0x33, 0x33, 0x33, 0xec, 0xc0, 0x60, 0x30, 0x18, 0x0c, 0x36, 0x33, 0x31, 0xb0, 0xf0,
0x78, 0x36, 0x19, 0x8c, 0x66, 0x18, 0xff, 0xff, 0xff, 0xf0, 0xdc, 0x7b, 0xfb, 0xee, 0x79, 0xf0,
0xc3, 0xc3, 0x0f, 0x0c, 0x3c, 0x30, 0xf0, 0xc3, 0xc3, 0x0f, 0x0c, 0x30, 0xde, 0xfe, 0xe7, 0xc3,
0xc3, 0xc3, 0xc3, 0xc3, 0xc3, 0xc3, 0x1e, 0x1f, 0xe6, 0x1b, 0x03, 0xc0, 0xf0, 0x3c, 0x0d, 0x86,
0x7f, 0x87, 0x80, 0xde, 0x7f, 0xb8, 0xf8, 0x3c, 0x1e, 0x0f, 0x07, 0xc7, 0xff, 0x6f, 0x30, 0x18,
0x0c, 0x06, 0x00, 0x3d, 0xbf, 0xf8, 0xf8, 0x3c, 0x1e, 0x0f, 0x07, 0xc7, 0x7f, 0x9e, 0xc0, 0x60,
0x30, 0x18, 0x0c, 0xdf, 0xfe, 0x30, 0xc3, 0x0c, 0x30, 0xc3, 0x00, 0x7c, 0xfe, 0xc2, 0xe0, 0x7c,
0x1e, 0x06, 0x86, 0xfe, 0x78, 0x61, 0x86, 0x3f, 0xfd, 0x86, 0x18, 0x61, 0x86, 0x1f, 0x3c, 0xc3,
0xc3, 0xc3, 0xc3, 0xc3, 0xc3, 0xc3, 0xe7, 0x7f, 0x7b, 0x60, 0x66, 0x06, 0x30, 0xc3, 0x0c, 0x19,
0x81, 0x98, 0x19, 0x80, 0xf0, 0x0f, 0x00, 0x60, 0x63, 0x8c, 0xc7, 0x19, 0x8e, 0x31, 0xb6, 0xc3,
0x6d, 0x86, 0xdb, 0x0f, 0x1e, 0x0e, 0x38, 0x1c, 0x70, 0x38, 0xe0, 0xe1, 0xd8, 0x63, 0x30, 0xcc,
0x1e, 0x07, 0x83, 0x30, 0xcc, 0x61, 0xb8, 0x70, 0x60, 0x66, 0x06, 0x30, 0xc3, 0x0c, 0x19, 0x81,
0x98, 0x0f, 0x00, 0xf0, 0x06, 0x00, 0x60, 0x06, 0x00, 0xc0, 0x3c, 0x03, 0x80, 0xff, 0xff, 0x06,
0x0c, 0x1c, 0x38, 0x30, 0x70, 0xff, 0xff, 0x0f, 0x1f, 0x18, 0x18, 0x18, 0x18, 0x38, 0xf0, 0xf0,
0x38, 0x18, 0x18, 0x18, 0x18, 0x18, 0x1f, 0x0f, 0xff, 0xff, 0xff, 0xff, 0xf0, 0xf0, 0xf8, 0x18,
0x18, 0x18, 0x18, 0x1c, 0x0f, 0x0f, 0x1c, 0x18, 0x18, 0x18, 0x18, 0x18, 0xf8, 0xf0, 0x00, 0x0f,
0x87, 0xff, 0xc3, 0xe0, 0x00, };

const GFXglyph DejaVu18Glyphs[] PROGMEM = {
 {    0,   0,   0,   7,   0,   1 },  // 0x20 ' '
 {    0,   2,  13,   8,   3, -12 },  // 0x21 '!'
 {    4,   6,   5,   9,   1, -12 },  // 0x22 '"'
 {    8,  12,  14,  16,   1, -13 },  // 0x23 '#'
 {   29,  10,  17,  12,   1, -13 },  // 0x24 '$'
 {   51,  15,  13,  18,   1, -12 },  // 0x25 '%'
 {   76,  12,  13,  14,   1, -12 },  // 0x26 '&'
 {   96,   2,   5,   5,   1, -12 },  // 0x27 '''
 {   98,   4,  16,   8,   2, -13 },  // 0x28 '('
 {  106,   4,  16,   8,   1, -13 },  // 0x29 ')'
 {  114,   7,   8,  10,   1, -12 },  // 0x2a '*'
 {  121,  12,  12,  16,   2, -11 },  // 0x2b '+'
 {  139,   3,   4,   7,   1,  -1 },  // 0x2c ','
 {  141,   5,   2,   8,   1,  -5 },  // 0x2d '-'
 {  143,   2,   2,   7,   2,  -1 },  // 0x2e '.'
 {  144,   6,  15,   7,   0, -12 },  // 0x2f '/'
 {  156,   9,  13,  12,   1, -12 },  // 0x30 '0'
 {  171,   8,  13,  12,   2, -12 },  // 0x31 '1'
 {  184,   9,  13,  12,   1, -12 },  // 0x32 '2'
 {  199,   9,  13,  12,   1, -12 },  // 0x33 '3'
 {  214,  10,  13,  12,   1, -12 },  // 0x34 '4'
 {  231,   8,  13,  12,   1, -12 },  // 0x35 '5'
 {  244,   9,  13,  12,   1, -12 },  // 0x36 '6'
 {  259,   8,  13,  12,   1, -12 },  // 0x37 '7'
 {  272,   9,  13,  12,   1, -12 },  // 0x38 '8'
 {  287,   9,  13,  12,   1, -12 },  // 0x39 '9'
 {  302,   2,   9,   7,   2,  -8 },  // 0x3a ':'
 {  305,   3,  11,   7,   1,  -8 },  // 0x3b ';'
 {  310,  11,  10,  16,   2,  -9 },  // 0x3c '<'
 {  324,  11,   6,  16,   2,  -8 },  // 0x3d '='
 {  333,  11,  10,  16,   2,  -9 },  // 0x3e '>'
 {  347,   7,  13,  11,   1, -12 },  // 0x3f '?'
 {  359,  16,  16,  19,   1, -12 },  // 0x40 '@'
 {  391,  12,  13,  13,   0, -12 },  // 0x41 'A'
 {  411,   9,  13,  13,   2, -12 },  // 0x42 'B'
 {  426,  11,  13,  14,   1, -12 },  // 0x43 'C'
 {  444,  11,  13,  15,   2, -12 },  // 0x44 'D'
 {  462,   8,  13,  12,   2, -12 },  // 0x45 'E'
 {  475,   8,  13,  11,   2, -12 },  // 0x46 'F'
 {  488,  11,  13,  15,   1, -12 },  // 0x47 'G'
 {  506,  10,  13,  15,   2, -12 },  // 0x48 'H'
 {  523,   2,  13,   7,   2, -12 },  // 0x49 'I'
 {  527,   5,  17,   7,  -1, -12 },  // 0x4a 'J'
 {  538,  11,  13,  13,   2, -12 },  // 0x4b 'K'
 {  556,   8,  13,  11,   2, -12 },  // 0x4c 'L'
 {  569,  12,  13,  17,   2, -12 },  // 0x4d 'M'
 {  589,  10,  13,  15,   2, -12 },  // 0x4e 'N'
 {  606,  12,  13,  15,   1, -12 },  // 0x4f 'O'
 {  626,   8,  13,  12,   2, -12 },  // 0x50 'P'
 {  639,  12,  15,  15,   1, -12 },  // 0x51 'Q'
 {  662,  10,  13,  14,   2, -12 },  // 0x52 'R'
 {  679,  10,  13,  12,   1, -12 },  // 0x53 'S'
 {  696,  12,  13,  13,   0, -12 },  // 0x54 'T'
 {  716,  10,  13,  15,   2, -12 },  // 0x55 'U'
 {  733,  12,  13,  13,   0, -12 },  // 0x56 'V'
 {  753,  17,  13,  20,   1, -12 },  // 0x57 'W'
 {  781,  11,  13,  14,   1, -12 },  // 0x58 'X'
 {  799,  12,  13,  13,   0, -12 },  // 0x59 'Y'
 {  819,  11,  13,  14,   1, -12 },  // 0x5a 'Z'
 {  837,   4,  16,   8,   1, -13 },  // 0x5b '['
 {  845,   6,  15,   7,   0, -12 },  // 0x5c '\'
 {  857,   4,  16,   8,   2, -13 },  // 0x5d ']'
 {  865,  11,   5,  16,   2, -12 },  // 0x5e '^'
 {  872,   9,   2,  10,   0,   3 },  // 0x5f '_'
 {  875,   4,   3,  10,   2, -13 },  // 0x60 '`'
 {  877,   8,  10,  11,   1,  -9 },  // 0x61 'a'
 {  887,   9,  14,  12,   2, -13 },  // 0x62 'b'
 {  903,   8,  10,  10,   1,  -9 },  // 0x63 'c'
 {  913,   9,  14,  12,   1, -13 },  // 0x64 'd'
 {  929,  10,  10,  12,   1,  -9 },  // 0x65 'e'
 {  942,   7,  14,   7,   0, -13 },  // 0x66 'f'
 {  955,   9,  14,  12,   1,  -9 },  // 0x67 'g'
 {  971,   8,  14,  12,   2, -13 },  // 0x68 'h'
 {  985,   2,  14,   6,   2, -13 },  // 0x69 'i'
 {  989,   4,  18,   6,   0, -13 },  // 0x6a 'j'
 {  998,   9,  14,  11,   2, -13 },  // 0x6b 'k'
 { 1014,   2,  14,   6,   2, -13 },  // 0x6c 'l'
 { 1018,  14,  10,  18,   2,  -9 },  // 0x6d 'm'
 { 1036,   8,  10,  12,   2,  -9 },  // 0x6e 'n'
 { 1046,  10,  10,  12,   1,  -9 },  // 0x6f 'o'
 { 1059,   9,  14,  12,   2,  -9 },  // 0x70 'p'
 { 1075,   9,  14,  12,   1,  -9 },  // 0x71 'q'
 { 1091,   6,  10,   9,   2,  -9 },  // 0x72 'r'
 { 1099,   8,  10,   9,   1,  -9 },  // 0x73 's'
 { 1109,   6,  13,   8,   1, -12 },  // 0x74 't'
 { 1119,   8,  10,  12,   2,  -9 },  // 0x75 'u'
 { 1129,  12,  10,  12,   0,  -9 },  // 0x76 'v'
 { 1144,  15,  10,  17,   1,  -9 },  // 0x77 'w'
 { 1163,  10,  10,  12,   1,  -9 },  // 0x78 'x'
 { 1176,  12,  14,  12,   0,  -9 },  // 0x79 'y'
 { 1197,   8,  10,  10,   1,  -9 },  // 0x7a 'z'
 { 1207,   8,  17,  12,   2, -13 },  // 0x7b '{'
 { 1224,   2,  18,   7,   2, -13 },  // 0x7c '|'
 { 1229,   8,  17,  12,   2, -13 },  // 0x7d '}'
 { 1246,  11,   5,  16,   2,  -8 },  // 0x7e '~'
};

const GFXfont DejaVu18 PROGMEM = {
  (uint8_t*)DejaVu18Bitmaps,
  (GFXglyph*)DejaVu18Glyphs,
  0x20, 0x7e, 18 };

