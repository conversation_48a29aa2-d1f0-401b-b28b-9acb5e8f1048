#include <stdint.h>
#define U8G2_USE_LARGE_FONTS

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

extern const uint8_t lgfx_efont_tw_10[];
extern const uint8_t lgfx_efont_tw_10_b[];
extern const uint8_t lgfx_efont_tw_10_bi[];
extern const uint8_t lgfx_efont_tw_10_i[];
extern const uint8_t lgfx_efont_tw_12[];
extern const uint8_t lgfx_efont_tw_12_b[];
extern const uint8_t lgfx_efont_tw_12_bi[];
extern const uint8_t lgfx_efont_tw_12_i[];
extern const uint8_t lgfx_efont_tw_14[];
extern const uint8_t lgfx_efont_tw_14_b[];
extern const uint8_t lgfx_efont_tw_14_bi[];
extern const uint8_t lgfx_efont_tw_14_i[];
extern const uint8_t lgfx_efont_tw_16[];
extern const uint8_t lgfx_efont_tw_16_b[];
extern const uint8_t lgfx_efont_tw_16_bi[];
extern const uint8_t lgfx_efont_tw_16_i[];
extern const uint8_t lgfx_efont_tw_24[];
extern const uint8_t lgfx_efont_tw_24_b[];
extern const uint8_t lgfx_efont_tw_24_bi[];
extern const uint8_t lgfx_efont_tw_24_i[];

#ifdef __cplusplus
}
#endif /* __cplusplus */
