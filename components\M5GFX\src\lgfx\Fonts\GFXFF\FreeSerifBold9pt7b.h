const uint8_t FreeSerifBold9pt7bBitmaps[] PROGMEM = {
  0xFF, 0xF4, 0x92, 0x1F, 0xF0, 0xCF, 0x3C, 0xE3, 0x88, 0x13, 0x09, 0x84,
  0xC2, 0x47, 0xF9, 0x90, 0xC8, 0x4C, 0xFF, 0x13, 0x09, 0x0C, 0x86, 0x40,
  0x10, 0x38, 0xD6, 0x92, 0xD2, 0xF0, 0x7C, 0x3E, 0x17, 0x93, 0x93, 0xD6,
  0x7C, 0x10, 0x3C, 0x21, 0xCF, 0x0E, 0x24, 0x30, 0xA0, 0xC5, 0x03, 0x34,
  0xE7, 0x26, 0x40, 0xB9, 0x04, 0xC4, 0x23, 0x30, 0x8C, 0x84, 0x1C, 0x0F,
  0x00, 0xCC, 0x06, 0x60, 0x3E, 0x00, 0xE7, 0x8F, 0x18, 0x9C, 0x8C, 0xE4,
  0xE3, 0xC7, 0x9E, 0x3C, 0x72, 0xFD, 0xE0, 0xFF, 0x80, 0x32, 0x44, 0xCC,
  0xCC, 0xCC, 0xC4, 0x62, 0x10, 0x84, 0x22, 0x33, 0x33, 0x33, 0x32, 0x64,
  0x80, 0x31, 0x6B, 0xB1, 0x8E, 0xD6, 0x8C, 0x00, 0x08, 0x04, 0x02, 0x01,
  0x0F, 0xF8, 0x40, 0x20, 0x10, 0x08, 0x00, 0xDF, 0x95, 0x00, 0xFF, 0xFF,
  0x80, 0x0C, 0x21, 0x86, 0x10, 0xC3, 0x08, 0x61, 0x84, 0x30, 0xC0, 0x1C,
  0x33, 0x98, 0xDC, 0x7E, 0x3F, 0x1F, 0x8F, 0xC7, 0xE3, 0xB1, 0x98, 0xC3,
  0x80, 0x08, 0xE3, 0x8E, 0x38, 0xE3, 0x8E, 0x38, 0xE3, 0xBF, 0x3C, 0x3F,
  0x23, 0xC0, 0xE0, 0x70, 0x30, 0x38, 0x18, 0x18, 0x18, 0x5F, 0xDF, 0xE0,
  0x7C, 0x8E, 0x0E, 0x0E, 0x0C, 0x1E, 0x07, 0x03, 0x03, 0x02, 0xE6, 0xF8,
  0x06, 0x0E, 0x0E, 0x3E, 0x2E, 0x4E, 0x8E, 0x8E, 0xFF, 0xFF, 0x0E, 0x0E,
  0x3F, 0x7E, 0x40, 0x40, 0xF8, 0xFC, 0x1E, 0x06, 0x02, 0x02, 0xE4, 0xF8,
  0x07, 0x1C, 0x30, 0x70, 0xFC, 0xE6, 0xE7, 0xE7, 0xE7, 0x67, 0x66, 0x3C,
  0x7F, 0x3F, 0xA0, 0xD0, 0x40, 0x60, 0x30, 0x10, 0x18, 0x0C, 0x04, 0x06,
  0x03, 0x00, 0x3C, 0xC6, 0xC6, 0xC6, 0xFC, 0x7C, 0x3E, 0xCF, 0xC7, 0xC7,
  0xC6, 0x7C, 0x3E, 0x33, 0xB8, 0xDC, 0x7E, 0x3F, 0x1D, 0xCE, 0x7F, 0x07,
  0x07, 0x0F, 0x1C, 0x00, 0xFF, 0x80, 0x3F, 0xE0, 0xFF, 0x80, 0x37, 0xE5,
  0x40, 0x00, 0x00, 0x70, 0x78, 0x78, 0x78, 0x38, 0x03, 0x80, 0x3C, 0x03,
  0xC0, 0x30, 0xFF, 0xC0, 0x00, 0x00, 0x00, 0xFF, 0xC0, 0xC0, 0x3C, 0x03,
  0xC0, 0x1C, 0x01, 0xC1, 0xE1, 0xE1, 0xE0, 0xE0, 0x00, 0x00, 0x3D, 0x9F,
  0x3E, 0x70, 0xE1, 0x04, 0x08, 0x00, 0x70, 0xE1, 0xC0, 0x0F, 0x81, 0x83,
  0x18, 0xC4, 0x89, 0x9C, 0x4C, 0xE4, 0x67, 0x22, 0x39, 0x22, 0x4F, 0xE3,
  0x00, 0x0C, 0x10, 0x1F, 0x00, 0x02, 0x00, 0x30, 0x01, 0xC0, 0x0E, 0x00,
  0xB8, 0x05, 0xC0, 0x4F, 0x02, 0x38, 0x3F, 0xE1, 0x07, 0x18, 0x3D, 0xE3,
  0xF0, 0xFF, 0x87, 0x1C, 0xE3, 0x9C, 0x73, 0x9C, 0x7F, 0x0E, 0x71, 0xC7,
  0x38, 0xE7, 0x1C, 0xE7, 0x7F, 0xC0, 0x1F, 0x26, 0x1D, 0xC1, 0xB0, 0x1E,
  0x01, 0xC0, 0x38, 0x07, 0x00, 0xE0, 0x0E, 0x04, 0xE1, 0x0F, 0xC0, 0xFF,
  0x0E, 0x71, 0xC7, 0x38, 0x77, 0x0E, 0xE1, 0xDC, 0x3B, 0x87, 0x70, 0xCE,
  0x39, 0xC6, 0x7F, 0x80, 0xFF, 0xCE, 0x19, 0xC1, 0x38, 0x87, 0x30, 0xFE,
  0x1C, 0xC3, 0x88, 0x70, 0x2E, 0x0D, 0xC3, 0x7F, 0xE0, 0xFF, 0xDC, 0x37,
  0x05, 0xC4, 0x73, 0x1F, 0xC7, 0x31, 0xC4, 0x70, 0x1C, 0x07, 0x03, 0xE0,
  0x1F, 0x23, 0x0E, 0x70, 0x6E, 0x02, 0xE0, 0x0E, 0x00, 0xE1, 0xFE, 0x0E,
  0x60, 0xE7, 0x0E, 0x38, 0xE0, 0xF8, 0xF9, 0xF7, 0x0E, 0x70, 0xE7, 0x0E,
  0x70, 0xE7, 0xFE, 0x70, 0xE7, 0x0E, 0x70, 0xE7, 0x0E, 0x70, 0xEF, 0x9F,
  0xFB, 0x9C, 0xE7, 0x39, 0xCE, 0x73, 0x9D, 0xF0, 0x1F, 0x0E, 0x0E, 0x0E,
  0x0E, 0x0E, 0x0E, 0x0E, 0x0E, 0x0E, 0x0E, 0xCE, 0xCC, 0x78, 0xF9, 0xF3,
  0x82, 0x1C, 0x20, 0xE2, 0x07, 0x20, 0x3F, 0x01, 0xDC, 0x0E, 0x70, 0x73,
  0xC3, 0x8F, 0x1C, 0x3D, 0xF3, 0xF0, 0xF8, 0x0E, 0x01, 0xC0, 0x38, 0x07,
  0x00, 0xE0, 0x1C, 0x03, 0x80, 0x70, 0x2E, 0x09, 0xC3, 0x7F, 0xE0, 0xF8,
  0x0F, 0x3C, 0x1E, 0x3C, 0x1E, 0x2E, 0x2E, 0x2E, 0x2E, 0x26, 0x4E, 0x27,
  0x4E, 0x27, 0x4E, 0x23, 0x8E, 0x23, 0x8E, 0x21, 0x0E, 0x71, 0x1F, 0xF0,
  0xEE, 0x09, 0xE1, 0x3E, 0x25, 0xE4, 0x9E, 0x91, 0xD2, 0x1E, 0x43, 0xC8,
  0x39, 0x03, 0x70, 0x20, 0x1F, 0x83, 0x0C, 0x70, 0xEE, 0x07, 0xE0, 0x7E,
  0x07, 0xE0, 0x7E, 0x07, 0xE0, 0x77, 0x0E, 0x30, 0xC1, 0xF8, 0xFF, 0x1C,
  0xE7, 0x1D, 0xC7, 0x71, 0xDC, 0xE7, 0xF1, 0xC0, 0x70, 0x1C, 0x07, 0x03,
  0xE0, 0x0F, 0x83, 0x9C, 0x70, 0xE6, 0x06, 0xE0, 0x7E, 0x07, 0xE0, 0x7E,
  0x07, 0xE0, 0x76, 0x06, 0x30, 0xC1, 0x98, 0x0F, 0x00, 0x78, 0x03, 0xE0,
  0xFF, 0x07, 0x38, 0x71, 0xC7, 0x1C, 0x71, 0xC7, 0x38, 0x7E, 0x07, 0x70,
  0x77, 0x87, 0x3C, 0x71, 0xEF, 0x8F, 0x39, 0x47, 0xC1, 0xC0, 0xF0, 0x7C,
  0x3E, 0x0F, 0x83, 0xC3, 0xC6, 0xBC, 0xFF, 0xFC, 0xE3, 0x8E, 0x10, 0xE0,
  0x0E, 0x00, 0xE0, 0x0E, 0x00, 0xE0, 0x0E, 0x00, 0xE0, 0x0E, 0x01, 0xF0,
  0xF8, 0xEE, 0x09, 0xC1, 0x38, 0x27, 0x04, 0xE0, 0x9C, 0x13, 0x82, 0x70,
  0x4E, 0x08, 0xE2, 0x0F, 0x80, 0xFC, 0x7B, 0xC1, 0x0E, 0x08, 0x70, 0x81,
  0xC4, 0x0E, 0x20, 0x7A, 0x01, 0xD0, 0x0E, 0x80, 0x38, 0x01, 0xC0, 0x04,
  0x00, 0x20, 0x00, 0xFD, 0xFB, 0xDC, 0x38, 0x43, 0x87, 0x10, 0xE1, 0xC4,
  0x38, 0xF2, 0x07, 0x2E, 0x81, 0xD3, 0xA0, 0x34, 0x70, 0x0E, 0x1C, 0x03,
  0x87, 0x00, 0x60, 0x80, 0x10, 0x20, 0xFE, 0xF3, 0xC3, 0x0F, 0x10, 0x39,
  0x00, 0xF0, 0x03, 0x80, 0x1E, 0x01, 0x70, 0x09, 0xC0, 0x8F, 0x08, 0x3D,
  0xF3, 0xF0, 0xFC, 0x7B, 0xC1, 0x8E, 0x08, 0x38, 0x81, 0xE8, 0x07, 0x40,
  0x1C, 0x00, 0xE0, 0x07, 0x00, 0x38, 0x01, 0xC0, 0x1F, 0x00, 0xFF, 0xD8,
  0x72, 0x1E, 0x43, 0x80, 0xE0, 0x1C, 0x07, 0x01, 0xC0, 0x38, 0x2E, 0x0F,
  0x83, 0x7F, 0xE0, 0xFC, 0xCC, 0xCC, 0xCC, 0xCC, 0xCC, 0xCC, 0xF0, 0xC1,
  0x06, 0x18, 0x20, 0xC3, 0x04, 0x18, 0x60, 0x83, 0x0C, 0xF3, 0x33, 0x33,
  0x33, 0x33, 0x33, 0x33, 0xF0, 0x18, 0x1C, 0x34, 0x26, 0x62, 0x43, 0xC1,
  0xFF, 0x80, 0xC6, 0x30, 0x7C, 0x63, 0xB1, 0xC0, 0xE1, 0xF3, 0x3B, 0x9D,
  0xCE, 0xFF, 0x80, 0xF0, 0x1C, 0x07, 0x01, 0xDC, 0x7B, 0x9C, 0x77, 0x1D,
  0xC7, 0x71, 0xDC, 0x77, 0x39, 0x3C, 0x3C, 0xED, 0x9F, 0x0E, 0x1C, 0x38,
  0x39, 0x3C, 0x07, 0x80, 0xE0, 0x38, 0xEE, 0x77, 0xB8, 0xEE, 0x3B, 0x8E,
  0xE3, 0xB8, 0xE7, 0x78, 0xEF, 0x3C, 0x66, 0xE6, 0xFE, 0xE0, 0xE0, 0xE0,
  0x72, 0x3C, 0x3E, 0xED, 0xC7, 0xC7, 0x0E, 0x1C, 0x38, 0x70, 0xE1, 0xC7,
  0xC0, 0x31, 0xDF, 0xBF, 0x7E, 0xE7, 0x90, 0x60, 0xFC, 0xFE, 0x0C, 0x17,
  0xC0, 0xF0, 0x1C, 0x07, 0x01, 0xDC, 0x7B, 0x9C, 0xE7, 0x39, 0xCE, 0x73,
  0x9C, 0xE7, 0x3B, 0xFF, 0x73, 0x9D, 0xE7, 0x39, 0xCE, 0x73, 0x9D, 0xF0,
  0x1C, 0x71, 0xCF, 0x1C, 0x71, 0xC7, 0x1C, 0x71, 0xC7, 0x1C, 0x7D, 0xBE,
  0xF0, 0x1C, 0x07, 0x01, 0xCE, 0x71, 0x1C, 0x87, 0x41, 0xF8, 0x77, 0x1C,
  0xE7, 0x1B, 0xEF, 0xF3, 0x9C, 0xE7, 0x39, 0xCE, 0x73, 0x9D, 0xF0, 0xF7,
  0x38, 0xF7, 0xB9, 0xCE, 0x73, 0x9C, 0xE7, 0x39, 0xCE, 0x73, 0x9C, 0xE7,
  0x39, 0xCE, 0xFF, 0xFE, 0xF7, 0x1E, 0xE7, 0x39, 0xCE, 0x73, 0x9C, 0xE7,
  0x39, 0xCE, 0xFF, 0xC0, 0x3E, 0x31, 0xB8, 0xFC, 0x7E, 0x3F, 0x1F, 0x8E,
  0xC6, 0x3E, 0x00, 0xF7, 0x1E, 0xE7, 0x1D, 0xC7, 0x71, 0xDC, 0x77, 0x1D,
  0xCE, 0x7F, 0x1C, 0x07, 0x01, 0xC0, 0xF8, 0x00, 0x3C, 0x9C, 0xEE, 0x3B,
  0x8E, 0xE3, 0xB8, 0xEE, 0x39, 0xCE, 0x3F, 0x80, 0xE0, 0x38, 0x0E, 0x07,
  0xC0, 0xF7, 0x7B, 0x70, 0x70, 0x70, 0x70, 0x70, 0x70, 0xF8, 0x7E, 0x73,
  0xC7, 0x8E, 0x39, 0xB0, 0x10, 0xCF, 0x9C, 0x71, 0xC7, 0x1C, 0x71, 0xD3,
  0x80, 0xF7, 0x9C, 0xE7, 0x39, 0xCE, 0x73, 0x9C, 0xE7, 0x39, 0xCE, 0x3F,
  0xC0, 0xFB, 0xB8, 0x8C, 0x87, 0x43, 0xC0, 0xE0, 0x70, 0x10, 0x08, 0x00,
  0xF7, 0xB6, 0x31, 0x73, 0xA3, 0x3A, 0x3D, 0xA3, 0xDC, 0x18, 0xC1, 0x88,
  0x10, 0x80, 0xFB, 0xB8, 0x8E, 0x83, 0x81, 0xC0, 0xF0, 0x98, 0xCE, 0xEF,
  0x80, 0xF7, 0x62, 0x72, 0x34, 0x34, 0x3C, 0x18, 0x18, 0x10, 0x10, 0x10,
  0xE0, 0xE0, 0xFF, 0x1C, 0x70, 0xE3, 0x87, 0x1C, 0x71, 0xFE, 0x19, 0x8C,
  0x63, 0x18, 0xCC, 0x61, 0x8C, 0x63, 0x18, 0xC3, 0xFF, 0xF8, 0xC3, 0x18,
  0xC6, 0x31, 0x86, 0x33, 0x18, 0xC6, 0x31, 0x98, 0xF0, 0x8E };

const GFXglyph FreeSerifBold9pt7bGlyphs[] PROGMEM = {
  {     0,   0,   0,   5,    0,    1 },   // 0x20 ' '
  {     0,   3,  12,   6,    1,  -11 },   // 0x21 '!'
  {     5,   6,   5,  10,    2,  -11 },   // 0x22 '"'
  {     9,   9,  13,   9,    0,  -12 },   // 0x23 '#'
  {    24,   8,  14,   9,    1,  -12 },   // 0x24 '$'
  {    38,  14,  12,  18,    2,  -11 },   // 0x25 '%'
  {    59,  13,  12,  15,    1,  -11 },   // 0x26 '&'
  {    79,   2,   5,   5,    1,  -11 },   // 0x27 '''
  {    81,   4,  15,   6,    1,  -11 },   // 0x28 '('
  {    89,   4,  15,   6,    1,  -11 },   // 0x29 ')'
  {    97,   7,   7,   9,    2,  -11 },   // 0x2A '*'
  {   104,   9,   9,  12,    1,   -8 },   // 0x2B '+'
  {   115,   3,   6,   4,    1,   -2 },   // 0x2C ','
  {   118,   4,   2,   6,    1,   -4 },   // 0x2D '-'
  {   119,   3,   3,   4,    1,   -2 },   // 0x2E '.'
  {   121,   6,  13,   5,    0,  -11 },   // 0x2F '/'
  {   131,   9,  12,   9,    0,  -11 },   // 0x30 '0'
  {   145,   6,  12,   9,    1,  -11 },   // 0x31 '1'
  {   154,   9,  12,   9,    0,  -11 },   // 0x32 '2'
  {   168,   8,  12,   9,    0,  -11 },   // 0x33 '3'
  {   180,   8,  12,   9,    1,  -11 },   // 0x34 '4'
  {   192,   8,  12,   9,    1,  -11 },   // 0x35 '5'
  {   204,   8,  12,   9,    1,  -11 },   // 0x36 '6'
  {   216,   9,  12,   9,    0,  -11 },   // 0x37 '7'
  {   230,   8,  12,   9,    1,  -11 },   // 0x38 '8'
  {   242,   9,  12,   9,    0,  -11 },   // 0x39 '9'
  {   256,   3,   9,   6,    1,   -8 },   // 0x3A ':'
  {   260,   3,  12,   6,    2,   -8 },   // 0x3B ';'
  {   265,  10,  10,  12,    1,   -9 },   // 0x3C '<'
  {   278,  10,   5,  12,    1,   -6 },   // 0x3D '='
  {   285,  10,  10,  12,    1,   -8 },   // 0x3E '>'
  {   298,   7,  12,   9,    1,  -11 },   // 0x3F '?'
  {   309,  13,  12,  17,    2,  -11 },   // 0x40 '@'
  {   329,  13,  12,  13,    0,  -11 },   // 0x41 'A'
  {   349,  11,  12,  12,    0,  -11 },   // 0x42 'B'
  {   366,  11,  12,  13,    1,  -11 },   // 0x43 'C'
  {   383,  11,  12,  13,    1,  -11 },   // 0x44 'D'
  {   400,  11,  12,  12,    1,  -11 },   // 0x45 'E'
  {   417,  10,  12,  11,    1,  -11 },   // 0x46 'F'
  {   432,  12,  12,  14,    1,  -11 },   // 0x47 'G'
  {   450,  12,  12,  14,    1,  -11 },   // 0x48 'H'
  {   468,   5,  12,   7,    1,  -11 },   // 0x49 'I'
  {   476,   8,  14,   9,    0,  -11 },   // 0x4A 'J'
  {   490,  13,  12,  14,    1,  -11 },   // 0x4B 'K'
  {   510,  11,  12,  12,    1,  -11 },   // 0x4C 'L'
  {   527,  16,  12,  17,    0,  -11 },   // 0x4D 'M'
  {   551,  11,  12,  13,    1,  -11 },   // 0x4E 'N'
  {   568,  12,  12,  14,    1,  -11 },   // 0x4F 'O'
  {   586,  10,  12,  11,    1,  -11 },   // 0x50 'P'
  {   601,  12,  15,  14,    1,  -11 },   // 0x51 'Q'
  {   624,  12,  12,  13,    1,  -11 },   // 0x52 'R'
  {   642,   8,  12,  10,    1,  -11 },   // 0x53 'S'
  {   654,  12,  12,  12,    0,  -11 },   // 0x54 'T'
  {   672,  11,  12,  13,    1,  -11 },   // 0x55 'U'
  {   689,  13,  13,  13,    0,  -11 },   // 0x56 'V'
  {   711,  18,  12,  18,    0,  -11 },   // 0x57 'W'
  {   738,  13,  12,  13,    0,  -11 },   // 0x58 'X'
  {   758,  13,  12,  13,    0,  -11 },   // 0x59 'Y'
  {   778,  11,  12,  12,    1,  -11 },   // 0x5A 'Z'
  {   795,   4,  15,   6,    1,  -11 },   // 0x5B '['
  {   803,   6,  13,   5,    0,  -11 },   // 0x5C '\'
  {   813,   4,  15,   6,    1,  -11 },   // 0x5D ']'
  {   821,   8,   7,  10,    1,  -11 },   // 0x5E '^'
  {   828,   9,   1,   9,    0,    3 },   // 0x5F '_'
  {   830,   4,   3,   6,    0,  -12 },   // 0x60 '`'
  {   832,   9,   9,   9,    0,   -8 },   // 0x61 'a'
  {   843,  10,  12,  10,    0,  -11 },   // 0x62 'b'
  {   858,   7,   9,   8,    0,   -8 },   // 0x63 'c'
  {   866,  10,  12,  10,    0,  -11 },   // 0x64 'd'
  {   881,   8,   9,   8,    0,   -8 },   // 0x65 'e'
  {   890,   7,  12,   7,    0,  -11 },   // 0x66 'f'
  {   901,   7,  13,   9,    1,   -8 },   // 0x67 'g'
  {   913,  10,  12,  10,    0,  -11 },   // 0x68 'h'
  {   928,   5,  12,   5,    0,  -11 },   // 0x69 'i'
  {   936,   6,  16,   7,    0,  -11 },   // 0x6A 'j'
  {   948,  10,  12,  10,    0,  -11 },   // 0x6B 'k'
  {   963,   5,  12,   5,    0,  -11 },   // 0x6C 'l'
  {   971,  15,   9,  15,    0,   -8 },   // 0x6D 'm'
  {   988,  10,   9,  10,    0,   -8 },   // 0x6E 'n'
  {  1000,   9,   9,   9,    0,   -8 },   // 0x6F 'o'
  {  1011,  10,  13,  10,    0,   -8 },   // 0x70 'p'
  {  1028,  10,  13,  10,    0,   -8 },   // 0x71 'q'
  {  1045,   8,   9,   8,    0,   -8 },   // 0x72 'r'
  {  1054,   5,   9,   7,    1,   -8 },   // 0x73 's'
  {  1060,   6,  11,   6,    0,  -10 },   // 0x74 't'
  {  1069,  10,   9,  10,    0,   -8 },   // 0x75 'u'
  {  1081,   9,   9,   9,    0,   -8 },   // 0x76 'v'
  {  1092,  12,   9,  13,    0,   -8 },   // 0x77 'w'
  {  1106,   9,   9,   9,    0,   -8 },   // 0x78 'x'
  {  1117,   8,  13,   9,    0,   -8 },   // 0x79 'y'
  {  1130,   7,   9,   8,    1,   -8 },   // 0x7A 'z'
  {  1138,   5,  16,   7,    0,  -12 },   // 0x7B '{'
  {  1148,   1,  13,   4,    1,  -11 },   // 0x7C '|'
  {  1150,   5,  16,   7,    2,  -12 },   // 0x7D '}'
  {  1160,   8,   2,   9,    1,   -4 } }; // 0x7E '~'

const GFXfont FreeSerifBold9pt7b PROGMEM = {
  (uint8_t  *)FreeSerifBold9pt7bBitmaps,
  (GFXglyph *)FreeSerifBold9pt7bGlyphs,
  0x20, 0x7E, 22 };

// Approx. 1834 bytes
