#include <stdint.h>
#define U8G2_USE_LARGE_FONTS

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

extern const uint8_t lgfx_efont_kr_10[];
extern const uint8_t lgfx_efont_kr_10_b[];
extern const uint8_t lgfx_efont_kr_10_bi[];
extern const uint8_t lgfx_efont_kr_10_i[];
extern const uint8_t lgfx_efont_kr_12[];
extern const uint8_t lgfx_efont_kr_12_b[];
extern const uint8_t lgfx_efont_kr_12_bi[];
extern const uint8_t lgfx_efont_kr_12_i[];
extern const uint8_t lgfx_efont_kr_14[];
extern const uint8_t lgfx_efont_kr_14_b[];
extern const uint8_t lgfx_efont_kr_14_bi[];
extern const uint8_t lgfx_efont_kr_14_i[];
extern const uint8_t lgfx_efont_kr_16[];
extern const uint8_t lgfx_efont_kr_16_b[];
extern const uint8_t lgfx_efont_kr_16_bi[];
extern const uint8_t lgfx_efont_kr_16_i[];
extern const uint8_t lgfx_efont_kr_24[];
extern const uint8_t lgfx_efont_kr_24_b[];
extern const uint8_t lgfx_efont_kr_24_bi[];
extern const uint8_t lgfx_efont_kr_24_i[];

#ifdef __cplusplus
}
#endif /* __cplusplus */
