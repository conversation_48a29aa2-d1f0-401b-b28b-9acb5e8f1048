// Created by http://oleddisplay.squix.ch/ Consider a donation
// In case of problems make sure that you are using the font file with the correct version!
const uint8_t Roboto_Thin_24Bitmaps[] PROGMEM = {

	// Bitmap Data:
	0x00, // ' '
	0x49,0x24,0x92,0x49,0x20,0x00,0x40, // '!'
	0xB6,0xDA, // '"'
	0x02,0x10,0x10,0x80,0x42,0x01,0x08,0x04,0x21,0xFF,0xF0,0x44,0x02,0x10,0x08,0x40,0x21,0x00,0x84,0x1F,0xFE,0x10,0x80,0x42,0x01,0x08,0x04,0x20,0x10,0x80, // '#'
	0x04,0x01,0x00,0x40,0xFC,0x61,0xB0,0x38,0x06,0x01,0x80,0x10,0x02,0x00,0x60,0x07,0x00,0x20,0x06,0x01,0x80,0x70,0x36,0x18,0x7C,0x04,0x01,0x00,0x40, // '$'
	0x38,0x01,0x8C,0x02,0x08,0x44,0x11,0x08,0x22,0x18,0xC8,0x0F,0x10,0x00,0x40,0x01,0x00,0x02,0x78,0x09,0x90,0x22,0x10,0x44,0x21,0x08,0x42,0x10,0x80,0x32,0x00,0x3C, // '%'
	0x1E,0x01,0x08,0x10,0x20,0x81,0x04,0x08,0x20,0x80,0x88,0x03,0x80,0x18,0x01,0x20,0x10,0x85,0x02,0x28,0x0A,0x40,0x33,0x01,0x8C,0x14,0x3F,0x10, // '&'
	0xF8, // '''
	0x00,0x21,0x04,0x21,0x04,0x10,0x42,0x08,0x20,0x82,0x08,0x20,0x82,0x04,0x10,0x40,0x82,0x04,0x08,0x00, // '('
	0x01,0x02,0x08,0x10,0x40,0x82,0x08,0x10,0x41,0x04,0x10,0x41,0x04,0x10,0x82,0x08,0x41,0x08,0x40,0x00, // ')'
	0x04,0x00,0x80,0x10,0x22,0x23,0xF8,0x08,0x02,0x80,0x88,0x20,0x80,0x00, // '*'
	0x02,0x00,0x10,0x00,0x80,0x04,0x00,0x20,0x3F,0xFC,0x08,0x00,0x40,0x02,0x00,0x10,0x00,0x80,0x04,0x00, // '+'
	0x55,0x80, // ','
	0x7C, // '-'
	0x40, // '.'
	0x01,0x00,0x80,0x80,0x40,0x20,0x20,0x10,0x10,0x08,0x04,0x04,0x02,0x01,0x01,0x00,0x80,0x80,0x40,0x20,0x20,0x00, // '/'
	0x1E,0x08,0x44,0x0B,0x02,0x80,0x60,0x18,0x06,0x01,0x80,0x60,0x18,0x06,0x01,0x80,0x70,0x34,0x08,0x84,0x1E,0x00, // '0'
	0x0D,0xD8,0x41,0x04,0x10,0x41,0x04,0x10,0x41,0x04,0x10,0x41,0x04, // '1'
	0x1F,0x0C,0x31,0x03,0x40,0x28,0x04,0x00,0x80,0x20,0x04,0x01,0x00,0x40,0x10,0x02,0x00,0x80,0x20,0x08,0x02,0x00,0x7F,0xE0, // '2'
	0x1F,0x0C,0x31,0x03,0x40,0x28,0x04,0x00,0x80,0x20,0x0C,0x1E,0x00,0x30,0x01,0x00,0x18,0x03,0x00,0x50,0x1B,0x06,0x1F,0x00, // '3'
	0x00,0xC0,0x0A,0x00,0x50,0x04,0x80,0x44,0x02,0x20,0x21,0x02,0x08,0x20,0x41,0x02,0x10,0x11,0xFF,0xF0,0x04,0x00,0x20,0x01,0x00,0x08,0x00,0x40, // '4'
	0x3F,0xE4,0x00,0x80,0x20,0x04,0x00,0x80,0x13,0xC3,0x86,0x00,0x40,0x04,0x00,0x80,0x10,0x02,0x80,0x50,0x11,0x06,0x1F,0x00, // '5'
	0x07,0x83,0x00,0x80,0x20,0x04,0x01,0x1E,0x2C,0x36,0x02,0x80,0x30,0x06,0x00,0xC0,0x18,0x03,0x80,0xD0,0x11,0x84,0x1F,0x00, // '6'
	0xFF,0xE0,0x04,0x01,0x00,0x20,0x08,0x01,0x00,0x40,0x08,0x02,0x00,0x40,0x08,0x02,0x00,0x40,0x10,0x02,0x00,0x80,0x10,0x00, // '7'
	0x1F,0x0C,0x1B,0x01,0xC0,0x18,0x03,0x00,0xD8,0x30,0xF8,0x31,0x88,0x0A,0x00,0xC0,0x18,0x03,0x00,0x50,0x13,0x06,0x1F,0x00, // '8'
	0x1F,0x04,0x11,0x01,0x60,0x18,0x03,0x00,0x60,0x0C,0x01,0x40,0x6C,0x14,0x7C,0x80,0x10,0x04,0x00,0x80,0x20,0x18,0x1C,0x00, // '9'
	0xC0,0x00,0x00,0xC0, // ':'
	0xC0,0x00,0x05,0x58, // ';'
	0x00,0x00,0x70,0x60,0x60,0x60,0x30,0x03,0x00,0x30,0x03,0x80,0x10, // '<'
	0x7F,0xE0,0x00,0x00,0x00,0x00,0x00,0xFF,0xC0, // '='
	0x00,0x38,0x01,0x80,0x18,0x01,0x80,0x30,0x30,0x30,0x30,0x30,0x00, // '>'
	0x3C,0x42,0x81,0x81,0x81,0x01,0x01,0x02,0x04,0x08,0x10,0x10,0x10,0x00,0x00,0x00,0x18, // '?'
	0x01,0xF8,0x00,0xC0,0xC0,0x60,0x04,0x18,0x00,0x42,0x00,0x04,0x81,0xE0,0x90,0x42,0x0C,0x10,0x41,0x84,0x08,0x30,0x81,0x06,0x10,0x20,0xC2,0x04,0x18,0x41,0x03,0x08,0x20,0x61,0x04,0x12,0x11,0x46,0x43,0xC7,0x0C,0x00,0x00,0x80,0x00,0x08,0x00,0x00,0xC0,0x80,0x07,0xE0,0x00, // '@'
	0x01,0x00,0x0C,0x00,0x30,0x01,0x20,0x04,0x80,0x21,0x00,0x84,0x02,0x10,0x10,0x20,0x40,0x82,0x02,0x0F,0xFC,0x20,0x11,0x00,0x24,0x00,0x90,0x02,0x80,0x04, // 'A'
	0xFE,0x10,0x32,0x03,0x40,0x28,0x05,0x00,0xA0,0x34,0x0C,0xFF,0x10,0x1A,0x01,0x40,0x18,0x03,0x00,0x60,0x1C,0x06,0xFF,0x00, // 'B'
	0x0F,0x81,0x83,0x18,0x04,0x80,0x28,0x01,0x40,0x02,0x00,0x10,0x00,0x80,0x04,0x00,0x20,0x01,0x00,0x08,0x01,0x20,0x09,0x80,0x46,0x0C,0x0F,0x80, // 'C'
	0xFF,0x08,0x18,0x80,0x48,0x02,0x80,0x28,0x01,0x80,0x18,0x01,0x80,0x18,0x01,0x80,0x18,0x01,0x80,0x28,0x02,0x80,0x48,0x18,0xFF,0x00, // 'D'
	0xFF,0xF0,0x02,0x00,0x40,0x08,0x01,0x00,0x20,0x04,0x00,0xFF,0xD0,0x02,0x00,0x40,0x08,0x01,0x00,0x20,0x04,0x00,0xFF,0xE0, // 'E'
	0xFF,0xF0,0x02,0x00,0x40,0x08,0x01,0x00,0x20,0x04,0x00,0xFF,0xD0,0x02,0x00,0x40,0x08,0x01,0x00,0x20,0x04,0x00,0x80,0x00, // 'F'
	0x0F,0xC1,0x83,0x18,0x04,0x80,0x18,0x00,0xC0,0x02,0x00,0x10,0x00,0x80,0x04,0x0F,0xE0,0x03,0x00,0x1C,0x00,0xA0,0x05,0x80,0x26,0x06,0x0F,0xC0, // 'G'
	0x80,0x18,0x01,0x80,0x18,0x01,0x80,0x18,0x01,0x80,0x18,0x01,0xFF,0xF8,0x01,0x80,0x18,0x01,0x80,0x18,0x01,0x80,0x18,0x01,0x80,0x10, // 'H'
	0xFF,0xFF,0x80, // 'I'
	0x00,0x40,0x10,0x04,0x01,0x00,0x40,0x10,0x04,0x01,0x00,0x40,0x10,0x04,0x01,0x80,0x60,0x1C,0x09,0x86,0x3E,0x00, // 'J'
	0x80,0x24,0x02,0x20,0x21,0x02,0x08,0x20,0x42,0x02,0x20,0x12,0x00,0xA8,0x06,0x20,0x20,0x81,0x02,0x08,0x10,0x40,0x42,0x01,0x10,0x04,0x80,0x10, // 'K'
	0x80,0x20,0x08,0x02,0x00,0x80,0x20,0x08,0x02,0x00,0x80,0x20,0x08,0x02,0x00,0x80,0x20,0x08,0x02,0x00,0xFF,0xC0, // 'L'
	0x80,0x03,0xC0,0x03,0xC0,0x05,0xA0,0x05,0xA0,0x05,0x90,0x09,0x90,0x09,0x90,0x11,0x88,0x11,0x88,0x21,0x84,0x21,0x84,0x21,0x84,0x41,0x82,0x41,0x82,0x81,0x81,0x81,0x81,0x01, // 'M'
	0x80,0x1C,0x01,0xA0,0x1A,0x01,0x90,0x19,0x01,0x88,0x18,0x41,0x84,0x18,0x21,0x81,0x18,0x11,0x80,0x98,0x09,0x80,0x58,0x03,0x80,0x10, // 'N'
	0x0F,0x81,0x83,0x18,0x0C,0x80,0x28,0x00,0xC0,0x06,0x00,0x30,0x01,0x80,0x0C,0x00,0x60,0x03,0x00,0x18,0x00,0xA0,0x09,0x80,0xC6,0x0C,0x0F,0x80, // 'O'
	0xFF,0x10,0x1A,0x01,0x40,0x18,0x03,0x00,0x60,0x0C,0x03,0x80,0xDF,0xE2,0x00,0x40,0x08,0x01,0x00,0x20,0x04,0x00,0x80,0x00, // 'P'
	0x0F,0x81,0x83,0x18,0x0C,0x80,0x28,0x00,0xC0,0x06,0x00,0x30,0x01,0x80,0x0C,0x00,0x60,0x03,0x00,0x18,0x01,0x20,0x09,0x80,0x86,0x08,0x0F,0xC0,0x01,0x00,0x08,0x00,0x20, // 'Q'
	0xFF,0x08,0x0C,0x80,0x48,0x02,0x80,0x28,0x02,0x80,0x28,0x04,0x80,0xCF,0xF0,0x81,0x08,0x10,0x80,0x88,0x04,0x80,0x48,0x02,0x80,0x10, // 'R'
	0x1F,0x86,0x0C,0xC0,0x28,0x01,0x80,0x18,0x00,0x40,0x03,0x00,0x0E,0x00,0x1C,0x00,0x20,0x01,0x80,0x18,0x01,0x40,0x36,0x06,0x1F,0x80, // 'S'
	0x7F,0xFC,0x02,0x00,0x04,0x00,0x08,0x00,0x10,0x00,0x20,0x00,0x40,0x00,0x80,0x01,0x00,0x02,0x00,0x04,0x00,0x08,0x00,0x10,0x00,0x20,0x00,0x40,0x00,0x80,0x01,0x00, // 'T'
	0x80,0x18,0x01,0x80,0x18,0x01,0x80,0x18,0x01,0x80,0x18,0x01,0x80,0x18,0x01,0x80,0x18,0x01,0x80,0x18,0x03,0x40,0x23,0x0C,0x1F,0x80, // 'U'
	0x80,0x05,0x00,0x24,0x00,0x90,0x02,0x20,0x10,0x80,0x41,0x01,0x04,0x08,0x10,0x20,0x21,0x00,0x84,0x02,0x10,0x04,0x80,0x12,0x00,0x28,0x00,0xC0,0x01,0x00, // 'V'
	0x40,0x20,0x09,0x00,0xC0,0x24,0x05,0x01,0x10,0x14,0x04,0x20,0x48,0x10,0x81,0x20,0x42,0x08,0x82,0x08,0x21,0x08,0x10,0x84,0x20,0x44,0x11,0x01,0x10,0x44,0x02,0x40,0x90,0x0A,0x02,0x40,0x28,0x0A,0x00,0xA0,0x18,0x01,0x80,0x60,0x04,0x00,0x80, // 'W'
	0x40,0x08,0x80,0x42,0x01,0x04,0x08,0x08,0x40,0x11,0x00,0x48,0x00,0xC0,0x01,0x00,0x0C,0x00,0x48,0x02,0x10,0x08,0x40,0x40,0x82,0x01,0x10,0x02,0x40,0x08, // 'X'
	0x40,0x04,0x80,0x08,0x80,0x20,0x80,0x81,0x01,0x01,0x04,0x02,0x10,0x02,0x20,0x02,0x80,0x05,0x00,0x04,0x00,0x08,0x00,0x10,0x00,0x20,0x00,0x40,0x00,0x80,0x01,0x00, // 'Y'
	0xFF,0xF0,0x01,0x00,0x08,0x00,0x80,0x08,0x00,0x40,0x04,0x00,0x40,0x04,0x00,0x20,0x02,0x00,0x20,0x02,0x00,0x10,0x01,0x00,0x10,0x00,0xFF,0xF0, // 'Z'
	0xF8,0x88,0x88,0x88,0x88,0x88,0x88,0x88,0x88,0x88,0x88,0xF0, // '['
	0x40,0x20,0x10,0x04,0x02,0x01,0x00,0x40,0x20,0x08,0x04,0x02,0x00,0x80,0x40,0x10,0x08,0x04,0x01,0x00,0x80,0x00, // '\'
	0xF1,0x11,0x11,0x11,0x11,0x11,0x11,0x11,0x11,0x11,0x11,0xF0, // ']'
	0x10,0x18,0x28,0x24,0x44,0x44,0x42,0x82,0x81, // '^'
	0x7F,0xE0, // '_'
	0x42,0x20, // '`'
	0x3F,0x0C,0x12,0x01,0x00,0x20,0x04,0x3F,0x98,0x16,0x02,0x80,0x50,0x0A,0x03,0x20,0xA3,0xE4, // 'a'
	0x80,0x20,0x08,0x02,0x00,0x80,0x27,0x8A,0x1B,0x02,0x80,0x60,0x18,0x06,0x01,0x80,0x60,0x18,0x07,0x02,0xA1,0xA7,0x80, // 'b'
	0x1F,0x08,0x64,0x0F,0x01,0x80,0x60,0x08,0x02,0x00,0x80,0x20,0x14,0x04,0x86,0x1F,0x00, // 'c'
	0x00,0x40,0x10,0x04,0x01,0x00,0x47,0x96,0x15,0x03,0x80,0x60,0x18,0x06,0x01,0x80,0x60,0x18,0x05,0x03,0x61,0x47,0x90, // 'd'
	0x1E,0x08,0x64,0x0A,0x01,0x80,0x7F,0xF8,0x02,0x00,0x80,0x30,0x04,0x00,0xC2,0x1F,0x00, // 'e'
	0x07,0x04,0x04,0x02,0x01,0x00,0x81,0xF8,0x20,0x10,0x08,0x04,0x02,0x01,0x00,0x80,0x40,0x20,0x10,0x08,0x04,0x00, // 'f'
	0x1E,0x58,0x54,0x0E,0x01,0x80,0x60,0x18,0x06,0x01,0x80,0x60,0x14,0x0D,0x85,0x1E,0x40,0x10,0x05,0x02,0x21,0x87,0x80, // 'g'
	0x80,0x40,0x20,0x10,0x08,0x04,0xF2,0x85,0x81,0xC0,0xC0,0x60,0x30,0x18,0x0C,0x06,0x03,0x01,0x80,0xC0,0x40, // 'h'
	0x40,0x04,0x92,0x49,0x24,0x92,0x40, // 'i'
	0x08,0x00,0x00,0x08,0x20,0x82,0x08,0x20,0x82,0x08,0x20,0x82,0x08,0x20,0x82,0x13,0x80, // 'j'
	0x80,0x20,0x08,0x02,0x00,0x80,0x20,0x28,0x12,0x08,0x8C,0x24,0x0A,0x03,0x40,0x88,0x21,0x08,0x22,0x08,0x81,0x20,0x20, // 'k'
	0xFF,0xFF,0xC0, // 'l'
	0x9E,0x1E,0x50,0x90,0xB0,0x30,0x38,0x10,0x18,0x08,0x0C,0x04,0x06,0x02,0x03,0x01,0x01,0x80,0x80,0xC0,0x40,0x60,0x20,0x30,0x10,0x18,0x08,0x08, // 'm'
	0x9E,0x50,0xB0,0x38,0x18,0x0C,0x06,0x03,0x01,0x80,0xC0,0x60,0x30,0x18,0x08, // 'n'
	0x1F,0x06,0x31,0x01,0x60,0x38,0x03,0x00,0x60,0x0C,0x01,0x80,0x38,0x0D,0x01,0x10,0x41,0xF0, // 'o'
	0x9E,0x28,0x6C,0x0A,0x01,0x80,0x60,0x18,0x06,0x01,0x80,0x60,0x3C,0x0A,0x86,0x9E,0x20,0x08,0x02,0x00,0x80,0x20,0x00, // 'p'
	0x1E,0x58,0x54,0x0E,0x01,0x80,0x60,0x18,0x06,0x01,0x80,0x60,0x14,0x0D,0x85,0x1E,0x40,0x10,0x04,0x01,0x00,0x40,0x10, // 'q'
	0x9D,0x43,0x04,0x08,0x10,0x20,0x40,0x81,0x02,0x04,0x08,0x00, // 'r'
	0x3E,0x21,0xA0,0x30,0x18,0x02,0x00,0xF0,0x06,0x00,0xC0,0x60,0x28,0x23,0xE0, // 's'
	0x10,0x10,0x10,0x10,0x7E,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x18,0x0E, // 't'
	0x80,0xC0,0x60,0x30,0x18,0x0C,0x06,0x03,0x01,0x80,0xC0,0x60,0x68,0x53,0xC8, // 'u'
	0x80,0x28,0x09,0x01,0x10,0x22,0x08,0x41,0x04,0x20,0x88,0x11,0x01,0x40,0x28,0x03,0x00,0x40, // 'v'
	0x40,0x40,0x90,0x30,0x24,0x0C,0x10,0x85,0x04,0x21,0x21,0x08,0x48,0x41,0x22,0x20,0x48,0x48,0x12,0x12,0x05,0x05,0x00,0xC0,0xC0,0x30,0x30,0x08,0x04,0x00, // 'w'
	0x40,0x44,0x08,0x82,0x08,0x80,0xA0,0x0C,0x01,0x00,0x50,0x09,0x02,0x20,0x82,0x10,0x24,0x04, // 'x'
	0x80,0x24,0x02,0x40,0x42,0x04,0x20,0x82,0x08,0x10,0x81,0x10,0x09,0x00,0xA0,0x0A,0x00,0x60,0x04,0x00,0x40,0x04,0x00,0x80,0x08,0x07,0x00, // 'y'
	0xFF,0x80,0x20,0x10,0x08,0x04,0x01,0x00,0x80,0x40,0x20,0x08,0x04,0x02,0x00,0xFF,0xC0, // 'z'
	0x00,0x06,0x08,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x20,0xC0,0x60,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x08,0x04,0x02, // '{'
	0xFF,0xFF,0xF0, // '|'
	0x00,0x60,0x10,0x08,0x08,0x08,0x08,0x08,0x08,0x08,0x08,0x04,0x03,0x06,0x08,0x08,0x08,0x08,0x08,0x08,0x08,0x08,0x10,0x20,0x40 // '}'
};
const GFXglyph Roboto_Thin_24Glyphs[] PROGMEM = {
// bitmapOffset, width, height, xAdvance, xOffset, yOffset
	  {     0,   1,   1,   7,    0,    0 }, // ' '
	  {     1,   3,  17,   6,    1,  -17 }, // '!'
	  {     8,   3,   5,   7,    2,  -18 }, // '"'
	  {    10,  14,  17,  14,    0,  -17 }, // '#'
	  {    40,  10,  23,  14,    1,  -20 }, // '$'
	  {    69,  15,  17,  18,    1,  -17 }, // '%'
	  {   101,  13,  17,  15,    1,  -17 }, // '&'
	  {   129,   1,   5,   6,    2,  -18 }, // '''
	  {   130,   6,  26,   9,    2,  -20 }, // '('
	  {   150,   6,  26,   9,    0,  -20 }, // ')'
	  {   170,  11,  10,  12,    0,  -17 }, // '*'
	  {   184,  13,  12,  15,    0,  -14 }, // '+'
	  {   204,   2,   5,   6,    1,   -2 }, // ','
	  {   206,   7,   1,   8,    0,   -8 }, // '-'
	  {   207,   3,   1,   6,    1,   -1 }, // '.'
	  {   208,   9,  19,  10,    0,  -17 }, // '/'
	  {   230,  10,  17,  13,    1,  -17 }, // '0'
	  {   252,   6,  17,  14,    2,  -17 }, // '1'
	  {   265,  11,  17,  14,    1,  -17 }, // '2'
	  {   289,  11,  17,  14,    1,  -17 }, // '3'
	  {   313,  13,  17,  14,    0,  -17 }, // '4'
	  {   341,  11,  17,  14,    1,  -17 }, // '5'
	  {   365,  11,  17,  15,    2,  -17 }, // '6'
	  {   389,  11,  17,  14,    1,  -17 }, // '7'
	  {   413,  11,  17,  14,    1,  -17 }, // '8'
	  {   437,  11,  17,  15,    1,  -17 }, // '9'
	  {   461,   2,  13,   5,    1,  -13 }, // ':'
	  {   465,   2,  16,   6,    1,  -13 }, // ';'
	  {   469,  10,  10,  14,    1,  -13 }, // '<'
	  {   482,  11,   6,  14,    1,  -11 }, // '='
	  {   491,  10,  10,  13,    1,  -13 }, // '>'
	  {   504,   8,  17,  11,    1,  -17 }, // '?'
	  {   521,  19,  22,  23,    2,  -17 }, // '@'
	  {   574,  14,  17,  15,    0,  -17 }, // 'A'
	  {   604,  11,  17,  15,    2,  -17 }, // 'B'
	  {   628,  13,  17,  17,    2,  -17 }, // 'C'
	  {   656,  12,  17,  17,    2,  -17 }, // 'D'
	  {   682,  11,  17,  15,    2,  -17 }, // 'E'
	  {   706,  11,  17,  15,    2,  -17 }, // 'F'
	  {   730,  13,  17,  18,    2,  -17 }, // 'G'
	  {   758,  12,  17,  17,    2,  -17 }, // 'H'
	  {   784,   1,  17,   8,    3,  -17 }, // 'I'
	  {   787,  10,  17,  14,    1,  -17 }, // 'J'
	  {   809,  13,  17,  16,    2,  -17 }, // 'K'
	  {   837,  10,  17,  13,    2,  -17 }, // 'L'
	  {   859,  16,  17,  21,    2,  -17 }, // 'M'
	  {   893,  12,  17,  17,    2,  -17 }, // 'N'
	  {   919,  13,  17,  18,    2,  -17 }, // 'O'
	  {   947,  11,  17,  15,    2,  -17 }, // 'P'
	  {   971,  13,  20,  18,    2,  -17 }, // 'Q'
	  {  1004,  12,  17,  17,    2,  -17 }, // 'R'
	  {  1030,  12,  17,  15,    1,  -17 }, // 'S'
	  {  1056,  15,  17,  16,    0,  -17 }, // 'T'
	  {  1088,  12,  17,  17,    2,  -17 }, // 'U'
	  {  1114,  14,  17,  15,    0,  -17 }, // 'V'
	  {  1144,  22,  17,  23,    0,  -17 }, // 'W'
	  {  1191,  14,  17,  15,    0,  -17 }, // 'X'
	  {  1221,  15,  17,  16,    0,  -17 }, // 'Y'
	  {  1253,  13,  17,  15,    1,  -17 }, // 'Z'
	  {  1281,   4,  23,   6,    2,  -19 }, // '['
	  {  1293,   9,  19,  10,    0,  -17 }, // '\'
	  {  1315,   4,  23,   7,    0,  -19 }, // ']'
	  {  1327,   8,   9,  11,    1,  -17 }, // '^'
	  {  1336,  11,   1,  11,   -1,    0 }, // '_'
	  {  1338,   4,   3,   7,    1,  -18 }, // '`'
	  {  1340,  11,  13,  14,    1,  -13 }, // 'a'
	  {  1358,  10,  18,  14,    2,  -18 }, // 'b'
	  {  1381,  10,  13,  13,    1,  -13 }, // 'c'
	  {  1398,  10,  18,  14,    1,  -18 }, // 'd'
	  {  1421,  10,  13,  13,    1,  -13 }, // 'e'
	  {  1438,   9,  19,   9,    0,  -19 }, // 'f'
	  {  1460,  10,  18,  14,    1,  -13 }, // 'g'
	  {  1483,   9,  18,  14,    2,  -18 }, // 'h'
	  {  1504,   3,  17,   6,    1,  -17 }, // 'i'
	  {  1511,   6,  22,   6,   -2,  -17 }, // 'j'
	  {  1528,  10,  18,  13,    2,  -18 }, // 'k'
	  {  1551,   1,  18,   6,    2,  -18 }, // 'l'
	  {  1554,  17,  13,  22,    2,  -13 }, // 'm'
	  {  1582,   9,  13,  14,    2,  -13 }, // 'n'
	  {  1597,  11,  13,  14,    1,  -13 }, // 'o'
	  {  1615,  10,  18,  14,    2,  -13 }, // 'p'
	  {  1638,  10,  18,  14,    1,  -13 }, // 'q'
	  {  1661,   7,  13,   9,    2,  -13 }, // 'r'
	  {  1673,   9,  13,  12,    1,  -13 }, // 's'
	  {  1688,   8,  17,   9,    0,  -17 }, // 't'
	  {  1705,   9,  13,  14,    2,  -13 }, // 'u'
	  {  1720,  11,  13,  12,    0,  -13 }, // 'v'
	  {  1738,  18,  13,  19,    0,  -13 }, // 'w'
	  {  1768,  11,  13,  12,    0,  -13 }, // 'x'
	  {  1786,  12,  18,  12,    0,  -13 }, // 'y'
	  {  1813,  10,  13,  12,    1,  -13 }, // 'z'
	  {  1830,   8,  25,   9,    1,  -19 }, // '{'
	  {  1855,   1,  20,   6,    2,  -17 }, // '|'
	  {  1858,   8,  25,   9,   -1,  -19 } // '}'
};
const GFXfont Roboto_Thin_24 PROGMEM = {
(uint8_t  *)Roboto_Thin_24Bitmaps,(GFXglyph *)Roboto_Thin_24Glyphs,0x20, 0x7D, 29};
