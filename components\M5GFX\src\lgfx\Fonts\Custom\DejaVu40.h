/* DejaVu 40
 original ttf url : https://dejavu-fonts.github.io/
 original license : https://dejavu-fonts.github.io/License.html
This data has been converted to AdafruitGFX font format from DejaVuSans.ttf.
*/
const uint8_t DejaVu40Bitmaps[] PROGMEM = {
0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x00, 0x00, 0xff, 0xff, 0xf0, 0xe1,
0xf8, 0x7e, 0x1f, 0x87, 0xe1, 0xf8, 0x7e, 0x1f, 0x87, 0xe1, 0xf8, 0x7e, 0x1c, 0x00, 0x1c, 0x0e,
0x00, 0x03, 0x81, 0xc0, 0x00, 0x70, 0x70, 0x00, 0x1c, 0x0e, 0x00, 0x03, 0x81, 0xc0, 0x00, 0x70,
0x78, 0x00, 0x0e, 0x0e, 0x00, 0x03, 0x81, 0xc0, 0x3f, 0xff, 0xff, 0xe7, 0xff, 0xff, 0xfc, 0xff,
0xff, 0xff, 0x80, 0x70, 0x38, 0x00, 0x0e, 0x07, 0x00, 0x01, 0xc0, 0xe0, 0x00, 0x38, 0x38, 0x00,
0x0e, 0x07, 0x00, 0x01, 0xc0, 0xe0, 0x00, 0x38, 0x1c, 0x03, 0xff, 0xff, 0xfe, 0x7f, 0xff, 0xff,
0xcf, 0xff, 0xff, 0xf8, 0x07, 0x03, 0x80, 0x00, 0xe0, 0xf0, 0x00, 0x3c, 0x1c, 0x00, 0x07, 0x03,
0x80, 0x00, 0xe0, 0x70, 0x00, 0x1c, 0x1e, 0x00, 0x07, 0x83, 0x80, 0x00, 0xe0, 0x70, 0x00, 0x00,
0xc0, 0x00, 0x0c, 0x00, 0x00, 0xc0, 0x00, 0x0c, 0x00, 0x00, 0xc0, 0x00, 0x7f, 0xe0, 0x1f, 0xff,
0x83, 0xff, 0xf8, 0x7c, 0xc1, 0x8f, 0x8c, 0x00, 0xf0, 0xc0, 0x0f, 0x0c, 0x00, 0xf0, 0xc0, 0x0f,
0x8c, 0x00, 0x7f, 0xc0, 0x07, 0xff, 0x00, 0x3f, 0xfe, 0x00, 0xff, 0xf0, 0x00, 0xff, 0x80, 0x0c,
0xfc, 0x00, 0xc3, 0xe0, 0x0c, 0x3e, 0x00, 0xc1, 0xe0, 0x0c, 0x1e, 0x00, 0xc1, 0xe8, 0x0c, 0x3e,
0xc0, 0xc3, 0xcf, 0x8c, 0xfc, 0xff, 0xff, 0x87, 0xff, 0xe0, 0x0f, 0xf8, 0x00, 0x0c, 0x00, 0x00,
0xc0, 0x00, 0x0c, 0x00, 0x00, 0xc0, 0x00, 0x0c, 0x00, 0x00, 0xc0, 0x00, 0x0f, 0x80, 0x00, 0xf0,
0x0f, 0xf8, 0x00, 0x38, 0x03, 0x8e, 0x00, 0x1e, 0x01, 0xc1, 0xc0, 0x0f, 0x00, 0x70, 0x70, 0x03,
0x80, 0x38, 0x0e, 0x01, 0xe0, 0x0e, 0x03, 0x80, 0x70, 0x03, 0x80, 0xe0, 0x38, 0x00, 0xe0, 0x38,
0x1e, 0x00, 0x38, 0x0e, 0x07, 0x00, 0x0e, 0x03, 0x83, 0xc0, 0x01, 0xc1, 0xc1, 0xe0, 0x00, 0x70,
0x70, 0x70, 0x00, 0x0e, 0x38, 0x3c, 0x00, 0x03, 0xfe, 0x0e, 0x07, 0xc0, 0x3e, 0x07, 0x07, 0xfc,
0x00, 0x03, 0xc1, 0xc7, 0x00, 0x00, 0xe0, 0xe0, 0xe0, 0x00, 0x78, 0x38, 0x38, 0x00, 0x3c, 0x1c,
0x07, 0x00, 0x0e, 0x07, 0x01, 0xc0, 0x07, 0x81, 0xc0, 0x70, 0x01, 0xc0, 0x70, 0x1c, 0x00, 0xe0,
0x1c, 0x07, 0x00, 0x78, 0x07, 0x01, 0xc0, 0x1c, 0x00, 0xe0, 0xe0, 0x0f, 0x00, 0x38, 0x38, 0x07,
0x80, 0x07, 0x1c, 0x01, 0xc0, 0x01, 0xff, 0x00, 0xf0, 0x00, 0x1f, 0x00, 0x00, 0xfc, 0x00, 0x00,
0x7f, 0xe0, 0x00, 0x1f, 0xfe, 0x00, 0x07, 0xc3, 0xc0, 0x01, 0xf0, 0x18, 0x00, 0x3c, 0x01, 0x00,
0x07, 0x80, 0x00, 0x00, 0xf0, 0x00, 0x00, 0x1e, 0x00, 0x00, 0x03, 0xe0, 0x00, 0x00, 0x7c, 0x00,
0x00, 0x07, 0xc0, 0x00, 0x00, 0xfc, 0x00, 0x00, 0x3f, 0xc0, 0x00, 0x0f, 0xfc, 0x00, 0xf1, 0xc7,
0xc0, 0x1e, 0x78, 0xfc, 0x03, 0xce, 0x0f, 0xc0, 0xf3, 0xc0, 0xfc, 0x1e, 0x78, 0x0f, 0xc3, 0x8f,
0x00, 0xfc, 0xf1, 0xe0, 0x0f, 0xdc, 0x3c, 0x00, 0xff, 0x87, 0xc0, 0x0f, 0xe0, 0x78, 0x00, 0xfc,
0x0f, 0x80, 0x3f, 0xc0, 0xfc, 0x1f, 0xfc, 0x0f, 0xff, 0xcf, 0xc0, 0xff, 0xf0, 0xfc, 0x03, 0xf8,
0x0f, 0xc0, 0xff, 0xff, 0xff, 0xff, 0x80, 0x03, 0x83, 0x83, 0xc1, 0xc1, 0xe0, 0xe0, 0xf0, 0x78,
0x38, 0x3c, 0x1e, 0x0f, 0x07, 0x07, 0x83, 0xc1, 0xe0, 0xf0, 0x78, 0x3c, 0x1e, 0x0f, 0x07, 0x83,
0xc0, 0xe0, 0x78, 0x3c, 0x1e, 0x07, 0x03, 0xc1, 0xe0, 0x70, 0x3c, 0x0e, 0x07, 0x81, 0xc0, 0x70,
0xe0, 0x38, 0x1e, 0x07, 0x03, 0xc0, 0xe0, 0x78, 0x3c, 0x0e, 0x07, 0x83, 0xc1, 0xe0, 0x70, 0x3c,
0x1e, 0x0f, 0x07, 0x83, 0xc1, 0xe0, 0xf0, 0x78, 0x3c, 0x1e, 0x0e, 0x0f, 0x07, 0x83, 0xc1, 0xc1,
0xe0, 0xf0, 0x70, 0x78, 0x38, 0x3c, 0x1c, 0x1c, 0x00, 0x00, 0xc0, 0x00, 0x30, 0x00, 0x0c, 0x01,
0x03, 0x02, 0xf0, 0xc3, 0xde, 0x31, 0xe1, 0xed, 0xe0, 0x1f, 0xe0, 0x03, 0xf0, 0x00, 0xfc, 0x00,
0x7f, 0x80, 0x7b, 0x78, 0x78, 0xc7, 0xbc, 0x30, 0xf4, 0x0c, 0x08, 0x03, 0x00, 0x00, 0xc0, 0x00,
0x30, 0x00, 0x00, 0x1c, 0x00, 0x00, 0x0e, 0x00, 0x00, 0x07, 0x00, 0x00, 0x03, 0x80, 0x00, 0x01,
0xc0, 0x00, 0x00, 0xe0, 0x00, 0x00, 0x70, 0x00, 0x00, 0x38, 0x00, 0x00, 0x1c, 0x00, 0x00, 0x0e,
0x00, 0x00, 0x07, 0x00, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x00, 0x70,
0x00, 0x00, 0x38, 0x00, 0x00, 0x1c, 0x00, 0x00, 0x0e, 0x00, 0x00, 0x07, 0x00, 0x00, 0x03, 0x80,
0x00, 0x01, 0xc0, 0x00, 0x00, 0xe0, 0x00, 0x00, 0x70, 0x00, 0x00, 0x38, 0x00, 0x00, 0x1c, 0x00,
0x00, 0x3c, 0xf3, 0xcf, 0x3d, 0xe7, 0x9c, 0xf3, 0x80, 0xff, 0xff, 0xff, 0xff, 0x80, 0xff, 0xff,
0xf0, 0x00, 0x38, 0x03, 0xc0, 0x1c, 0x00, 0xe0, 0x07, 0x00, 0x70, 0x03, 0x80, 0x1c, 0x01, 0xc0,
0x0e, 0x00, 0x70, 0x07, 0x80, 0x38, 0x01, 0xc0, 0x0e, 0x00, 0xe0, 0x07, 0x00, 0x38, 0x03, 0x80,
0x1c, 0x00, 0xe0, 0x0f, 0x00, 0x70, 0x03, 0x80, 0x1c, 0x01, 0xc0, 0x0e, 0x00, 0x70, 0x07, 0x00,
0x38, 0x01, 0xc0, 0x1e, 0x00, 0xe0, 0x00, 0x01, 0xf8, 0x00, 0x7f, 0xe0, 0x0f, 0xff, 0x01, 0xf0,
0xf8, 0x3e, 0x07, 0xc3, 0xc0, 0x3c, 0x78, 0x01, 0xe7, 0x80, 0x1e, 0x78, 0x01, 0xef, 0x00, 0x0f,
0xf0, 0x00, 0xff, 0x00, 0x0f, 0xf0, 0x00, 0xff, 0x00, 0x0f, 0xf0, 0x00, 0xff, 0x00, 0x0f, 0xf0,
0x00, 0xff, 0x00, 0x0f, 0xf0, 0x00, 0xff, 0x00, 0x0f, 0xf0, 0x00, 0xf7, 0x80, 0x1e, 0x78, 0x01,
0xe7, 0x80, 0x1e, 0x3c, 0x03, 0xc3, 0xe0, 0x7c, 0x1f, 0x0f, 0x80, 0xff, 0xf0, 0x07, 0xfe, 0x00,
0x1f, 0x80, 0x0f, 0xe0, 0x7f, 0xf0, 0x3f, 0xf8, 0x1e, 0x3c, 0x00, 0x1e, 0x00, 0x0f, 0x00, 0x07,
0x80, 0x03, 0xc0, 0x01, 0xe0, 0x00, 0xf0, 0x00, 0x78, 0x00, 0x3c, 0x00, 0x1e, 0x00, 0x0f, 0x00,
0x07, 0x80, 0x03, 0xc0, 0x01, 0xe0, 0x00, 0xf0, 0x00, 0x78, 0x00, 0x3c, 0x00, 0x1e, 0x00, 0x0f,
0x00, 0x07, 0x80, 0x03, 0xc0, 0x01, 0xe0, 0x00, 0xf0, 0x1f, 0xff, 0xef, 0xff, 0xf7, 0xff, 0xf8,
0x07, 0xf0, 0x0f, 0xff, 0x83, 0xff, 0xf8, 0x7c, 0x0f, 0x8e, 0x00, 0xf9, 0x00, 0x0f, 0x00, 0x00,
0xf0, 0x00, 0x1e, 0x00, 0x03, 0xc0, 0x00, 0x78, 0x00, 0x0f, 0x00, 0x03, 0xe0, 0x00, 0x78, 0x00,
0x1f, 0x00, 0x07, 0xc0, 0x01, 0xf0, 0x00, 0x3e, 0x00, 0x0f, 0x80, 0x03, 0xe0, 0x00, 0xf8, 0x00,
0x3e, 0x00, 0x0f, 0x80, 0x03, 0xe0, 0x00, 0xfc, 0x00, 0x3f, 0x00, 0x0f, 0xc0, 0x03, 0xf0, 0x00,
0x7f, 0xff, 0xef, 0xff, 0xfd, 0xff, 0xff, 0x80, 0x0f, 0xf0, 0x0f, 0xff, 0xc1, 0xff, 0xfc, 0x3c,
0x0f, 0xc4, 0x00, 0x78, 0x00, 0x0f, 0x80, 0x00, 0xf0, 0x00, 0x1e, 0x00, 0x03, 0xc0, 0x00, 0x78,
0x00, 0x1e, 0x00, 0x03, 0xc0, 0x01, 0xf0, 0x0f, 0xfc, 0x01, 0xff, 0x00, 0x3f, 0xf8, 0x00, 0x1f,
0x80, 0x00, 0xf8, 0x00, 0x0f, 0x00, 0x00, 0xf0, 0x00, 0x1e, 0x00, 0x03, 0xc0, 0x00, 0x78, 0x00,
0x0f, 0x00, 0x03, 0xd0, 0x00, 0xfb, 0xc0, 0x7e, 0x7f, 0xff, 0x8f, 0xff, 0xe0, 0x7f, 0xe0, 0x00,
0x00, 0x0f, 0x80, 0x00, 0xfc, 0x00, 0x0f, 0xe0, 0x00, 0x7f, 0x00, 0x07, 0x78, 0x00, 0x7b, 0xc0,
0x03, 0x9e, 0x00, 0x38, 0xf0, 0x03, 0xc7, 0x80, 0x1c, 0x3c, 0x01, 0xe1, 0xe0, 0x1e, 0x0f, 0x00,
0xe0, 0x78, 0x0f, 0x03, 0xc0, 0xf0, 0x1e, 0x07, 0x00, 0xf0, 0x78, 0x07, 0x87, 0x80, 0x3c, 0x38,
0x01, 0xe1, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x00, 0x1e, 0x00, 0x00, 0xf0, 0x00,
0x07, 0x80, 0x00, 0x3c, 0x00, 0x01, 0xe0, 0x00, 0x0f, 0x00, 0x00, 0x78, 0x00, 0x7f, 0xff, 0x8f,
0xff, 0xf1, 0xff, 0xfe, 0x3c, 0x00, 0x07, 0x80, 0x00, 0xf0, 0x00, 0x1e, 0x00, 0x03, 0xc0, 0x00,
0x78, 0x00, 0x0f, 0x00, 0x01, 0xff, 0xc0, 0x3f, 0xff, 0x07, 0xff, 0xf0, 0xc0, 0x3f, 0x00, 0x01,
0xf0, 0x00, 0x1e, 0x00, 0x03, 0xe0, 0x00, 0x3c, 0x00, 0x07, 0x80, 0x00, 0xf0, 0x00, 0x1e, 0x00,
0x03, 0xc0, 0x00, 0xf8, 0x00, 0x1e, 0x80, 0x07, 0xde, 0x03, 0xf3, 0xff, 0xfc, 0x7f, 0xff, 0x03,
0xff, 0x00, 0x00, 0xff, 0x00, 0x3f, 0xfc, 0x07, 0xff, 0xc0, 0xf8, 0x1c, 0x1e, 0x00, 0x43, 0xc0,
0x00, 0x3c, 0x00, 0x07, 0x80, 0x00, 0x78, 0x00, 0x07, 0x80, 0x00, 0xf0, 0xfc, 0x0f, 0x3f, 0xf0,
0xf7, 0xff, 0x8f, 0xf0, 0x7c, 0xfe, 0x03, 0xef, 0xc0, 0x1e, 0xfc, 0x01, 0xff, 0x80, 0x0f, 0xf8,
0x00, 0xff, 0x80, 0x0f, 0x78, 0x00, 0xf7, 0x80, 0x0f, 0x78, 0x00, 0xf7, 0xc0, 0x1e, 0x3c, 0x01,
0xe3, 0xe0, 0x3e, 0x1f, 0x07, 0xc0, 0xff, 0xf8, 0x07, 0xff, 0x00, 0x1f, 0xc0, 0xff, 0xff, 0xff,
0xff, 0xff, 0xff, 0xff, 0x80, 0x01, 0xe0, 0x00, 0x3c, 0x00, 0x0f, 0x00, 0x01, 0xe0, 0x00, 0x7c,
0x00, 0x0f, 0x00, 0x01, 0xe0, 0x00, 0x7c, 0x00, 0x0f, 0x00, 0x03, 0xe0, 0x00, 0x78, 0x00, 0x0f,
0x00, 0x03, 0xe0, 0x00, 0x78, 0x00, 0x0f, 0x00, 0x03, 0xc0, 0x00, 0x78, 0x00, 0x1f, 0x00, 0x03,
0xc0, 0x00, 0x78, 0x00, 0x1e, 0x00, 0x03, 0xc0, 0x00, 0xf8, 0x00, 0x1e, 0x00, 0x03, 0xc0, 0x00,
0xf0, 0x00, 0x03, 0xfc, 0x00, 0xff, 0xf0, 0x1f, 0xff, 0x83, 0xf0, 0xfc, 0x3c, 0x03, 0xc7, 0xc0,
0x3e, 0x78, 0x01, 0xe7, 0x80, 0x1e, 0x78, 0x01, 0xe7, 0x80, 0x1e, 0x3c, 0x03, 0xc3, 0xc0, 0x3c,
0x1f, 0x0f, 0x80, 0xff, 0xf0, 0x03, 0xfc, 0x01, 0xff, 0xf8, 0x3e, 0x07, 0xc7, 0xc0, 0x3e, 0x78,
0x01, 0xef, 0x00, 0x0f, 0xf0, 0x00, 0xff, 0x00, 0x0f, 0xf0, 0x00, 0xff, 0x00, 0x0f, 0xf8, 0x01,
0xf7, 0xc0, 0x3e, 0x7e, 0x07, 0xe3, 0xff, 0xfc, 0x0f, 0xff, 0x00, 0x3f, 0xc0, 0x03, 0xf8, 0x00,
0xff, 0xe0, 0x1f, 0xff, 0x03, 0xe0, 0xf8, 0x7c, 0x07, 0xc7, 0x80, 0x3c, 0x78, 0x03, 0xef, 0x00,
0x1e, 0xf0, 0x01, 0xef, 0x00, 0x1e, 0xf0, 0x01, 0xff, 0x00, 0x1f, 0xf0, 0x01, 0xff, 0x80, 0x3f,
0x78, 0x03, 0xf7, 0xc0, 0x7f, 0x3e, 0x0f, 0xf1, 0xff, 0xef, 0x0f, 0xfc, 0xf0, 0x3f, 0x0f, 0x00,
0x01, 0xe0, 0x00, 0x1e, 0x00, 0x01, 0xe0, 0x00, 0x3c, 0x00, 0x03, 0xc2, 0x00, 0x78, 0x38, 0x1f,
0x03, 0xff, 0xe0, 0x3f, 0xfc, 0x00, 0xff, 0x00, 0xff, 0xff, 0xf0, 0x00, 0x00, 0x00, 0x00, 0x00,
0xff, 0xff, 0xf0, 0x3c, 0xf3, 0xcf, 0x3c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3c,
0xf3, 0xcf, 0x3d, 0xe7, 0x9c, 0xf3, 0x80, 0x00, 0x00, 0x00, 0x80, 0x00, 0x03, 0xc0, 0x00, 0x0f,
0xe0, 0x00, 0x3f, 0xe0, 0x00, 0xff, 0x80, 0x01, 0xff, 0x00, 0x07, 0xfc, 0x00, 0x1f, 0xf0, 0x00,
0x7f, 0xc0, 0x00, 0x7f, 0x00, 0x00, 0x3e, 0x00, 0x00, 0x1f, 0xc0, 0x00, 0x07, 0xfc, 0x00, 0x00,
0x7f, 0xc0, 0x00, 0x07, 0xfc, 0x00, 0x00, 0x7f, 0x80, 0x00, 0x0f, 0xf8, 0x00, 0x00, 0xff, 0x80,
0x00, 0x0f, 0xe0, 0x00, 0x00, 0xf0, 0x00, 0x00, 0x08, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
0xff, 0xff, 0xe0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
0x00, 0x00, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe0, 0x80, 0x00, 0x00, 0x78,
0x00, 0x00, 0x3f, 0x80, 0x00, 0x0f, 0xf8, 0x00, 0x00, 0xff, 0x80, 0x00, 0x1f, 0xf0, 0x00, 0x01,
0xff, 0x00, 0x00, 0x1f, 0xf0, 0x00, 0x01, 0xff, 0x00, 0x00, 0x1f, 0xc0, 0x00, 0x03, 0xe0, 0x00,
0x07, 0xf0, 0x00, 0x1f, 0xf0, 0x00, 0x7f, 0xc0, 0x01, 0xff, 0x00, 0x03, 0xfc, 0x00, 0x0f, 0xf8,
0x00, 0x3f, 0xe0, 0x00, 0x3f, 0x80, 0x00, 0x1e, 0x00, 0x00, 0x08, 0x00, 0x00, 0x00, 0x0f, 0xe0,
0x7f, 0xf8, 0xff, 0xfc, 0xf8, 0x3e, 0xc0, 0x1f, 0x80, 0x0f, 0x00, 0x0f, 0x00, 0x0f, 0x00, 0x0f,
0x00, 0x1e, 0x00, 0x3e, 0x00, 0x7c, 0x00, 0xf8, 0x01, 0xf0, 0x03, 0xe0, 0x03, 0xc0, 0x07, 0x80,
0x07, 0x80, 0x07, 0x80, 0x07, 0x80, 0x07, 0x80, 0x07, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
0x07, 0x80, 0x07, 0x80, 0x07, 0x80, 0x07, 0x80, 0x07, 0x80, 0x00, 0x03, 0xfc, 0x00, 0x00, 0x03,
0xff, 0xf0, 0x00, 0x01, 0xff, 0xff, 0x80, 0x00, 0xfe, 0x01, 0xfc, 0x00, 0x3e, 0x00, 0x07, 0xc0,
0x0f, 0x80, 0x00, 0x3c, 0x03, 0xc0, 0x00, 0x03, 0xc0, 0xf0, 0x00, 0x00, 0x3c, 0x1c, 0x00, 0x00,
0x03, 0x87, 0x00, 0x7c, 0x38, 0x79, 0xe0, 0x3f, 0xe7, 0x07, 0x38, 0x0f, 0xfe, 0xe0, 0xe7, 0x03,
0xe0, 0xfc, 0x0f, 0xe0, 0x78, 0x0f, 0x81, 0xf8, 0x1e, 0x00, 0xf0, 0x3f, 0x03, 0x80, 0x0e, 0x07,
0xe0, 0x70, 0x01, 0xc0, 0xfc, 0x0e, 0x00, 0x38, 0x1f, 0x81, 0xc0, 0x07, 0x07, 0x70, 0x38, 0x00,
0xe0, 0xee, 0x07, 0x80, 0x3c, 0x3d, 0xe0, 0x70, 0x07, 0x8f, 0x1c, 0x0f, 0x83, 0xf3, 0xc3, 0x80,
0xff, 0xef, 0xf0, 0x78, 0x0f, 0xf9, 0xfc, 0x07, 0x00, 0x7e, 0x3c, 0x00, 0xf0, 0x00, 0x00, 0x00,
0x0f, 0x00, 0x00, 0x00, 0x00, 0xf0, 0x00, 0x00, 0x00, 0x0f, 0x00, 0x00, 0x20, 0x00, 0xf8, 0x00,
0x1e, 0x00, 0x0f, 0xe0, 0x1f, 0x80, 0x00, 0xff, 0xff, 0xe0, 0x00, 0x07, 0xff, 0xf0, 0x00, 0x00,
0x1f, 0xf0, 0x00, 0x00, 0x00, 0x1f, 0x00, 0x00, 0x03, 0xe0, 0x00, 0x00, 0xfe, 0x00, 0x00, 0x1f,
0xc0, 0x00, 0x03, 0xfc, 0x00, 0x00, 0xff, 0x80, 0x00, 0x1e, 0xf0, 0x00, 0x07, 0xdf, 0x00, 0x00,
0xf1, 0xe0, 0x00, 0x1e, 0x3c, 0x00, 0x07, 0x83, 0xc0, 0x00, 0xf0, 0x78, 0x00, 0x3e, 0x0f, 0x80,
0x07, 0x80, 0xf0, 0x00, 0xf0, 0x1e, 0x00, 0x3e, 0x03, 0xe0, 0x07, 0x80, 0x3c, 0x01, 0xf0, 0x07,
0xc0, 0x3f, 0xff, 0xf8, 0x07, 0xff, 0xff, 0x01, 0xff, 0xff, 0xf0, 0x3c, 0x00, 0x1e, 0x07, 0x80,
0x03, 0xc1, 0xe0, 0x00, 0x3c, 0x3c, 0x00, 0x07, 0x8f, 0x80, 0x00, 0xf9, 0xe0, 0x00, 0x0f, 0x3c,
0x00, 0x01, 0xef, 0x00, 0x00, 0x1e, 0xff, 0xfe, 0x07, 0xff, 0xfc, 0x3f, 0xff, 0xf1, 0xe0, 0x0f,
0xcf, 0x00, 0x1e, 0x78, 0x00, 0x7b, 0xc0, 0x03, 0xde, 0x00, 0x1e, 0xf0, 0x00, 0xf7, 0x80, 0x07,
0xbc, 0x00, 0x79, 0xe0, 0x0f, 0xcf, 0xff, 0xf8, 0x7f, 0xff, 0x83, 0xff, 0xff, 0x1e, 0x00, 0x7c,
0xf0, 0x00, 0xf7, 0x80, 0x07, 0xbc, 0x00, 0x1f, 0xe0, 0x00, 0xff, 0x00, 0x07, 0xf8, 0x00, 0x3f,
0xc0, 0x01, 0xfe, 0x00, 0x1f, 0xf0, 0x00, 0xf7, 0x80, 0x1f, 0xbf, 0xff, 0xf9, 0xff, 0xff, 0x0f,
0xff, 0xe0, 0x00, 0x00, 0x3f, 0xc0, 0x00, 0xff, 0xf8, 0x03, 0xff, 0xfe, 0x07, 0xe0, 0x3f, 0x0f,
0x80, 0x07, 0x1e, 0x00, 0x03, 0x3c, 0x00, 0x01, 0x3c, 0x00, 0x00, 0x78, 0x00, 0x00, 0x78, 0x00,
0x00, 0x78, 0x00, 0x00, 0xf0, 0x00, 0x00, 0xf0, 0x00, 0x00, 0xf0, 0x00, 0x00, 0xf0, 0x00, 0x00,
0xf0, 0x00, 0x00, 0xf0, 0x00, 0x00, 0xf0, 0x00, 0x00, 0xf0, 0x00, 0x00, 0x78, 0x00, 0x00, 0x78,
0x00, 0x00, 0x78, 0x00, 0x00, 0x3c, 0x00, 0x00, 0x3c, 0x00, 0x01, 0x1e, 0x00, 0x03, 0x0f, 0x80,
0x07, 0x07, 0xe0, 0x3f, 0x03, 0xff, 0xfe, 0x00, 0xff, 0xf8, 0x00, 0x3f, 0xc0, 0xff, 0xfc, 0x00,
0x7f, 0xff, 0xe0, 0x3f, 0xff, 0xfc, 0x1e, 0x00, 0xff, 0x0f, 0x00, 0x0f, 0xc7, 0x80, 0x01, 0xf3,
0xc0, 0x00, 0x79, 0xe0, 0x00, 0x3e, 0xf0, 0x00, 0x0f, 0x78, 0x00, 0x07, 0xbc, 0x00, 0x01, 0xfe,
0x00, 0x00, 0xff, 0x00, 0x00, 0x7f, 0x80, 0x00, 0x3f, 0xc0, 0x00, 0x1f, 0xe0, 0x00, 0x0f, 0xf0,
0x00, 0x07, 0xf8, 0x00, 0x03, 0xfc, 0x00, 0x01, 0xfe, 0x00, 0x01, 0xef, 0x00, 0x00, 0xf7, 0x80,
0x00, 0xfb, 0xc0, 0x00, 0x79, 0xe0, 0x00, 0x7c, 0xf0, 0x00, 0xfc, 0x78, 0x03, 0xfc, 0x3f, 0xff,
0xfc, 0x1f, 0xff, 0xf8, 0x0f, 0xff, 0xc0, 0x00, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
0x00, 0x0f, 0x00, 0x01, 0xe0, 0x00, 0x3c, 0x00, 0x07, 0x80, 0x00, 0xf0, 0x00, 0x1e, 0x00, 0x03,
0xc0, 0x00, 0x78, 0x00, 0x0f, 0xff, 0xfd, 0xff, 0xff, 0xbf, 0xff, 0xf7, 0x80, 0x00, 0xf0, 0x00,
0x1e, 0x00, 0x03, 0xc0, 0x00, 0x78, 0x00, 0x0f, 0x00, 0x01, 0xe0, 0x00, 0x3c, 0x00, 0x07, 0x80,
0x00, 0xf0, 0x00, 0x1e, 0x00, 0x03, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff,
0xff, 0xff, 0xff, 0xfe, 0x00, 0x0f, 0x00, 0x07, 0x80, 0x03, 0xc0, 0x01, 0xe0, 0x00, 0xf0, 0x00,
0x78, 0x00, 0x3c, 0x00, 0x1e, 0x00, 0x0f, 0xff, 0xf7, 0xff, 0xfb, 0xff, 0xfd, 0xe0, 0x00, 0xf0,
0x00, 0x78, 0x00, 0x3c, 0x00, 0x1e, 0x00, 0x0f, 0x00, 0x07, 0x80, 0x03, 0xc0, 0x01, 0xe0, 0x00,
0xf0, 0x00, 0x78, 0x00, 0x3c, 0x00, 0x1e, 0x00, 0x0f, 0x00, 0x00, 0x00, 0x3f, 0xc0, 0x00, 0xff,
0xfc, 0x01, 0xff, 0xff, 0x81, 0xfc, 0x07, 0xe1, 0xf0, 0x00, 0xf0, 0xf0, 0x00, 0x18, 0xf0, 0x00,
0x04, 0xf8, 0x00, 0x00, 0x78, 0x00, 0x00, 0x3c, 0x00, 0x00, 0x1e, 0x00, 0x00, 0x1e, 0x00, 0x00,
0x0f, 0x00, 0x00, 0x07, 0x80, 0x00, 0x03, 0xc0, 0x07, 0xff, 0xe0, 0x03, 0xff, 0xf0, 0x01, 0xff,
0xf8, 0x00, 0x03, 0xfc, 0x00, 0x01, 0xef, 0x00, 0x00, 0xf7, 0x80, 0x00, 0x7b, 0xc0, 0x00, 0x3d,
0xf0, 0x00, 0x1e, 0x78, 0x00, 0x0f, 0x1e, 0x00, 0x07, 0x8f, 0x80, 0x03, 0xc3, 0xf8, 0x0f, 0xe0,
0x7f, 0xff, 0xe0, 0x1f, 0xff, 0xc0, 0x01, 0xff, 0x00, 0xf0, 0x00, 0x3f, 0xc0, 0x00, 0xff, 0x00,
0x03, 0xfc, 0x00, 0x0f, 0xf0, 0x00, 0x3f, 0xc0, 0x00, 0xff, 0x00, 0x03, 0xfc, 0x00, 0x0f, 0xf0,
0x00, 0x3f, 0xc0, 0x00, 0xff, 0x00, 0x03, 0xfc, 0x00, 0x0f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
0xff, 0xff, 0xfc, 0x00, 0x0f, 0xf0, 0x00, 0x3f, 0xc0, 0x00, 0xff, 0x00, 0x03, 0xfc, 0x00, 0x0f,
0xf0, 0x00, 0x3f, 0xc0, 0x00, 0xff, 0x00, 0x03, 0xfc, 0x00, 0x0f, 0xf0, 0x00, 0x3f, 0xc0, 0x00,
0xff, 0x00, 0x03, 0xfc, 0x00, 0x0f, 0xf0, 0x00, 0x3c, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x03, 0xc0, 0xf0, 0x3c, 0x0f, 0x03, 0xc0, 0xf0,
0x3c, 0x0f, 0x03, 0xc0, 0xf0, 0x3c, 0x0f, 0x03, 0xc0, 0xf0, 0x3c, 0x0f, 0x03, 0xc0, 0xf0, 0x3c,
0x0f, 0x03, 0xc0, 0xf0, 0x3c, 0x0f, 0x03, 0xc0, 0xf0, 0x3c, 0x0f, 0x03, 0xc0, 0xf0, 0x3c, 0x1f,
0x07, 0x83, 0xef, 0xf3, 0xf8, 0xf8, 0x00, 0xf0, 0x00, 0xf9, 0xe0, 0x03, 0xe3, 0xc0, 0x0f, 0x87,
0x80, 0x7e, 0x0f, 0x01, 0xf8, 0x1e, 0x07, 0xe0, 0x3c, 0x1f, 0x80, 0x78, 0x7e, 0x00, 0xf1, 0xf8,
0x01, 0xe7, 0xe0, 0x03, 0xdf, 0x80, 0x07, 0xfe, 0x00, 0x0f, 0xf8, 0x00, 0x1f, 0xe0, 0x00, 0x3f,
0xe0, 0x00, 0x7f, 0xe0, 0x00, 0xf7, 0xe0, 0x01, 0xe7, 0xe0, 0x03, 0xc7, 0xe0, 0x07, 0x87, 0xe0,
0x0f, 0x07, 0xe0, 0x1e, 0x07, 0xe0, 0x3c, 0x07, 0xe0, 0x78, 0x07, 0xe0, 0xf0, 0x07, 0xe1, 0xe0,
0x07, 0xe3, 0xc0, 0x07, 0xe7, 0x80, 0x07, 0xef, 0x00, 0x07, 0xe0, 0xf0, 0x00, 0x3c, 0x00, 0x0f,
0x00, 0x03, 0xc0, 0x00, 0xf0, 0x00, 0x3c, 0x00, 0x0f, 0x00, 0x03, 0xc0, 0x00, 0xf0, 0x00, 0x3c,
0x00, 0x0f, 0x00, 0x03, 0xc0, 0x00, 0xf0, 0x00, 0x3c, 0x00, 0x0f, 0x00, 0x03, 0xc0, 0x00, 0xf0,
0x00, 0x3c, 0x00, 0x0f, 0x00, 0x03, 0xc0, 0x00, 0xf0, 0x00, 0x3c, 0x00, 0x0f, 0x00, 0x03, 0xc0,
0x00, 0xf0, 0x00, 0x3c, 0x00, 0x0f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc0, 0xfe, 0x00, 0x0f,
0xff, 0xe0, 0x03, 0xff, 0xfc, 0x00, 0x7f, 0xff, 0x80, 0x0f, 0xff, 0xf8, 0x03, 0xff, 0xef, 0x00,
0x7b, 0xfd, 0xe0, 0x0f, 0x7f, 0x9e, 0x03, 0xcf, 0xf3, 0xc0, 0x79, 0xfe, 0x7c, 0x1f, 0x3f, 0xc7,
0x83, 0xc7, 0xf8, 0xf0, 0x78, 0xff, 0x1f, 0x1f, 0x1f, 0xe1, 0xe3, 0xc3, 0xfc, 0x3e, 0xf8, 0x7f,
0x83, 0xde, 0x0f, 0xf0, 0x7f, 0xc1, 0xfe, 0x0f, 0xf8, 0x3f, 0xc0, 0xfe, 0x07, 0xf8, 0x1f, 0xc0,
0xff, 0x01, 0xf0, 0x1f, 0xe0, 0x3e, 0x03, 0xfc, 0x07, 0xc0, 0x7f, 0x80, 0x00, 0x0f, 0xf0, 0x00,
0x01, 0xfe, 0x00, 0x00, 0x3f, 0xc0, 0x00, 0x07, 0xf8, 0x00, 0x00, 0xff, 0x00, 0x00, 0x1e, 0xfc,
0x00, 0x3f, 0xf0, 0x00, 0xff, 0xe0, 0x03, 0xff, 0x80, 0x0f, 0xff, 0x00, 0x3f, 0xfc, 0x00, 0xff,
0xf8, 0x03, 0xfd, 0xf0, 0x0f, 0xf7, 0xc0, 0x3f, 0xcf, 0x80, 0xff, 0x1e, 0x03, 0xfc, 0x7c, 0x0f,
0xf0, 0xf0, 0x3f, 0xc3, 0xe0, 0xff, 0x07, 0x83, 0xfc, 0x1f, 0x0f, 0xf0, 0x3c, 0x3f, 0xc0, 0xf8,
0xff, 0x01, 0xf3, 0xfc, 0x07, 0xcf, 0xf0, 0x0f, 0xbf, 0xc0, 0x1e, 0xff, 0x00, 0x7f, 0xfc, 0x00,
0xff, 0xf0, 0x03, 0xff, 0xc0, 0x07, 0xff, 0x00, 0x1f, 0xfc, 0x00, 0x3f, 0xf0, 0x00, 0xfc, 0x00,
0x3f, 0x80, 0x00, 0x3f, 0xfe, 0x00, 0x1f, 0xff, 0xf0, 0x07, 0xf0, 0x7f, 0x01, 0xf0, 0x01, 0xf0,
0x3c, 0x00, 0x1e, 0x0f, 0x00, 0x01, 0xe1, 0xe0, 0x00, 0x3c, 0x78, 0x00, 0x03, 0xcf, 0x00, 0x00,
0x79, 0xe0, 0x00, 0x0f, 0x78, 0x00, 0x00, 0xff, 0x00, 0x00, 0x1f, 0xe0, 0x00, 0x03, 0xfc, 0x00,
0x00, 0x7f, 0x80, 0x00, 0x0f, 0xf0, 0x00, 0x01, 0xfe, 0x00, 0x00, 0x3f, 0xc0, 0x00, 0x07, 0xbc,
0x00, 0x01, 0xe7, 0x80, 0x00, 0x3c, 0xf0, 0x00, 0x07, 0x8f, 0x00, 0x01, 0xe1, 0xe0, 0x00, 0x3c,
0x1e, 0x00, 0x0f, 0x03, 0xe0, 0x03, 0xe0, 0x3f, 0x81, 0xf8, 0x03, 0xff, 0xfe, 0x00, 0x1f, 0xff,
0x00, 0x00, 0x7f, 0x00, 0x00, 0xff, 0xf8, 0x1f, 0xff, 0xc3, 0xff, 0xfe, 0x78, 0x07, 0xef, 0x00,
0x7d, 0xe0, 0x07, 0xfc, 0x00, 0x7f, 0x80, 0x0f, 0xf0, 0x01, 0xfe, 0x00, 0x3f, 0xc0, 0x07, 0xf8,
0x01, 0xff, 0x00, 0x7d, 0xe0, 0x1f, 0xbf, 0xff, 0xe7, 0xff, 0xf8, 0xff, 0xf8, 0x1e, 0x00, 0x03,
0xc0, 0x00, 0x78, 0x00, 0x0f, 0x00, 0x01, 0xe0, 0x00, 0x3c, 0x00, 0x07, 0x80, 0x00, 0xf0, 0x00,
0x1e, 0x00, 0x03, 0xc0, 0x00, 0x78, 0x00, 0x0f, 0x00, 0x00, 0x00, 0x3f, 0x80, 0x00, 0x3f, 0xfe,
0x00, 0x1f, 0xff, 0xf0, 0x07, 0xf0, 0x7f, 0x01, 0xf0, 0x01, 0xf0, 0x3c, 0x00, 0x1e, 0x0f, 0x00,
0x01, 0xe1, 0xe0, 0x00, 0x3c, 0x78, 0x00, 0x03, 0xcf, 0x00, 0x00, 0x79, 0xe0, 0x00, 0x0f, 0x78,
0x00, 0x00, 0xff, 0x00, 0x00, 0x1f, 0xe0, 0x00, 0x03, 0xfc, 0x00, 0x00, 0x7f, 0x80, 0x00, 0x0f,
0xf0, 0x00, 0x01, 0xfe, 0x00, 0x00, 0x3f, 0xc0, 0x00, 0x07, 0xbc, 0x00, 0x01, 0xf7, 0x80, 0x00,
0x3c, 0xf0, 0x00, 0x07, 0x8f, 0x00, 0x01, 0xf1, 0xe0, 0x00, 0x3c, 0x1e, 0x00, 0x0f, 0x03, 0xe0,
0x03, 0xe0, 0x3f, 0x81, 0xf8, 0x03, 0xff, 0xfe, 0x00, 0x1f, 0xff, 0x00, 0x00, 0x7f, 0xe0, 0x00,
0x00, 0x7c, 0x00, 0x00, 0x07, 0xc0, 0x00, 0x00, 0x7c, 0x00, 0x00, 0x07, 0xc0, 0x00, 0x00, 0x7c,
0x00, 0xff, 0xf8, 0x01, 0xff, 0xfe, 0x03, 0xff, 0xfe, 0x07, 0x80, 0x7e, 0x0f, 0x00, 0x3c, 0x1e,
0x00, 0x7c, 0x3c, 0x00, 0x78, 0x78, 0x00, 0xf0, 0xf0, 0x01, 0xe1, 0xe0, 0x03, 0xc3, 0xc0, 0x07,
0x87, 0x80, 0x1f, 0x0f, 0x00, 0x3c, 0x1e, 0x01, 0xf0, 0x3f, 0xff, 0xe0, 0x7f, 0xff, 0x00, 0xff,
0xfe, 0x01, 0xe0, 0x3e, 0x03, 0xc0, 0x3e, 0x07, 0x80, 0x3e, 0x0f, 0x00, 0x3c, 0x1e, 0x00, 0x3c,
0x3c, 0x00, 0x7c, 0x78, 0x00, 0x78, 0xf0, 0x00, 0xf9, 0xe0, 0x00, 0xf3, 0xc0, 0x01, 0xf7, 0x80,
0x01, 0xef, 0x00, 0x03, 0xe0, 0x03, 0xfe, 0x00, 0x3f, 0xff, 0x01, 0xff, 0xfe, 0x0f, 0x80, 0xf8,
0x7c, 0x00, 0xe3, 0xe0, 0x00, 0x8f, 0x00, 0x00, 0x3c, 0x00, 0x00, 0xf0, 0x00, 0x03, 0xc0, 0x00,
0x0f, 0x80, 0x00, 0x1f, 0x00, 0x00, 0x7f, 0xc0, 0x00, 0xff, 0xf0, 0x01, 0xff, 0xf8, 0x01, 0xff,
0xf0, 0x00, 0x7f, 0xe0, 0x00, 0x0f, 0xc0, 0x00, 0x1f, 0x00, 0x00, 0x3e, 0x00, 0x00, 0x78, 0x00,
0x01, 0xe0, 0x00, 0x07, 0x80, 0x00, 0x1e, 0x80, 0x00, 0xfb, 0x80, 0x07, 0xcf, 0xc0, 0x7e, 0x3f,
0xff, 0xf0, 0x7f, 0xff, 0x80, 0x1f, 0xf0, 0x00, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
0xff, 0x00, 0x3c, 0x00, 0x00, 0x3c, 0x00, 0x00, 0x3c, 0x00, 0x00, 0x3c, 0x00, 0x00, 0x3c, 0x00,
0x00, 0x3c, 0x00, 0x00, 0x3c, 0x00, 0x00, 0x3c, 0x00, 0x00, 0x3c, 0x00, 0x00, 0x3c, 0x00, 0x00,
0x3c, 0x00, 0x00, 0x3c, 0x00, 0x00, 0x3c, 0x00, 0x00, 0x3c, 0x00, 0x00, 0x3c, 0x00, 0x00, 0x3c,
0x00, 0x00, 0x3c, 0x00, 0x00, 0x3c, 0x00, 0x00, 0x3c, 0x00, 0x00, 0x3c, 0x00, 0x00, 0x3c, 0x00,
0x00, 0x3c, 0x00, 0x00, 0x3c, 0x00, 0x00, 0x3c, 0x00, 0x00, 0x3c, 0x00, 0x00, 0x3c, 0x00, 0xf0,
0x00, 0x3f, 0xc0, 0x00, 0xff, 0x00, 0x03, 0xfc, 0x00, 0x0f, 0xf0, 0x00, 0x3f, 0xc0, 0x00, 0xff,
0x00, 0x03, 0xfc, 0x00, 0x0f, 0xf0, 0x00, 0x3f, 0xc0, 0x00, 0xff, 0x00, 0x03, 0xfc, 0x00, 0x0f,
0xf0, 0x00, 0x3f, 0xc0, 0x00, 0xff, 0x00, 0x03, 0xfc, 0x00, 0x0f, 0xf0, 0x00, 0x3f, 0xc0, 0x00,
0xff, 0x00, 0x03, 0xfc, 0x00, 0x0f, 0xf0, 0x00, 0x3f, 0xc0, 0x00, 0xf7, 0x80, 0x07, 0x9e, 0x00,
0x1e, 0x3c, 0x00, 0xf0, 0xfc, 0x0f, 0xc1, 0xff, 0xfe, 0x01, 0xff, 0xe0, 0x01, 0xfe, 0x00, 0xf0,
0x00, 0x01, 0xef, 0x00, 0x00, 0x79, 0xe0, 0x00, 0x0f, 0x3e, 0x00, 0x03, 0xe3, 0xc0, 0x00, 0x78,
0x78, 0x00, 0x0f, 0x07, 0x80, 0x03, 0xc0, 0xf0, 0x00, 0x78, 0x1f, 0x00, 0x1f, 0x01, 0xe0, 0x03,
0xc0, 0x3c, 0x00, 0x78, 0x07, 0xc0, 0x1f, 0x00, 0x78, 0x03, 0xc0, 0x0f, 0x80, 0xf8, 0x00, 0xf0,
0x1e, 0x00, 0x1e, 0x03, 0xc0, 0x03, 0xe0, 0xf8, 0x00, 0x3c, 0x1e, 0x00, 0x07, 0x83, 0xc0, 0x00,
0x78, 0xf0, 0x00, 0x0f, 0x1e, 0x00, 0x01, 0xf7, 0xc0, 0x00, 0x1e, 0xf0, 0x00, 0x03, 0xfe, 0x00,
0x00, 0x3f, 0xc0, 0x00, 0x07, 0xf0, 0x00, 0x00, 0xfe, 0x00, 0x00, 0x0f, 0x80, 0x00, 0x01, 0xf0,
0x00, 0xf0, 0x00, 0xf8, 0x00, 0x7f, 0xc0, 0x07, 0xc0, 0x07, 0xde, 0x00, 0x3e, 0x00, 0x3c, 0xf0,
0x01, 0xf8, 0x01, 0xe7, 0x80, 0x1f, 0xc0, 0x0f, 0x3e, 0x00, 0xee, 0x00, 0xf8, 0xf0, 0x07, 0x70,
0x07, 0x87, 0x80, 0x3b, 0xc0, 0x3c, 0x3c, 0x03, 0x8e, 0x01, 0xe1, 0xf0, 0x1c, 0x70, 0x1f, 0x07,
0x80, 0xe3, 0x80, 0xf0, 0x3c, 0x07, 0x1c, 0x07, 0x81, 0xe0, 0x70, 0x70, 0x3c, 0x0f, 0x83, 0x83,
0x83, 0xe0, 0x3c, 0x1c, 0x1c, 0x1e, 0x01, 0xe0, 0xe0, 0xe0, 0xf0, 0x0f, 0x0e, 0x03, 0x87, 0x80,
0x7c, 0x70, 0x1c, 0x7c, 0x01, 0xe3, 0x80, 0xe3, 0xc0, 0x0f, 0x1c, 0x07, 0x1e, 0x00, 0x79, 0xc0,
0x1c, 0xf0, 0x03, 0xee, 0x00, 0xef, 0x80, 0x0f, 0x70, 0x07, 0x78, 0x00, 0x7b, 0x80, 0x3f, 0xc0,
0x03, 0xf8, 0x00, 0xfe, 0x00, 0x1f, 0xc0, 0x07, 0xf0, 0x00, 0x7e, 0x00, 0x3f, 0x00, 0x03, 0xf0,
0x01, 0xf8, 0x00, 0x1f, 0x00, 0x07, 0xc0, 0x00, 0x7c, 0x00, 0x1f, 0x1f, 0x00, 0x1f, 0x07, 0x80,
0x0f, 0x03, 0xe0, 0x0f, 0x80, 0xf8, 0x0f, 0x80, 0x3c, 0x07, 0x80, 0x1f, 0x07, 0xc0, 0x07, 0xc7,
0xc0, 0x01, 0xe3, 0xc0, 0x00, 0xfb, 0xe0, 0x00, 0x3f, 0xe0, 0x00, 0x0f, 0xe0, 0x00, 0x07, 0xf0,
0x00, 0x01, 0xf0, 0x00, 0x00, 0xf8, 0x00, 0x00, 0xfe, 0x00, 0x00, 0xff, 0x80, 0x00, 0x7f, 0xc0,
0x00, 0x7d, 0xf0, 0x00, 0x7c, 0x7c, 0x00, 0x3c, 0x1e, 0x00, 0x3e, 0x0f, 0x80, 0x3e, 0x03, 0xe0,
0x1e, 0x00, 0xf0, 0x1f, 0x00, 0x7c, 0x1f, 0x00, 0x1f, 0x0f, 0x00, 0x0f, 0x8f, 0x80, 0x03, 0xef,
0x80, 0x00, 0xf8, 0xf8, 0x00, 0x1f, 0x7c, 0x00, 0x3e, 0x3c, 0x00, 0x3c, 0x3e, 0x00, 0x7c, 0x1f,
0x00, 0xf8, 0x0f, 0x00, 0xf0, 0x0f, 0x81, 0xf0, 0x07, 0xc3, 0xe0, 0x03, 0xc3, 0xc0, 0x03, 0xe7,
0xc0, 0x01, 0xff, 0x80, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0x7e, 0x00, 0x00, 0x3c, 0x00,
0x00, 0x3c, 0x00, 0x00, 0x3c, 0x00, 0x00, 0x3c, 0x00, 0x00, 0x3c, 0x00, 0x00, 0x3c, 0x00, 0x00,
0x3c, 0x00, 0x00, 0x3c, 0x00, 0x00, 0x3c, 0x00, 0x00, 0x3c, 0x00, 0x00, 0x3c, 0x00, 0x00, 0x3c,
0x00, 0x00, 0x3c, 0x00, 0x00, 0x3c, 0x00, 0x00, 0x3c, 0x00, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xfe,
0xff, 0xff, 0xfe, 0x00, 0x00, 0x7e, 0x00, 0x00, 0x7c, 0x00, 0x00, 0xf8, 0x00, 0x01, 0xf0, 0x00,
0x03, 0xf0, 0x00, 0x03, 0xe0, 0x00, 0x07, 0xc0, 0x00, 0x0f, 0x80, 0x00, 0x1f, 0x00, 0x00, 0x3f,
0x00, 0x00, 0x3e, 0x00, 0x00, 0x7c, 0x00, 0x00, 0xf8, 0x00, 0x01, 0xf0, 0x00, 0x01, 0xf0, 0x00,
0x03, 0xe0, 0x00, 0x07, 0xc0, 0x00, 0x0f, 0x80, 0x00, 0x1f, 0x80, 0x00, 0x1f, 0x00, 0x00, 0x3e,
0x00, 0x00, 0x7c, 0x00, 0x00, 0xf8, 0x00, 0x00, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
0xff, 0xff, 0xff, 0xff, 0xfe, 0x0f, 0x07, 0x83, 0xc1, 0xe0, 0xf0, 0x78, 0x3c, 0x1e, 0x0f, 0x07,
0x83, 0xc1, 0xe0, 0xf0, 0x78, 0x3c, 0x1e, 0x0f, 0x07, 0x83, 0xc1, 0xe0, 0xf0, 0x78, 0x3c, 0x1e,
0x0f, 0x07, 0x83, 0xc1, 0xe0, 0xf0, 0x7f, 0xff, 0xff, 0xf0, 0xe0, 0x07, 0x80, 0x1c, 0x00, 0xe0,
0x07, 0x00, 0x1c, 0x00, 0xe0, 0x07, 0x00, 0x1c, 0x00, 0xe0, 0x07, 0x00, 0x3c, 0x00, 0xe0, 0x07,
0x00, 0x38, 0x00, 0xe0, 0x07, 0x00, 0x38, 0x00, 0xe0, 0x07, 0x00, 0x38, 0x01, 0xe0, 0x07, 0x00,
0x38, 0x01, 0xc0, 0x07, 0x00, 0x38, 0x01, 0xc0, 0x07, 0x00, 0x38, 0x01, 0xc0, 0x0f, 0x00, 0x38,
0xff, 0xff, 0xff, 0xe0, 0xf0, 0x78, 0x3c, 0x1e, 0x0f, 0x07, 0x83, 0xc1, 0xe0, 0xf0, 0x78, 0x3c,
0x1e, 0x0f, 0x07, 0x83, 0xc1, 0xe0, 0xf0, 0x78, 0x3c, 0x1e, 0x0f, 0x07, 0x83, 0xc1, 0xe0, 0xf0,
0x78, 0x3c, 0x1e, 0x0f, 0x07, 0xff, 0xff, 0xff, 0xf0, 0x00, 0x3e, 0x00, 0x00, 0x3f, 0x80, 0x00,
0x3f, 0xe0, 0x00, 0x3e, 0xf8, 0x00, 0x3e, 0x3e, 0x00, 0x3e, 0x0f, 0x80, 0x3e, 0x03, 0xe0, 0x3e,
0x00, 0xf8, 0x3e, 0x00, 0x3e, 0x3e, 0x00, 0x0f, 0xbc, 0x00, 0x01, 0xe0, 0xff, 0xff, 0xff, 0xff,
0xff, 0xff, 0xff, 0xf0, 0xf0, 0x3c, 0x0f, 0x03, 0x80, 0xe0, 0x38, 0x0e, 0x03, 0xf0, 0x07, 0xff,
0x83, 0xff, 0xf0, 0xe0, 0x7c, 0x20, 0x0f, 0x80, 0x01, 0xe0, 0x00, 0x3c, 0x00, 0x0f, 0x00, 0x03,
0xc1, 0xff, 0xf1, 0xff, 0xfc, 0xff, 0xff, 0x7e, 0x03, 0xfe, 0x00, 0xff, 0x00, 0x3f, 0xc0, 0x0f,
0xf0, 0x07, 0xfc, 0x01, 0xff, 0x80, 0xfd, 0xf0, 0xff, 0x7f, 0xfb, 0xcf, 0xfc, 0xf0, 0xfc, 0x3c,
0xf0, 0x00, 0x0f, 0x00, 0x00, 0xf0, 0x00, 0x0f, 0x00, 0x00, 0xf0, 0x00, 0x0f, 0x00, 0x00, 0xf0,
0x00, 0x0f, 0x0f, 0xc0, 0xf3, 0xff, 0x0f, 0x7f, 0xf8, 0xff, 0x0f, 0xcf, 0xc0, 0x3c, 0xf8, 0x01,
0xef, 0x80, 0x1e, 0xf8, 0x01, 0xff, 0x00, 0x0f, 0xf0, 0x00, 0xff, 0x00, 0x0f, 0xf0, 0x00, 0xff,
0x00, 0x0f, 0xf0, 0x00, 0xff, 0x00, 0x0f, 0xf8, 0x01, 0xff, 0x80, 0x1e, 0xf8, 0x01, 0xef, 0xc0,
0x3c, 0xff, 0x0f, 0xcf, 0x7f, 0xf8, 0xf3, 0xff, 0x0f, 0x0f, 0xc0, 0x01, 0xfc, 0x03, 0xff, 0x83,
0xff, 0xe3, 0xe0, 0x73, 0xe0, 0x0b, 0xe0, 0x01, 0xe0, 0x00, 0xf0, 0x00, 0xf0, 0x00, 0x78, 0x00,
0x3c, 0x00, 0x1e, 0x00, 0x0f, 0x00, 0x07, 0x80, 0x03, 0xc0, 0x00, 0xf0, 0x00, 0x78, 0x00, 0x3e,
0x00, 0x0f, 0x80, 0x23, 0xe0, 0x70, 0xff, 0xf8, 0x3f, 0xf8, 0x07, 0xf0, 0x00, 0x00, 0xf0, 0x00,
0x0f, 0x00, 0x00, 0xf0, 0x00, 0x0f, 0x00, 0x00, 0xf0, 0x00, 0x0f, 0x00, 0x00, 0xf0, 0x3f, 0x0f,
0x0f, 0xfc, 0xf1, 0xff, 0xef, 0x3f, 0x0f, 0xf3, 0xc0, 0x3f, 0x78, 0x01, 0xf7, 0x80, 0x1f, 0xf8,
0x01, 0xff, 0x00, 0x0f, 0xf0, 0x00, 0xff, 0x00, 0x0f, 0xf0, 0x00, 0xff, 0x00, 0x0f, 0xf0, 0x00,
0xff, 0x00, 0x0f, 0xf8, 0x01, 0xf7, 0x80, 0x1f, 0x78, 0x01, 0xf3, 0xc0, 0x3f, 0x3f, 0x0f, 0xf1,
0xff, 0xef, 0x0f, 0xfc, 0xf0, 0x3f, 0x0f, 0x01, 0xfc, 0x00, 0x7f, 0xf0, 0x0f, 0xff, 0x81, 0xf0,
0x7c, 0x3c, 0x03, 0xe7, 0x80, 0x1e, 0x78, 0x00, 0xe7, 0x00, 0x0f, 0xf0, 0x00, 0xff, 0xff, 0xff,
0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x00, 0x0f, 0x00, 0x00, 0xf0, 0x00, 0x07, 0x80, 0x00, 0x78,
0x00, 0x07, 0xc0, 0x00, 0x3e, 0x00, 0x21, 0xf8, 0x1e, 0x0f, 0xff, 0xe0, 0x7f, 0xfc, 0x01, 0xfe,
0x00, 0x01, 0xfc, 0x1f, 0xf0, 0xff, 0xc3, 0xc0, 0x1e, 0x00, 0x78, 0x01, 0xe0, 0x07, 0x80, 0xff,
0xfb, 0xff, 0xef, 0xff, 0x87, 0x80, 0x1e, 0x00, 0x78, 0x01, 0xe0, 0x07, 0x80, 0x1e, 0x00, 0x78,
0x01, 0xe0, 0x07, 0x80, 0x1e, 0x00, 0x78, 0x01, 0xe0, 0x07, 0x80, 0x1e, 0x00, 0x78, 0x01, 0xe0,
0x07, 0x80, 0x1e, 0x00, 0x78, 0x00, 0x03, 0xf0, 0x00, 0xff, 0xcf, 0x1f, 0xfe, 0xf3, 0xf0, 0xff,
0x3c, 0x03, 0xf7, 0x80, 0x1f, 0x78, 0x01, 0xff, 0x80, 0x1f, 0xf0, 0x00, 0xff, 0x00, 0x0f, 0xf0,
0x00, 0xff, 0x00, 0x0f, 0xf0, 0x00, 0xff, 0x00, 0x0f, 0xf0, 0x00, 0xff, 0x80, 0x1f, 0x78, 0x01,
0xf7, 0x80, 0x1f, 0x3c, 0x03, 0xf3, 0xf0, 0xff, 0x1f, 0xfe, 0xf0, 0xff, 0xcf, 0x03, 0xf0, 0xf0,
0x00, 0x0f, 0x00, 0x01, 0xe0, 0x00, 0x1e, 0x10, 0x03, 0xe1, 0xc0, 0xfc, 0x1f, 0xff, 0x81, 0xff,
0xf0, 0x07, 0xfc, 0x00, 0xf0, 0x00, 0x3c, 0x00, 0x0f, 0x00, 0x03, 0xc0, 0x00, 0xf0, 0x00, 0x3c,
0x00, 0x0f, 0x00, 0x03, 0xc3, 0xf0, 0xf3, 0xfe, 0x3d, 0xff, 0xcf, 0xe0, 0xfb, 0xf0, 0x1e, 0xf8,
0x07, 0xfe, 0x00, 0xff, 0x00, 0x3f, 0xc0, 0x0f, 0xf0, 0x03, 0xfc, 0x00, 0xff, 0x00, 0x3f, 0xc0,
0x0f, 0xf0, 0x03, 0xfc, 0x00, 0xff, 0x00, 0x3f, 0xc0, 0x0f, 0xf0, 0x03, 0xfc, 0x00, 0xff, 0x00,
0x3f, 0xc0, 0x0f, 0xf0, 0x03, 0xfc, 0x00, 0xf0, 0xff, 0xff, 0xf0, 0x00, 0xff, 0xff, 0xff, 0xff,
0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x00, 0x00, 0x00, 0x0f,
0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f,
0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x0f, 0x1f, 0x1e, 0xfe, 0xfc, 0xf0, 0xf0, 0x00, 0x1e,
0x00, 0x03, 0xc0, 0x00, 0x78, 0x00, 0x0f, 0x00, 0x01, 0xe0, 0x00, 0x3c, 0x00, 0x07, 0x80, 0x00,
0xf0, 0x0f, 0xde, 0x03, 0xf3, 0xc0, 0xfc, 0x78, 0x3f, 0x0f, 0x0f, 0xc1, 0xe3, 0xf0, 0x3c, 0xfc,
0x07, 0xbf, 0x00, 0xff, 0xc0, 0x1f, 0xf0, 0x03, 0xfc, 0x00, 0x7f, 0xc0, 0x0f, 0xfc, 0x01, 0xef,
0xc0, 0x3c, 0xfc, 0x07, 0x8f, 0xc0, 0xf0, 0xfc, 0x1e, 0x0f, 0xc3, 0xc0, 0xfc, 0x78, 0x0f, 0xcf,
0x00, 0xfd, 0xe0, 0x0f, 0xc0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
0xff, 0xff, 0xff, 0xff, 0x00, 0xfc, 0x03, 0xf0, 0xf3, 0xfe, 0x0f, 0xf8, 0xf7, 0xff, 0x1f, 0xfc,
0xfe, 0x0f, 0xb8, 0x3e, 0xfc, 0x07, 0xf0, 0x1e, 0xf8, 0x07, 0xe0, 0x1f, 0xf8, 0x03, 0xe0, 0x0f,
0xf0, 0x03, 0xc0, 0x0f, 0xf0, 0x03, 0xc0, 0x0f, 0xf0, 0x03, 0xc0, 0x0f, 0xf0, 0x03, 0xc0, 0x0f,
0xf0, 0x03, 0xc0, 0x0f, 0xf0, 0x03, 0xc0, 0x0f, 0xf0, 0x03, 0xc0, 0x0f, 0xf0, 0x03, 0xc0, 0x0f,
0xf0, 0x03, 0xc0, 0x0f, 0xf0, 0x03, 0xc0, 0x0f, 0xf0, 0x03, 0xc0, 0x0f, 0xf0, 0x03, 0xc0, 0x0f,
0xf0, 0x03, 0xc0, 0x0f, 0xf0, 0x03, 0xc0, 0x0f, 0xf0, 0x03, 0xc0, 0x0f, 0xf0, 0x03, 0xc0, 0x0f,
0x00, 0xfc, 0x3c, 0xff, 0x8f, 0x7f, 0xf3, 0xf8, 0x3e, 0xfc, 0x07, 0xbe, 0x01, 0xff, 0x80, 0x3f,
0xc0, 0x0f, 0xf0, 0x03, 0xfc, 0x00, 0xff, 0x00, 0x3f, 0xc0, 0x0f, 0xf0, 0x03, 0xfc, 0x00, 0xff,
0x00, 0x3f, 0xc0, 0x0f, 0xf0, 0x03, 0xfc, 0x00, 0xff, 0x00, 0x3f, 0xc0, 0x0f, 0xf0, 0x03, 0xfc,
0x00, 0xff, 0x00, 0x3c, 0x01, 0xf8, 0x00, 0x7f, 0xe0, 0x1f, 0xff, 0x83, 0xf0, 0xfc, 0x3c, 0x03,
0xc7, 0x80, 0x3e, 0x78, 0x01, 0xef, 0x80, 0x1f, 0xf0, 0x00, 0xff, 0x00, 0x0f, 0xf0, 0x00, 0xff,
0x00, 0x0f, 0xf0, 0x00, 0xff, 0x00, 0x0f, 0xf0, 0x00, 0xff, 0x80, 0x1f, 0x78, 0x01, 0xe7, 0x80,
0x3e, 0x3c, 0x03, 0xc3, 0xf0, 0xfc, 0x1f, 0xff, 0x80, 0x7f, 0xe0, 0x01, 0xf8, 0x00, 0x00, 0xfc,
0x0f, 0x3f, 0xf0, 0xf7, 0xff, 0x8f, 0xf0, 0xfc, 0xfc, 0x03, 0xcf, 0x80, 0x1e, 0xf8, 0x01, 0xef,
0x80, 0x1f, 0xf0, 0x00, 0xff, 0x00, 0x0f, 0xf0, 0x00, 0xff, 0x00, 0x0f, 0xf0, 0x00, 0xff, 0x00,
0x0f, 0xf0, 0x00, 0xff, 0x80, 0x1f, 0xf8, 0x01, 0xef, 0x80, 0x1e, 0xfc, 0x03, 0xcf, 0xf0, 0xfc,
0xf7, 0xff, 0x8f, 0x3f, 0xf0, 0xf0, 0xfc, 0x0f, 0x00, 0x00, 0xf0, 0x00, 0x0f, 0x00, 0x00, 0xf0,
0x00, 0x0f, 0x00, 0x00, 0xf0, 0x00, 0x0f, 0x00, 0x00, 0xf0, 0x00, 0x00, 0x03, 0xf0, 0x00, 0xff,
0xcf, 0x1f, 0xfe, 0xf3, 0xf0, 0xff, 0x3c, 0x03, 0xf7, 0x80, 0x1f, 0x78, 0x01, 0xff, 0x80, 0x1f,
0xf0, 0x00, 0xff, 0x00, 0x0f, 0xf0, 0x00, 0xff, 0x00, 0x0f, 0xf0, 0x00, 0xff, 0x00, 0x0f, 0xf0,
0x00, 0xff, 0x80, 0x1f, 0x78, 0x01, 0xf7, 0x80, 0x1f, 0x3c, 0x03, 0xf3, 0xf0, 0xff, 0x1f, 0xfe,
0xf0, 0xff, 0xcf, 0x03, 0xf0, 0xf0, 0x00, 0x0f, 0x00, 0x00, 0xf0, 0x00, 0x0f, 0x00, 0x00, 0xf0,
0x00, 0x0f, 0x00, 0x00, 0xf0, 0x00, 0x0f, 0x00, 0x00, 0xf0, 0x00, 0xff, 0x9f, 0xfd, 0xff, 0xfe,
0x1f, 0xc0, 0x7c, 0x03, 0xe0, 0x1e, 0x00, 0xf0, 0x07, 0x80, 0x3c, 0x01, 0xe0, 0x0f, 0x00, 0x78,
0x03, 0xc0, 0x1e, 0x00, 0xf0, 0x07, 0x80, 0x3c, 0x01, 0xe0, 0x0f, 0x00, 0x78, 0x03, 0xc0, 0x00,
0x07, 0xf8, 0x0f, 0xff, 0x1f, 0xff, 0xcf, 0x80, 0xef, 0x80, 0x17, 0x80, 0x03, 0xc0, 0x01, 0xe0,
0x00, 0xf8, 0x00, 0x3e, 0x00, 0x1f, 0xf0, 0x03, 0xff, 0x00, 0x7f, 0xe0, 0x03, 0xf8, 0x00, 0x3e,
0x00, 0x0f, 0x00, 0x07, 0x80, 0x03, 0xe0, 0x03, 0xfe, 0x03, 0xef, 0xff, 0xe7, 0xff, 0xe0, 0xff,
0xc0, 0x1e, 0x00, 0x78, 0x01, 0xe0, 0x07, 0x80, 0x1e, 0x00, 0x78, 0x0f, 0xff, 0xff, 0xff, 0xff,
0xfc, 0x78, 0x01, 0xe0, 0x07, 0x80, 0x1e, 0x00, 0x78, 0x01, 0xe0, 0x07, 0x80, 0x1e, 0x00, 0x78,
0x01, 0xe0, 0x07, 0x80, 0x1e, 0x00, 0x78, 0x01, 0xe0, 0x07, 0x80, 0x0f, 0x00, 0x3f, 0xf0, 0x7f,
0xc0, 0x7f, 0x00, 0x00, 0x3c, 0x00, 0xff, 0x00, 0x3f, 0xc0, 0x0f, 0xf0, 0x03, 0xfc, 0x00, 0xff,
0x00, 0x3f, 0xc0, 0x0f, 0xf0, 0x03, 0xfc, 0x00, 0xff, 0x00, 0x3f, 0xc0, 0x0f, 0xf0, 0x03, 0xfc,
0x00, 0xff, 0x00, 0x3f, 0xc0, 0x0f, 0xf0, 0x07, 0xfe, 0x01, 0xf7, 0x80, 0xfd, 0xf0, 0x7f, 0x3f,
0xfb, 0xc7, 0xfc, 0xf0, 0xfc, 0x3c, 0xf0, 0x00, 0x7b, 0xc0, 0x07, 0x9e, 0x00, 0x3c, 0xf8, 0x03,
0xe3, 0xc0, 0x1e, 0x1e, 0x00, 0xf0, 0xf8, 0x0f, 0x83, 0xc0, 0x78, 0x1e, 0x03, 0xc0, 0xf8, 0x3e,
0x03, 0xc1, 0xe0, 0x1e, 0x0f, 0x00, 0x78, 0xf0, 0x03, 0xc7, 0x80, 0x1f, 0x7c, 0x00, 0x7b, 0xc0,
0x03, 0xfe, 0x00, 0x1f, 0xf0, 0x00, 0x7f, 0x00, 0x03, 0xf8, 0x00, 0x0f, 0x80, 0x00, 0x7c, 0x00,
0xf0, 0x0f, 0x80, 0x7f, 0xc0, 0x7c, 0x07, 0xde, 0x03, 0xe0, 0x3c, 0xf0, 0x1f, 0x01, 0xe7, 0x81,
0xfc, 0x0f, 0x3e, 0x0e, 0xe0, 0xf8, 0xf0, 0x77, 0x07, 0x87, 0x87, 0xbc, 0x3c, 0x3c, 0x38, 0xe1,
0xe1, 0xf1, 0xc7, 0x1f, 0x07, 0x8e, 0x38, 0xf0, 0x3c, 0xf1, 0xe7, 0x81, 0xe7, 0x07, 0x3c, 0x0f,
0xb8, 0x3b, 0xe0, 0x3d, 0xc1, 0xde, 0x01, 0xfe, 0x0f, 0xf0, 0x0f, 0xe0, 0x3f, 0x80, 0x7f, 0x01,
0xfc, 0x01, 0xf8, 0x0f, 0xc0, 0x0f, 0x80, 0x3e, 0x00, 0x7c, 0x01, 0xf0, 0x01, 0xe0, 0x0f, 0x00,
0x7c, 0x00, 0xf9, 0xf0, 0x0f, 0x87, 0xc0, 0xf8, 0x1f, 0x0f, 0x80, 0xf8, 0x7c, 0x03, 0xe7, 0xc0,
0x0f, 0xfc, 0x00, 0x3f, 0xc0, 0x01, 0xfe, 0x00, 0x07, 0xe0, 0x00, 0x1e, 0x00, 0x01, 0xf8, 0x00,
0x1f, 0xc0, 0x01, 0xff, 0x00, 0x0f, 0xfc, 0x00, 0xf9, 0xe0, 0x0f, 0x8f, 0x80, 0xf8, 0x3e, 0x07,
0xc0, 0xf8, 0x7c, 0x07, 0xc7, 0xc0, 0x1f, 0x7c, 0x00, 0x7c, 0xf8, 0x00, 0xfb, 0xc0, 0x07, 0x9e,
0x00, 0x3c, 0xf8, 0x03, 0xe3, 0xc0, 0x1e, 0x1f, 0x01, 0xf0, 0x78, 0x0f, 0x03, 0xc0, 0x78, 0x1f,
0x07, 0xc0, 0x78, 0x3c, 0x03, 0xe1, 0xe0, 0x0f, 0x1e, 0x00, 0x78, 0xf0, 0x03, 0xef, 0x80, 0x0f,
0x78, 0x00, 0x7f, 0xc0, 0x01, 0xfc, 0x00, 0x0f, 0xe0, 0x00, 0x3f, 0x00, 0x01, 0xf0, 0x00, 0x0f,
0x80, 0x00, 0x78, 0x00, 0x03, 0xc0, 0x00, 0x3e, 0x00, 0x01, 0xe0, 0x00, 0x1f, 0x00, 0x01, 0xf0,
0x00, 0x7f, 0x80, 0x03, 0xf8, 0x00, 0x1f, 0x00, 0x00, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,
0x00, 0x1f, 0x00, 0x0f, 0x80, 0x07, 0xc0, 0x03, 0xe0, 0x01, 0xf8, 0x00, 0xfc, 0x00, 0x3e, 0x00,
0x1f, 0x00, 0x0f, 0x80, 0x07, 0xc0, 0x03, 0xf0, 0x01, 0xf8, 0x00, 0x7c, 0x00, 0x3e, 0x00, 0x1f,
0x00, 0x0f, 0x80, 0x03, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x00, 0x3f, 0x00, 0xff, 0x01,
0xff, 0x01, 0xe0, 0x03, 0xc0, 0x03, 0xc0, 0x03, 0xc0, 0x03, 0xc0, 0x03, 0xc0, 0x03, 0xc0, 0x03,
0xc0, 0x03, 0xc0, 0x03, 0xc0, 0x03, 0xc0, 0x03, 0xc0, 0x07, 0xc0, 0x0f, 0x80, 0xff, 0x00, 0xfe,
0x00, 0xff, 0x00, 0x0f, 0x80, 0x07, 0xc0, 0x03, 0xc0, 0x03, 0xc0, 0x03, 0xc0, 0x03, 0xc0, 0x03,
0xc0, 0x03, 0xc0, 0x03, 0xc0, 0x03, 0xc0, 0x03, 0xc0, 0x03, 0xc0, 0x03, 0xc0, 0x01, 0xe0, 0x01,
0xff, 0x00, 0xff, 0x00, 0x3f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
0xff, 0xff, 0xff, 0xff, 0xfc, 0x00, 0xff, 0x00, 0xff, 0x80, 0x07, 0x80, 0x03, 0xc0, 0x03, 0xc0,
0x03, 0xc0, 0x03, 0xc0, 0x03, 0xc0, 0x03, 0xc0, 0x03, 0xc0, 0x03, 0xc0, 0x03, 0xc0, 0x03, 0xc0,
0x03, 0xc0, 0x03, 0xe0, 0x01, 0xf0, 0x00, 0xff, 0x00, 0x7f, 0x00, 0xff, 0x01, 0xf0, 0x03, 0xe0,
0x03, 0xc0, 0x03, 0xc0, 0x03, 0xc0, 0x03, 0xc0, 0x03, 0xc0, 0x03, 0xc0, 0x03, 0xc0, 0x03, 0xc0,
0x03, 0xc0, 0x03, 0xc0, 0x03, 0xc0, 0x07, 0x80, 0xff, 0x80, 0xff, 0x00, 0xfc, 0x00, 0x00, 0x00,
0x00, 0x07, 0xe0, 0x00, 0x4f, 0xfe, 0x00, 0xef, 0xff, 0xc1, 0xff, 0x03, 0xff, 0xf6, 0x00, 0x7f,
0xf2, 0x00, 0x07, 0xe0, 0x00, 0x00, 0x00, };

const GFXglyph DejaVu40Glyphs[] PROGMEM = {
 {    0,   0,   0,  14,   0,   1 },  // 0x20 ' '
 {    0,   4,  29,  17,   6, -28 },  // 0x21 '!'
 {   15,  10,  11,  19,   4, -28 },  // 0x22 '"'
 {   29,  27,  29,  35,   3, -28 },  // 0x23 '#'
 {  127,  20,  37,  26,   3, -30 },  // 0x24 '$'
 {  220,  34,  30,  39,   2, -29 },  // 0x25 '%'
 {  348,  27,  30,  32,   2, -29 },  // 0x26 '&'
 {  450,   3,  11,  12,   4, -28 },  // 0x27 '''
 {  455,   9,  36,  17,   3, -29 },  // 0x28 '('
 {  496,   9,  36,  17,   3, -29 },  // 0x29 ')'
 {  537,  18,  18,  21,   1, -29 },  // 0x2a '*'
 {  578,  25,  25,  35,   4, -24 },  // 0x2b '+'
 {  657,   6,  10,  14,   3,  -4 },  // 0x2c ','
 {  665,  11,   3,  15,   2, -11 },  // 0x2d '-'
 {  670,   4,   5,  14,   5,  -4 },  // 0x2e '.'
 {  673,  13,  33,  14,   0, -28 },  // 0x2f '/'
 {  727,  20,  30,  26,   3, -29 },  // 0x30 '0'
 {  802,  17,  29,  26,   4, -28 },  // 0x31 '1'
 {  864,  19,  30,  26,   3, -29 },  // 0x32 '2'
 {  936,  19,  30,  26,   3, -29 },  // 0x33 '3'
 { 1008,  21,  29,  26,   2, -28 },  // 0x34 '4'
 { 1085,  19,  29,  26,   3, -28 },  // 0x35 '5'
 { 1154,  20,  30,  26,   3, -29 },  // 0x36 '6'
 { 1229,  19,  29,  26,   3, -28 },  // 0x37 '7'
 { 1298,  20,  30,  26,   3, -29 },  // 0x38 '8'
 { 1373,  20,  30,  26,   3, -29 },  // 0x39 '9'
 { 1448,   4,  21,  14,   5, -20 },  // 0x3a ':'
 { 1459,   6,  26,  14,   3, -20 },  // 0x3b ';'
 { 1479,  25,  21,  35,   4, -22 },  // 0x3c '<'
 { 1545,  25,  11,  35,   4, -17 },  // 0x3d '='
 { 1580,  25,  21,  35,   4, -22 },  // 0x3e '>'
 { 1646,  16,  30,  22,   3, -29 },  // 0x3f '?'
 { 1706,  35,  35,  41,   3, -27 },  // 0x40 '@'
 { 1860,  27,  29,  28,   0, -28 },  // 0x41 'A'
 { 1958,  21,  29,  28,   4, -28 },  // 0x42 'B'
 { 2035,  24,  30,  29,   2, -29 },  // 0x43 'C'
 { 2125,  25,  29,  32,   4, -28 },  // 0x44 'D'
 { 2216,  19,  29,  26,   4, -28 },  // 0x45 'E'
 { 2285,  17,  29,  24,   4, -28 },  // 0x46 'F'
 { 2347,  25,  30,  32,   2, -29 },  // 0x47 'G'
 { 2441,  22,  29,  31,   4, -28 },  // 0x48 'H'
 { 2521,   4,  29,  13,   4, -28 },  // 0x49 'I'
 { 2536,  10,  37,  13,  -2, -28 },  // 0x4a 'J'
 { 2583,  23,  29,  27,   4, -28 },  // 0x4b 'K'
 { 2667,  18,  29,  23,   4, -28 },  // 0x4c 'L'
 { 2733,  27,  29,  36,   4, -28 },  // 0x4d 'M'
 { 2831,  22,  29,  31,   4, -28 },  // 0x4e 'N'
 { 2911,  27,  30,  32,   2, -29 },  // 0x4f 'O'
 { 3013,  19,  29,  25,   4, -28 },  // 0x50 'P'
 { 3082,  27,  35,  32,   2, -29 },  // 0x51 'Q'
 { 3201,  23,  29,  29,   4, -28 },  // 0x52 'R'
 { 3285,  22,  30,  26,   3, -29 },  // 0x53 'S'
 { 3368,  24,  29,  25,   0, -28 },  // 0x54 'T'
 { 3455,  22,  29,  30,   4, -28 },  // 0x55 'U'
 { 3535,  27,  29,  28,   0, -28 },  // 0x56 'V'
 { 3633,  37,  29,  41,   1, -28 },  // 0x57 'W'
 { 3768,  25,  29,  28,   1, -28 },  // 0x58 'X'
 { 3859,  24,  29,  25,   0, -28 },  // 0x59 'Y'
 { 3946,  24,  29,  28,   2, -28 },  // 0x5a 'Z'
 { 4033,   9,  36,  17,   3, -29 },  // 0x5b '['
 { 4074,  13,  33,  14,   0, -28 },  // 0x5c '\'
 { 4128,   9,  36,  17,   4, -29 },  // 0x5d ']'
 { 4169,  25,  11,  35,   4, -28 },  // 0x5e '^'
 { 4204,  20,   3,  21,   0,   7 },  // 0x5f '_'
 { 4212,   9,   7,  21,   4, -31 },  // 0x60 '`'
 { 4220,  18,  23,  26,   2, -22 },  // 0x61 'a'
 { 4272,  20,  30,  26,   4, -29 },  // 0x62 'b'
 { 4347,  17,  23,  23,   2, -22 },  // 0x63 'c'
 { 4396,  20,  30,  26,   2, -29 },  // 0x64 'd'
 { 4471,  20,  23,  26,   2, -22 },  // 0x65 'e'
 { 4529,  14,  30,  15,   1, -29 },  // 0x66 'f'
 { 4582,  20,  31,  26,   2, -22 },  // 0x67 'g'
 { 4660,  18,  30,  26,   4, -29 },  // 0x68 'h'
 { 4728,   4,  30,  12,   4, -29 },  // 0x69 'i'
 { 4743,   8,  38,  12,   0, -29 },  // 0x6a 'j'
 { 4781,  19,  30,  24,   4, -29 },  // 0x6b 'k'
 { 4853,   4,  30,  12,   4, -29 },  // 0x6c 'l'
 { 4868,  32,  23,  40,   4, -22 },  // 0x6d 'm'
 { 4960,  18,  23,  26,   4, -22 },  // 0x6e 'n'
 { 5012,  20,  23,  25,   2, -22 },  // 0x6f 'o'
 { 5070,  20,  31,  26,   4, -22 },  // 0x70 'p'
 { 5148,  20,  31,  26,   2, -22 },  // 0x71 'q'
 { 5226,  13,  23,  17,   4, -22 },  // 0x72 'r'
 { 5264,  17,  23,  22,   2, -22 },  // 0x73 's'
 { 5313,  14,  28,  17,   1, -27 },  // 0x74 't'
 { 5362,  18,  23,  26,   4, -22 },  // 0x75 'u'
 { 5414,  21,  22,  25,   1, -21 },  // 0x76 'v'
 { 5472,  29,  22,  34,   2, -21 },  // 0x77 'w'
 { 5552,  21,  22,  25,   1, -21 },  // 0x78 'x'
 { 5610,  21,  30,  25,   1, -21 },  // 0x79 'y'
 { 5689,  18,  22,  22,   2, -21 },  // 0x7a 'z'
 { 5739,  16,  37,  26,   5, -29 },  // 0x7b '{'
 { 5813,   3,  40,  14,   5, -29 },  // 0x7c '|'
 { 5828,  16,  37,  26,   5, -29 },  // 0x7d '}'
 { 5902,  25,   8,  35,   4, -15 },  // 0x7e '~'
};

const GFXfont DejaVu40 PROGMEM = {
  (uint8_t*)DejaVu40Bitmaps,
  (GFXglyph*)DejaVu40Glyphs,
  0x20, 0x7e, 42 };

