{"name": "M5Unified", "description": "Library for M5Stack/Core2/Tough/CoreS3, M5StickC/C-Plus, M5CoreInk, M5Paper, M5ATOM, M5STAMP, M5Station", "keywords": "M5Unified", "authors": {"name": "M5<PERSON><PERSON>ck, lovyan03", "url": "http://www.m5stack.com"}, "repository": {"type": "git", "url": "https://github.com/m5stack/M5Unified.git"}, "dependencies": [{"name": "M5GFX", "version": ">=0.1.9"}], "version": "0.1.9", "frameworks": ["a<PERSON><PERSON><PERSON>", "espidf", "*"], "platforms": ["espressif32", "native"], "headers": "M5Unified.h"}