; PlatformIO Project Configuration File
;
;   Build options: build flags, source filter
;   Upload options: custom upload port, speed and extra flags
;   Library options: dependencies, extra library storages
;   Advanced options: extra scripting
;
; Please visit documentation for the other options and examples
; https://docs.platformio.org/page/projectconf.html

[platformio]
default_envs = native

[env]
lib_extra_dirs=../../../

[env:native]
platform = native
build_type = debug
build_flags = -O0 -xc++ -std=c++14 -lSDL2
  -I"/usr/local/include/SDL2"                ; for intel mac homebrew SDL2
  -L"/usr/local/lib"                         ; for intel mac homebrew SDL2
  -I"${sysenv.HOMEBREW_PREFIX}/include/SDL2" ; for arm mac homebrew SDL2
  -L"${sysenv.HOMEBREW_PREFIX}/lib"          ; for arm mac homebrew SDL2

[env:native_StickCPlus]
extends = native
platform = native
build_flags = ${env:native.build_flags}
  -DM5GFX_SCALE=2
  -DM5GFX_ROTATION=0
  -DM5GFX_BOARD=board_M5StickCPlus

[env:native_Paper]
extends = native
platform = native
build_flags = ${env:native.build_flags}
  -DM5GFX_ROTATION=0
  -DM5GFX_BOARD=board_M5Paper

[esp32_base]
build_type = debug
platform = espressif32
board = esp32dev
upload_speed = 1500000
monitor_speed = 115200
monitor_filters = esp32_exception_decoder

[env:esp32_arduino]
extends = esp32_base
framework = arduino

[env:esp32c3_arduino]
extends = esp32_base
framework = arduino
board = esp32-c3-devkitm-1

[env:esp32s3_arduino]
extends = esp32_base
framework = arduino
board = esp32-s3-devkitc-1

[env:esp32_idf]
extends = esp32_base
framework = espidf

