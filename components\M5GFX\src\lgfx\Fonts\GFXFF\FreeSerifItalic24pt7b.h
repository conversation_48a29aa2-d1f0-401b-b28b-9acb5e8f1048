const uint8_t FreeSerifItalic24pt7bBitmaps[] PROGMEM = {
  0x00, 0xF0, 0x0F, 0x00, 0xF0, 0x0F, 0x01, 0xF0, 0x1E, 0x01, 0xE0, 0x1C,
  0x01, 0xC0, 0x3C, 0x03, 0x80, 0x38, 0x03, 0x80, 0x30, 0x07, 0x00, 0x60,
  0x06, 0x00, 0x60, 0x04, 0x00, 0x40, 0x0C, 0x00, 0x80, 0x08, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x07, 0x00, 0xF8, 0x0F, 0x80, 0xF8, 0x07, 0x00,
  0x38, 0x1D, 0xE0, 0x77, 0x83, 0xDC, 0x0E, 0x70, 0x39, 0xC1, 0xEE, 0x07,
  0x38, 0x1C, 0xC0, 0x63, 0x01, 0x8C, 0x06, 0x20, 0x10, 0x00, 0x06, 0x03,
  0x00, 0x07, 0x03, 0x80, 0x03, 0x81, 0xC0, 0x03, 0x81, 0xC0, 0x01, 0xC0,
  0xE0, 0x00, 0xE0, 0x70, 0x00, 0xE0, 0x70, 0x00, 0x70, 0x38, 0x00, 0x30,
  0x18, 0x00, 0x38, 0x1C, 0x03, 0xFF, 0xFF, 0xE1, 0xFF, 0xFF, 0xF0, 0x0E,
  0x07, 0x00, 0x06, 0x03, 0x00, 0x07, 0x03, 0x80, 0x03, 0x81, 0xC0, 0x03,
  0x81, 0xC0, 0x01, 0xC0, 0xE0, 0x00, 0xE0, 0x70, 0x1F, 0xFF, 0xFF, 0x8F,
  0xFF, 0xFF, 0x80, 0x70, 0x38, 0x00, 0x38, 0x1C, 0x00, 0x1C, 0x0C, 0x00,
  0x1C, 0x0E, 0x00, 0x0E, 0x07, 0x00, 0x0E, 0x07, 0x00, 0x07, 0x03, 0x80,
  0x03, 0x81, 0xC0, 0x03, 0x81, 0xC0, 0x01, 0xC0, 0xE0, 0x00, 0x00, 0x01,
  0x00, 0x00, 0x18, 0x00, 0x00, 0xC0, 0x00, 0xFF, 0x80, 0x1C, 0x2F, 0x01,
  0x83, 0x3C, 0x1C, 0x18, 0xE1, 0xC0, 0xC3, 0x0E, 0x06, 0x18, 0x70, 0x60,
  0x83, 0x83, 0x04, 0x1E, 0x18, 0x00, 0xF8, 0xC0, 0x03, 0xEC, 0x00, 0x0F,
  0xE0, 0x00, 0x3F, 0x00, 0x00, 0xFC, 0x00, 0x03, 0xF0, 0x00, 0x0F, 0xC0,
  0x00, 0x7F, 0x00, 0x03, 0x7C, 0x00, 0x19, 0xE0, 0x01, 0x87, 0x80, 0x0C,
  0x3C, 0x00, 0x60, 0xE2, 0x03, 0x07, 0x10, 0x30, 0x39, 0x81, 0x81, 0xCE,
  0x0C, 0x0C, 0x70, 0x60, 0xE3, 0xC6, 0x06, 0x0F, 0x30, 0x60, 0x1F, 0x9E,
  0x00, 0x3F, 0x80, 0x00, 0xC0, 0x00, 0x06, 0x00, 0x00, 0x30, 0x00, 0x01,
  0x80, 0x00, 0x01, 0xF0, 0x00, 0xC0, 0x03, 0xFE, 0x01, 0xE0, 0x03, 0xC7,
  0x83, 0xE0, 0x03, 0xC0, 0x7F, 0x60, 0x03, 0xC0, 0x20, 0x70, 0x01, 0xC0,
  0x10, 0x30, 0x01, 0xE0, 0x08, 0x38, 0x00, 0xE0, 0x04, 0x18, 0x00, 0xF0,
  0x02, 0x1C, 0x00, 0x70, 0x02, 0x0C, 0x00, 0x38, 0x01, 0x0E, 0x00, 0x1C,
  0x01, 0x8E, 0x00, 0x0E, 0x00, 0x86, 0x00, 0x07, 0x00, 0x87, 0x03, 0xE1,
  0x80, 0xC3, 0x07, 0xFC, 0xE1, 0xC3, 0x87, 0xC6, 0x3F, 0x81, 0x87, 0x81,
  0x8F, 0x81, 0xC7, 0x80, 0x40, 0x00, 0xC3, 0xC0, 0x20, 0x00, 0xE3, 0xC0,
  0x10, 0x00, 0x61, 0xC0, 0x08, 0x00, 0x61, 0xE0, 0x04, 0x00, 0x70, 0xF0,
  0x06, 0x00, 0x30, 0x70, 0x02, 0x00, 0x38, 0x38, 0x03, 0x00, 0x18, 0x1C,
  0x01, 0x00, 0x1C, 0x0E, 0x01, 0x80, 0x0C, 0x07, 0x01, 0x80, 0x0E, 0x01,
  0xC3, 0x80, 0x06, 0x00, 0x7F, 0x80, 0x06, 0x00, 0x1F, 0x00, 0x07, 0x00,
  0x00, 0x00, 0x00, 0x00, 0xF0, 0x00, 0x00, 0x0F, 0xF0, 0x00, 0x00, 0x71,
  0xC0, 0x00, 0x01, 0xC3, 0x80, 0x00, 0x0E, 0x0E, 0x00, 0x00, 0x38, 0x38,
  0x00, 0x01, 0xE0, 0xE0, 0x00, 0x07, 0x87, 0x00, 0x00, 0x1E, 0x18, 0x00,
  0x00, 0x78, 0xC0, 0x00, 0x01, 0xE6, 0x00, 0x00, 0x07, 0xF0, 0x00, 0x00,
  0x1F, 0x00, 0x00, 0x00, 0x78, 0x00, 0x00, 0x07, 0xE0, 0x00, 0x00, 0x7F,
  0xC1, 0xFE, 0x03, 0x9F, 0x03, 0xE0, 0x3C, 0x3C, 0x07, 0x01, 0xE0, 0xF8,
  0x1C, 0x0F, 0x03, 0xE0, 0xE0, 0x7C, 0x07, 0x83, 0x01, 0xE0, 0x1F, 0x1C,
  0x07, 0x80, 0x7C, 0x60, 0x3E, 0x00, 0xFB, 0x00, 0xF8, 0x03, 0xFC, 0x03,
  0xE0, 0x07, 0xE0, 0x0F, 0x80, 0x1F, 0x00, 0x3F, 0x00, 0x3E, 0x00, 0x7C,
  0x00, 0xFC, 0x01, 0xF8, 0x0F, 0xF0, 0x03, 0xF0, 0xF3, 0xF0, 0x87, 0xFF,
  0x07, 0xFC, 0x07, 0xF0, 0x07, 0xC0, 0x39, 0xDE, 0xE7, 0x3B, 0x9C, 0xC6,
  0x31, 0x00, 0x00, 0x10, 0x01, 0x00, 0x18, 0x01, 0x80, 0x18, 0x01, 0x80,
  0x1C, 0x00, 0xC0, 0x0E, 0x00, 0xE0, 0x07, 0x00, 0x78, 0x03, 0x80, 0x3C,
  0x01, 0xE0, 0x0E, 0x00, 0x70, 0x07, 0x80, 0x3C, 0x01, 0xE0, 0x0E, 0x00,
  0x70, 0x03, 0x80, 0x1C, 0x00, 0xE0, 0x07, 0x00, 0x38, 0x01, 0xC0, 0x0E,
  0x00, 0x30, 0x01, 0x80, 0x0C, 0x00, 0x60, 0x01, 0x80, 0x0C, 0x00, 0x60,
  0x01, 0x00, 0x0C, 0x00, 0x20, 0x00, 0x00, 0x80, 0x06, 0x00, 0x10, 0x00,
  0x80, 0x06, 0x00, 0x30, 0x00, 0xC0, 0x06, 0x00, 0x30, 0x01, 0x80, 0x0C,
  0x00, 0x70, 0x03, 0x80, 0x1C, 0x00, 0xE0, 0x07, 0x00, 0x38, 0x01, 0xC0,
  0x1E, 0x00, 0xF0, 0x07, 0x80, 0x3C, 0x01, 0xC0, 0x1E, 0x00, 0xF0, 0x07,
  0x80, 0x38, 0x03, 0xC0, 0x1C, 0x00, 0xE0, 0x0E, 0x00, 0x60, 0x07, 0x00,
  0x30, 0x03, 0x00, 0x30, 0x03, 0x00, 0x10, 0x01, 0x00, 0x00, 0x01, 0x00,
  0x03, 0x80, 0x03, 0x80, 0x03, 0x80, 0x03, 0x80, 0xE1, 0x07, 0xE1, 0x0F,
  0xF1, 0x1F, 0x19, 0x30, 0x07, 0xC0, 0x03, 0x80, 0x0D, 0x60, 0x79, 0x3C,
  0xF1, 0x1F, 0xE1, 0x0F, 0xE1, 0x07, 0x03, 0x80, 0x03, 0x80, 0x03, 0x80,
  0x03, 0x00, 0x00, 0x38, 0x00, 0x00, 0x70, 0x00, 0x00, 0xE0, 0x00, 0x01,
  0xC0, 0x00, 0x03, 0x80, 0x00, 0x07, 0x00, 0x00, 0x0E, 0x00, 0x00, 0x1C,
  0x00, 0x00, 0x38, 0x00, 0x00, 0x70, 0x03, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xE0, 0x07, 0x00, 0x00, 0x0E, 0x00, 0x00, 0x1C, 0x00,
  0x00, 0x38, 0x00, 0x00, 0x70, 0x00, 0x00, 0xE0, 0x00, 0x01, 0xC0, 0x00,
  0x03, 0x80, 0x00, 0x07, 0x00, 0x00, 0x0E, 0x00, 0x00, 0x1C, 0x7C, 0xF9,
  0xF1, 0xE1, 0xC3, 0x0C, 0x10, 0xC1, 0x00, 0x7F, 0xFF, 0xFF, 0xFF, 0x00,
  0x77, 0xFF, 0xF7, 0x00, 0x00, 0x00, 0x78, 0x00, 0x03, 0x80, 0x00, 0x3C,
  0x00, 0x01, 0xC0, 0x00, 0x1E, 0x00, 0x00, 0xE0, 0x00, 0x0E, 0x00, 0x00,
  0xF0, 0x00, 0x07, 0x00, 0x00, 0x78, 0x00, 0x03, 0x80, 0x00, 0x3C, 0x00,
  0x01, 0xC0, 0x00, 0x1E, 0x00, 0x00, 0xE0, 0x00, 0x0F, 0x00, 0x00, 0x70,
  0x00, 0x07, 0x80, 0x00, 0x38, 0x00, 0x03, 0x80, 0x00, 0x3C, 0x00, 0x01,
  0xC0, 0x00, 0x1E, 0x00, 0x00, 0xE0, 0x00, 0x0F, 0x00, 0x00, 0x70, 0x00,
  0x07, 0x80, 0x00, 0x38, 0x00, 0x03, 0xC0, 0x00, 0x1C, 0x00, 0x01, 0xE0,
  0x00, 0x0E, 0x00, 0x00, 0xE0, 0x00, 0x00, 0x00, 0x1F, 0x80, 0x03, 0x86,
  0x00, 0x30, 0x18, 0x03, 0x00, 0xC0, 0x38, 0x03, 0x03, 0x80, 0x18, 0x38,
  0x00, 0xC1, 0xC0, 0x07, 0x1C, 0x00, 0x38, 0xE0, 0x01, 0xCF, 0x00, 0x0E,
  0x70, 0x00, 0x77, 0x80, 0x07, 0xBC, 0x00, 0x3D, 0xE0, 0x01, 0xEE, 0x00,
  0x0F, 0xF0, 0x00, 0x77, 0x80, 0x07, 0xBC, 0x00, 0x3D, 0xC0, 0x01, 0xCE,
  0x00, 0x1E, 0x70, 0x00, 0xF3, 0x80, 0x07, 0x1C, 0x00, 0x78, 0xE0, 0x03,
  0x83, 0x00, 0x38, 0x18, 0x03, 0x80, 0xE0, 0x18, 0x03, 0x01, 0x80, 0x0C,
  0x38, 0x00, 0x1F, 0x00, 0x00, 0x00, 0x00, 0x80, 0x1F, 0xC0, 0x3F, 0xE0,
  0x01, 0xF0, 0x00, 0xF0, 0x00, 0x78, 0x00, 0x3C, 0x00, 0x1C, 0x00, 0x1E,
  0x00, 0x0F, 0x00, 0x07, 0x80, 0x07, 0x80, 0x03, 0xC0, 0x01, 0xE0, 0x00,
  0xF0, 0x00, 0xF0, 0x00, 0x78, 0x00, 0x3C, 0x00, 0x3C, 0x00, 0x1E, 0x00,
  0x0F, 0x00, 0x07, 0x80, 0x07, 0x80, 0x03, 0xC0, 0x01, 0xE0, 0x01, 0xE0,
  0x00, 0xF0, 0x00, 0x78, 0x00, 0x3C, 0x00, 0x3C, 0x00, 0x3F, 0x01, 0xFF,
  0xF0, 0x00, 0x3F, 0x00, 0x07, 0xFE, 0x00, 0x7F, 0xF8, 0x07, 0x07, 0xE0,
  0x60, 0x1F, 0x06, 0x00, 0x7C, 0x20, 0x01, 0xE0, 0x00, 0x0F, 0x00, 0x00,
  0x78, 0x00, 0x03, 0xC0, 0x00, 0x1C, 0x00, 0x01, 0xE0, 0x00, 0x0E, 0x00,
  0x00, 0xF0, 0x00, 0x07, 0x00, 0x00, 0x70, 0x00, 0x07, 0x00, 0x00, 0x70,
  0x00, 0x03, 0x00, 0x00, 0x30, 0x00, 0x03, 0x00, 0x00, 0x30, 0x00, 0x03,
  0x00, 0x00, 0x30, 0x00, 0x03, 0x00, 0x00, 0x30, 0x01, 0x03, 0x00, 0x08,
  0x30, 0x00, 0xC3, 0xFF, 0xFC, 0x3F, 0xFF, 0xE3, 0xFF, 0xFE, 0x00, 0x00,
  0x0F, 0xC0, 0x00, 0xFF, 0xC0, 0x06, 0x0F, 0x80, 0x30, 0x1E, 0x01, 0x80,
  0x3C, 0x00, 0x00, 0xF0, 0x00, 0x03, 0xC0, 0x00, 0x0F, 0x00, 0x00, 0x78,
  0x00, 0x01, 0xE0, 0x00, 0x0E, 0x00, 0x00, 0xF0, 0x00, 0x0E, 0x00, 0x01,
  0xF8, 0x00, 0x3F, 0xF8, 0x00, 0x0F, 0xF0, 0x00, 0x07, 0xC0, 0x00, 0x0F,
  0x80, 0x00, 0x3E, 0x00, 0x00, 0x78, 0x00, 0x01, 0xE0, 0x00, 0x07, 0x80,
  0x00, 0x1E, 0x00, 0x00, 0x70, 0x00, 0x01, 0xC0, 0x00, 0x07, 0x00, 0x00,
  0x38, 0x00, 0x00, 0xC0, 0x70, 0x06, 0x03, 0xF8, 0x70, 0x07, 0xFF, 0x00,
  0x0F, 0xF0, 0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x70, 0x00, 0x03, 0xC0,
  0x00, 0x1F, 0x00, 0x00, 0xF8, 0x00, 0x07, 0xE0, 0x00, 0x37, 0x80, 0x00,
  0xDC, 0x00, 0x06, 0x70, 0x00, 0x33, 0xC0, 0x01, 0x8F, 0x00, 0x0C, 0x38,
  0x00, 0x60, 0xE0, 0x03, 0x07, 0x80, 0x18, 0x1E, 0x00, 0xC0, 0x70, 0x06,
  0x03, 0xC0, 0x30, 0x0F, 0x01, 0x80, 0x38, 0x0C, 0x00, 0xE0, 0x70, 0x07,
  0x81, 0xFF, 0xFF, 0xEF, 0xFF, 0xFF, 0xBF, 0xFF, 0xFE, 0x00, 0x0F, 0x00,
  0x00, 0x38, 0x00, 0x00, 0xE0, 0x00, 0x07, 0x80, 0x00, 0x1E, 0x00, 0x00,
  0x70, 0x00, 0x03, 0xC0, 0x00, 0x0F, 0x00, 0x00, 0x3F, 0xFC, 0x00, 0xFF,
  0xF0, 0x07, 0xFF, 0x80, 0x10, 0x00, 0x00, 0x40, 0x00, 0x02, 0x00, 0x00,
  0x08, 0x00, 0x00, 0x70, 0x00, 0x01, 0xF8, 0x00, 0x0F, 0xF0, 0x00, 0x3F,
  0xF0, 0x00, 0x1F, 0xE0, 0x00, 0x1F, 0x80, 0x00, 0x1F, 0x00, 0x00, 0x3C,
  0x00, 0x00, 0x78, 0x00, 0x01, 0xE0, 0x00, 0x03, 0x80, 0x00, 0x0E, 0x00,
  0x00, 0x38, 0x00, 0x00, 0xE0, 0x00, 0x03, 0x80, 0x00, 0x0C, 0x00, 0x00,
  0x70, 0x00, 0x01, 0xC0, 0x00, 0x06, 0x00, 0x00, 0x30, 0x00, 0x01, 0x80,
  0x70, 0x0E, 0x03, 0xF0, 0xE0, 0x07, 0xFF, 0x00, 0x0F, 0xE0, 0x00, 0x00,
  0x00, 0x0E, 0x00, 0x01, 0xF0, 0x00, 0x1F, 0x00, 0x00, 0xF8, 0x00, 0x03,
  0xE0, 0x00, 0x0F, 0x00, 0x00, 0x7C, 0x00, 0x01, 0xF0, 0x00, 0x03, 0xC0,
  0x00, 0x0F, 0x80, 0x00, 0x3E, 0x00, 0x00, 0xF9, 0xF8, 0x01, 0xFF, 0xFC,
  0x07, 0xE0, 0x7C, 0x0F, 0x80, 0x7C, 0x3E, 0x00, 0x78, 0x78, 0x00, 0x78,
  0xF0, 0x00, 0xF3, 0xC0, 0x01, 0xE7, 0x80, 0x03, 0xCF, 0x00, 0x07, 0x9C,
  0x00, 0x0F, 0x38, 0x00, 0x3E, 0x70, 0x00, 0x78, 0xE0, 0x00, 0xF1, 0xC0,
  0x03, 0xC1, 0x80, 0x07, 0x83, 0x00, 0x1E, 0x03, 0x00, 0x38, 0x06, 0x01,
  0xE0, 0x03, 0x07, 0x00, 0x01, 0xF8, 0x00, 0x1F, 0xFF, 0xF9, 0xFF, 0xFF,
  0xCF, 0xFF, 0xFC, 0xE0, 0x00, 0xCC, 0x00, 0x0E, 0x40, 0x00, 0x60, 0x00,
  0x07, 0x00, 0x00, 0x70, 0x00, 0x03, 0x80, 0x00, 0x38, 0x00, 0x01, 0x80,
  0x00, 0x1C, 0x00, 0x01, 0xC0, 0x00, 0x0E, 0x00, 0x00, 0xE0, 0x00, 0x07,
  0x00, 0x00, 0x70, 0x00, 0x07, 0x00, 0x00, 0x38, 0x00, 0x03, 0x80, 0x00,
  0x1C, 0x00, 0x01, 0xC0, 0x00, 0x1E, 0x00, 0x00, 0xE0, 0x00, 0x0E, 0x00,
  0x00, 0x70, 0x00, 0x07, 0x00, 0x00, 0x78, 0x00, 0x03, 0x80, 0x00, 0x38,
  0x00, 0x01, 0xC0, 0x00, 0x1C, 0x00, 0x00, 0x00, 0x3F, 0x80, 0x03, 0x83,
  0x80, 0x1C, 0x03, 0x00, 0xE0, 0x0E, 0x07, 0x00, 0x1C, 0x1C, 0x00, 0x70,
  0x70, 0x01, 0xC1, 0xC0, 0x07, 0x07, 0x80, 0x1C, 0x1E, 0x00, 0xE0, 0x3C,
  0x07, 0x80, 0xFC, 0x38, 0x01, 0xFB, 0xC0, 0x03, 0xF8, 0x00, 0x0F, 0xE0,
  0x00, 0x7F, 0xC0, 0x07, 0x1F, 0x80, 0x78, 0x3F, 0x03, 0x80, 0x7C, 0x1E,
  0x00, 0xF8, 0x70, 0x01, 0xE3, 0x80, 0x03, 0xCE, 0x00, 0x07, 0x38, 0x00,
  0x1C, 0xE0, 0x00, 0x73, 0x80, 0x01, 0xCE, 0x00, 0x06, 0x1C, 0x00, 0x38,
  0x70, 0x01, 0xC0, 0xE0, 0x0E, 0x01, 0xE0, 0xE0, 0x01, 0xFE, 0x00, 0x00,
  0x1F, 0x80, 0x03, 0xC3, 0x00, 0x1C, 0x02, 0x00, 0xE0, 0x0C, 0x07, 0x00,
  0x18, 0x3C, 0x00, 0x60, 0xE0, 0x01, 0xC7, 0x80, 0x07, 0x1E, 0x00, 0x1C,
  0xF0, 0x00, 0x73, 0xC0, 0x01, 0xCF, 0x00, 0x07, 0x3C, 0x00, 0x3C, 0xF0,
  0x00, 0xF3, 0xC0, 0x03, 0xCF, 0x00, 0x1E, 0x1E, 0x00, 0x78, 0x7C, 0x03,
  0xE0, 0xF8, 0x3F, 0x01, 0xFF, 0xBC, 0x03, 0xF1, 0xE0, 0x00, 0x0F, 0x80,
  0x00, 0x3C, 0x00, 0x01, 0xF0, 0x00, 0x0F, 0x80, 0x00, 0x7C, 0x00, 0x03,
  0xE0, 0x00, 0x1F, 0x00, 0x00, 0xF0, 0x00, 0x0F, 0x80, 0x00, 0x78, 0x00,
  0x0F, 0x80, 0x00, 0xE0, 0x00, 0x00, 0x07, 0x07, 0xC3, 0xE1, 0xF0, 0x70,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x38, 0x3E, 0x1F, 0x0F, 0x83, 0x80, 0x01, 0xC0, 0x7C, 0x0F, 0x81,
  0xF0, 0x1C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0x07, 0x80, 0xF8, 0x1F, 0x01, 0xE0,
  0x1C, 0x03, 0x00, 0xC0, 0x18, 0x04, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x0C, 0x00, 0x00, 0xF8, 0x00, 0x07, 0xF0, 0x00, 0x3F, 0xC0,
  0x01, 0xFC, 0x00, 0x0F, 0xE0, 0x00, 0xFF, 0x00, 0x07, 0xF8, 0x00, 0x3F,
  0xC0, 0x01, 0xFC, 0x00, 0x07, 0xE0, 0x00, 0x0F, 0x00, 0x00, 0x1F, 0x80,
  0x00, 0x3F, 0xC0, 0x00, 0x1F, 0xE0, 0x00, 0x07, 0xF0, 0x00, 0x03, 0xF8,
  0x00, 0x01, 0xFE, 0x00, 0x00, 0xFF, 0x00, 0x00, 0x7F, 0x80, 0x00, 0x1F,
  0xC0, 0x00, 0x0F, 0x80, 0x00, 0x07, 0x00, 0x00, 0x02, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x80, 0x00, 0x00,
  0xE0, 0x00, 0x00, 0xF8, 0x00, 0x00, 0xFE, 0x00, 0x00, 0x3F, 0x80, 0x00,
  0x0F, 0xF0, 0x00, 0x03, 0xFC, 0x00, 0x00, 0xFF, 0x00, 0x00, 0x1F, 0xC0,
  0x00, 0x07, 0xF0, 0x00, 0x01, 0xFE, 0x00, 0x00, 0x7F, 0x00, 0x00, 0x1F,
  0x00, 0x00, 0x3F, 0x00, 0x00, 0xFE, 0x00, 0x07, 0xF8, 0x00, 0x1F, 0xE0,
  0x00, 0x7F, 0x80, 0x01, 0xFC, 0x00, 0x07, 0xF0, 0x00, 0x3F, 0xC0, 0x00,
  0xFF, 0x00, 0x00, 0xFC, 0x00, 0x00, 0xE0, 0x00, 0x00, 0x80, 0x00, 0x00,
  0x03, 0xF0, 0x06, 0x1C, 0x0C, 0x0E, 0x1C, 0x06, 0x1C, 0x07, 0x1C, 0x07,
  0x1C, 0x07, 0x00, 0x07, 0x00, 0x0F, 0x00, 0x0E, 0x00, 0x1E, 0x00, 0x3C,
  0x00, 0x38, 0x00, 0x70, 0x00, 0xE0, 0x01, 0xC0, 0x03, 0x80, 0x03, 0x00,
  0x06, 0x00, 0x04, 0x00, 0x08, 0x00, 0x08, 0x00, 0x08, 0x00, 0x10, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x70, 0x00, 0xF8, 0x00,
  0xF8, 0x00, 0xF8, 0x00, 0x70, 0x00, 0x00, 0x07, 0xFC, 0x00, 0x00, 0x1F,
  0xFF, 0x80, 0x00, 0x3F, 0x01, 0xF0, 0x00, 0x3C, 0x00, 0x1E, 0x00, 0x7C,
  0x00, 0x07, 0x80, 0x7C, 0x00, 0x00, 0xE0, 0x3C, 0x00, 0x00, 0x38, 0x3C,
  0x00, 0x00, 0x0C, 0x3C, 0x00, 0x78, 0x07, 0x1E, 0x00, 0xFE, 0xE1, 0x9E,
  0x00, 0xF1, 0xF0, 0xEF, 0x00, 0xE0, 0xF0, 0x37, 0x80, 0xE0, 0x38, 0x1F,
  0x80, 0x70, 0x1C, 0x0F, 0xC0, 0x70, 0x1E, 0x07, 0xE0, 0x38, 0x0F, 0x03,
  0xF0, 0x18, 0x07, 0x01, 0xF8, 0x1C, 0x03, 0x80, 0xFC, 0x0E, 0x01, 0xC0,
  0xDE, 0x07, 0x01, 0xE0, 0x6F, 0x03, 0x80, 0xE0, 0x73, 0xC1, 0xC0, 0xF0,
  0x31, 0xE0, 0xF0, 0xF8, 0x30, 0xF0, 0x38, 0xDC, 0x30, 0x3C, 0x1F, 0xC7,
  0xF0, 0x0E, 0x07, 0x81, 0xF0, 0x07, 0x80, 0x00, 0x00, 0x01, 0xE0, 0x00,
  0x00, 0x00, 0x78, 0x00, 0x00, 0x00, 0x1F, 0x00, 0x01, 0x00, 0x03, 0xF0,
  0x0F, 0x80, 0x00, 0x7F, 0xFF, 0x00, 0x00, 0x07, 0xFC, 0x00, 0x00, 0x00,
  0x00, 0x18, 0x00, 0x00, 0x01, 0xC0, 0x00, 0x00, 0x0E, 0x00, 0x00, 0x00,
  0xF0, 0x00, 0x00, 0x07, 0xC0, 0x00, 0x00, 0x7E, 0x00, 0x00, 0x03, 0xF0,
  0x00, 0x00, 0x37, 0x80, 0x00, 0x03, 0x3C, 0x00, 0x00, 0x19, 0xE0, 0x00,
  0x01, 0x8F, 0x80, 0x00, 0x08, 0x7C, 0x00, 0x00, 0xC3, 0xE0, 0x00, 0x0C,
  0x0F, 0x00, 0x00, 0x60, 0x78, 0x00, 0x06, 0x03, 0xC0, 0x00, 0x20, 0x1F,
  0x00, 0x03, 0x00, 0xF8, 0x00, 0x3F, 0xFF, 0xC0, 0x01, 0xFF, 0xFE, 0x00,
  0x18, 0x00, 0xF0, 0x00, 0xC0, 0x07, 0x80, 0x0C, 0x00, 0x3E, 0x00, 0xE0,
  0x01, 0xF0, 0x06, 0x00, 0x0F, 0x80, 0x70, 0x00, 0x3C, 0x03, 0x00, 0x01,
  0xE0, 0x38, 0x00, 0x0F, 0x83, 0xC0, 0x00, 0x7C, 0x3E, 0x00, 0x07, 0xF3,
  0xFC, 0x01, 0xFF, 0xE0, 0x03, 0xFF, 0xFE, 0x00, 0x07, 0xFF, 0xF8, 0x00,
  0x3E, 0x07, 0xC0, 0x03, 0xE0, 0x3E, 0x00, 0x3E, 0x01, 0xF0, 0x03, 0xC0,
  0x1F, 0x00, 0x7C, 0x01, 0xF0, 0x07, 0xC0, 0x1F, 0x00, 0x78, 0x01, 0xF0,
  0x07, 0x80, 0x3E, 0x00, 0xF8, 0x03, 0xE0, 0x0F, 0x80, 0x7C, 0x00, 0xF0,
  0x3F, 0x00, 0x1F, 0xFF, 0x80, 0x01, 0xFF, 0xFC, 0x00, 0x1F, 0x07, 0xE0,
  0x01, 0xE0, 0x1F, 0x00, 0x3E, 0x00, 0xF8, 0x03, 0xE0, 0x07, 0xC0, 0x3C,
  0x00, 0x7C, 0x03, 0xC0, 0x07, 0xC0, 0x7C, 0x00, 0x7C, 0x07, 0xC0, 0x07,
  0xC0, 0x78, 0x00, 0x7C, 0x0F, 0x80, 0x0F, 0x80, 0xF8, 0x00, 0xF8, 0x0F,
  0x00, 0x1F, 0x00, 0xF0, 0x03, 0xE0, 0x1F, 0x81, 0xFC, 0x03, 0xFF, 0xFF,
  0x80, 0xFF, 0xFF, 0xC0, 0x00, 0x00, 0x01, 0xFE, 0x04, 0x00, 0x3F, 0xFF,
  0xE0, 0x03, 0xF0, 0x1F, 0x80, 0x1F, 0x00, 0x3E, 0x00, 0xF0, 0x00, 0x78,
  0x0F, 0x80, 0x00, 0xE0, 0x3C, 0x00, 0x03, 0x81, 0xF0, 0x00, 0x04, 0x0F,
  0x80, 0x00, 0x10, 0x7C, 0x00, 0x00, 0x41, 0xF0, 0x00, 0x00, 0x0F, 0x80,
  0x00, 0x00, 0x3E, 0x00, 0x00, 0x01, 0xF0, 0x00, 0x00, 0x07, 0xC0, 0x00,
  0x00, 0x1F, 0x00, 0x00, 0x00, 0xF8, 0x00, 0x00, 0x03, 0xE0, 0x00, 0x00,
  0x0F, 0x80, 0x00, 0x00, 0x3E, 0x00, 0x00, 0x00, 0xF8, 0x00, 0x00, 0x03,
  0xE0, 0x00, 0x00, 0x0F, 0x80, 0x00, 0x00, 0x3E, 0x00, 0x00, 0x00, 0xF8,
  0x00, 0x00, 0x01, 0xF0, 0x00, 0x02, 0x07, 0xC0, 0x00, 0x18, 0x0F, 0x80,
  0x00, 0xC0, 0x3E, 0x00, 0x06, 0x00, 0x7C, 0x00, 0x70, 0x00, 0xFC, 0x07,
  0x00, 0x00, 0xFF, 0xF8, 0x00, 0x00, 0xFF, 0x00, 0x00, 0x03, 0xFF, 0xFF,
  0x00, 0x00, 0x3F, 0xFF, 0xE0, 0x00, 0x0F, 0xC0, 0xFC, 0x00, 0x07, 0xC0,
  0x1F, 0x00, 0x03, 0xE0, 0x07, 0xC0, 0x01, 0xE0, 0x01, 0xF0, 0x01, 0xF0,
  0x00, 0x7C, 0x00, 0xF8, 0x00, 0x3E, 0x00, 0x7C, 0x00, 0x0F, 0x00, 0x3C,
  0x00, 0x07, 0xC0, 0x3E, 0x00, 0x03, 0xE0, 0x1F, 0x00, 0x01, 0xF0, 0x0F,
  0x00, 0x00, 0xF8, 0x0F, 0x80, 0x00, 0x7C, 0x07, 0xC0, 0x00, 0x3E, 0x03,
  0xE0, 0x00, 0x1F, 0x01, 0xE0, 0x00, 0x1F, 0x81, 0xF0, 0x00, 0x0F, 0x80,
  0xF8, 0x00, 0x07, 0xC0, 0x78, 0x00, 0x03, 0xE0, 0x3C, 0x00, 0x03, 0xE0,
  0x3E, 0x00, 0x01, 0xF0, 0x1F, 0x00, 0x01, 0xF0, 0x0F, 0x00, 0x01, 0xF0,
  0x0F, 0x80, 0x01, 0xF8, 0x07, 0xC0, 0x01, 0xF0, 0x03, 0xE0, 0x01, 0xF0,
  0x01, 0xE0, 0x03, 0xF0, 0x01, 0xF8, 0x0F, 0xE0, 0x01, 0xFF, 0xFF, 0xC0,
  0x03, 0xFF, 0xFE, 0x00, 0x00, 0x03, 0xFF, 0xFF, 0xF8, 0x03, 0xFF, 0xFF,
  0xC0, 0x0F, 0x80, 0x1E, 0x00, 0x7C, 0x00, 0x30, 0x03, 0xE0, 0x01, 0x00,
  0x1E, 0x00, 0x08, 0x01, 0xF0, 0x00, 0x40, 0x0F, 0x80, 0x00, 0x00, 0x78,
  0x00, 0x00, 0x03, 0xC0, 0x10, 0x00, 0x3E, 0x01, 0x80, 0x01, 0xF0, 0x08,
  0x00, 0x0F, 0x01, 0xC0, 0x00, 0xFF, 0xFE, 0x00, 0x07, 0xFF, 0xF0, 0x00,
  0x3E, 0x07, 0x00, 0x01, 0xE0, 0x18, 0x00, 0x1F, 0x00, 0xC0, 0x00, 0xF8,
  0x04, 0x00, 0x07, 0x80, 0x20, 0x00, 0x3C, 0x00, 0x00, 0x03, 0xE0, 0x00,
  0x00, 0x1F, 0x00, 0x00, 0x00, 0xF0, 0x00, 0x08, 0x0F, 0x80, 0x00, 0xC0,
  0x7C, 0x00, 0x0E, 0x03, 0xC0, 0x00, 0xE0, 0x1E, 0x00, 0x0F, 0x01, 0xF8,
  0x03, 0xF8, 0x1F, 0xFF, 0xFF, 0x83, 0xFF, 0xFF, 0xFC, 0x00, 0x03, 0xFF,
  0xFF, 0xF8, 0x03, 0xFF, 0xFF, 0xC0, 0x0F, 0x80, 0x1E, 0x00, 0x7C, 0x00,
  0x30, 0x03, 0xE0, 0x01, 0x00, 0x1E, 0x00, 0x08, 0x01, 0xF0, 0x00, 0x40,
  0x0F, 0x80, 0x02, 0x00, 0x7C, 0x00, 0x00, 0x03, 0xC0, 0x00, 0x00, 0x3E,
  0x00, 0x80, 0x01, 0xF0, 0x0C, 0x00, 0x0F, 0x00, 0xC0, 0x00, 0xF8, 0x0E,
  0x00, 0x07, 0xFF, 0xF0, 0x00, 0x3F, 0xFF, 0x00, 0x01, 0xE0, 0x18, 0x00,
  0x1F, 0x00, 0xC0, 0x00, 0xF8, 0x06, 0x00, 0x07, 0xC0, 0x20, 0x00, 0x3C,
  0x01, 0x00, 0x03, 0xE0, 0x00, 0x00, 0x1F, 0x00, 0x00, 0x00, 0xF0, 0x00,
  0x00, 0x0F, 0x80, 0x00, 0x00, 0x7C, 0x00, 0x00, 0x03, 0xE0, 0x00, 0x00,
  0x1E, 0x00, 0x00, 0x01, 0xF0, 0x00, 0x00, 0x1F, 0xC0, 0x00, 0x03, 0xFF,
  0xC0, 0x00, 0x00, 0x00, 0x01, 0xFE, 0x02, 0x00, 0x1F, 0xFF, 0x8C, 0x00,
  0xFC, 0x07, 0xF8, 0x03, 0xE0, 0x03, 0xF0, 0x0F, 0x00, 0x03, 0xC0, 0x3C,
  0x00, 0x03, 0x80, 0xF0, 0x00, 0x07, 0x03, 0xC0, 0x00, 0x0E, 0x0F, 0x80,
  0x00, 0x08, 0x3E, 0x00, 0x00, 0x10, 0x7C, 0x00, 0x00, 0x01, 0xF0, 0x00,
  0x00, 0x03, 0xE0, 0x00, 0x00, 0x0F, 0x80, 0x00, 0x00, 0x1F, 0x00, 0x00,
  0x00, 0x3E, 0x00, 0x00, 0x00, 0xFC, 0x00, 0x00, 0x01, 0xF0, 0x00, 0x3F,
  0xFF, 0xE0, 0x00, 0x0F, 0xE7, 0xC0, 0x00, 0x0F, 0x0F, 0x80, 0x00, 0x1E,
  0x1F, 0x00, 0x00, 0x7C, 0x3E, 0x00, 0x00, 0xF0, 0x7C, 0x00, 0x01, 0xE0,
  0x78, 0x00, 0x03, 0xC0, 0xF8, 0x00, 0x0F, 0x01, 0xF0, 0x00, 0x1E, 0x01,
  0xF0, 0x00, 0x3C, 0x01, 0xE0, 0x00, 0xF8, 0x01, 0xF0, 0x03, 0xE0, 0x01,
  0xF8, 0x0F, 0x80, 0x00, 0xFF, 0xFC, 0x00, 0x00, 0x7F, 0xC0, 0x00, 0x03,
  0xFF, 0xE0, 0x7F, 0xF0, 0x07, 0xF8, 0x01, 0xFC, 0x00, 0x3E, 0x00, 0x0F,
  0x80, 0x03, 0xE0, 0x00, 0xF8, 0x00, 0x3C, 0x00, 0x0F, 0x00, 0x03, 0xC0,
  0x00, 0xF0, 0x00, 0x7C, 0x00, 0x1F, 0x00, 0x07, 0xC0, 0x01, 0xF0, 0x00,
  0x78, 0x00, 0x1E, 0x00, 0x07, 0x80, 0x01, 0xE0, 0x00, 0xF8, 0x00, 0x3E,
  0x00, 0x0F, 0x80, 0x03, 0xE0, 0x00, 0xF0, 0x00, 0x3C, 0x00, 0x1F, 0x00,
  0x03, 0xC0, 0x01, 0xFF, 0xFF, 0xFC, 0x00, 0x1F, 0xFF, 0xFF, 0x80, 0x01,
  0xE0, 0x00, 0x78, 0x00, 0x3E, 0x00, 0x0F, 0x80, 0x03, 0xE0, 0x00, 0xF8,
  0x00, 0x3C, 0x00, 0x0F, 0x00, 0x03, 0xC0, 0x00, 0xF0, 0x00, 0x7C, 0x00,
  0x1F, 0x00, 0x07, 0xC0, 0x01, 0xF0, 0x00, 0x78, 0x00, 0x1E, 0x00, 0x0F,
  0x80, 0x03, 0xE0, 0x00, 0xF8, 0x00, 0x3E, 0x00, 0x0F, 0x00, 0x03, 0xC0,
  0x00, 0xF0, 0x00, 0x3C, 0x00, 0x1F, 0x00, 0x07, 0xC0, 0x03, 0xF8, 0x00,
  0xFE, 0x00, 0xFF, 0xE0, 0x7F, 0xFC, 0x00, 0x01, 0xFF, 0xC0, 0x1F, 0xE0,
  0x03, 0xE0, 0x00, 0xF8, 0x00, 0x3E, 0x00, 0x0F, 0x00, 0x07, 0xC0, 0x01,
  0xF0, 0x00, 0x78, 0x00, 0x1E, 0x00, 0x0F, 0x80, 0x03, 0xE0, 0x00, 0xF0,
  0x00, 0x7C, 0x00, 0x1F, 0x00, 0x07, 0xC0, 0x01, 0xE0, 0x00, 0xF8, 0x00,
  0x3E, 0x00, 0x0F, 0x00, 0x03, 0xC0, 0x01, 0xF0, 0x00, 0x7C, 0x00, 0x1E,
  0x00, 0x0F, 0x80, 0x03, 0xE0, 0x00, 0xF0, 0x00, 0x3C, 0x00, 0x1F, 0x00,
  0x0F, 0xE0, 0x0F, 0xFE, 0x00, 0x00, 0x1F, 0xFE, 0x00, 0x07, 0xF0, 0x00,
  0x07, 0xC0, 0x00, 0x0F, 0x80, 0x00, 0x1E, 0x00, 0x00, 0x3C, 0x00, 0x00,
  0xF8, 0x00, 0x01, 0xE0, 0x00, 0x03, 0xC0, 0x00, 0x0F, 0x80, 0x00, 0x1F,
  0x00, 0x00, 0x3C, 0x00, 0x00, 0x78, 0x00, 0x01, 0xF0, 0x00, 0x03, 0xE0,
  0x00, 0x07, 0x80, 0x00, 0x1F, 0x00, 0x00, 0x3E, 0x00, 0x00, 0x78, 0x00,
  0x00, 0xF0, 0x00, 0x03, 0xE0, 0x00, 0x07, 0xC0, 0x00, 0x0F, 0x00, 0x00,
  0x1E, 0x00, 0x00, 0x7C, 0x00, 0x00, 0xF0, 0x01, 0xC1, 0xE0, 0x07, 0xC7,
  0x80, 0x0F, 0x8F, 0x00, 0x1F, 0x3C, 0x00, 0x1F, 0xF0, 0x00, 0x0F, 0x80,
  0x00, 0x01, 0xFF, 0xE1, 0xFF, 0x80, 0x3F, 0xC0, 0x1F, 0x80, 0x0F, 0x80,
  0x0F, 0x00, 0x07, 0xC0, 0x0F, 0x00, 0x03, 0xC0, 0x0F, 0x00, 0x01, 0xE0,
  0x0E, 0x00, 0x01, 0xF0, 0x0E, 0x00, 0x00, 0xF8, 0x0E, 0x00, 0x00, 0x78,
  0x1C, 0x00, 0x00, 0x3C, 0x1C, 0x00, 0x00, 0x3E, 0x3C, 0x00, 0x00, 0x1F,
  0x38, 0x00, 0x00, 0x0F, 0x38, 0x00, 0x00, 0x07, 0xF8, 0x00, 0x00, 0x07,
  0xFE, 0x00, 0x00, 0x03, 0xDF, 0x00, 0x00, 0x01, 0xE7, 0xC0, 0x00, 0x01,
  0xF3, 0xE0, 0x00, 0x00, 0xF8, 0xF8, 0x00, 0x00, 0x78, 0x3C, 0x00, 0x00,
  0x3C, 0x1F, 0x00, 0x00, 0x3E, 0x07, 0xC0, 0x00, 0x1F, 0x03, 0xE0, 0x00,
  0x0F, 0x00, 0xF8, 0x00, 0x0F, 0x80, 0x3C, 0x00, 0x07, 0xC0, 0x1F, 0x00,
  0x03, 0xC0, 0x07, 0x80, 0x01, 0xE0, 0x03, 0xE0, 0x01, 0xF0, 0x01, 0xF8,
  0x01, 0xFC, 0x01, 0xFE, 0x03, 0xFF, 0xC3, 0xFF, 0xE0, 0x03, 0xFF, 0xE0,
  0x00, 0x0F, 0xF0, 0x00, 0x00, 0xFC, 0x00, 0x00, 0x1F, 0x00, 0x00, 0x03,
  0xE0, 0x00, 0x00, 0x78, 0x00, 0x00, 0x1F, 0x00, 0x00, 0x03, 0xE0, 0x00,
  0x00, 0x78, 0x00, 0x00, 0x0F, 0x00, 0x00, 0x03, 0xE0, 0x00, 0x00, 0x7C,
  0x00, 0x00, 0x0F, 0x00, 0x00, 0x03, 0xE0, 0x00, 0x00, 0x7C, 0x00, 0x00,
  0x0F, 0x80, 0x00, 0x01, 0xE0, 0x00, 0x00, 0x7C, 0x00, 0x00, 0x0F, 0x80,
  0x00, 0x01, 0xE0, 0x00, 0x00, 0x3C, 0x00, 0x00, 0x0F, 0x80, 0x00, 0x01,
  0xF0, 0x00, 0x08, 0x3C, 0x00, 0x03, 0x0F, 0x80, 0x00, 0x41, 0xF0, 0x00,
  0x18, 0x3C, 0x00, 0x07, 0x07, 0x80, 0x01, 0xC1, 0xF8, 0x01, 0xF8, 0x7F,
  0xFF, 0xFF, 0x3F, 0xFF, 0xFF, 0xC0, 0x01, 0xFF, 0x00, 0x00, 0x3F, 0xC0,
  0x0F, 0xC0, 0x00, 0x1F, 0xC0, 0x01, 0xF0, 0x00, 0x0F, 0xE0, 0x00, 0xFC,
  0x00, 0x03, 0xF0, 0x00, 0x3F, 0x00, 0x01, 0xFC, 0x00, 0x0F, 0xC0, 0x00,
  0xFF, 0x00, 0x02, 0xF0, 0x00, 0x37, 0x80, 0x01, 0xBC, 0x00, 0x19, 0xE0,
  0x00, 0x6F, 0x80, 0x0E, 0xF8, 0x00, 0x1B, 0xE0, 0x03, 0x3E, 0x00, 0x04,
  0x78, 0x01, 0x8F, 0x00, 0x03, 0x1E, 0x00, 0xE7, 0xC0, 0x00, 0xC7, 0x80,
  0x31, 0xF0, 0x00, 0x21, 0xE0, 0x18, 0x78, 0x00, 0x18, 0x78, 0x0E, 0x1E,
  0x00, 0x06, 0x1E, 0x03, 0x0F, 0x80, 0x01, 0x87, 0x81, 0x83, 0xE0, 0x00,
  0x41, 0xF0, 0xE0, 0xF0, 0x00, 0x30, 0x7C, 0x30, 0x3C, 0x00, 0x0C, 0x0F,
  0x18, 0x1F, 0x00, 0x03, 0x03, 0xCE, 0x07, 0xC0, 0x01, 0x80, 0xF3, 0x01,
  0xE0, 0x00, 0x60, 0x3D, 0x80, 0xF8, 0x00, 0x18, 0x0F, 0xE0, 0x3E, 0x00,
  0x0C, 0x03, 0xF0, 0x0F, 0x00, 0x03, 0x00, 0xF8, 0x03, 0xC0, 0x00, 0xC0,
  0x3E, 0x01, 0xF0, 0x00, 0x70, 0x0F, 0x00, 0x7C, 0x00, 0x1C, 0x01, 0x80,
  0x3F, 0x00, 0x0F, 0x80, 0x60, 0x1F, 0xC0, 0x0F, 0xF8, 0x10, 0x1F, 0xFE,
  0x00, 0x03, 0xFC, 0x00, 0x3F, 0xE0, 0x1F, 0xC0, 0x01, 0xF8, 0x00, 0xF8,
  0x00, 0x1C, 0x00, 0x1F, 0x00, 0x03, 0x80, 0x03, 0xF0, 0x00, 0x60, 0x00,
  0x7E, 0x00, 0x0C, 0x00, 0x0B, 0xE0, 0x03, 0x80, 0x03, 0x7C, 0x00, 0x60,
  0x00, 0x67, 0x80, 0x0C, 0x00, 0x0C, 0xF8, 0x03, 0x80, 0x03, 0x0F, 0x00,
  0x70, 0x00, 0x61, 0xF0, 0x0C, 0x00, 0x0C, 0x3E, 0x01, 0x80, 0x01, 0x83,
  0xC0, 0x70, 0x00, 0x60, 0x7C, 0x0C, 0x00, 0x0C, 0x07, 0x81, 0x80, 0x01,
  0x80, 0xF8, 0x30, 0x00, 0x60, 0x0F, 0x0E, 0x00, 0x0C, 0x01, 0xE1, 0x80,
  0x01, 0x80, 0x3E, 0x30, 0x00, 0x30, 0x03, 0xCE, 0x00, 0x0C, 0x00, 0x7D,
  0x80, 0x01, 0x80, 0x07, 0xB0, 0x00, 0x30, 0x00, 0xF6, 0x00, 0x0E, 0x00,
  0x1F, 0xC0, 0x01, 0x80, 0x01, 0xF0, 0x00, 0x30, 0x00, 0x3E, 0x00, 0x0E,
  0x00, 0x03, 0xC0, 0x01, 0xC0, 0x00, 0x70, 0x00, 0x7C, 0x00, 0x06, 0x00,
  0x3F, 0xE0, 0x00, 0xC0, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0xFE,
  0x00, 0x00, 0x1F, 0xFE, 0x00, 0x01, 0xF0, 0x7C, 0x00, 0x0F, 0x00, 0x78,
  0x00, 0x78, 0x00, 0xF0, 0x07, 0xC0, 0x03, 0xE0, 0x3E, 0x00, 0x07, 0x81,
  0xF0, 0x00, 0x1E, 0x07, 0xC0, 0x00, 0x7C, 0x3E, 0x00, 0x01, 0xF1, 0xF0,
  0x00, 0x07, 0xC7, 0xC0, 0x00, 0x1F, 0x3F, 0x00, 0x00, 0x7C, 0xF8, 0x00,
  0x01, 0xF7, 0xE0, 0x00, 0x0F, 0xDF, 0x00, 0x00, 0x3F, 0x7C, 0x00, 0x00,
  0xFB, 0xF0, 0x00, 0x07, 0xEF, 0xC0, 0x00, 0x1F, 0xBE, 0x00, 0x00, 0x7C,
  0xF8, 0x00, 0x03, 0xF3, 0xE0, 0x00, 0x0F, 0x8F, 0x80, 0x00, 0x3E, 0x3E,
  0x00, 0x01, 0xF0, 0xF8, 0x00, 0x0F, 0x81, 0xE0, 0x00, 0x3E, 0x07, 0x80,
  0x01, 0xF0, 0x1F, 0x00, 0x0F, 0x80, 0x3C, 0x00, 0x7C, 0x00, 0x78, 0x03,
  0xC0, 0x00, 0xF8, 0x3E, 0x00, 0x01, 0xFF, 0xE0, 0x00, 0x01, 0xFC, 0x00,
  0x00, 0x03, 0xFF, 0xFE, 0x00, 0x03, 0xFF, 0xFE, 0x00, 0x0F, 0x81, 0xF8,
  0x00, 0x7C, 0x03, 0xE0, 0x03, 0xE0, 0x1F, 0x00, 0x1E, 0x00, 0x7C, 0x01,
  0xF0, 0x03, 0xE0, 0x0F, 0x80, 0x1F, 0x00, 0x78, 0x00, 0xF8, 0x03, 0xC0,
  0x07, 0xC0, 0x3E, 0x00, 0x3C, 0x01, 0xF0, 0x03, 0xE0, 0x0F, 0x00, 0x3E,
  0x00, 0xF8, 0x03, 0xF0, 0x07, 0xC0, 0x7E, 0x00, 0x3F, 0xFF, 0xE0, 0x01,
  0xEF, 0xF8, 0x00, 0x1F, 0x00, 0x00, 0x00, 0xF8, 0x00, 0x00, 0x07, 0x80,
  0x00, 0x00, 0x3C, 0x00, 0x00, 0x03, 0xE0, 0x00, 0x00, 0x1F, 0x00, 0x00,
  0x00, 0xF0, 0x00, 0x00, 0x0F, 0x80, 0x00, 0x00, 0x7C, 0x00, 0x00, 0x03,
  0xC0, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x01, 0xF0, 0x00, 0x00, 0x1F, 0xC0,
  0x00, 0x03, 0xFF, 0x80, 0x00, 0x00, 0x00, 0x00, 0xFE, 0x00, 0x00, 0x1F,
  0xFE, 0x00, 0x00, 0xF0, 0x7C, 0x00, 0x0F, 0x00, 0x78, 0x00, 0x78, 0x00,
  0xF0, 0x03, 0xC0, 0x03, 0xE0, 0x1E, 0x00, 0x07, 0x80, 0xF0, 0x00, 0x1E,
  0x07, 0xC0, 0x00, 0x7C, 0x3E, 0x00, 0x01, 0xF1, 0xF8, 0x00, 0x07, 0xC7,
  0xC0, 0x00, 0x1F, 0x3F, 0x00, 0x00, 0x7C, 0xF8, 0x00, 0x01, 0xF7, 0xE0,
  0x00, 0x0F, 0xDF, 0x80, 0x00, 0x3F, 0x7C, 0x00, 0x00, 0xFB, 0xF0, 0x00,
  0x03, 0xEF, 0xC0, 0x00, 0x1F, 0xBE, 0x00, 0x00, 0x7C, 0xF8, 0x00, 0x01,
  0xF3, 0xE0, 0x00, 0x0F, 0x8F, 0x80, 0x00, 0x3E, 0x3E, 0x00, 0x01, 0xF0,
  0xF8, 0x00, 0x07, 0xC3, 0xE0, 0x00, 0x3E, 0x07, 0x80, 0x01, 0xF0, 0x1F,
  0x00, 0x07, 0x80, 0x3C, 0x00, 0x3C, 0x00, 0xF8, 0x01, 0xE0, 0x01, 0xE0,
  0x1E, 0x00, 0x01, 0xF3, 0xE0, 0x00, 0x01, 0xFE, 0x00, 0x00, 0x03, 0x00,
  0x00, 0x00, 0x18, 0x00, 0x00, 0x01, 0xC0, 0x00, 0x04, 0x0F, 0xF0, 0x00,
  0x60, 0x7F, 0xFC, 0x07, 0x03, 0xFF, 0xFF, 0xF8, 0x38, 0x1F, 0xFF, 0x80,
  0x00, 0x07, 0xF8, 0x00, 0x03, 0xFF, 0xFE, 0x00, 0x07, 0xFF, 0xF8, 0x00,
  0x3E, 0x0F, 0xC0, 0x03, 0xE0, 0x3E, 0x00, 0x3E, 0x01, 0xF0, 0x03, 0xC0,
  0x1F, 0x00, 0x7C, 0x01, 0xF0, 0x07, 0xC0, 0x1F, 0x00, 0x78, 0x01, 0xF0,
  0x07, 0x80, 0x3E, 0x00, 0xF8, 0x03, 0xE0, 0x0F, 0x80, 0x7C, 0x00, 0xF0,
  0x1F, 0x80, 0x1F, 0xFF, 0xE0, 0x01, 0xFF, 0xF0, 0x00, 0x1E, 0x1E, 0x00,
  0x01, 0xE1, 0xE0, 0x00, 0x3E, 0x1F, 0x00, 0x03, 0xE0, 0xF0, 0x00, 0x3C,
  0x0F, 0x00, 0x03, 0xC0, 0xF8, 0x00, 0x7C, 0x07, 0x80, 0x07, 0xC0, 0x7C,
  0x00, 0x78, 0x03, 0xC0, 0x0F, 0x80, 0x3C, 0x00, 0xF8, 0x03, 0xE0, 0x0F,
  0x00, 0x1E, 0x00, 0xF0, 0x01, 0xE0, 0x1F, 0x00, 0x1F, 0x03, 0xF8, 0x00,
  0xF8, 0xFF, 0xE0, 0x0F, 0xE0, 0x00, 0x3F, 0x06, 0x01, 0xFF, 0xDC, 0x07,
  0xC1, 0xF0, 0x1E, 0x01, 0xE0, 0x3C, 0x01, 0xC0, 0xF0, 0x03, 0x81, 0xE0,
  0x03, 0x03, 0xC0, 0x04, 0x07, 0x80, 0x08, 0x0F, 0x80, 0x00, 0x1F, 0x00,
  0x00, 0x1F, 0x00, 0x00, 0x3F, 0x00, 0x00, 0x3F, 0x00, 0x00, 0x3F, 0x00,
  0x00, 0x3F, 0x00, 0x00, 0x3F, 0x00, 0x00, 0x3F, 0x00, 0x00, 0x3F, 0x00,
  0x00, 0x3E, 0x00, 0x00, 0x7E, 0x00, 0x00, 0x7C, 0x08, 0x00, 0x78, 0x10,
  0x00, 0xF0, 0x20, 0x01, 0xE0, 0xC0, 0x03, 0xC1, 0x80, 0x07, 0x83, 0x80,
  0x1E, 0x07, 0x00, 0x3C, 0x0F, 0x00, 0xF0, 0x1F, 0x87, 0xC0, 0x23, 0xFF,
  0x00, 0x81, 0xF8, 0x00, 0x3F, 0xFF, 0xFF, 0xE7, 0xFF, 0xFF, 0xFD, 0xF0,
  0x3E, 0x07, 0xB8, 0x07, 0xC0, 0x76, 0x00, 0xF8, 0x04, 0x80, 0x3E, 0x00,
  0xB0, 0x07, 0xC0, 0x14, 0x00, 0xF8, 0x02, 0x00, 0x1E, 0x00, 0x00, 0x07,
  0xC0, 0x00, 0x00, 0xF8, 0x00, 0x00, 0x1F, 0x00, 0x00, 0x03, 0xC0, 0x00,
  0x00, 0xF8, 0x00, 0x00, 0x1F, 0x00, 0x00, 0x03, 0xC0, 0x00, 0x00, 0xF8,
  0x00, 0x00, 0x1F, 0x00, 0x00, 0x03, 0xE0, 0x00, 0x00, 0x78, 0x00, 0x00,
  0x1F, 0x00, 0x00, 0x03, 0xE0, 0x00, 0x00, 0x78, 0x00, 0x00, 0x0F, 0x00,
  0x00, 0x03, 0xE0, 0x00, 0x00, 0x7C, 0x00, 0x00, 0x0F, 0x00, 0x00, 0x03,
  0xE0, 0x00, 0x00, 0x7C, 0x00, 0x00, 0x3F, 0xC0, 0x00, 0x3F, 0xFF, 0x00,
  0x00, 0x7F, 0xFE, 0x03, 0xFE, 0x1F, 0xE0, 0x01, 0xF8, 0x1F, 0x80, 0x01,
  0xC0, 0x3E, 0x00, 0x03, 0x80, 0x7C, 0x00, 0x07, 0x00, 0xF8, 0x00, 0x0C,
  0x03, 0xE0, 0x00, 0x18, 0x07, 0xC0, 0x00, 0x70, 0x0F, 0x80, 0x00, 0xC0,
  0x1F, 0x00, 0x01, 0x80, 0x7C, 0x00, 0x03, 0x00, 0xF8, 0x00, 0x0E, 0x01,
  0xF0, 0x00, 0x18, 0x07, 0xC0, 0x00, 0x30, 0x0F, 0x80, 0x00, 0x60, 0x1F,
  0x00, 0x01, 0x80, 0x3E, 0x00, 0x03, 0x00, 0xF8, 0x00, 0x06, 0x01, 0xF0,
  0x00, 0x18, 0x03, 0xE0, 0x00, 0x30, 0x07, 0xC0, 0x00, 0x60, 0x1F, 0x00,
  0x00, 0xC0, 0x3E, 0x00, 0x03, 0x00, 0x7C, 0x00, 0x06, 0x00, 0xF8, 0x00,
  0x18, 0x01, 0xF0, 0x00, 0x30, 0x03, 0xE0, 0x00, 0xC0, 0x03, 0xE0, 0x03,
  0x80, 0x03, 0xE0, 0x0E, 0x00, 0x03, 0xF0, 0x78, 0x00, 0x03, 0xFF, 0xC0,
  0x00, 0x01, 0xFE, 0x00, 0x00, 0xFF, 0xE0, 0x0F, 0xF9, 0xFC, 0x00, 0x1F,
  0x07, 0xC0, 0x00, 0x78, 0x3E, 0x00, 0x03, 0x81, 0xF0, 0x00, 0x18, 0x0F,
  0x80, 0x01, 0xC0, 0x7C, 0x00, 0x0C, 0x01, 0xE0, 0x00, 0xC0, 0x0F, 0x80,
  0x06, 0x00, 0x7C, 0x00, 0x60, 0x03, 0xE0, 0x07, 0x00, 0x1F, 0x00, 0x30,
  0x00, 0xF8, 0x03, 0x00, 0x03, 0xC0, 0x18, 0x00, 0x1E, 0x01, 0x80, 0x00,
  0xF8, 0x1C, 0x00, 0x07, 0xC0, 0xC0, 0x00, 0x3E, 0x0C, 0x00, 0x01, 0xF0,
  0x60, 0x00, 0x07, 0x86, 0x00, 0x00, 0x3C, 0x30, 0x00, 0x01, 0xE3, 0x00,
  0x00, 0x0F, 0xB0, 0x00, 0x00, 0x7D, 0x80, 0x00, 0x03, 0xF8, 0x00, 0x00,
  0x0F, 0xC0, 0x00, 0x00, 0x7C, 0x00, 0x00, 0x03, 0xC0, 0x00, 0x00, 0x1E,
  0x00, 0x00, 0x00, 0xE0, 0x00, 0x00, 0x07, 0x00, 0x00, 0x00, 0x10, 0x00,
  0x00, 0xFF, 0xE3, 0xFF, 0x81, 0xFE, 0x7F, 0x01, 0xFC, 0x00, 0xF8, 0x7C,
  0x01, 0xF0, 0x00, 0xE0, 0xF8, 0x03, 0xE0, 0x01, 0x81, 0xF0, 0x03, 0xC0,
  0x07, 0x03, 0xE0, 0x07, 0x80, 0x0C, 0x03, 0xC0, 0x0F, 0x00, 0x18, 0x07,
  0x80, 0x1E, 0x00, 0x60, 0x0F, 0x00, 0x7E, 0x00, 0xC0, 0x1F, 0x00, 0xFC,
  0x03, 0x00, 0x3E, 0x03, 0xF8, 0x06, 0x00, 0x7C, 0x05, 0xF0, 0x18, 0x00,
  0xF8, 0x1B, 0xE0, 0x30, 0x01, 0xF0, 0x33, 0xC0, 0xC0, 0x01, 0xE0, 0xC7,
  0x83, 0x80, 0x03, 0xC1, 0x8F, 0x06, 0x00, 0x07, 0x86, 0x1E, 0x1C, 0x00,
  0x0F, 0x0C, 0x3C, 0x30, 0x00, 0x1F, 0x30, 0x7C, 0xE0, 0x00, 0x3E, 0x60,
  0xF9, 0x80, 0x00, 0x7D, 0x81, 0xF7, 0x00, 0x00, 0xFB, 0x03, 0xEC, 0x00,
  0x01, 0xFC, 0x03, 0xF8, 0x00, 0x01, 0xF8, 0x07, 0xE0, 0x00, 0x03, 0xE0,
  0x0F, 0x80, 0x00, 0x07, 0xC0, 0x1F, 0x00, 0x00, 0x0F, 0x00, 0x3C, 0x00,
  0x00, 0x1E, 0x00, 0x78, 0x00, 0x00, 0x38, 0x00, 0xE0, 0x00, 0x00, 0x70,
  0x01, 0xC0, 0x00, 0x00, 0xC0, 0x03, 0x00, 0x00, 0x00, 0x80, 0x06, 0x00,
  0x00, 0x07, 0xFF, 0x83, 0xFF, 0x01, 0xFE, 0x00, 0xFE, 0x00, 0x7C, 0x00,
  0x78, 0x00, 0x7C, 0x00, 0x70, 0x00, 0x3C, 0x00, 0xE0, 0x00, 0x3E, 0x01,
  0xC0, 0x00, 0x3E, 0x01, 0x80, 0x00, 0x1F, 0x03, 0x00, 0x00, 0x1F, 0x07,
  0x00, 0x00, 0x0F, 0x0E, 0x00, 0x00, 0x0F, 0x9C, 0x00, 0x00, 0x0F, 0xB8,
  0x00, 0x00, 0x07, 0xF0, 0x00, 0x00, 0x07, 0xE0, 0x00, 0x00, 0x03, 0xC0,
  0x00, 0x00, 0x03, 0xE0, 0x00, 0x00, 0x03, 0xE0, 0x00, 0x00, 0x07, 0xF0,
  0x00, 0x00, 0x0F, 0xF0, 0x00, 0x00, 0x1C, 0xF0, 0x00, 0x00, 0x38, 0xF8,
  0x00, 0x00, 0x30, 0xF8, 0x00, 0x00, 0x60, 0x7C, 0x00, 0x00, 0xC0, 0x7C,
  0x00, 0x01, 0xC0, 0x3C, 0x00, 0x03, 0x80, 0x3E, 0x00, 0x07, 0x00, 0x3E,
  0x00, 0x0E, 0x00, 0x1F, 0x00, 0x1E, 0x00, 0x1F, 0x00, 0x7F, 0x00, 0x3F,
  0xC0, 0xFF, 0xC1, 0xFF, 0xF0, 0x7F, 0xF0, 0x7F, 0xC7, 0xF0, 0x03, 0xE0,
  0xF8, 0x00, 0x70, 0x3E, 0x00, 0x38, 0x07, 0x80, 0x0C, 0x01, 0xE0, 0x07,
  0x00, 0x7C, 0x03, 0x80, 0x1F, 0x00, 0xC0, 0x03, 0xC0, 0x60, 0x00, 0xF0,
  0x30, 0x00, 0x3E, 0x1C, 0x00, 0x07, 0x8E, 0x00, 0x01, 0xE3, 0x00, 0x00,
  0x7D, 0x80, 0x00, 0x1F, 0xC0, 0x00, 0x03, 0xF0, 0x00, 0x00, 0xF8, 0x00,
  0x00, 0x3C, 0x00, 0x00, 0x0F, 0x00, 0x00, 0x07, 0xC0, 0x00, 0x01, 0xF0,
  0x00, 0x00, 0x78, 0x00, 0x00, 0x3E, 0x00, 0x00, 0x0F, 0x80, 0x00, 0x03,
  0xC0, 0x00, 0x00, 0xF0, 0x00, 0x00, 0x7C, 0x00, 0x00, 0x1F, 0x00, 0x00,
  0x0F, 0xC0, 0x00, 0x07, 0xF0, 0x00, 0x0F, 0xFF, 0xC0, 0x00, 0x03, 0xFF,
  0xFF, 0xF8, 0x1F, 0xFF, 0xFF, 0x81, 0xF0, 0x00, 0xFC, 0x0E, 0x00, 0x0F,
  0xC0, 0x60, 0x00, 0xFC, 0x06, 0x00, 0x0F, 0xC0, 0x20, 0x00, 0x7C, 0x00,
  0x00, 0x07, 0xE0, 0x00, 0x00, 0x7E, 0x00, 0x00, 0x07, 0xE0, 0x00, 0x00,
  0x7E, 0x00, 0x00, 0x03, 0xE0, 0x00, 0x00, 0x3F, 0x00, 0x00, 0x03, 0xF0,
  0x00, 0x00, 0x3F, 0x00, 0x00, 0x03, 0xF0, 0x00, 0x00, 0x1F, 0x00, 0x00,
  0x01, 0xF8, 0x00, 0x00, 0x1F, 0x80, 0x00, 0x01, 0xF8, 0x00, 0x00, 0x1F,
  0x80, 0x00, 0x00, 0xF8, 0x00, 0x00, 0x0F, 0xC0, 0x00, 0x00, 0xFC, 0x00,
  0x08, 0x0F, 0xC0, 0x00, 0x80, 0xFC, 0x00, 0x0C, 0x07, 0xC0, 0x00, 0x60,
  0x7E, 0x00, 0x07, 0x07, 0xE0, 0x01, 0xF0, 0x7F, 0xFF, 0xFF, 0x83, 0xFF,
  0xFF, 0xFC, 0x00, 0x00, 0x3F, 0x80, 0x3C, 0x00, 0x1C, 0x00, 0x0E, 0x00,
  0x07, 0x00, 0x07, 0x00, 0x03, 0x80, 0x01, 0xC0, 0x00, 0xE0, 0x00, 0xE0,
  0x00, 0x70, 0x00, 0x38, 0x00, 0x1C, 0x00, 0x1C, 0x00, 0x0E, 0x00, 0x07,
  0x00, 0x03, 0x80, 0x03, 0x80, 0x01, 0xC0, 0x00, 0xE0, 0x00, 0x70, 0x00,
  0x70, 0x00, 0x38, 0x00, 0x1C, 0x00, 0x0E, 0x00, 0x0E, 0x00, 0x07, 0x00,
  0x03, 0x80, 0x01, 0xC0, 0x01, 0xC0, 0x00, 0xE0, 0x00, 0x70, 0x00, 0x38,
  0x00, 0x38, 0x00, 0x1C, 0x00, 0x0E, 0x00, 0x07, 0x00, 0x07, 0x80, 0x03,
  0xFC, 0x00, 0xF0, 0x00, 0x38, 0x00, 0x1E, 0x00, 0x07, 0x00, 0x03, 0x80,
  0x01, 0xE0, 0x00, 0x70, 0x00, 0x3C, 0x00, 0x0E, 0x00, 0x07, 0x00, 0x03,
  0xC0, 0x00, 0xE0, 0x00, 0x78, 0x00, 0x1C, 0x00, 0x0E, 0x00, 0x03, 0x80,
  0x01, 0xC0, 0x00, 0xF0, 0x00, 0x38, 0x00, 0x1E, 0x00, 0x07, 0x00, 0x03,
  0x80, 0x01, 0xE0, 0x00, 0x70, 0x00, 0x3C, 0x00, 0x0E, 0x00, 0x07, 0x00,
  0x03, 0xC0, 0x00, 0xE0, 0x00, 0x78, 0x00, 0x1C, 0x00, 0x0E, 0x00, 0x07,
  0x80, 0x00, 0xFF, 0x80, 0x07, 0x80, 0x01, 0xC0, 0x00, 0xE0, 0x00, 0xF0,
  0x00, 0x70, 0x00, 0x38, 0x00, 0x1C, 0x00, 0x0E, 0x00, 0x0E, 0x00, 0x07,
  0x00, 0x03, 0x80, 0x01, 0xC0, 0x01, 0xE0, 0x00, 0xE0, 0x00, 0x70, 0x00,
  0x38, 0x00, 0x1C, 0x00, 0x1C, 0x00, 0x0E, 0x00, 0x07, 0x00, 0x03, 0x80,
  0x03, 0x80, 0x01, 0xC0, 0x00, 0xE0, 0x00, 0x70, 0x00, 0x78, 0x00, 0x38,
  0x00, 0x1C, 0x00, 0x0E, 0x00, 0x07, 0x00, 0x07, 0x00, 0x03, 0x80, 0x01,
  0xC0, 0x00, 0xE0, 0x00, 0xF0, 0x00, 0x70, 0x00, 0x38, 0x03, 0xFC, 0x00,
  0x00, 0xF0, 0x00, 0x0F, 0x00, 0x01, 0xF8, 0x00, 0x1F, 0x80, 0x03, 0xBC,
  0x00, 0x39, 0xC0, 0x07, 0x1E, 0x00, 0x70, 0xE0, 0x0E, 0x0F, 0x00, 0xE0,
  0x70, 0x1E, 0x07, 0x81, 0xC0, 0x38, 0x3C, 0x03, 0xC3, 0x80, 0x1C, 0x78,
  0x01, 0xE7, 0x00, 0x0E, 0xF0, 0x00, 0xF0, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0x60, 0xF0, 0xF8, 0x78, 0x3C, 0x1E, 0x0E, 0x07, 0x00, 0x1E, 0x70,
  0x03, 0x0B, 0x80, 0x70, 0x3C, 0x07, 0x01, 0xE0, 0x70, 0x0E, 0x07, 0x00,
  0x70, 0x78, 0x03, 0x83, 0x80, 0x38, 0x3C, 0x01, 0xC1, 0xC0, 0x0E, 0x1E,
  0x00, 0xF0, 0xF0, 0x07, 0x0F, 0x00, 0x78, 0x78, 0x03, 0xC3, 0xC0, 0x3E,
  0x1E, 0x01, 0x70, 0xF0, 0x17, 0x0F, 0x81, 0x38, 0xBE, 0x11, 0xC8, 0xFF,
  0x0F, 0x83, 0xF0, 0x70, 0x00, 0x00, 0xF0, 0x00, 0x7F, 0x00, 0x00, 0x78,
  0x00, 0x03, 0xC0, 0x00, 0x1E, 0x00, 0x00, 0xE0, 0x00, 0x07, 0x00, 0x00,
  0x78, 0x00, 0x03, 0x80, 0x00, 0x1C, 0x00, 0x01, 0xE0, 0x00, 0x0F, 0x0F,
  0x80, 0x71, 0xFE, 0x03, 0x98, 0xF8, 0x3D, 0x03, 0xE1, 0xE8, 0x0F, 0x0E,
  0x80, 0x78, 0x78, 0x03, 0xC7, 0xC0, 0x1E, 0x3C, 0x00, 0xF1, 0xE0, 0x0F,
  0x1E, 0x00, 0x78, 0xF0, 0x03, 0xC7, 0x80, 0x3C, 0x38, 0x01, 0xE3, 0xC0,
  0x1E, 0x1E, 0x00, 0xE0, 0xE0, 0x0E, 0x07, 0x00, 0xF0, 0x78, 0x07, 0x03,
  0xC0, 0xE0, 0x0F, 0x0E, 0x00, 0x1F, 0x80, 0x00, 0x00, 0x3F, 0x00, 0x38,
  0x60, 0x38, 0x1C, 0x1C, 0x0F, 0x0E, 0x03, 0x87, 0x80, 0x03, 0xC0, 0x00,
  0xE0, 0x00, 0x78, 0x00, 0x1E, 0x00, 0x07, 0x00, 0x03, 0xC0, 0x00, 0xF0,
  0x00, 0x3C, 0x00, 0x0F, 0x00, 0x03, 0xC0, 0x00, 0xF0, 0x02, 0x3E, 0x01,
  0x87, 0x80, 0xC1, 0xF0, 0x60, 0x3F, 0xF0, 0x03, 0xF0, 0x00, 0x00, 0x00,
  0x0E, 0x00, 0x00, 0xFE, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x1E, 0x00, 0x00,
  0x1C, 0x00, 0x00, 0x1C, 0x00, 0x00, 0x1C, 0x00, 0x00, 0x3C, 0x00, 0x00,
  0x38, 0x00, 0x00, 0x38, 0x00, 0x00, 0x78, 0x00, 0x1E, 0x78, 0x00, 0x71,
  0x70, 0x00, 0xC1, 0x70, 0x03, 0x80, 0xF0, 0x07, 0x80, 0xE0, 0x07, 0x01,
  0xE0, 0x0E, 0x01, 0xE0, 0x1E, 0x01, 0xE0, 0x3C, 0x01, 0xC0, 0x3C, 0x01,
  0xC0, 0x78, 0x03, 0xC0, 0x78, 0x03, 0xC0, 0x78, 0x03, 0x80, 0xF0, 0x07,
  0x80, 0xF0, 0x07, 0x80, 0xF0, 0x0F, 0x80, 0xF0, 0x0F, 0x00, 0xF0, 0x17,
  0x08, 0xF0, 0x27, 0x10, 0x78, 0x47, 0x20, 0x7F, 0x87, 0xC0, 0x1E, 0x07,
  0x00, 0x00, 0x1F, 0x00, 0x1C, 0xF0, 0x1C, 0x1C, 0x0E, 0x07, 0x07, 0x01,
  0xC3, 0xC0, 0xF1, 0xE0, 0x38, 0x70, 0x1C, 0x3C, 0x0E, 0x1F, 0x0F, 0x07,
  0x8F, 0x01, 0xFE, 0x00, 0xF0, 0x00, 0x3C, 0x00, 0x0F, 0x00, 0x03, 0xC0,
  0x00, 0xF0, 0x01, 0x3C, 0x00, 0xC7, 0x80, 0x61, 0xF0, 0x60, 0x3F, 0xF0,
  0x03, 0xE0, 0x00, 0x00, 0x00, 0x07, 0xC0, 0x00, 0x03, 0x1C, 0x00, 0x00,
  0xC3, 0x80, 0x00, 0x38, 0x70, 0x00, 0x06, 0x00, 0x00, 0x01, 0xC0, 0x00,
  0x00, 0x30, 0x00, 0x00, 0x0E, 0x00, 0x00, 0x01, 0xC0, 0x00, 0x00, 0x78,
  0x00, 0x00, 0x0E, 0x00, 0x00, 0x01, 0xC0, 0x00, 0x07, 0xFF, 0xC0, 0x00,
  0xFF, 0xF8, 0x00, 0x01, 0xE0, 0x00, 0x00, 0x38, 0x00, 0x00, 0x07, 0x00,
  0x00, 0x01, 0xE0, 0x00, 0x00, 0x3C, 0x00, 0x00, 0x07, 0x80, 0x00, 0x00,
  0xE0, 0x00, 0x00, 0x1C, 0x00, 0x00, 0x07, 0x80, 0x00, 0x00, 0xF0, 0x00,
  0x00, 0x1C, 0x00, 0x00, 0x03, 0x80, 0x00, 0x00, 0xF0, 0x00, 0x00, 0x1E,
  0x00, 0x00, 0x03, 0x80, 0x00, 0x00, 0x70, 0x00, 0x00, 0x0E, 0x00, 0x00,
  0x03, 0xC0, 0x00, 0x00, 0x70, 0x00, 0x00, 0x0E, 0x00, 0x00, 0x01, 0xC0,
  0x00, 0x00, 0x70, 0x00, 0x00, 0x0E, 0x00, 0x00, 0x01, 0x80, 0x00, 0x38,
  0x60, 0x00, 0x07, 0x0C, 0x00, 0x00, 0xE3, 0x00, 0x00, 0x0F, 0x80, 0x00,
  0x00, 0x00, 0x3F, 0x00, 0x07, 0x0E, 0x00, 0x70, 0x3E, 0x07, 0x01, 0xF0,
  0x70, 0x0E, 0x07, 0x80, 0x70, 0x3C, 0x03, 0x81, 0xC0, 0x1C, 0x0E, 0x01,
  0xE0, 0x70, 0x0E, 0x03, 0x80, 0xF0, 0x0E, 0x0F, 0x00, 0x30, 0xE0, 0x00,
  0xFE, 0x00, 0x0C, 0x00, 0x00, 0xC0, 0x00, 0x0E, 0x00, 0x00, 0x7E, 0x00,
  0x03, 0xFE, 0x00, 0x0F, 0xFC, 0x00, 0x8F, 0xF0, 0x18, 0x0F, 0xC1, 0x80,
  0x1F, 0x18, 0x00, 0x78, 0xC0, 0x01, 0xC6, 0x00, 0x0E, 0x30, 0x00, 0x61,
  0xC0, 0x07, 0x06, 0x00, 0x70, 0x1C, 0x0E, 0x00, 0x3F, 0xC0, 0x00, 0x00,
  0xF0, 0x00, 0x7F, 0x00, 0x00, 0x78, 0x00, 0x03, 0xC0, 0x00, 0x1E, 0x00,
  0x00, 0xE0, 0x00, 0x07, 0x00, 0x00, 0x78, 0x00, 0x03, 0xC0, 0x00, 0x1C,
  0x00, 0x00, 0xE0, 0x00, 0x0F, 0x03, 0x80, 0x78, 0x7E, 0x03, 0x86, 0x70,
  0x3C, 0x43, 0x81, 0xE4, 0x1C, 0x0E, 0x40, 0xE0, 0x74, 0x0E, 0x07, 0xA0,
  0x70, 0x3E, 0x03, 0x81, 0xE0, 0x1C, 0x0F, 0x00, 0xE0, 0xF0, 0x0E, 0x07,
  0x80, 0x70, 0x38, 0x03, 0x81, 0xC0, 0x1C, 0x1E, 0x00, 0xC2, 0xF0, 0x0E,
  0x27, 0x00, 0x73, 0x38, 0x03, 0x93, 0xC0, 0x1F, 0x1E, 0x00, 0xE0, 0x03,
  0x81, 0xF0, 0x7C, 0x1F, 0x03, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x71, 0xFC, 0x1F, 0x07, 0x81, 0xE0, 0x78, 0x1C, 0x07, 0x03, 0xC0, 0xF0,
  0x38, 0x0E, 0x07, 0x81, 0xE0, 0x70, 0x1C, 0x0F, 0x03, 0x84, 0xE2, 0x39,
  0x0F, 0x81, 0xC0, 0x00, 0x01, 0xC0, 0x00, 0x7C, 0x00, 0x0F, 0x80, 0x01,
  0xF0, 0x00, 0x1C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x0E, 0x00, 0x3F, 0xC0, 0x00, 0xF0, 0x00, 0x1E,
  0x00, 0x03, 0xC0, 0x00, 0x78, 0x00, 0x0E, 0x00, 0x03, 0xC0, 0x00, 0x78,
  0x00, 0x0F, 0x00, 0x01, 0xC0, 0x00, 0x38, 0x00, 0x0F, 0x00, 0x01, 0xE0,
  0x00, 0x38, 0x00, 0x07, 0x00, 0x01, 0xE0, 0x00, 0x38, 0x00, 0x07, 0x00,
  0x00, 0xE0, 0x00, 0x3C, 0x00, 0x07, 0x00, 0x00, 0xE0, 0x00, 0x1C, 0x00,
  0x07, 0x00, 0x00, 0xE0, 0x00, 0x1C, 0x01, 0xC7, 0x00, 0x38, 0xC0, 0x07,
  0x30, 0x00, 0x7C, 0x00, 0x00, 0x00, 0xF0, 0x00, 0x7F, 0x00, 0x00, 0x78,
  0x00, 0x03, 0xC0, 0x00, 0x1E, 0x00, 0x00, 0xE0, 0x00, 0x07, 0x00, 0x00,
  0x78, 0x00, 0x03, 0xC0, 0x00, 0x1C, 0x00, 0x00, 0xE0, 0x00, 0x0F, 0x00,
  0x00, 0x70, 0xFF, 0x83, 0x80, 0xF0, 0x3C, 0x06, 0x01, 0xE0, 0x60, 0x0E,
  0x06, 0x00, 0x70, 0xE0, 0x07, 0x8C, 0x00, 0x3C, 0xC0, 0x01, 0xCC, 0x00,
  0x0F, 0xF0, 0x00, 0xFF, 0x80, 0x07, 0x9E, 0x00, 0x38, 0xF0, 0x01, 0xC3,
  0x80, 0x1E, 0x1E, 0x00, 0xF0, 0x70, 0x07, 0x03, 0xC2, 0x78, 0x0E, 0x13,
  0xC0, 0x79, 0x1E, 0x01, 0xF0, 0x00, 0x07, 0x00, 0x00, 0xE1, 0xFC, 0x0F,
  0x80, 0xE0, 0x3C, 0x07, 0x80, 0xF0, 0x1C, 0x07, 0x80, 0xF0, 0x1E, 0x03,
  0x80, 0xF0, 0x1E, 0x03, 0xC0, 0x70, 0x1E, 0x03, 0xC0, 0x78, 0x0E, 0x03,
  0xC0, 0x78, 0x0E, 0x01, 0xC0, 0x78, 0x0F, 0x01, 0xC0, 0x38, 0x4F, 0x11,
  0xE4, 0x39, 0x07, 0xC0, 0x70, 0x00, 0x07, 0x81, 0xC0, 0x78, 0xFE, 0x0F,
  0xC1, 0xF8, 0x3C, 0x33, 0x84, 0x70, 0x78, 0x87, 0x10, 0xE0, 0xF2, 0x0E,
  0x41, 0xC1, 0xC8, 0x39, 0x07, 0x87, 0xA0, 0x74, 0x0F, 0x0F, 0x40, 0xE8,
  0x1E, 0x1F, 0x01, 0xE0, 0x38, 0x3C, 0x07, 0xC0, 0xF0, 0xF8, 0x0F, 0x01,
  0xE1, 0xE0, 0x1E, 0x03, 0xC3, 0xC0, 0x38, 0x07, 0x07, 0x00, 0xF0, 0x1E,
  0x1E, 0x01, 0xE0, 0x3C, 0x3C, 0x03, 0x80, 0x79, 0x70, 0x07, 0x00, 0xE2,
  0xE0, 0x1E, 0x03, 0x8B, 0xC0, 0x3C, 0x07, 0x27, 0x80, 0x70, 0x0F, 0x8E,
  0x00, 0xE0, 0x1E, 0x00, 0x07, 0x81, 0xE3, 0xFC, 0x3F, 0x83, 0xC2, 0x3C,
  0x1E, 0x21, 0xE0, 0xF2, 0x0F, 0x07, 0x20, 0x70, 0x39, 0x07, 0x83, 0xD0,
  0x3C, 0x1F, 0x01, 0xE0, 0xE8, 0x0E, 0x0F, 0x80, 0xF0, 0x78, 0x07, 0x83,
  0xC0, 0x38, 0x1C, 0x01, 0xC1, 0xE0, 0x1E, 0x0F, 0x00, 0xF1, 0x70, 0x07,
  0x0B, 0x80, 0x38, 0xBC, 0x01, 0xC9, 0xE0, 0x0F, 0x8E, 0x00, 0x38, 0x00,
  0x00, 0x1F, 0x80, 0x07, 0x8F, 0x00, 0x70, 0x3C, 0x07, 0x00, 0xE0, 0x70,
  0x07, 0x87, 0x80, 0x3C, 0x78, 0x01, 0xE7, 0x80, 0x0F, 0x3C, 0x00, 0x7B,
  0xC0, 0x03, 0xDE, 0x00, 0x3D, 0xF0, 0x01, 0xEF, 0x80, 0x0F, 0x78, 0x00,
  0xF3, 0xC0, 0x07, 0x9E, 0x00, 0x78, 0xF0, 0x03, 0x87, 0x80, 0x38, 0x1C,
  0x03, 0x80, 0xF0, 0x38, 0x03, 0xC3, 0x00, 0x07, 0xE0, 0x00, 0x00, 0x3C,
  0x3F, 0x00, 0x7F, 0x8F, 0xF0, 0x01, 0xF7, 0x3F, 0x00, 0x1D, 0x83, 0xF0,
  0x07, 0xA0, 0x3E, 0x00, 0xF8, 0x07, 0xC0, 0x1E, 0x00, 0xF8, 0x03, 0xC0,
  0x1F, 0x00, 0xF0, 0x03, 0xE0, 0x1E, 0x00, 0x7C, 0x03, 0xC0, 0x1F, 0x00,
  0x70, 0x03, 0xE0, 0x1E, 0x00, 0x78, 0x03, 0xC0, 0x1F, 0x00, 0x70, 0x03,
  0xC0, 0x0E, 0x00, 0xF8, 0x03, 0xC0, 0x1E, 0x00, 0x78, 0x07, 0x80, 0x0F,
  0x01, 0xE0, 0x01, 0xE0, 0x70, 0x00, 0x7C, 0x3C, 0x00, 0x0F, 0x7C, 0x00,
  0x01, 0xC0, 0x00, 0x00, 0x78, 0x00, 0x00, 0x0F, 0x00, 0x00, 0x01, 0xE0,
  0x00, 0x00, 0x38, 0x00, 0x00, 0x0F, 0x00, 0x00, 0x01, 0xE0, 0x00, 0x00,
  0x7E, 0x00, 0x00, 0x3F, 0xF0, 0x00, 0x00, 0x00, 0x1F, 0x00, 0x03, 0x8D,
  0xC0, 0x38, 0x2E, 0x07, 0x80, 0xF0, 0x78, 0x07, 0x03, 0x80, 0x38, 0x38,
  0x03, 0xC3, 0xC0, 0x1E, 0x3C, 0x00, 0xE1, 0xE0, 0x07, 0x1E, 0x00, 0x78,
  0xF0, 0x03, 0x87, 0x80, 0x3C, 0x78, 0x01, 0xE3, 0xC0, 0x1F, 0x1E, 0x01,
  0x70, 0xF0, 0x17, 0x87, 0x80, 0xBC, 0x3C, 0x09, 0xC0, 0xF1, 0x8E, 0x07,
  0xF8, 0xF0, 0x1F, 0x07, 0x80, 0x00, 0x38, 0x00, 0x03, 0xC0, 0x00, 0x1E,
  0x00, 0x00, 0xE0, 0x00, 0x0F, 0x00, 0x00, 0x78, 0x00, 0x03, 0xC0, 0x00,
  0x3E, 0x00, 0x0F, 0xFE, 0x00, 0x07, 0x87, 0x3F, 0x87, 0xC3, 0xC7, 0xE1,
  0xE6, 0xF0, 0xF6, 0x00, 0x72, 0x00, 0x3A, 0x00, 0x1D, 0x00, 0x1F, 0x00,
  0x0E, 0x80, 0x07, 0x80, 0x03, 0xC0, 0x03, 0xC0, 0x01, 0xE0, 0x00, 0xF0,
  0x00, 0xF0, 0x00, 0x78, 0x00, 0x3C, 0x00, 0x1C, 0x00, 0x1E, 0x00, 0x0F,
  0x00, 0x00, 0x01, 0xF8, 0x81, 0x87, 0xC1, 0x80, 0xE1, 0xC0, 0x60, 0xE0,
  0x10, 0x70, 0x08, 0x3C, 0x04, 0x1F, 0x00, 0x07, 0xC0, 0x03, 0xE0, 0x00,
  0xF8, 0x00, 0x3E, 0x00, 0x0F, 0x00, 0x03, 0xC1, 0x01, 0xE0, 0x80, 0x70,
  0x40, 0x38, 0x30, 0x1C, 0x38, 0x0C, 0x1C, 0x0E, 0x0F, 0x0E, 0x04, 0x7C,
  0x00, 0x00, 0xC0, 0x18, 0x03, 0x80, 0x78, 0x1F, 0x03, 0xFF, 0x7F, 0xF0,
  0xF0, 0x0E, 0x00, 0xE0, 0x1E, 0x01, 0xE0, 0x1C, 0x01, 0xC0, 0x3C, 0x03,
  0xC0, 0x38, 0x03, 0x80, 0x78, 0x07, 0x80, 0x70, 0x8F, 0x10, 0xF1, 0x0F,
  0x20, 0xFC, 0x07, 0x80, 0x00, 0x00, 0x00, 0xF0, 0x0E, 0x7F, 0x00, 0xE0,
  0xF0, 0x1E, 0x0E, 0x01, 0xE1, 0xE0, 0x3C, 0x1E, 0x03, 0xC1, 0xE0, 0x3C,
  0x1C, 0x07, 0xC3, 0xC0, 0x78, 0x3C, 0x0F, 0x83, 0xC0, 0xB8, 0x38, 0x1F,
  0x87, 0x83, 0x70, 0x78, 0x27, 0x07, 0x86, 0x70, 0x70, 0xC7, 0x1F, 0x08,
  0xE1, 0xE1, 0x0E, 0x2E, 0x60, 0xE4, 0xFC, 0x0F, 0x87, 0x00, 0x70, 0x1C,
  0x03, 0xBF, 0x00, 0xF1, 0xE0, 0x3C, 0x78, 0x07, 0x1E, 0x00, 0xC3, 0x80,
  0x30, 0xE0, 0x08, 0x38, 0x06, 0x0E, 0x01, 0x03, 0x80, 0xC0, 0xF0, 0x20,
  0x3C, 0x10, 0x07, 0x04, 0x01, 0xC2, 0x00, 0x71, 0x00, 0x1C, 0xC0, 0x07,
  0x60, 0x01, 0xF0, 0x00, 0x78, 0x00, 0x1C, 0x00, 0x06, 0x00, 0x01, 0x00,
  0x00, 0x0C, 0x00, 0x40, 0x3B, 0xF8, 0x01, 0x00, 0xF1, 0xE0, 0x0C, 0x03,
  0xC3, 0x80, 0x78, 0x07, 0x0E, 0x01, 0xE0, 0x0C, 0x38, 0x0F, 0x80, 0x20,
  0xE0, 0x6E, 0x00, 0x83, 0x81, 0x38, 0x04, 0x0F, 0x0C, 0xE0, 0x10, 0x1C,
  0x23, 0x80, 0x80, 0x71, 0x8E, 0x06, 0x01, 0xCC, 0x38, 0x10, 0x07, 0x20,
  0xE0, 0x80, 0x1D, 0x83, 0x86, 0x00, 0x7C, 0x07, 0x30, 0x01, 0xF0, 0x1C,
  0x80, 0x07, 0x80, 0x74, 0x00, 0x1E, 0x01, 0xF0, 0x00, 0x70, 0x07, 0x80,
  0x01, 0xC0, 0x1C, 0x00, 0x06, 0x00, 0x60, 0x00, 0x10, 0x01, 0x00, 0x00,
  0x00, 0xE0, 0x38, 0x1F, 0x81, 0xF0, 0x8F, 0x09, 0x80, 0x3C, 0x40, 0x00,
  0x72, 0x00, 0x01, 0xD0, 0x00, 0x07, 0xC0, 0x00, 0x1E, 0x00, 0x00, 0x38,
  0x00, 0x00, 0xE0, 0x00, 0x03, 0x80, 0x00, 0x0F, 0x00, 0x00, 0x7C, 0x00,
  0x01, 0x70, 0x00, 0x09, 0xC0, 0x00, 0x67, 0x00, 0x01, 0x1E, 0x10, 0x08,
  0x38, 0x40, 0x40, 0xE2, 0x39, 0x03, 0xD0, 0xF8, 0x0F, 0x83, 0xC0, 0x1C,
  0x00, 0x07, 0x80, 0x33, 0xFC, 0x03, 0xC1, 0xE0, 0x1E, 0x07, 0x80, 0x70,
  0x3C, 0x01, 0x80, 0xE0, 0x0C, 0x07, 0x80, 0x40, 0x3C, 0x02, 0x00, 0xE0,
  0x20, 0x07, 0x81, 0x00, 0x3C, 0x18, 0x01, 0xE0, 0x80, 0x07, 0x0C, 0x00,
  0x38, 0x40, 0x01, 0xE4, 0x00, 0x0F, 0x60, 0x00, 0x3A, 0x00, 0x01, 0xF0,
  0x00, 0x0F, 0x00, 0x00, 0x70, 0x00, 0x03, 0x80, 0x00, 0x18, 0x00, 0x00,
  0x80, 0x00, 0x0C, 0x00, 0x00, 0x40, 0x00, 0x04, 0x00, 0x00, 0x40, 0x00,
  0x04, 0x00, 0x0E, 0x40, 0x00, 0x7C, 0x00, 0x03, 0xC0, 0x00, 0x00, 0x0F,
  0xFF, 0x87, 0xFF, 0x82, 0x00, 0x83, 0x00, 0xC1, 0x00, 0xC0, 0x00, 0xC0,
  0x00, 0xC0, 0x00, 0x60, 0x00, 0x60, 0x00, 0x60, 0x00, 0x60, 0x00, 0x20,
  0x00, 0x30, 0x00, 0x30, 0x00, 0x30, 0x00, 0x30, 0x00, 0x18, 0x00, 0x1E,
  0x00, 0x1F, 0xC0, 0x1F, 0xF0, 0xE8, 0xFC, 0x70, 0x1E, 0x38, 0x03, 0x88,
  0x00, 0x78, 0x00, 0x0F, 0x00, 0x1E, 0x00, 0x1E, 0x00, 0x0E, 0x00, 0x0F,
  0x00, 0x07, 0x80, 0x03, 0x80, 0x01, 0xC0, 0x01, 0xE0, 0x00, 0xF0, 0x00,
  0x70, 0x00, 0x78, 0x00, 0x3C, 0x00, 0x1C, 0x00, 0x0E, 0x00, 0x0F, 0x00,
  0x07, 0x80, 0x07, 0x80, 0x03, 0xC0, 0x07, 0xC0, 0x07, 0xC0, 0x00, 0x80,
  0x00, 0x60, 0x00, 0x38, 0x00, 0x1C, 0x00, 0x0E, 0x00, 0x0F, 0x00, 0x07,
  0x80, 0x03, 0x80, 0x01, 0xC0, 0x01, 0xE0, 0x00, 0xF0, 0x00, 0x70, 0x00,
  0x38, 0x00, 0x3C, 0x00, 0x1E, 0x00, 0x0E, 0x00, 0x07, 0x00, 0x01, 0x80,
  0x00, 0x70, 0x00, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xE0, 0x00, 0x18, 0x00, 0x0E, 0x00, 0x06, 0x00, 0x07,
  0x00, 0x07, 0x00, 0x07, 0x00, 0x07, 0x00, 0x0F, 0x00, 0x0F, 0x00, 0x0E,
  0x00, 0x0E, 0x00, 0x1E, 0x00, 0x1E, 0x00, 0x1C, 0x00, 0x1C, 0x00, 0x3C,
  0x00, 0x3C, 0x00, 0x38, 0x00, 0x38, 0x00, 0x18, 0x00, 0x08, 0x00, 0x1C,
  0x00, 0x7E, 0x00, 0x78, 0x00, 0xF0, 0x00, 0xE0, 0x01, 0xE0, 0x01, 0xE0,
  0x01, 0xC0, 0x01, 0xC0, 0x03, 0xC0, 0x03, 0x80, 0x03, 0x80, 0x07, 0x80,
  0x07, 0x80, 0x07, 0x00, 0x07, 0x00, 0x0F, 0x00, 0x0E, 0x00, 0x1C, 0x00,
  0xF8, 0x00, 0x1F, 0x80, 0x00, 0xFF, 0x80, 0xC7, 0xFF, 0x87, 0xBC, 0x3F,
  0xFE, 0x60, 0x3F, 0xF0, 0x00, 0x1F, 0x00 };

const GFXglyph FreeSerifItalic24pt7bGlyphs[] PROGMEM = {
  {     0,   0,   0,  12,    0,    1 },   // 0x20 ' '
  {     0,  12,  32,  16,    2,  -30 },   // 0x21 '!'
  {    48,  14,  12,  16,    6,  -31 },   // 0x22 '"'
  {    69,  25,  31,  23,    0,  -30 },   // 0x23 '#'
  {   166,  21,  38,  24,    2,  -33 },   // 0x24 '$'
  {   266,  33,  32,  39,    4,  -30 },   // 0x25 '%'
  {   398,  30,  33,  37,    4,  -31 },   // 0x26 '&'
  {   522,   5,  12,   9,    6,  -31 },   // 0x27 '''
  {   530,  13,  39,  16,    2,  -30 },   // 0x28 '('
  {   594,  13,  39,  16,    0,  -30 },   // 0x29 ')'
  {   658,  16,  20,  23,    7,  -31 },   // 0x2A '*'
  {   698,  23,  23,  32,    4,  -22 },   // 0x2B '+'
  {   765,   7,  11,  12,   -1,   -4 },   // 0x2C ','
  {   775,  11,   3,  16,    2,  -11 },   // 0x2D '-'
  {   780,   5,   5,  12,    1,   -3 },   // 0x2E '.'
  {   784,  21,  33,  14,    0,  -31 },   // 0x2F '/'
  {   871,  21,  31,  23,    2,  -30 },   // 0x30 '0'
  {   953,  17,  32,  23,    2,  -31 },   // 0x31 '1'
  {  1021,  21,  31,  24,    0,  -30 },   // 0x32 '2'
  {  1103,  22,  32,  23,    0,  -31 },   // 0x33 '3'
  {  1191,  22,  32,  23,    0,  -31 },   // 0x34 '4'
  {  1279,  22,  32,  24,    0,  -31 },   // 0x35 '5'
  {  1367,  23,  32,  23,    1,  -31 },   // 0x36 '6'
  {  1459,  21,  32,  23,    4,  -31 },   // 0x37 '7'
  {  1543,  22,  32,  23,    1,  -31 },   // 0x38 '8'
  {  1631,  22,  33,  23,    1,  -31 },   // 0x39 '9'
  {  1722,   9,  22,  12,    2,  -20 },   // 0x3A ':'
  {  1747,  11,  27,  12,    1,  -20 },   // 0x3B ';'
  {  1785,  23,  25,  27,    3,  -24 },   // 0x3C '<'
  {  1857,  24,  12,  31,    4,  -17 },   // 0x3D '='
  {  1893,  24,  25,  27,    3,  -24 },   // 0x3E '>'
  {  1968,  16,  33,  21,    6,  -31 },   // 0x3F '?'
  {  2034,  33,  33,  37,    3,  -31 },   // 0x40 '@'
  {  2171,  29,  31,  31,    0,  -30 },   // 0x41 'A'
  {  2284,  28,  31,  28,    0,  -30 },   // 0x42 'B'
  {  2393,  30,  33,  29,    2,  -31 },   // 0x43 'C'
  {  2517,  33,  31,  33,    0,  -30 },   // 0x44 'D'
  {  2645,  29,  31,  27,    0,  -30 },   // 0x45 'E'
  {  2758,  29,  31,  27,    0,  -30 },   // 0x46 'F'
  {  2871,  31,  33,  32,    2,  -31 },   // 0x47 'G'
  {  2999,  36,  31,  33,    0,  -30 },   // 0x48 'H'
  {  3139,  18,  31,  15,    0,  -30 },   // 0x49 'I'
  {  3209,  23,  32,  20,    0,  -30 },   // 0x4A 'J'
  {  3301,  33,  31,  30,    0,  -30 },   // 0x4B 'K'
  {  3429,  27,  31,  27,    0,  -30 },   // 0x4C 'L'
  {  3534,  42,  31,  39,    0,  -30 },   // 0x4D 'M'
  {  3697,  35,  32,  32,    0,  -30 },   // 0x4E 'N'
  {  3837,  30,  33,  31,    2,  -31 },   // 0x4F 'O'
  {  3961,  29,  31,  27,    0,  -30 },   // 0x50 'P'
  {  4074,  30,  41,  31,    2,  -31 },   // 0x51 'Q'
  {  4228,  28,  31,  29,    0,  -30 },   // 0x52 'R'
  {  4337,  23,  33,  21,    0,  -31 },   // 0x53 'S'
  {  4432,  27,  31,  28,    4,  -30 },   // 0x54 'T'
  {  4537,  31,  32,  33,    5,  -30 },   // 0x55 'U'
  {  4661,  29,  32,  31,    6,  -30 },   // 0x56 'V'
  {  4777,  39,  32,  42,    6,  -30 },   // 0x57 'W'
  {  4933,  32,  31,  31,    0,  -30 },   // 0x58 'X'
  {  5057,  26,  31,  28,    5,  -30 },   // 0x59 'Y'
  {  5158,  29,  31,  26,    0,  -30 },   // 0x5A 'Z'
  {  5271,  17,  39,  18,    1,  -31 },   // 0x5B '['
  {  5354,  17,  33,  23,    5,  -31 },   // 0x5C '\'
  {  5425,  17,  39,  18,    1,  -31 },   // 0x5D ']'
  {  5508,  20,  17,  20,    0,  -31 },   // 0x5E '^'
  {  5551,  24,   2,  23,    0,    5 },   // 0x5F '_'
  {  5557,   8,   8,  12,    6,  -31 },   // 0x60 '`'
  {  5565,  21,  21,  23,    1,  -20 },   // 0x61 'a'
  {  5621,  21,  33,  22,    1,  -31 },   // 0x62 'b'
  {  5708,  18,  22,  19,    1,  -20 },   // 0x63 'c'
  {  5758,  24,  33,  23,    1,  -31 },   // 0x64 'd'
  {  5857,  18,  22,  19,    1,  -20 },   // 0x65 'e'
  {  5907,  27,  42,  20,   -4,  -31 },   // 0x66 'f'
  {  6049,  21,  31,  21,   -1,  -20 },   // 0x67 'g'
  {  6131,  21,  32,  23,    1,  -31 },   // 0x68 'h'
  {  6215,  10,  32,  12,    2,  -30 },   // 0x69 'i'
  {  6255,  19,  41,  13,   -3,  -30 },   // 0x6A 'j'
  {  6353,  21,  33,  21,    1,  -31 },   // 0x6B 'k'
  {  6440,  11,  33,  12,    2,  -31 },   // 0x6C 'l'
  {  6486,  31,  21,  34,    1,  -20 },   // 0x6D 'm'
  {  6568,  21,  21,  23,    1,  -20 },   // 0x6E 'n'
  {  6624,  21,  22,  22,    1,  -20 },   // 0x6F 'o'
  {  6682,  27,  31,  22,   -4,  -20 },   // 0x70 'p'
  {  6787,  21,  31,  23,    1,  -20 },   // 0x71 'q'
  {  6869,  17,  21,  17,    1,  -20 },   // 0x72 'r'
  {  6914,  17,  22,  16,    0,  -20 },   // 0x73 's'
  {  6961,  12,  26,  11,    1,  -24 },   // 0x74 't'
  {  7000,  20,  22,  23,    1,  -20 },   // 0x75 'u'
  {  7055,  18,  22,  21,    3,  -20 },   // 0x76 'v'
  {  7105,  30,  22,  32,    2,  -20 },   // 0x77 'w'
  {  7188,  22,  22,  20,   -1,  -20 },   // 0x78 'x'
  {  7249,  21,  31,  22,    1,  -20 },   // 0x79 'y'
  {  7331,  17,  24,  18,    0,  -19 },   // 0x7A 'z'
  {  7382,  17,  40,  19,    2,  -31 },   // 0x7B '{'
  {  7467,   3,  33,  13,    5,  -31 },   // 0x7C '|'
  {  7480,  16,  41,  19,    0,  -32 },   // 0x7D '}'
  {  7562,  22,   6,  25,    2,  -14 } }; // 0x7E '~'

const GFXfont FreeSerifItalic24pt7b PROGMEM = {
  (uint8_t  *)FreeSerifItalic24pt7bBitmaps,
  (GFXglyph *)FreeSerifItalic24pt7bGlyphs,
  0x20, 0x7E, 56 };

// Approx. 8251 bytes
