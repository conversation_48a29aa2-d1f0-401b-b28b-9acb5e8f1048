CONFIG_IDF_TARGET="esp32s3"
CONFIG_ESPTOOLPY_NO_STUB=y
CONFIG_ESPTOOLPY_FLASHMODE_QIO=y
CONFIG_ESPTOOLPY_FLASHSIZE_4MB=y
CONFIG_PARTITION_TABLE_CUSTOM=y
CONFIG_BT_ENABLED=y
CONFIG_BT_NIMBLE_ENABLED=y
CONFIG_BT_NIMBLE_MAX_CONNECTIONS=1
# CONFIG_BT_NIMBLE_ROLE_CENTRAL is not set
# CONFIG_BT_NIMBLE_ROLE_OBSERVER is not set
CONFIG_BT_NIMBLE_NVS_PERSIST=y
CONFIG_BT_NIMBLE_ACL_BUF_COUNT=20
# CONFIG_ESP_ERR_TO_NAME_LOOKUP is not set
CONFIG_ESP_HTTP_CLIENT_ENABLE_DIGEST_AUTH=y
CONFIG_ESP_IPC_TASK_STACK_SIZE=1536
CONFIG_FREERTOS_HZ=1000
CONFIG_FREERTOS_ENABLE_BACKWARD_COMPATIBILITY=y
CONFIG_FREERTOS_TIMER_TASK_STACK_DEPTH=3120
CONFIG_FREERTOS_GENERATE_RUN_TIME_STATS=y
# CONFIG_LWIP_DHCPS is not set
# CONFIG_LWIP_IPV6 is not set
# CONFIG_LWIP_ICMP is not set
# CONFIG_MQTT_TRANSPORT_WEBSOCKET is not set
# CONFIG_ESP_PROTOCOMM_SUPPORT_SECURITY_VERSION_2 is not set
CONFIG_LV_COLOR_16_SWAP=y
CONFIG_LV_FONT_MONTSERRAT_12=y
CONFIG_LV_FONT_MONTSERRAT_16=y
CONFIG_LV_FONT_MONTSERRAT_18=y
CONFIG_LV_FONT_MONTSERRAT_20=y
CONFIG_LV_FONT_MONTSERRAT_22=y
CONFIG_LV_FONT_MONTSERRAT_24=y
CONFIG_LV_FONT_MONTSERRAT_26=y
CONFIG_LV_FONT_MONTSERRAT_28=y
CONFIG_LV_FONT_MONTSERRAT_30=y
CONFIG_LV_FONT_MONTSERRAT_32=y
CONFIG_LV_FONT_MONTSERRAT_34=y
CONFIG_LV_FONT_MONTSERRAT_36=y
CONFIG_LV_FONT_MONTSERRAT_38=y
CONFIG_LV_FONT_MONTSERRAT_40=y
CONFIG_LV_FONT_MONTSERRAT_42=y
CONFIG_LV_FONT_MONTSERRAT_44=y
CONFIG_LV_FONT_MONTSERRAT_46=y
CONFIG_LV_FONT_MONTSERRAT_48=y
CONFIG_LV_FONT_MONTSERRAT_12_SUBPX=y
CONFIG_LV_FONT_SIMSUN_16_CJK=y
CONFIG_LV_THEME_DEFAULT_DARK=y
