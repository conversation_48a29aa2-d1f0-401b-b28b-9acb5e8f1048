/**
 * @file lv_example_event.h
 *
 */

#ifndef LV_EXAMPLE_EVENT_H
#define LV_EXAMPLE_EVENT_H

#ifdef __cplusplus
extern "C" {
#endif

/*********************
 *      INCLUDES
 *********************/

/*********************
 *      DEFINES
 *********************/

/**********************
 *      TYPEDEFS
 **********************/

/**********************
 * GLOBAL PROTOTYPES
 **********************/
void lv_example_event_1(void);
void lv_example_event_2(void);
void lv_example_event_3(void);
void lv_example_event_4(void);

/**********************
 *      MACROS
 **********************/

#ifdef __cplusplus
} /*extern "C"*/
#endif

#endif /*LV_EXAMPLE_EVENT_H*/
