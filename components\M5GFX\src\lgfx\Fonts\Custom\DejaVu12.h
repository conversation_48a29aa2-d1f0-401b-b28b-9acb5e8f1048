/* DejaVu 12
 original ttf url : https://dejavu-fonts.github.io/
 original license : https://dejavu-fonts.github.io/License.html
This data has been converted to AdafruitGFX font format from DejaVuSans.ttf.
*/
const uint8_t DejaVu12Bitmaps[] PROGMEM = {
0xfd, 0x80, 0xb6, 0x80, 0x12, 0x14, 0x7f, 0x24, 0x24, 0xfe, 0x28, 0x48, 0x21, 0xca, 0xa8, 0xe0,
0xe2, 0xaa, 0x70, 0x82, 0x00, 0x61, 0x24, 0x89, 0x22, 0x50, 0x6d, 0x82, 0x91, 0x24, 0x49, 0x21,
0x80, 0x30, 0x24, 0x10, 0x0c, 0x05, 0x14, 0x4a, 0x19, 0x8c, 0x7b, 0x00, 0xe0, 0x69, 0x49, 0x24,
0x48, 0x80, 0x89, 0x12, 0x49, 0x4a, 0x00, 0x25, 0x5c, 0xea, 0x90, 0x10, 0x20, 0x47, 0xf1, 0x02,
0x04, 0x00, 0xe0, 0xe0, 0xc0, 0x11, 0x22, 0x24, 0x44, 0x88, 0x79, 0x28, 0x61, 0x86, 0x18, 0x52,
0x78, 0xe1, 0x08, 0x42, 0x10, 0x84, 0xf8, 0x79, 0x18, 0x10, 0x20, 0x82, 0x08, 0x20, 0xfc, 0x7a,
0x10, 0x41, 0x38, 0x30, 0x63, 0x78, 0x18, 0x62, 0x92, 0x4a, 0x2f, 0xc2, 0x08, 0xfa, 0x08, 0x3c,
0x0c, 0x10, 0x63, 0x78, 0x39, 0x18, 0x3e, 0xce, 0x18, 0x53, 0x78, 0xfc, 0x10, 0x82, 0x10, 0x42,
0x08, 0x40, 0x7b, 0x38, 0x73, 0x7b, 0x38, 0x73, 0x78, 0x7b, 0x28, 0x61, 0xcd, 0xd0, 0x62, 0x70,
0xcc, 0xce, 0x03, 0x1e, 0xe0, 0xe0, 0x1e, 0x03, 0xff, 0x00, 0xff, 0xc0, 0x78, 0x07, 0x07, 0x78,
0xc0, 0x74, 0x42, 0x22, 0x10, 0x04, 0x20, 0x1f, 0x06, 0x19, 0x01, 0x46, 0x99, 0x13, 0x22, 0x64,
0x54, 0x6c, 0x40, 0x04, 0x10, 0x7c, 0x00, 0x18, 0x18, 0x24, 0x24, 0x24, 0x42, 0x7e, 0x42, 0x81,
0xfa, 0x18, 0x61, 0xfa, 0x18, 0x61, 0xf8, 0x39, 0x18, 0x20, 0x82, 0x08, 0x11, 0x38, 0xf9, 0x0a,
0x0c, 0x18, 0x30, 0x60, 0xc2, 0xf8, 0xfe, 0x08, 0x20, 0xfe, 0x08, 0x20, 0xfc, 0xfc, 0x21, 0x0f,
0xc2, 0x10, 0x80, 0x3c, 0x86, 0x04, 0x08, 0xf0, 0x60, 0xa1, 0x3c, 0x83, 0x06, 0x0c, 0x1f, 0xf0,
0x60, 0xc1, 0x82, 0xff, 0x80, 0x24, 0x92, 0x49, 0x27, 0x00, 0x85, 0x12, 0x45, 0x0c, 0x14, 0x24,
0x44, 0x84, 0x84, 0x21, 0x08, 0x42, 0x10, 0xf8, 0x81, 0xc3, 0xc3, 0xa5, 0xa5, 0x99, 0x99, 0x81,
0x81, 0xc3, 0x86, 0x8d, 0x19, 0x31, 0x62, 0xc3, 0x86, 0x38, 0x8a, 0x0c, 0x18, 0x30, 0x60, 0xa2,
0x38, 0xfa, 0x38, 0x63, 0xfa, 0x08, 0x20, 0x80, 0x38, 0x8a, 0x0c, 0x18, 0x30, 0x60, 0xa2, 0x38,
0x10, 0x10, 0xf9, 0x1a, 0x14, 0x6f, 0x91, 0x21, 0x42, 0x82, 0x7b, 0x18, 0x30, 0x78, 0x30, 0x63,
0x78, 0xfe, 0x20, 0x40, 0x81, 0x02, 0x04, 0x08, 0x10, 0x83, 0x06, 0x0c, 0x18, 0x30, 0x60, 0xa2,
0x38, 0x40, 0x90, 0x22, 0x10, 0x84, 0x21, 0x04, 0x81, 0x20, 0x30, 0x0c, 0x00, 0x84, 0x28, 0x89,
0x11, 0x27, 0x22, 0xa8, 0x55, 0x0e, 0xe0, 0x88, 0x11, 0x00, 0xc6, 0x88, 0xa1, 0xc1, 0x07, 0x0a,
0x22, 0x82, 0x82, 0x89, 0x11, 0x43, 0x82, 0x04, 0x08, 0x10, 0xfe, 0x04, 0x10, 0x41, 0x04, 0x10,
0x40, 0xfe, 0xea, 0xaa, 0xac, 0x88, 0x44, 0x42, 0x22, 0x11, 0xd5, 0x55, 0x5c, 0x18, 0x24, 0x42,
0xfc, 0x44, 0x7a, 0x30, 0x5f, 0x86, 0x37, 0x40, 0x82, 0x08, 0x2e, 0xca, 0x18, 0x61, 0xce, 0xe0,
0x72, 0x61, 0x08, 0x25, 0xc0, 0x04, 0x10, 0x5d, 0xce, 0x18, 0x61, 0xcd, 0xd0, 0x39, 0x38, 0x7f,
0x81, 0x13, 0x80, 0x34, 0x4f, 0x44, 0x44, 0x44, 0x77, 0x38, 0x61, 0x87, 0x37, 0x41, 0x4c, 0xe0,
0x82, 0x08, 0x2e, 0xc6, 0x18, 0x61, 0x86, 0x10, 0xbf, 0x80, 0x45, 0x55, 0x56, 0x82, 0x08, 0x22,
0x92, 0x8e, 0x28, 0x92, 0x20, 0xff, 0xc0, 0xb3, 0x66, 0x62, 0x31, 0x18, 0x8c, 0x46, 0x22, 0xbb,
0x18, 0x61, 0x86, 0x18, 0x40, 0x7b, 0x38, 0x61, 0x87, 0x37, 0x80, 0xbb, 0x28, 0x61, 0x87, 0x3b,
0xa0, 0x82, 0x00, 0x77, 0x38, 0x61, 0x87, 0x37, 0x41, 0x04, 0x10, 0xbc, 0x88, 0x88, 0x80, 0x72,
0x28, 0x1c, 0x0a, 0x27, 0x00, 0x44, 0xf4, 0x44, 0x44, 0x30, 0x86, 0x18, 0x61, 0x86, 0x37, 0x40,
0x42, 0x42, 0x24, 0x24, 0x24, 0x18, 0x18, 0x88, 0xc4, 0x57, 0x4a, 0xa5, 0x51, 0x10, 0x88, 0x85,
0x24, 0x8c, 0x49, 0x28, 0x40, 0x42, 0x42, 0x24, 0x24, 0x14, 0x18, 0x08, 0x08, 0x10, 0x60, 0xf8,
0x44, 0x44, 0x43, 0xe0, 0x19, 0x08, 0x42, 0x60, 0x84, 0x21, 0x06, 0xff, 0xf0, 0xc1, 0x08, 0x42,
0x0c, 0x84, 0x21, 0x30, 0x00, 0x71, 0x8e, };

const GFXglyph DejaVu12Glyphs[] PROGMEM = {
 {    0,   0,   0,   5,   0,   1 },  // 0x20 ' '
 {    0,   1,   9,   6,   2,  -8 },  // 0x21 '!'
 {    2,   3,   3,   6,   1,  -8 },  // 0x22 '"'
 {    4,   8,   8,  11,   1,  -7 },  // 0x23 '#'
 {   12,   6,  11,   9,   2,  -8 },  // 0x24 '$'
 {   21,  10,   9,  12,   0,  -8 },  // 0x25 '%'
 {   33,   9,   9,  11,   1,  -8 },  // 0x26 '&'
 {   44,   1,   3,   4,   1,  -8 },  // 0x27 '''
 {   45,   3,  11,   6,   1,  -9 },  // 0x28 '('
 {   50,   3,  11,   6,   1,  -9 },  // 0x29 ')'
 {   55,   5,   6,   7,   1,  -8 },  // 0x2a '*'
 {   59,   7,   7,  11,   1,  -6 },  // 0x2b '+'
 {   66,   1,   3,   5,   1,  -1 },  // 0x2c ','
 {   67,   3,   1,   5,   1,  -3 },  // 0x2d '-'
 {   68,   1,   2,   5,   1,  -1 },  // 0x2e '.'
 {   69,   4,  10,   5,   0,  -8 },  // 0x2f '/'
 {   74,   6,   9,   9,   1,  -8 },  // 0x30 '0'
 {   81,   5,   9,   9,   1,  -8 },  // 0x31 '1'
 {   87,   7,   9,   9,   1,  -8 },  // 0x32 '2'
 {   95,   6,   9,   9,   1,  -8 },  // 0x33 '3'
 {  102,   6,   9,   9,   1,  -8 },  // 0x34 '4'
 {  109,   6,   9,   9,   1,  -8 },  // 0x35 '5'
 {  116,   6,   9,   9,   1,  -8 },  // 0x36 '6'
 {  123,   6,   9,   9,   1,  -8 },  // 0x37 '7'
 {  130,   6,   9,   9,   1,  -8 },  // 0x38 '8'
 {  137,   6,   9,   9,   1,  -8 },  // 0x39 '9'
 {  144,   1,   6,   5,   1,  -5 },  // 0x3a ':'
 {  145,   1,   7,   5,   1,  -5 },  // 0x3b ';'
 {  146,   8,   6,  11,   1,  -6 },  // 0x3c '<'
 {  152,   8,   3,  11,   1,  -4 },  // 0x3d '='
 {  155,   8,   6,  11,   1,  -6 },  // 0x3e '>'
 {  161,   5,   9,   7,   0,  -8 },  // 0x3f '?'
 {  167,  11,  11,  14,   1,  -8 },  // 0x40 '@'
 {  183,   8,   9,   9,   0,  -8 },  // 0x41 'A'
 {  192,   6,   9,   9,   1,  -8 },  // 0x42 'B'
 {  199,   6,   9,   9,   1,  -8 },  // 0x43 'C'
 {  206,   7,   9,  10,   1,  -8 },  // 0x44 'D'
 {  214,   6,   9,   9,   1,  -8 },  // 0x45 'E'
 {  221,   5,   9,   8,   1,  -8 },  // 0x46 'F'
 {  227,   7,   9,  10,   1,  -8 },  // 0x47 'G'
 {  235,   7,   9,  10,   1,  -8 },  // 0x48 'H'
 {  243,   1,   9,   4,   1,  -8 },  // 0x49 'I'
 {  245,   3,  11,   4,  -1,  -8 },  // 0x4a 'J'
 {  250,   7,   9,   8,   1,  -8 },  // 0x4b 'K'
 {  258,   5,   9,   7,   1,  -8 },  // 0x4c 'L'
 {  264,   8,   9,  11,   1,  -8 },  // 0x4d 'M'
 {  273,   7,   9,  10,   1,  -8 },  // 0x4e 'N'
 {  281,   7,   9,  10,   1,  -8 },  // 0x4f 'O'
 {  289,   6,   9,   9,   1,  -8 },  // 0x50 'P'
 {  296,   7,  11,  10,   1,  -8 },  // 0x51 'Q'
 {  306,   7,   9,   9,   1,  -8 },  // 0x52 'R'
 {  314,   6,   9,   9,   1,  -8 },  // 0x53 'S'
 {  321,   7,   9,   8,   0,  -8 },  // 0x54 'T'
 {  329,   7,   9,  10,   1,  -8 },  // 0x55 'U'
 {  337,  10,   9,   9,  -1,  -8 },  // 0x56 'V'
 {  349,  11,   9,  12,   0,  -8 },  // 0x57 'W'
 {  362,   7,   9,   8,   0,  -8 },  // 0x58 'X'
 {  370,   7,   9,   8,   0,  -8 },  // 0x59 'Y'
 {  378,   7,   9,  10,   1,  -8 },  // 0x5a 'Z'
 {  386,   2,  11,   6,   2,  -8 },  // 0x5b '['
 {  389,   4,  10,   5,   0,  -8 },  // 0x5c '\'
 {  394,   2,  11,   6,   1,  -8 },  // 0x5d ']'
 {  397,   8,   3,  11,   1,  -8 },  // 0x5e '^'
 {  400,   6,   1,   7,   0,   3 },  // 0x5f '_'
 {  401,   3,   2,   7,   1,  -9 },  // 0x60 '`'
 {  402,   6,   7,   9,   1,  -6 },  // 0x61 'a'
 {  408,   6,  10,   9,   1,  -9 },  // 0x62 'b'
 {  416,   5,   7,   8,   1,  -6 },  // 0x63 'c'
 {  421,   6,  10,   9,   1,  -9 },  // 0x64 'd'
 {  429,   6,   7,   9,   1,  -6 },  // 0x65 'e'
 {  435,   4,  10,   5,   0,  -9 },  // 0x66 'f'
 {  440,   6,  10,   9,   1,  -6 },  // 0x67 'g'
 {  448,   6,  10,   9,   1,  -9 },  // 0x68 'h'
 {  456,   1,   9,   4,   1,  -8 },  // 0x69 'i'
 {  458,   2,  12,   4,   0,  -8 },  // 0x6a 'j'
 {  461,   6,  10,   8,   1,  -9 },  // 0x6b 'k'
 {  469,   1,  10,   4,   1,  -9 },  // 0x6c 'l'
 {  471,   9,   7,  12,   1,  -6 },  // 0x6d 'm'
 {  479,   6,   7,   9,   1,  -6 },  // 0x6e 'n'
 {  485,   6,   7,   9,   1,  -6 },  // 0x6f 'o'
 {  491,   6,  10,   9,   1,  -6 },  // 0x70 'p'
 {  499,   6,  10,   9,   1,  -6 },  // 0x71 'q'
 {  507,   4,   7,   6,   1,  -6 },  // 0x72 'r'
 {  511,   6,   7,   8,   1,  -6 },  // 0x73 's'
 {  517,   4,   9,   6,   0,  -8 },  // 0x74 't'
 {  522,   6,   7,   9,   1,  -6 },  // 0x75 'u'
 {  528,   8,   7,   7,  -1,  -6 },  // 0x76 'v'
 {  535,   9,   7,  10,   0,  -6 },  // 0x77 'w'
 {  543,   6,   7,   7,   0,  -6 },  // 0x78 'x'
 {  549,   8,  10,   7,  -1,  -6 },  // 0x79 'y'
 {  559,   5,   7,   6,   0,  -6 },  // 0x7a 'z'
 {  564,   5,  11,   9,   2,  -8 },  // 0x7b '{'
 {  571,   1,  12,   5,   2,  -8 },  // 0x7c '|'
 {  573,   5,  11,   9,   1,  -8 },  // 0x7d '}'
 {  580,   8,   3,  11,   1,  -5 },  // 0x7e '~'
};

const GFXfont DejaVu12 PROGMEM = {
  (uint8_t*)DejaVu12Bitmaps,
  (GFXglyph*)DejaVu12Glyphs,
  0x20, 0x7e, 13 };

