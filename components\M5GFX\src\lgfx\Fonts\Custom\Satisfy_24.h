// Created by http://oleddisplay.squix.ch/ Consider a donation
// In case of problems make sure that you are using the font file with the correct version!
const uint8_t Satisfy_24Bitmaps[] PROGMEM = {

	// Bitmap Data:
	0x00, // ' '
	0x06,0x06,0x0E,0x0E,0x0C,0x0C,0x0C,0x1C,0x18,0x18,0x18,0x18,0x30,0x30,0x30,0x00,0x00,0x70,0x60, // '!'
	0x00,0x33,0x36,0x36,0x26,0x66,0x6C,0x6C,0x00, // '"'
	0x00,0x00,0x66,0x06,0x40,0x4C,0x3F,0xF0,0x88,0x09,0x81,0x90,0x7F,0xE1,0x30,0x33,0x03,0x60,0x00,0x00, // '#'
	0x02,0x01,0x03,0xC3,0x31,0x19,0x8C,0xC4,0x30,0x18,0x04,0x03,0x00,0x8C,0x66,0x31,0x30,0xF0,0x30,0x10,0x00, // '$'
	0x00,0x00,0x0E,0x06,0x1A,0x0C,0x3B,0x18,0x33,0x10,0x33,0x30,0x32,0x60,0x36,0x40,0x1C,0xC0,0x01,0x80,0x01,0x00,0x03,0x78,0x06,0xCC,0x0C,0xCC,0x0D,0x8C,0x19,0x8C,0x31,0x98,0x31,0x98,0x60,0xE0, // '%'
	0x00,0x70,0x04,0x80,0x44,0x06,0x60,0x36,0x01,0xE0,0x0F,0x00,0x70,0x07,0x00,0x78,0x07,0xC0,0x36,0x03,0x30,0x39,0xC1,0x8E,0x0C,0x32,0x61,0xE3,0x8E,0x0F,0xF0,0x00,0x40, // '&'
	0x01,0x8C,0x62,0x31,0x8C,0x00, // '''
	0x00,0x02,0x06,0x0C,0x0C,0x18,0x18,0x30,0x30,0x20,0x60,0x60,0x60,0x60,0x40,0xC0,0xC0,0xC0,0xC0,0xC0,0x40,0x40,0x40,0x00, // '('
	0x00,0x02,0x02,0x02,0x03,0x03,0x03,0x03,0x03,0x03,0x02,0x06,0x06,0x06,0x04,0x0C,0x0C,0x08,0x18,0x10,0x30,0x60,0x40,0x80, // ')'
	0x00,0x0C,0x4C,0x6B,0x3F,0x18,0x7C,0x66,0x24, // '*'
	0x06,0x01,0x80,0x60,0x10,0x7F,0x83,0x00,0xC0,0x20,0x00,0x00, // '+'
	0x6E,0x64,0xC0, // ','
	0x7F,0x80,0x00, // '-'
	0x0E,0xE0, // '.'
	0x00,0x00,0x01,0x80,0x18,0x00,0xC0,0x0C,0x00,0x60,0x06,0x00,0x20,0x03,0x00,0x10,0x01,0x80,0x18,0x00,0xC0,0x0C,0x00,0x60,0x06,0x00,0x30,0x03,0x00,0x18,0x00, // '/'
	0x07,0x01,0xB0,0x62,0x08,0x63,0x0C,0x41,0x98,0x33,0x06,0x60,0xD8,0x1B,0x07,0x60,0xCC,0x19,0x83,0x30,0xC6,0x18,0x46,0x0D,0x80,0xE0,0x00, // '0'
	0x0C,0x38,0x70,0x60,0xC3,0x06,0x0C,0x18,0x20,0xC1,0x83,0x04,0x18,0x30,0x60,0xC0,0x00, // '1'
	0x03,0xC0,0x8C,0x31,0x8C,0x31,0x86,0x30,0xC0,0x30,0x06,0x01,0x80,0x30,0x0C,0x03,0x00,0xC0,0x30,0x0C,0x03,0x00,0xFF,0x8F,0xF1,0x02,0x00, // '2'
	0x07,0x83,0x98,0x61,0x98,0x33,0x06,0x71,0xC0,0x30,0x0C,0x03,0x00,0xC0,0x7C,0x00,0xC0,0x18,0x03,0x00,0x66,0x18,0xC3,0x1C,0xC0,0xF0,0x00, // '3'
	0x00,0x00,0x38,0x0F,0x01,0x40,0x68,0x1B,0x06,0x61,0x8C,0x31,0x0C,0x21,0xFE,0x3F,0xC0,0x30,0x04,0x01,0x80,0x30,0x06,0x00,0xC0,0x18,0x00, // '4'
	0x00,0x00,0xFF,0x0F,0xF0,0x80,0x08,0x01,0x80,0x10,0x01,0x38,0x3F,0xC3,0x8C,0x38,0xC0,0x0C,0x00,0x80,0x08,0x21,0x86,0x10,0x73,0x07,0xE0,0x38,0x00, // '5'
	0x03,0x83,0x60,0x98,0x66,0x31,0x8C,0x06,0x01,0xBC,0x79,0x3C,0x6F,0x1B,0x06,0xC1,0xB0,0xEC,0x33,0x0C,0x46,0x19,0x03,0x80, // '6'
	0x3F,0xDC,0x34,0x0C,0x06,0x03,0x80,0xC0,0x60,0x38,0x0C,0x07,0x01,0x80,0xE0,0x30,0x1C,0x06,0x01,0x80,0x60,0x18,0x00,0x00, // '7'
	0x03,0xC0,0x8C,0x21,0x8C,0x31,0x8E,0x31,0x87,0x70,0xFC,0x0F,0x03,0xE0,0xDC,0x39,0xC6,0x19,0x83,0x30,0x66,0x0C,0xC3,0x08,0xC0,0xF0,0x00, // '8'
	0x07,0x03,0x61,0x8C,0xC3,0x30,0xD8,0x36,0x1D,0x87,0x61,0xD8,0xE6,0x29,0xD6,0x39,0x80,0xC0,0x32,0x18,0xCE,0x3F,0x07,0x00, // '9'
	0x01,0xCC,0x00,0x03,0x9C, // ':'
	0x01,0x9C,0x00,0x03,0x9C,0x62,0x00, // ';'
	0x03,0x07,0x07,0x07,0x07,0x03,0x00,0xC0,0x60,0x18,0x0C,0x00, // '<'
	0x3F,0xC8,0x00,0x01,0xFE,0x00,0x00, // '='
	0x18,0x0C,0x0C,0x06,0x07,0x07,0x0E,0x18,0x30,0x60, // '>'
	0x07,0xC0,0xE3,0x06,0x0C,0x70,0x61,0x83,0x00,0x38,0x01,0x80,0x1C,0x01,0xC0,0x7C,0x07,0x80,0x30,0x01,0x80,0x1C,0x00,0xC0,0x04,0x00,0x00,0x03,0x80,0x1C,0x00, // '?'
	0x00,0xF8,0x03,0x87,0x03,0x00,0xC3,0x0F,0x61,0x1B,0x99,0x99,0xCD,0x98,0xE6,0xCC,0x23,0x6C,0x11,0xB6,0x18,0xDB,0x08,0xCD,0x8C,0x66,0x7E,0x61,0x83,0x70,0x61,0xE0,0x1E,0x00,0x00,0x00,0x00, // '@'
	0x00,0x1C,0x00,0x4C,0x01,0x18,0x06,0x30,0x18,0x60,0x31,0x80,0xC3,0x01,0x86,0x03,0x0C,0x0C,0x38,0x18,0x60,0x30,0xC0,0xFF,0xA7,0xFF,0x9F,0x06,0x0C,0x18,0x18,0x30,0x70,0x60,0xE0,0xC1,0x81,0x87,0x03,0x0C,0x07,0x00,0x0E,0x00,0x00, // 'A'
	0x00,0x00,0x03,0xF0,0x0F,0x0C,0x1F,0x0E,0x1F,0x0E,0x06,0x0E,0x06,0x0E,0x06,0x0C,0x0C,0x1C,0x0C,0x18,0x0C,0x30,0x0F,0xE0,0x1F,0xB0,0x18,0x18,0x18,0x18,0x38,0x18,0x30,0x1C,0x30,0x18,0x30,0x38,0x70,0x30,0x70,0x70,0x61,0xC0,0x3F,0x00, // 'B'
	0x00,0xF0,0x0C,0x60,0x61,0x83,0x06,0x18,0x1C,0x40,0xE3,0x03,0x8C,0x1C,0x60,0x01,0x80,0x06,0x00,0x30,0x00,0xC0,0x03,0x00,0x0C,0x00,0x30,0x00,0xC0,0x03,0x01,0x06,0x08,0x18,0x60,0x7F,0x00,0x70,0x00, // 'C'
	0x07,0xC0,0x3F,0xE0,0x60,0xE0,0x80,0xC0,0x60,0xC1,0xC1,0x83,0x03,0x06,0x06,0x0C,0x0C,0x18,0x1C,0x70,0x30,0xC0,0x61,0x80,0xC3,0x03,0x8E,0x06,0x18,0x1C,0x30,0x30,0x60,0xC1,0x83,0x83,0x0E,0x0E,0x30,0x0F,0x80,0x00, // 'D'
	0x00,0xF8,0x0E,0x30,0x60,0xC3,0x03,0x1C,0x1C,0x60,0x61,0x83,0x86,0x00,0x1C,0x00,0x30,0x00,0x7C,0x07,0xE0,0x38,0x01,0xC0,0x06,0x00,0x38,0x04,0xE0,0x13,0x80,0x8E,0x06,0x1C,0x30,0x3F,0x80,0x7C,0x00, // 'E'
	0x07,0xF0,0xE0,0x66,0x01,0xD8,0x06,0x7C,0x19,0xE0,0x40,0x03,0x00,0x0C,0x00,0x60,0x01,0x83,0xFF,0x88,0x30,0x01,0x80,0x06,0x00,0x30,0x00,0xC0,0x07,0x00,0x18,0x00,0x60,0x03,0x80,0x0E,0x00,0x38,0x00,0x00,0x00, // 'F'
	0x00,0xF0,0x0C,0x40,0x61,0x83,0x06,0x18,0x18,0x60,0xE3,0x07,0x0C,0x1C,0x70,0x01,0x80,0x06,0x00,0x38,0xFE,0xC7,0xFB,0x00,0xCC,0x03,0x30,0x18,0xC0,0x63,0x03,0x04,0x0C,0x18,0x60,0x23,0x00,0x78,0x00, // 'G'
	0x00,0x00,0x03,0x00,0x01,0x80,0xC1,0xC0,0xE0,0xC0,0x60,0x60,0x30,0x30,0x38,0x18,0x18,0x18,0x0C,0x0C,0x0E,0x06,0x06,0x03,0xFF,0xC7,0xF1,0xC1,0x9D,0x80,0xC6,0xC0,0x61,0xE0,0x70,0x70,0x38,0x30,0x18,0x18,0x0C,0x0C,0x06,0x06,0x07,0x03,0x03,0x83,0x80, // 'H'
	0x00,0x1C,0x00,0xF8,0x03,0xB0,0x06,0x60,0x1C,0xC0,0x33,0x80,0x67,0x00,0xCC,0x01,0x98,0x03,0x70,0x02,0xE0,0x05,0x80,0x03,0x01,0xFF,0xCF,0x98,0x38,0x30,0xC0,0x61,0x81,0x83,0x03,0x03,0x0C,0x07,0x30,0x03,0xC0,0x00, // 'I'
	0x01,0xFF,0x00,0x8C,0x00,0x06,0x00,0x03,0x00,0x01,0x80,0x01,0x80,0x00,0xC0,0x00,0x60,0x00,0x70,0x00,0x38,0x00,0x18,0x03,0x8C,0x03,0xC6,0x03,0x87,0x01,0x83,0x01,0xC1,0x80,0xC1,0xC0,0x60,0xC0,0x30,0x60,0x18,0x60,0x06,0x60,0x01,0xE0,0x00, // 'J'
	0x00,0x00,0x06,0x08,0x0C,0x38,0x38,0x60,0x60,0xC0,0xC3,0x83,0x86,0x06,0x1C,0x0C,0x30,0x18,0xC0,0x63,0x00,0xCC,0x01,0xF0,0x07,0xF8,0x0E,0x38,0x18,0x30,0x30,0x60,0x60,0xC0,0xC3,0x03,0x86,0x07,0x18,0x0E,0x30,0x18,0x60,0x00,0x60,0x00,0x78, // 'K'
	0x00,0x0F,0x00,0x1F,0x00,0x3B,0x00,0x33,0x00,0x76,0x00,0x6E,0x00,0x7C,0x00,0x70,0x0F,0xE0,0x00,0xC0,0x00,0xC0,0x00,0xC0,0x01,0xC0,0x01,0x80,0x01,0x80,0x03,0x00,0x03,0x00,0x3E,0x00,0x6F,0x04,0x7F,0x8C,0x79,0xFC,0x00,0x78, // 'L'
	0x00,0x30,0x30,0x07,0x83,0x80,0x3C,0x3C,0x01,0xE1,0xE0,0x1B,0x0B,0x00,0xD8,0xD0,0x06,0xC5,0x80,0x76,0x6C,0x03,0x72,0x60,0x1B,0x33,0x00,0xD9,0x30,0x0E,0xD9,0x83,0x66,0xCC,0x13,0x34,0x61,0x99,0xE7,0x09,0xCE,0x30,0xCC,0x71,0x86,0x63,0x8C,0x33,0x0C,0x61,0xB8,0x07,0x07,0x80,0x1C,0x1C,0x00,0xF0, // 'M'
	0x00,0x00,0x0C,0x06,0x0C,0x0D,0x0E,0x19,0x1E,0x1B,0x16,0x1B,0x16,0x36,0x16,0x7C,0x16,0x30,0x26,0x20,0x26,0x20,0x26,0x20,0x26,0x60,0x66,0x60,0x46,0x60,0x46,0x40,0x46,0x40,0xC7,0xC0,0xC7,0xC0,0xC3,0x80,0xC3,0x80,0xC3,0x80,0xC1,0x00, // 'N'
	0x01,0xE0,0x1F,0x81,0xCC,0x1C,0x30,0xC1,0x8C,0x0C,0x60,0x67,0x03,0x30,0x19,0x80,0xDC,0x06,0xC0,0x36,0x03,0x30,0x19,0x80,0xCC,0x06,0x60,0x63,0x03,0x18,0x30,0x43,0x03,0x30,0x0F,0x00, // 'O'
	0x01,0xF8,0x07,0x0C,0x1F,0x06,0x1B,0x06,0x02,0x06,0x06,0x06,0x06,0x06,0x06,0x0E,0x04,0x0C,0x0C,0x1C,0x0C,0x38,0x0C,0x70,0x0F,0xC0,0x1F,0x00,0x18,0x00,0x18,0x00,0x18,0x00,0x38,0x00,0x30,0x00,0x30,0x00,0x70,0x00,0x60,0x00, // 'P'
	0x01,0xE0,0x31,0x83,0x0C,0x38,0x31,0x81,0x9C,0x0C,0xC0,0x6E,0x03,0x70,0x1B,0x00,0xD8,0x0E,0xC0,0x66,0x03,0x30,0x39,0x81,0x8E,0x0C,0x38,0xC0,0x06,0x0F,0xE0,0xE7,0x06,0x7C,0x3E,0x38, // 'Q'
	0x01,0xF8,0x07,0x0C,0x1F,0x06,0x1B,0x06,0x02,0x06,0x06,0x06,0x06,0x06,0x06,0x0E,0x06,0x0C,0x0C,0x1C,0x0C,0x38,0x1F,0x70,0x1D,0xC0,0x1F,0x80,0x18,0xC0,0x18,0xC0,0x18,0xC0,0x38,0xC0,0x38,0xE0,0x30,0xE0,0x70,0x60,0x60,0x60,0x00,0x78,0x00,0x30, // 'R'
	0x00,0x00,0x07,0xC0,0x71,0x81,0x82,0x0C,0x18,0x30,0x61,0xC1,0x83,0x0E,0x0C,0x00,0x38,0x00,0x70,0x00,0xE0,0x01,0x80,0x07,0x00,0x0C,0x00,0x30,0x00,0xC1,0x83,0x06,0x0C,0x18,0x30,0x61,0x81,0xCE,0x03,0xF0,0x02,0x00, // 'S'
	0x00,0x03,0xFF,0xDC,0xC0,0x06,0x00,0x70,0x03,0x00,0x18,0x00,0xC0,0x0E,0x00,0x60,0x03,0x00,0x18,0x00,0xC0,0x0C,0x00,0x60,0x03,0x00,0x18,0x01,0xC0,0x0E,0x00,0x60,0x03,0x00,0x18,0x01,0xC0,0x00, // 'T'
	0x00,0x00,0x18,0x18,0x70,0x70,0xE0,0xE1,0x81,0x87,0x07,0x0E,0x0E,0x18,0x18,0x30,0x30,0xE0,0xE1,0x81,0x83,0x03,0x06,0x06,0x1C,0x0C,0x30,0x30,0x60,0x60,0xC0,0xC1,0x83,0x83,0x07,0x06,0x1E,0x06,0x6C,0x0F,0x18,0x00, // 'U'
	0x00,0x01,0x81,0x9C,0x0C,0xC0,0xE6,0x06,0x30,0x71,0x83,0x18,0x18,0xC1,0xC6,0x0C,0x30,0x61,0x87,0x0C,0x30,0x61,0x83,0x0C,0x18,0xC0,0xC6,0x06,0x20,0x33,0x00,0x98,0x07,0x80,0x3C,0x00,0xC0,0x00, // 'V'
	0x06,0x00,0x60,0xC1,0x06,0x0C,0x30,0x61,0x83,0x06,0x18,0x70,0x63,0x87,0x06,0x30,0x70,0x63,0x07,0x06,0x30,0x60,0x66,0x06,0x0C,0x60,0x60,0xC6,0x0E,0x0C,0x60,0xE0,0xC6,0x0E,0x18,0x61,0xE1,0x86,0x1E,0x18,0x61,0x61,0x06,0x36,0x30,0x62,0x62,0x06,0x67,0x40,0x3C,0x3C,0x01,0x83,0x80, // 'W'
	0x06,0x00,0xC1,0x60,0x78,0x66,0x0C,0x08,0xC3,0x01,0x98,0xE0,0x3F,0x18,0x03,0xA6,0x00,0x05,0xC0,0x00,0xF0,0x00,0x1C,0x00,0x03,0x80,0x00,0x60,0x00,0x1C,0x00,0x07,0x80,0x00,0xF0,0x00,0x36,0x00,0x06,0x40,0x01,0x8C,0x00,0x71,0x80,0x0C,0x30,0x83,0x83,0x20,0xE0,0x78,0x00,0x00,0x00, // 'X'
	0x00,0x0C,0x70,0x71,0xC1,0x86,0x06,0x18,0x18,0xE0,0xE3,0x03,0x0C,0x0C,0x30,0x31,0xC1,0xC6,0x06,0x18,0x38,0x60,0xE1,0x87,0x87,0x34,0x0F,0x30,0x00,0xC0,0x03,0x00,0x18,0x10,0x60,0x63,0x03,0xF8,0x07,0xC0,0x00, // 'Y'
	0x07,0xFC,0x3F,0xF0,0xC0,0xC0,0x07,0x00,0x18,0x00,0xE0,0x03,0x00,0x18,0x00,0xC0,0x07,0x00,0x38,0x00,0xC0,0x06,0x00,0x30,0x01,0xC0,0x0E,0x00,0x30,0x01,0x80,0x06,0x00,0x37,0xF8,0xFF,0xE1,0x01,0x00, // 'Z'
	0x03,0x80,0x80,0x60,0x18,0x04,0x01,0x00,0xC0,0x30,0x08,0x02,0x01,0x80,0x60,0x10,0x04,0x03,0x00,0xC0,0x30,0x08,0x02,0x01,0x80,0x60,0x18,0x07,0x00, // '['
	0x01,0x86,0x18,0x60,0x82,0x08,0x30,0xC3,0x0C,0x10,0x41,0x86,0x18,0x61,0xC0, // '\'
	0x03,0x80,0xC0,0x60,0x30,0x10,0x18,0x0C,0x06,0x02,0x01,0x01,0x80,0xC0,0x60,0x20,0x30,0x18,0x0C,0x04,0x06,0x03,0x01,0x01,0x81,0xC0, // ']'
	0x06,0x07,0x83,0xC3,0x61,0x31,0x99,0x8C,0xC6,0xC3,0x00,0x00, // '^'
	0x7F,0xF7,0xFF,0x80,0x00, // '_'
	0x47,0x0C,0x00, // '`'
	0x00,0x00,0x3C,0x01,0xB0,0x0C,0x40,0x31,0x01,0x8C,0x26,0x31,0x38,0xCC,0xEE,0x61,0xEF,0x07,0x18,0x00, // 'a'
	0x00,0x00,0xC0,0x06,0x00,0x70,0x03,0x80,0x1C,0x01,0xE0,0x0E,0x00,0x70,0x03,0x30,0x39,0x8D,0xCF,0x8C,0x20,0x63,0x03,0x18,0x19,0x80,0xC8,0x03,0x80,0x00, // 'b'
	0x1C,0x03,0x20,0x66,0x06,0x60,0xC0,0x0C,0x02,0xC0,0x4C,0x0C,0xC1,0x86,0x30,0x3C,0x00, // 'c'
	0x00,0x60,0x01,0x80,0x06,0x00,0x18,0x00,0xE0,0x03,0x80,0x0E,0x03,0x30,0x33,0xC1,0x8F,0x06,0x38,0x38,0x60,0xC3,0x07,0x0C,0x2C,0x31,0xB1,0xCC,0xCB,0x61,0xCE,0x00, // 'd'
	0x07,0x01,0xB0,0x13,0x03,0x20,0x66,0x06,0xC1,0x70,0x26,0x06,0x60,0xC3,0x10,0x1E,0x00, // 'e'
	0x07,0x00,0x38,0x01,0xC0,0x0E,0x00,0x60,0x03,0x00,0x18,0x00,0x80,0x0C,0x00,0x60,0x03,0x3C,0x1E,0x01,0x80,0x1C,0x00,0x60,0x03,0x80,0x36,0x01,0xB0,0x08,0xC0,0x46,0x06,0x30,0x31,0x81,0x9C,0x08,0xC0,0xCE,0x06,0xE0,0x3E,0x01,0xE0,0x06,0x00,0x00, // 'f'
	0x1C,0x00,0xCE,0x06,0x38,0x18,0xE0,0xC3,0x03,0x04,0x0C,0x30,0xB0,0xC4,0xC7,0x23,0x1F,0x07,0xB8,0x01,0x80,0x0E,0x00,0x58,0x03,0x40,0x1B,0x00,0xCC,0x03,0x30,0x0C,0xC0,0x32,0x00,0xD8,0x01,0xE0,0x03,0x00,0x00, // 'g'
	0x0C,0x00,0x30,0x00,0xC0,0x03,0x00,0x0C,0x00,0x60,0x01,0x80,0x06,0xE0,0x17,0x80,0xF7,0x03,0x98,0x0C,0x60,0x31,0x8C,0x8C,0x66,0x33,0x18,0xD8,0x63,0xC1,0x86,0x00, // 'h'
	0x00,0x18,0x10,0x00,0x00,0x00,0x00,0x10,0x60,0x60,0xE0,0xC1,0xC3,0xC6,0xCC,0xD8,0x70, // 'i'
	0x00,0x40,0x01,0x00,0x04,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x60,0x01,0x80,0x0E,0x00,0x30,0x40,0xC2,0x03,0x18,0x1C,0xC0,0x66,0x01,0xA0,0x07,0x00,0x38,0x01,0xE0,0x0D,0x00,0x7C,0x01,0xB0,0x0C,0xC0,0x32,0x00,0xD8,0x03,0x40,0x07,0x00,0x00, // 'j'
	0x00,0x00,0x30,0x00,0xC0,0x03,0x00,0x0C,0x00,0x20,0x01,0x80,0x06,0x00,0x19,0x80,0x5E,0x03,0xD8,0x0E,0x60,0x31,0x00,0xB8,0x02,0xF0,0x98,0xC4,0x63,0x31,0x0D,0x84,0x38,0x00, // 'k'
	0x0C,0x18,0x30,0x61,0xC3,0x06,0x0C,0x18,0x60,0xC1,0x83,0x0C,0x18,0x30,0x60,0xC0, // 'l'
	0x03,0x9C,0x03,0x7B,0xC0,0x39,0xEC,0x07,0x1C,0xC0,0x71,0x8C,0x26,0x11,0xC2,0x63,0x18,0x46,0x31,0x88,0xC3,0x19,0x0C,0x61,0xE0,0x00,0x00,0x00, // 'm'
	0x33,0x00,0xDE,0x03,0x98,0x0C,0x60,0x61,0x89,0x8C,0x26,0x31,0x18,0xCC,0xC3,0x63,0x0E,0x00, // 'n'
	0x1C,0x01,0xF0,0x18,0x80,0xC4,0x2E,0x36,0x79,0xE3,0x7C,0x18,0xC0,0xC6,0x06,0x60,0x1E,0x00, // 'o'
	0x00,0x00,0x06,0x60,0x07,0xF0,0x07,0x30,0x0E,0x30,0x0C,0x31,0x0C,0x72,0x08,0x66,0x1E,0xC4,0x1F,0xD8,0x1F,0xF0,0x18,0x00,0x30,0x00,0x30,0x00,0x30,0x00,0x30,0x00,0x60,0x00,0x60,0x00,0x60,0x00,0x60,0x00,0x00,0x00, // 'p'
	0x1C,0xC1,0x9C,0x18,0xE0,0xC7,0x0C,0x18,0x61,0x83,0x0C,0x18,0x62,0xC7,0x26,0x33,0x1E,0xB0,0x0F,0x00,0x70,0x03,0x00,0x30,0x01,0x80,0x1C,0x00,0xE0,0x07,0x00,0x38,0x00,0xC0,0x00, // 'q'
	0x06,0x00,0x1C,0x00,0x38,0x00,0x7C,0x00,0xCC,0x03,0x18,0x04,0x71,0x18,0xC2,0x23,0x08,0xC6,0x21,0x0C,0x80,0x0E,0x00, // 'r'
	0x00,0x38,0xE1,0xC3,0x8F,0x13,0x46,0x06,0xC9,0x91,0xC0, // 's'
	0x00,0x00,0x18,0x00,0xC0,0x0E,0x07,0xFF,0x03,0x00,0x38,0x01,0xC0,0x0C,0x00,0x60,0x03,0x00,0x30,0x01,0x82,0x0C,0x10,0x41,0x02,0x10,0x19,0x00,0x70,0x00, // 't'
	0x00,0x80,0x63,0x01,0xC6,0x03,0x1C,0x06,0x38,0x4C,0x60,0x90,0xC2,0x21,0x88,0x67,0x20,0x73,0x80, // 'u'
	0x01,0x83,0x1C,0x30,0xC1,0x86,0x2C,0x33,0x63,0xE3,0x10,0x19,0x80,0xD8,0x03,0x80,0x18,0x00, // 'v'
	0x03,0x08,0x00,0xC6,0x06,0x31,0x81,0x88,0xE0,0xE2,0x11,0xB9,0x8F,0x8C,0x43,0x03,0x18,0x80,0xC6,0x60,0x32,0x90,0x07,0x18,0x00, // 'w'
	0x18,0x40,0xC6,0x02,0x70,0x1F,0x00,0xF0,0x07,0x00,0x30,0x03,0xC2,0x36,0x23,0x1E,0x18,0xE0,0x00,0x00, // 'x'
	0x00,0x1C,0x66,0x19,0x86,0x63,0x30,0xCC,0x73,0x18,0xCE,0x3F,0x82,0x60,0x18,0x04,0x03,0x00,0xC0,0x30,0x0C,0x06,0x01,0x80,0x60,0x00,0x00, // 'y'
	0x0F,0xC0,0xFE,0x00,0x20,0x03,0x00,0x18,0x01,0x84,0x18,0x61,0xF6,0x0C,0xE0,0x4C,0x01,0xE0,0x1B,0x01,0x90,0x19,0x81,0x8C,0x08,0xC0,0xC6,0x06,0x60,0x33,0x01,0xF0,0x07,0x00,0x00, // 'z'
	0x00,0x00,0xE0,0x78,0x38,0x0C,0x03,0x00,0xC0,0x30,0x0C,0x03,0x01,0x80,0xC0,0x30,0x06,0x01,0x80,0x60,0x30,0x1C,0x06,0x01,0x00,0xE0,0x1C,0x07,0x00,0xC0, // '{'
	0x00,0x06,0x06,0x04,0x0C,0x0C,0x08,0x08,0x18,0x18,0x18,0x10,0x30,0x30,0x30,0x30,0x60,0x60,0x60, // '|'
	0x03,0x00,0xC0,0x38,0x06,0x01,0x80,0x60,0x30,0x0C,0x06,0x01,0x80,0x60,0x0C,0x03,0x01,0x80,0xC0,0x30,0x0C,0x03,0x00,0xC0,0x30,0x1C,0x1E,0x07,0x00,0x00 // '}'
};
const GFXglyph Satisfy_24Glyphs[] PROGMEM = {
// bitmapOffset, width, height, xAdvance, xOffset, yOffset
	  {     0,   1,   1,   8,    0,    0 }, // ' '
	  {     1,   8,  19,   7,   -1,  -18 }, // '!'
	  {    20,   8,   9,   7,    0,  -19 }, // '"'
	  {    29,  12,  13,  11,   -1,  -16 }, // '#'
	  {    49,   9,  18,   8,   -1,  -17 }, // '$'
	  {    70,  16,  19,  15,   -1,  -18 }, // '%'
	  {   108,  13,  20,  12,   -2,  -18 }, // '&'
	  {   141,   5,   9,   4,    0,  -19 }, // '''
	  {   147,   8,  24,   7,    0,  -21 }, // '('
	  {   171,   8,  24,   6,   -3,  -21 }, // ')'
	  {   195,   8,   9,   9,    1,  -18 }, // '*'
	  {   204,  10,   9,   9,   -1,  -14 }, // '+'
	  {   216,   4,   5,   6,   -1,   -2 }, // ','
	  {   219,  10,   2,   9,   -1,   -8 }, // '-'
	  {   222,   4,   3,   6,   -1,   -2 }, // '.'
	  {   224,  13,  19,   7,   -3,  -18 }, // '/'
	  {   255,  11,  19,  13,    1,  -18 }, // '0'
	  {   282,   7,  19,   8,    0,  -17 }, // '1'
	  {   299,  11,  19,  12,   -1,  -18 }, // '2'
	  {   326,  11,  19,  13,    0,  -18 }, // '3'
	  {   353,  11,  19,  12,    0,  -18 }, // '4'
	  {   380,  12,  19,  12,   -1,  -18 }, // '5'
	  {   409,  10,  19,  13,    1,  -18 }, // '6'
	  {   433,  10,  19,  11,    1,  -17 }, // '7'
	  {   457,  11,  19,  13,    0,  -18 }, // '8'
	  {   484,  10,  19,  13,    1,  -18 }, // '9'
	  {   508,   5,   8,   6,   -1,   -7 }, // ':'
	  {   513,   5,  11,   6,   -1,   -7 }, // ';'
	  {   520,   9,  10,   8,   -1,  -13 }, // '<'
	  {   532,  10,   5,  10,   -1,  -11 }, // '='
	  {   539,   8,  10,   7,   -2,  -13 }, // '>'
	  {   549,  13,  19,  11,   -1,  -18 }, // '?'
	  {   580,  17,  17,  18,   -1,  -16 }, // '@'
	  {   617,  15,  24,  14,   -3,  -18 }, // 'A'
	  {   662,  16,  23,  16,   -1,  -19 }, // 'B'
	  {   708,  14,  22,  15,    0,  -18 }, // 'C'
	  {   747,  15,  22,  16,    0,  -18 }, // 'D'
	  {   789,  14,  22,  14,   -1,  -18 }, // 'E'
	  {   828,  14,  23,  15,    0,  -18 }, // 'F'
	  {   869,  14,  22,  16,    1,  -18 }, // 'G'
	  {   908,  17,  23,  17,    0,  -19 }, // 'H'
	  {   957,  15,  22,  14,   -2,  -18 }, // 'I'
	  {   999,  17,  22,  13,   -2,  -18 }, // 'J'
	  {  1046,  15,  25,  13,   -2,  -19 }, // 'K'
	  {  1093,  16,  22,  13,   -3,  -18 }, // 'L'
	  {  1137,  21,  22,  18,   -3,  -18 }, // 'M'
	  {  1195,  16,  23,  14,   -1,  -19 }, // 'N'
	  {  1241,  13,  22,  15,    0,  -18 }, // 'O'
	  {  1277,  16,  22,  14,   -2,  -18 }, // 'P'
	  {  1321,  13,  22,  15,    0,  -18 }, // 'Q'
	  {  1357,  16,  24,  14,   -2,  -18 }, // 'R'
	  {  1405,  14,  24,  12,   -2,  -19 }, // 'S'
	  {  1447,  13,  23,  11,    0,  -19 }, // 'T'
	  {  1485,  15,  22,  15,    1,  -18 }, // 'U'
	  {  1527,  13,  23,  13,    1,  -19 }, // 'V'
	  {  1565,  20,  22,  21,    0,  -18 }, // 'W'
	  {  1620,  19,  23,  15,   -2,  -18 }, // 'X'
	  {  1675,  14,  23,  14,    0,  -19 }, // 'Y'
	  {  1716,  14,  22,  12,   -2,  -17 }, // 'Z'
	  {  1755,  10,  23,   6,   -2,  -21 }, // '['
	  {  1784,   6,  19,   8,    0,  -18 }, // '\'
	  {  1799,   9,  23,   6,   -3,  -21 }, // ']'
	  {  1825,   9,  10,   8,   -1,  -18 }, // '^'
	  {  1837,  13,   3,  12,   -3,   -2 }, // '_'
	  {  1842,   5,   4,  15,    6,  -17 }, // '`'
	  {  1845,  14,  11,  11,   -1,  -10 }, // 'a'
	  {  1865,  13,  18,  12,    1,  -17 }, // 'b'
	  {  1895,  12,  11,  10,    0,  -10 }, // 'c'
	  {  1912,  14,  18,  13,    0,  -17 }, // 'd'
	  {  1944,  12,  11,  10,   -1,  -10 }, // 'e'
	  {  1961,  13,  29,   9,   -2,  -17 }, // 'f'
	  {  2009,  14,  23,  12,    0,  -10 }, // 'g'
	  {  2050,  14,  18,  12,   -1,  -17 }, // 'h'
	  {  2082,   8,  17,   8,    1,  -16 }, // 'i'
	  {  2099,  14,  27,   7,   -5,  -16 }, // 'j'
	  {  2147,  14,  19,  12,   -1,  -18 }, // 'k'
	  {  2181,   7,  18,   6,   -1,  -17 }, // 'l'
	  {  2197,  20,  11,  18,    0,   -9 }, // 'm'
	  {  2225,  14,  10,  12,   -1,   -9 }, // 'n'
	  {  2243,  13,  11,  11,    0,  -10 }, // 'o'
	  {  2261,  16,  21,  12,   -3,  -10 }, // 'p'
	  {  2303,  13,  21,  12,    0,  -10 }, // 'q'
	  {  2338,  15,  12,  11,   -3,  -11 }, // 'r'
	  {  2361,   7,  12,   9,    0,  -11 }, // 's'
	  {  2372,  13,  18,   8,   -2,  -17 }, // 't'
	  {  2402,  15,  10,  13,    0,   -9 }, // 'u'
	  {  2421,  13,  11,  12,    1,  -10 }, // 'v'
	  {  2439,  18,  11,  17,    1,  -10 }, // 'w'
	  {  2464,  13,  12,  11,   -2,  -10 }, // 'x'
	  {  2484,  10,  21,  11,    0,  -10 }, // 'y'
	  {  2511,  13,  21,  11,   -1,  -10 }, // 'z'
	  {  2546,  10,  24,   7,   -1,  -21 }, // '{'
	  {  2576,   8,  19,   7,   -1,  -18 }, // '|'
	  {  2595,  10,  24,   7,   -3,  -21 } // '}'
};
const GFXfont Satisfy_24 PROGMEM = {
(uint8_t  *)Satisfy_24Bitmaps,(GFXglyph *)Satisfy_24Glyphs,0x20, 0x7D, 36};
