// Created by http://oleddisplay.squix.ch/ Consider a donation
// In case of problems make sure that you are using the font file with the correct version!
const uint8_t Yellowtail_32Bitmaps[] PROGMEM = {

	// Bitmap Data:
	0x00, // ' '
	0x00,0x01,0x80,0x00,0xE0,0x00,0x70,0x00,0x38,0x00,0x0E,0x00,0x07,0x00,0x03,0x80,0x00,0xE0,0x00,0x70,0x00,0x38,0x00,0x0E,0x00,0x07,0x00,0x03,0xC0,0x00,0xE0,0x00,0x70,0x00,0x1C,0x00,0x0E,0x00,0x03,0x80,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x1C,0x00,0x0F,0x00,0x03,0x80,0x00, // '!'
	0x19,0x8E,0x63,0x39,0xCC,0x67,0x19,0x8C,0xE2,0x30, // '"'
	0x00,0x20,0x00,0x08,0xC0,0x06,0x30,0x01,0x9C,0x00,0xCE,0x03,0xFF,0xE1,0xFF,0xF8,0x0C,0xE0,0x07,0x30,0x01,0x9C,0x00,0xE7,0xC1,0xFF,0xF8,0xFF,0xC0,0x0E,0x70,0x03,0x18,0x01,0xC6,0x00,0x60,0x80,0x08,0x00,0x00, // '#'
	0x00,0x18,0x00,0xE0,0x03,0x00,0x1C,0x00,0x60,0x03,0xE0,0x3F,0xC3,0xFE,0x1F,0xB0,0xEE,0x06,0x70,0x1D,0x80,0x7F,0x80,0xFF,0x01,0xFE,0x06,0x38,0x79,0xE3,0xFF,0x0F,0xF8,0x1F,0x00,0x70,0x01,0x80,0x0E,0x00,0x30,0x00, // '$'
	0x03,0x80,0x41,0xF0,0x20,0xEC,0x18,0x73,0x0C,0x39,0x86,0x1C,0x63,0x86,0x39,0xC3,0x8C,0xE0,0xC6,0x73,0xB3,0xB9,0xFF,0xDC,0xED,0xCE,0x73,0x07,0x39,0xC3,0x9C,0x60,0xE6,0x38,0x73,0x8C,0x38,0xC7,0x1C,0x33,0x86,0x0F,0xC1,0x81,0xC0, // '%'
	0x00,0x0C,0x00,0x38,0x00,0x60,0x01,0xC0,0x03,0x00,0x7F,0x83,0xFF,0x0F,0xFC,0x3D,0xF0,0x63,0x80,0xFF,0x00,0xFE,0x03,0xF0,0x0E,0xC3,0x3B,0x9E,0x76,0xF8,0xFF,0xE1,0xFF,0x01,0xF8,0x01,0xC0,0x03,0x00,0x0E,0x00,0x18,0x00,0x00, // '&'
	0x0C,0x73,0x8E,0x71,0xCE,0x30, // '''
	0x00,0x00,0xE0,0x00,0x7C,0x00,0x1F,0x00,0x0F,0x80,0x03,0xC0,0x00,0xF0,0x00,0x3C,0x00,0x0F,0x00,0x03,0xC0,0x00,0xF0,0x00,0x1C,0x00,0x07,0x00,0x01,0xE0,0x00,0x38,0x00,0x0F,0x00,0x01,0xC0,0x00,0x38,0x00,0x0E,0x00,0x01,0xC0,0x00,0x38,0x00,0x07,0x00,0x01,0xC0,0x00,0x38,0x00,0x07,0x00,0x00,0xE0,0x00,0x1C,0x00,0x03,0x80,0x00,0x70,0x00,0x0E,0x00,0x00,0xC0,0x00,0x0C,0x00,0x01,0x80,0x00, // '('
	0x00,0x00,0x00,0x00,0x30,0x00,0x0C,0x00,0x03,0x00,0x00,0xC0,0x00,0x30,0x00,0x0C,0x00,0x03,0x00,0x00,0xC0,0x00,0x30,0x00,0x1C,0x00,0x06,0x00,0x01,0x80,0x00,0xE0,0x00,0x38,0x00,0x0C,0x00,0x07,0x00,0x01,0x80,0x00,0xE0,0x00,0x30,0x00,0x1C,0x00,0x06,0x00,0x03,0x80,0x01,0xC0,0x00,0xF0,0x00,0x78,0x00,0x3C,0x00,0x1E,0x00,0x0F,0x00,0x1F,0x80,0x0F,0xC0,0x01,0xC0,0x00, // ')'
	0x03,0x0C,0xC3,0xE4,0x7F,0x3F,0xBF,0x8D,0xF0,0xCC,0x33,0x00, // '*'
	0x01,0x80,0x18,0x03,0x80,0x30,0x06,0x07,0xFF,0xFF,0xE0,0xC0,0x0C,0x01,0x80,0x08,0x00,0x00, // '+'
	0x00,0xF3,0xCF,0x79,0x80, // ','
	0x7F,0x7F,0xBF,0x00,0x00, // '-'
	0x7F,0xE0, // '.'
	0x00,0x00,0x00,0x00,0x00,0x03,0x00,0x00,0x07,0x00,0x00,0x0F,0x00,0x00,0x0E,0x00,0x00,0x1C,0x00,0x00,0x38,0x00,0x00,0x70,0x00,0x00,0xE0,0x00,0x01,0xC0,0x00,0x03,0xC0,0x00,0x07,0x80,0x00,0x0F,0x00,0x00,0x1E,0x00,0x00,0x3C,0x00,0x00,0x78,0x00,0x00,0xF0,0x00,0x01,0xE0,0x00,0x03,0xC0,0x00,0x07,0x80,0x00,0x07,0x00,0x00,0x0E,0x00,0x00,0x1E,0x00,0x00,0x3C,0x00,0x00,0x78,0x00,0x00,0x70,0x00,0x00,0xE0,0x00,0x00,0xE0,0x00,0x00,0xC0,0x00,0x00,0x80,0x00,0x00, // '/'
	0x00,0x0E,0x00,0x3F,0x00,0x73,0x00,0xE3,0x01,0xC7,0x03,0x86,0x07,0x0E,0x0E,0x1E,0x0E,0x7C,0x1C,0x7C,0x38,0x18,0x38,0x38,0x70,0x30,0x70,0x60,0xE0,0xE0,0xE1,0xC0,0xE7,0x80,0xFF,0x00,0xFC,0x00,0x78,0x00, // '0'
	0x00,0x10,0x03,0xC0,0x3C,0x03,0xE0,0x3E,0x00,0xF0,0x07,0x00,0x30,0x03,0x80,0x38,0x01,0x80,0x1C,0x01,0xC0,0x0E,0x00,0xE0,0x07,0x00,0x70,0x03,0x00,0x38,0x00,0x80,0x00, // '1'
	0x00,0x1E,0x00,0x7F,0x00,0xF3,0x01,0xE7,0x03,0x86,0x07,0x0E,0x07,0x1C,0x0E,0x38,0x00,0x70,0x00,0xE0,0x01,0xC0,0x03,0xC0,0x07,0x80,0x0F,0x00,0x1E,0x00,0x3C,0x00,0x79,0xC0,0xFF,0xE0,0xFF,0xE0,0xFE,0x00, // '2'
	0x00,0x3C,0x01,0xFC,0x07,0x98,0x3C,0x70,0xF1,0xC1,0x87,0x07,0x1C,0x04,0xF0,0x03,0x80,0x0F,0xC0,0x1F,0xC0,0x03,0x80,0x03,0x00,0x0E,0x00,0x38,0x63,0xE0,0xFF,0x81,0xFE,0x01,0xE0,0x00, // '3'
	0x00,0x06,0x00,0x1F,0x00,0x3E,0x00,0xFC,0x01,0xDC,0x03,0xB8,0x07,0x30,0x0E,0x70,0x3C,0xE0,0x78,0xE0,0x71,0xFC,0xFF,0xF8,0xFF,0xF0,0x7F,0x00,0x07,0x00,0x0E,0x00,0x0E,0x00,0x1C,0x00,0x1C,0x00,0x38,0x00,0x30,0x00, // '4'
	0x00,0x00,0x00,0x3F,0xC0,0x3F,0xE0,0x1F,0xC0,0x18,0x00,0x1C,0x00,0x0C,0x00,0x0E,0x00,0x07,0xE0,0x07,0xF8,0x03,0xFC,0x00,0x06,0x00,0x03,0x00,0x01,0x00,0x01,0x80,0x81,0x80,0xC3,0xC0,0x7F,0x80,0x3F,0x80,0x0F,0x00,0x00, // '5'
	0x00,0x00,0x00,0x03,0xC0,0x07,0xE0,0x0F,0xC0,0x0F,0x80,0x0F,0x00,0x0F,0x00,0x0F,0x00,0x0F,0x00,0x07,0x70,0x07,0xFC,0x07,0xC6,0x03,0x83,0x03,0x83,0x01,0x81,0x80,0xC1,0x80,0xC1,0xC0,0x61,0xC0,0x33,0xC0,0x1F,0xC0,0x07,0x80,0x00, // '6'
	0x03,0xFE,0x0F,0xFC,0x3F,0xF8,0x00,0xE0,0x03,0x80,0x07,0x00,0x1C,0x00,0x70,0x01,0xC0,0x07,0x00,0x0E,0x00,0x38,0x00,0xE0,0x01,0xC0,0x07,0x00,0x1C,0x00,0x38,0x00,0xE0,0x01,0x80,0x03,0x00,0x00, // '7'
	0x00,0x3C,0x00,0x07,0xE7,0x00,0xFE,0xE0,0x1C,0x7C,0x03,0x87,0x80,0x30,0xE0,0x03,0x1C,0x00,0x37,0x80,0x03,0xF0,0x00,0x3C,0x00,0x07,0xE0,0x00,0xEF,0x00,0x1C,0x70,0x03,0x03,0x00,0x70,0x30,0x06,0x06,0x00,0x61,0xE0,0x07,0xFC,0x00,0x7F,0x80,0x03,0xE0,0x00, // '8'
	0x00,0x0F,0x00,0x1F,0xC0,0x1C,0x60,0x1C,0x30,0x18,0x18,0x18,0x18,0x18,0x1C,0x0C,0x0C,0x0C,0x1E,0x06,0x3E,0x03,0xFE,0x01,0xFF,0x00,0x77,0x00,0x07,0x00,0x07,0x00,0x07,0x00,0x07,0x00,0x0F,0x00,0x1F,0x00,0x1F,0x00,0x06,0x00,0x00, // '9'
	0x07,0x0F,0x0E,0x00,0x00,0x00,0x00,0x00,0x00,0x70,0xF0,0xE0, // ':'
	0x00,0xE0,0x3C,0x07,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x18,0x07,0x80,0xF0,0x1C,0x07,0x00,0x00,0x00, // ';'
	0x00,0x00,0xC0,0xE1,0xE1,0xE3,0xC3,0x81,0x80,0x40,0x30,0x18,0x0C,0x06,0x01,0x00,0x80, // '<'
	0x0F,0xFC,0x3F,0xF8,0x70,0x00,0x00,0x01,0xFE,0x0F,0xFE,0x1F,0xE0,0x00, // '='
	0x00,0x01,0x01,0x80,0xC0,0x70,0x18,0x0C,0x07,0x03,0x87,0xC7,0x8F,0x0F,0x06,0x02,0x00, // '>'
	0x00,0xFE,0x00,0xFF,0xC0,0xFF,0xF8,0x7E,0x0F,0x1E,0x01,0xCF,0x00,0x73,0x80,0x1C,0xF0,0x0E,0x00,0x07,0x80,0x07,0xC0,0x03,0xE0,0x03,0xE0,0x01,0xF0,0x01,0xF0,0x00,0xF0,0x00,0x38,0x00,0x1C,0x00,0x03,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0C,0x00,0x07,0x00,0x01,0x80,0x00, // '?'
	0x00,0x00,0xF8,0x00,0x07,0xFC,0x00,0x1F,0xFE,0x00,0x7E,0x0F,0x00,0xF8,0x07,0x03,0xE0,0x07,0x07,0xC0,0x07,0x0F,0x8E,0xC7,0x1F,0x3F,0xCE,0x1E,0x7B,0x8E,0x3C,0xF3,0x8E,0x39,0xE7,0x1C,0x79,0xCF,0x3C,0x73,0x9E,0x38,0xF3,0x1E,0x70,0xE3,0x3E,0xF0,0xE3,0xF7,0xE0,0xE1,0xC3,0x80,0xE0,0x00,0x00,0xE0,0x02,0x00,0x70,0x06,0x00,0x78,0x3E,0x00,0x3F,0xF8,0x00,0x0F,0xE0,0x00, // '@'
	0x00,0x00,0x1E,0x00,0x00,0x3F,0x00,0x00,0x7E,0x00,0x01,0xEE,0x00,0x03,0xDC,0x00,0x07,0x9C,0x00,0x0F,0x38,0x00,0x1E,0x38,0x00,0x1C,0x78,0x00,0x38,0x70,0x00,0x70,0x70,0x00,0xE0,0xE0,0x01,0xE0,0xE0,0x03,0xC1,0xC0,0x7F,0xFF,0xC0,0x7F,0xFF,0x80,0x0E,0x03,0x80,0x1C,0x07,0x00,0x38,0x07,0x00,0x78,0x0F,0x00,0x70,0x0E,0x00,0xE0,0x1E,0x00,0xE0,0x1C,0x00,0xC0,0x38,0x00, // 'A'
	0x00,0x1F,0xE0,0x01,0xFF,0xF0,0x0F,0xFF,0xF0,0x3F,0x80,0xF0,0xF9,0xC0,0xE3,0xC7,0x01,0xC7,0x1E,0x07,0x8E,0x38,0x1E,0x00,0xE0,0xF8,0x03,0xC3,0xE0,0x07,0x3F,0x00,0x1F,0xF8,0x00,0x3F,0xF8,0x00,0xFF,0xFC,0x03,0xC0,0x7C,0x07,0x00,0x38,0x1C,0x00,0x70,0x78,0x01,0xE0,0xFC,0x07,0x83,0xF8,0x3E,0x07,0xFF,0xF8,0x0E,0xFF,0xC0,0x18,0xFE,0x00,0x00, // 'B'
	0x00,0x01,0xF0,0x00,0x7F,0xC0,0x0F,0x8E,0x00,0xF0,0x70,0x0F,0x07,0x00,0xE0,0x78,0x0E,0x07,0x80,0xE0,0x78,0x0E,0x0F,0x80,0xE0,0x70,0x0E,0x00,0x00,0x70,0x00,0x07,0x00,0x00,0x30,0x00,0x01,0x80,0x04,0x1C,0x00,0x60,0xC0,0x06,0x06,0x00,0xF0,0x30,0x0F,0x01,0x81,0xF0,0x06,0x3F,0x00,0x3F,0xE0,0x00,0x7C,0x00,0x00, // 'C'
	0x00,0x7F,0xC0,0x03,0xFF,0xF0,0x0F,0xC0,0x7C,0x1E,0x1C,0x1E,0x3C,0x38,0x0E,0x3C,0x78,0x07,0x30,0x70,0x07,0x00,0xE0,0x07,0x01,0xE0,0x07,0x01,0xC0,0x07,0x03,0x80,0x0E,0x07,0x80,0x1E,0x07,0x00,0x1E,0x0E,0x00,0x3C,0x1E,0x00,0x78,0x1C,0x00,0xF0,0x3C,0x03,0xE0,0x38,0x07,0xC0,0x70,0x1F,0x80,0x70,0xFF,0x00,0x7F,0xFC,0x00,0x7F,0xF0,0x00,0x7F,0x00,0x00, // 'D'
	0x00,0x0F,0xE0,0x07,0xFE,0x01,0xFF,0xE0,0x7E,0x3C,0x0F,0x80,0x00,0xE0,0x00,0x1C,0x00,0x01,0xC0,0x00,0x1F,0xFE,0x00,0xFF,0xE0,0x07,0xFE,0x00,0x7C,0x00,0x0F,0x80,0x01,0xE0,0x0C,0x3C,0x00,0xC7,0x80,0x1C,0x70,0x03,0x8F,0x00,0x70,0xE0,0x1E,0x0E,0x03,0xC0,0xE1,0xF8,0x07,0xFE,0x00,0x1F,0x00,0x00, // 'E'
	0x30,0x00,0x1E,0xC0,0x00,0xFD,0xFF,0xFF,0xF0,0xFF,0xFE,0x00,0x00,0x78,0x00,0x01,0xE0,0x00,0x03,0x80,0x00,0x0E,0x00,0x00,0x38,0x00,0x00,0x70,0x00,0x01,0xC0,0x01,0xFF,0xF0,0x07,0xFF,0xE0,0x0F,0xFF,0x80,0x00,0xE0,0x00,0x03,0xC0,0x00,0x07,0x00,0x00,0x1C,0x00,0x00,0x78,0x00,0x00,0xE0,0x00,0x03,0x80,0x00,0x0F,0x00,0x00,0x1C,0x00,0x00,0x00, // 'F'
	0x00,0x00,0x3F,0x00,0x00,0xFF,0xC0,0x01,0xF8,0x60,0x03,0xE0,0x30,0x03,0xC0,0x30,0x07,0xC0,0x70,0x07,0x84,0xF0,0x07,0x87,0xF0,0x07,0x81,0xF0,0x07,0x80,0x1C,0x07,0x80,0x3C,0x03,0x80,0x3C,0x03,0x80,0x3C,0x03,0x80,0x3C,0x01,0x80,0x7E,0x00,0xC0,0x7E,0x00,0xC0,0x7E,0x00,0x60,0xF6,0xC0,0x31,0xF6,0xC0,0x1F,0xE7,0xC0,0x07,0xE7,0xC0,0x01,0x87,0xC0,0x00,0x07,0x80,0x00,0x07,0x80,0x00,0x07,0x80,0x00,0x07,0x80,0x00,0x07,0x80,0x00,0x07,0x80,0x00,0x07,0x80,0x00,0x07,0x80,0x00,0x03,0x80,0x00,0x00, // 'G'
	0x00,0x03,0x00,0x70,0x00,0x78,0x03,0x81,0x0F,0xC0,0x38,0x1F,0xFC,0x03,0xC0,0xFF,0xC0,0x1C,0x03,0xCE,0x01,0xE0,0x00,0xE0,0x0E,0x00,0x0E,0x00,0xE0,0x00,0xF0,0x0F,0x00,0x07,0x00,0x70,0x00,0x70,0x07,0x80,0x7F,0xFF,0xF8,0x03,0xFF,0xFF,0x80,0x1F,0xFF,0xFC,0x00,0x1C,0x01,0xC0,0x01,0xC0,0x1E,0x00,0x1E,0x00,0xE0,0x00,0xE0,0x0E,0x00,0x0E,0x00,0xF0,0x00,0xF0,0x07,0x00,0x07,0x00,0x78,0x00,0x78,0x03,0x80,0x03,0x80,0x38,0x00,0x00,0x00,0x00,0x00, // 'H'
	0x00,0x03,0x80,0x01,0xE0,0x00,0x70,0x00,0x38,0x00,0x1E,0x00,0x0F,0x00,0x03,0x80,0x01,0xE0,0x00,0xF0,0x00,0x38,0x00,0x1E,0x00,0x0F,0x00,0x03,0xC0,0x01,0xE0,0x00,0xF0,0x00,0x3C,0x00,0x1E,0x00,0x0F,0x00,0x03,0xC0,0x01,0xE0,0x00,0x78,0x00,0x3C,0x00,0x0E,0x00,0x00, // 'I'
	0x00,0x00,0x03,0xF0,0x00,0x03,0xFF,0x00,0x01,0xFF,0xF0,0x00,0x7F,0x8E,0x00,0x0F,0xC1,0xC0,0x03,0xE0,0x38,0x00,0x7C,0x03,0x80,0x1F,0x00,0x70,0x03,0xE0,0x0E,0x00,0x78,0x01,0xC0,0x07,0x00,0x1C,0x00,0xE0,0x03,0x80,0x1E,0x00,0x70,0x01,0xC0,0x06,0x00,0x1C,0x00,0xE0,0x01,0xE2,0x1C,0x00,0x0F,0xE1,0x80,0x00,0x7C,0x3F,0x00,0x00,0x07,0xE0,0x00,0x00,0x7C,0x00,0x00,0x1F,0x80,0x00,0x07,0xE0,0x00,0x01,0xF8,0x00,0x00,0x3B,0x80,0x00,0x0F,0x70,0x00,0x01,0xCE,0x00,0x00,0x39,0xE0,0x00,0x07,0x3C,0x00,0x00,0xE7,0x80,0x00,0x0C,0xF0,0x00,0x00,0xFE,0x00,0x00,0x0F,0xC0,0x00,0x00,0x78,0x00,0x00,0x00, // 'J'
	0x00,0x03,0x00,0x30,0x00,0x7C,0x07,0xC0,0x27,0xC0,0x7C,0x03,0xFE,0x0F,0xC0,0x1F,0xE0,0xF8,0x00,0xEE,0x1F,0x80,0x00,0x71,0xF0,0x00,0x07,0x3F,0x00,0x00,0x77,0xE0,0x00,0x07,0xFC,0x00,0x00,0x3F,0x80,0x00,0x0F,0xF0,0x00,0x00,0xFE,0x00,0x00,0x01,0xFC,0x00,0x00,0x1D,0xF0,0x00,0x01,0xE3,0xE0,0x00,0x0E,0x0F,0x80,0x00,0xF0,0x3F,0x00,0x0F,0x00,0x7C,0x00,0x70,0x01,0xF8,0x07,0x80,0x07,0xF0,0x78,0x00,0x0F,0x83,0x80,0x00,0x38,0x00, // 'K'
	0x00,0x00,0x00,0x78,0x00,0x00,0x07,0xF0,0x00,0x00,0x3F,0xC0,0x00,0x01,0xE7,0x00,0x00,0x0F,0x38,0x00,0x00,0x79,0xE0,0x00,0x03,0xCF,0x00,0x00,0x1E,0xF8,0x00,0x00,0x73,0x80,0x00,0x03,0xC0,0x00,0x00,0x1E,0x00,0x00,0x00,0x70,0x00,0x00,0x03,0xC0,0x00,0x00,0x1E,0x00,0x00,0x00,0x70,0x00,0x00,0x03,0xC0,0x00,0x00,0x1E,0x00,0x00,0x00,0x70,0x00,0x03,0xFF,0x80,0x00,0x1F,0xFF,0x80,0x00,0xE1,0xFF,0xF0,0x03,0xFF,0xFF,0xFF,0x8F,0xFC,0x3F,0xFC,0x1F,0x80,0x0F,0xE0, // 'L'
	0x00,0x00,0x1E,0x03,0xE0,0x00,0x0F,0xC0,0xF8,0x00,0x03,0xF0,0x3F,0x00,0x00,0x7E,0x0F,0xC0,0x00,0x1F,0x83,0xF0,0x00,0x07,0xF0,0xFC,0x00,0x01,0xFC,0x3B,0x80,0x00,0x7F,0x0F,0xE0,0x00,0x1E,0xE3,0xF8,0x00,0x03,0xB8,0xF7,0x00,0x00,0xE7,0x3D,0xC0,0x00,0x3D,0xCF,0x70,0x00,0x0F,0x3B,0xCE,0x00,0x01,0xCE,0xF3,0x80,0x00,0x71,0xDC,0xE0,0x00,0x1E,0x77,0x1C,0x00,0x03,0x8F,0xC7,0x10,0x00,0xE3,0xF1,0xC6,0x00,0x3C,0x7C,0x39,0xC0,0x0F,0x1F,0x0E,0x70,0x01,0xC3,0xC1,0xFC,0x00,0x78,0x70,0x3F,0x00,0x0E,0x0C,0x07,0xC0,0x03,0xC0,0x00,0xE0,0x00,0xF0,0x00,0x00,0x00,0x1C,0x00,0x00,0x00,0x01,0x80,0x00,0x00,0x00,0x00, // 'M'
	0x00,0x00,0x00,0x00,0x00,0x1E,0x03,0xC0,0x01,0xF0,0x1E,0x00,0x0F,0x81,0xE0,0x00,0xF8,0x0F,0x00,0x0F,0xC0,0xF0,0x00,0xFE,0x07,0x00,0x07,0x70,0x78,0x00,0x73,0x87,0x80,0x07,0xB8,0x38,0x00,0x39,0xC3,0xC0,0x03,0x8E,0x3C,0x00,0x3C,0x73,0xC0,0x01,0xC3,0x9E,0x00,0x1E,0x39,0xE0,0x00,0xE1,0xDE,0x00,0x0E,0x0F,0xE0,0x00,0xF0,0x7F,0x00,0x07,0x03,0xF0,0x00,0x78,0x3F,0x00,0x03,0x81,0xF0,0x00,0x38,0x0F,0x80,0x01,0xC0,0x78,0x00,0x1E,0x03,0x80,0x00,0xE0,0x00,0x00,0x00, // 'N'
	0x00,0x01,0xF0,0x00,0x1F,0xE0,0x00,0x7F,0xA0,0x03,0xF3,0xF0,0x0F,0x9F,0xE0,0x3E,0x7F,0xC0,0xF1,0xE7,0x83,0xC3,0x8E,0x0F,0x06,0x3C,0x3C,0x04,0x70,0x70,0x01,0xE1,0xE0,0x07,0x87,0x80,0x0F,0x0E,0x00,0x3C,0x1C,0x00,0xF0,0x70,0x03,0xC0,0xE0,0x0F,0x01,0xC0,0x7C,0x03,0x81,0xF0,0x07,0x8F,0xC0,0x07,0xFF,0x00,0x0F,0xF8,0x00,0x07,0xC0,0x00,0x00, // 'O'
	0x00,0x1F,0xE0,0x01,0xFF,0xFC,0x07,0xF6,0x3E,0x1F,0x8C,0x1F,0x3E,0x1C,0x0F,0x7C,0x38,0x0F,0x78,0x78,0x0F,0x7C,0x70,0x1E,0x38,0xE0,0x3E,0x01,0xE0,0x7C,0x01,0xC0,0xF8,0x03,0xC3,0xF0,0x03,0xFF,0xE0,0x07,0xFF,0x80,0x0F,0xBE,0x00,0x0F,0x00,0x00,0x1E,0x00,0x00,0x3E,0x00,0x00,0x3C,0x00,0x00,0x7C,0x00,0x00,0x78,0x00,0x00,0xF0,0x00,0x00,0xF0,0x00,0x00,0x00,0x00,0x00, // 'P'
	0x00,0x00,0x7E,0x00,0x01,0xFF,0x80,0x03,0xE0,0xE0,0x03,0xC0,0x70,0x07,0x80,0x38,0x07,0x80,0x3C,0x07,0x80,0x1E,0x07,0x80,0x0E,0x07,0x80,0x0F,0x07,0x80,0x0F,0x83,0x80,0x07,0x83,0x80,0x07,0xC3,0x80,0x07,0xC1,0xC0,0x03,0xC1,0xC0,0x03,0xE0,0xE0,0x03,0xE0,0xE0,0x63,0xE0,0x70,0x73,0xE0,0x38,0x7B,0xE0,0x1C,0xFB,0xE0,0x0F,0xFF,0xC0,0x03,0xFF,0xC0,0x00,0xFF,0x80,0x00,0x03,0xE0,0x00,0x00,0xF8,0x00,0x00,0x3F,0x00,0x00,0x0F,0xE0,0x00,0x01,0xC0,0x00, // 'Q'
	0x00,0x3F,0xE0,0x03,0xFF,0xF0,0x3F,0xB0,0xF1,0xF8,0xE0,0x77,0xC1,0xC0,0xEE,0x07,0x03,0xFE,0x1E,0x0F,0xB8,0x38,0x3E,0x00,0xE0,0xF8,0x03,0x87,0xE0,0x0F,0x7F,0x00,0x1F,0xF8,0x00,0x7F,0x80,0x01,0xFC,0x00,0x03,0xBC,0x00,0x0F,0x3C,0x00,0x3C,0x3C,0x00,0x70,0x3C,0x01,0xE0,0x78,0x03,0x80,0x78,0x0F,0x00,0x78,0x1C,0x00,0x7E,0x00,0x00,0x78,0x00,0x00,0x00, // 'R'
	0x00,0x00,0xFE,0x00,0x03,0xFF,0x80,0x0F,0xFF,0xC0,0x0F,0xE7,0xE0,0x1F,0x80,0x00,0x1F,0x00,0x00,0x1E,0x00,0x00,0x1E,0x00,0x00,0x0E,0x00,0x00,0x07,0x00,0x00,0x03,0xE0,0x00,0x00,0xFF,0x00,0x00,0x3F,0xE0,0x00,0x07,0xFC,0x00,0x00,0x3F,0x00,0x00,0x03,0xC0,0x00,0x00,0xE0,0x00,0x00,0x70,0x00,0x00,0xF8,0x08,0x00,0xF8,0x06,0x03,0xF8,0x03,0xFF,0xF0,0x01,0xFF,0xE0,0x00,0x7F,0x80,0x00, // 'S'
	0x00,0x00,0x3C,0x00,0x00,0xF8,0xC0,0x0F,0xF1,0xFF,0xFF,0x83,0xFF,0xF8,0x03,0xFC,0xE0,0x00,0x03,0x80,0x00,0x0F,0x00,0x00,0x1C,0x00,0x00,0x70,0x00,0x01,0xC0,0x00,0x03,0x80,0x00,0x0E,0x00,0x00,0x3C,0x00,0x00,0x70,0x00,0x01,0xC0,0x00,0x07,0x80,0x00,0x0E,0x00,0x00,0xB8,0x00,0x03,0xF0,0x00,0x07,0xC0,0x00,0x1F,0x00,0x00,0x3C,0x00,0x00,0x70,0x00,0x00, // 'T'
	0x00,0x0C,0x00,0x00,0x07,0xC0,0x78,0x01,0xF0,0x1F,0x02,0xFC,0x07,0xC0,0xFF,0x00,0xF0,0x1F,0xC0,0x3E,0x01,0xF8,0x0F,0x80,0x0E,0x03,0xE0,0x03,0x80,0xFC,0x00,0xF0,0x3F,0x00,0x1C,0x0F,0xC0,0x07,0x03,0xF0,0x01,0xC0,0xEE,0x00,0x38,0x3F,0x80,0x0E,0x0F,0xE1,0x01,0xC3,0xDC,0x60,0x70,0xF7,0x1C,0x0E,0x3D,0xE7,0x83,0x8F,0x39,0xE0,0x77,0xCF,0xF8,0x0F,0xF1,0xFC,0x01,0xF8,0x3F,0x00,0x1C,0x07,0x80,0x00, // 'U'
	0x00,0x0E,0x07,0xC0,0x07,0x80,0xF0,0x07,0xE0,0x3C,0x03,0xF0,0x1E,0x09,0xF8,0x0F,0x83,0xFC,0x03,0xC0,0xFF,0x01,0xE0,0x3B,0x80,0xF8,0x01,0xC0,0x7C,0x00,0xF0,0x1E,0x00,0x78,0x0F,0x00,0x3C,0x07,0x80,0x0F,0x03,0xE0,0x07,0x81,0xF0,0x03,0xC0,0xF8,0x00,0xF0,0x7C,0x00,0x78,0x3C,0x00,0x1E,0x1E,0x00,0x0F,0x1F,0x00,0x03,0xCF,0x80,0x00,0xFF,0x80,0x00,0x3F,0xC0,0x00,0x07,0x80,0x00,0x00, // 'V'
	0x00,0x0E,0x00,0x07,0x80,0x0F,0x80,0x00,0xF0,0x0F,0xE0,0xF0,0x3C,0x0F,0xF0,0x78,0x1E,0x03,0xB8,0x1E,0x07,0x80,0xDE,0x0F,0x03,0xC0,0x0F,0x07,0x81,0xF0,0x07,0x83,0xC0,0x78,0x03,0xC1,0xF0,0x3C,0x01,0xF0,0xF8,0x1F,0x00,0x78,0x7E,0x0F,0x80,0x3C,0x3F,0x07,0xC0,0x1F,0x1F,0xC3,0xE0,0x07,0x8F,0xE0,0xF0,0x03,0xC7,0xF8,0x78,0x00,0xF3,0xDC,0x3C,0x00,0x79,0xEF,0x1E,0x00,0x1E,0xF3,0x8F,0x00,0x0F,0x79,0xEF,0x80,0x03,0xFC,0x7F,0xC0,0x00,0xFE,0x1F,0xC0,0x00,0x3F,0x07,0xE0,0x00,0x07,0x00,0xE0,0x00,0x00, // 'W'
	0x00,0x01,0x80,0x00,0x00,0x1F,0x00,0xF0,0x01,0xFC,0x0F,0x80,0x0F,0xF0,0x78,0x00,0x79,0xC7,0xC0,0x01,0xC6,0x3E,0x00,0x06,0x39,0xE0,0x00,0x10,0xFF,0x00,0x00,0x03,0xF8,0x00,0x00,0x1F,0x80,0x00,0x00,0x7C,0x00,0x00,0x03,0xE0,0x00,0x00,0x1F,0x00,0x00,0x00,0xF8,0x00,0x00,0x0F,0xE0,0x00,0x00,0x7B,0x82,0x00,0x03,0xDC,0x38,0x00,0x3E,0x71,0xE0,0x01,0xF1,0xCF,0x00,0x0F,0x87,0x7C,0x00,0x7C,0x1F,0xC0,0x01,0xC0,0x7E,0x00,0x06,0x00,0xE0,0x00,0x00, // 'X'
	0x00,0x00,0x00,0x00,0x07,0x00,0x40,0x07,0xC0,0x78,0x03,0xE0,0x3C,0x0B,0xF8,0x1F,0x03,0xFC,0x07,0x80,0xEE,0x03,0xE0,0x07,0x81,0xF0,0x03,0xC0,0xF8,0x00,0xF0,0x7E,0x00,0x78,0x3F,0x00,0x3C,0x1F,0xC0,0x0F,0x0E,0xE0,0x07,0x87,0x70,0x03,0xC3,0xBC,0x00,0xF1,0xCE,0x00,0x78,0xE7,0x80,0x1E,0x73,0xC0,0x0F,0x38,0xE0,0x03,0xFC,0x78,0x00,0xFE,0x1C,0x00,0x3E,0x0F,0x00,0x07,0x07,0x80,0x00,0x01,0xC0,0x00,0x00,0xF0,0x00,0x00,0x38,0x00,0x00,0x1E,0x00,0x00,0x0F,0x00,0x00,0x03,0xA0,0x00,0x01,0xF0,0x00,0x00,0x78,0x00,0x00,0x1E,0x00,0x00,0x03,0x00,0x00,0x00, // 'Y'
	0x00,0xC0,0x01,0xC0,0x70,0x00,0xF0,0x1F,0xFF,0xF8,0x07,0xFF,0xFC,0x00,0xFF,0x3E,0x00,0x00,0x1E,0x00,0x00,0x0F,0x00,0x00,0x0F,0x80,0x00,0x07,0xC0,0x00,0x03,0xE0,0x00,0x01,0xF0,0x00,0x00,0xF8,0x00,0x00,0x7C,0x00,0x00,0x3E,0x00,0x00,0x1F,0x00,0x00,0x0F,0x80,0x00,0x07,0xC0,0x00,0x03,0xE0,0x00,0x01,0xF0,0x00,0x00,0xF8,0x00,0x00,0x3C,0x7F,0xE0,0x1F,0xFF,0xFC,0x07,0xFF,0xFF,0x00,0xFE,0x00,0x40, // 'Z'
	0x00,0x07,0xF0,0x00,0x3F,0x80,0x03,0xFC,0x00,0x1C,0x00,0x00,0xC0,0x00,0x0E,0x00,0x00,0x70,0x00,0x07,0x00,0x00,0x38,0x00,0x03,0x80,0x00,0x1C,0x00,0x01,0xC0,0x00,0x0E,0x00,0x00,0xE0,0x00,0x07,0x00,0x00,0x70,0x00,0x03,0x80,0x00,0x3C,0x00,0x01,0xC0,0x00,0x1E,0x00,0x00,0xE0,0x00,0x0F,0x00,0x00,0x70,0x00,0x07,0x80,0x00,0x38,0x00,0x03,0xC0,0x00,0x1C,0x00,0x01,0xE0,0x00,0x0E,0x00,0x00,0x7F,0x80,0x03,0xF8,0x00,0x0F,0x00,0x00, // '['
	0x03,0x18,0xE7,0x18,0xC7,0x39,0xCE,0x71,0x8C,0x63,0x18,0xC6,0x31,0x8C,0x63,0x18,0xC6,0x31,0x08, // '\'
	0x00,0x07,0xF0,0x00,0x7F,0x80,0x01,0xFC,0x00,0x00,0x70,0x00,0x07,0x00,0x00,0x38,0x00,0x03,0x80,0x00,0x1C,0x00,0x01,0xC0,0x00,0x0E,0x00,0x00,0x70,0x00,0x07,0x00,0x00,0x38,0x00,0x03,0x80,0x00,0x1C,0x00,0x01,0xC0,0x00,0x0E,0x00,0x00,0xE0,0x00,0x07,0x00,0x00,0x70,0x00,0x03,0x80,0x00,0x3C,0x00,0x01,0xC0,0x00,0x1E,0x00,0x00,0xE0,0x00,0x07,0x00,0x00,0x70,0x00,0x03,0x80,0x00,0x38,0x00,0x3F,0xC0,0x03,0xFC,0x00,0x1F,0xE0,0x00, // ']'
	0x00,0x40,0x1C,0x0F,0x83,0xF0,0xF6,0x3C,0xDF,0x3F,0x87,0xE0,0xE8,0x1C, // '^'
	0x3F,0xFC,0xFF,0xF9,0x80,0x00, // '_'
	0x07,0x38,0xE3,0x18,0x60, // '`'
	0x01,0xFC,0x07,0xFC,0x0F,0x38,0x1C,0x78,0x38,0xF0,0x70,0xF0,0x61,0xE0,0xE3,0xE0,0xC7,0xC2,0xDF,0xC6,0xFE,0xCE,0xFC,0xFC,0x70,0x70, // 'a'
	0x00,0x1C,0x00,0xE0,0x03,0x00,0x1C,0x00,0xE0,0x03,0x00,0x1C,0x00,0xE0,0x03,0x00,0x1C,0x00,0x7F,0x03,0xFE,0x1F,0x38,0x60,0xE3,0x83,0x8C,0x1C,0x70,0x71,0x83,0x86,0x1C,0x30,0xF0,0xC7,0x83,0x3C,0x0F,0xC0,0x1C,0x00, // 'b'
	0x01,0xE0,0x3F,0x83,0xCC,0x38,0xE3,0x86,0x38,0x01,0x80,0x1C,0x00,0xC0,0x06,0x00,0xF0,0x0D,0xC3,0xE7,0xFC,0x1F,0x00, // 'c'
	0x00,0x00,0x18,0x00,0x01,0xC0,0x00,0x1C,0x00,0x01,0xC0,0x00,0x0E,0x00,0x00,0xE0,0x00,0x0F,0x00,0x00,0x70,0x00,0x07,0x00,0x00,0x78,0x00,0x3B,0x80,0x07,0xFC,0x00,0x7F,0xC0,0x07,0x1E,0x00,0x71,0xE0,0x07,0x1E,0x00,0x71,0xF0,0x03,0x1F,0x00,0x39,0xF8,0x81,0x9F,0xCC,0x0D,0xEC,0x60,0x7E,0x6E,0x01,0xC3,0xE0,0x00,0x0E,0x00, // 'd'
	0x01,0xE0,0x7E,0x0E,0xC1,0xC0,0x30,0x03,0xF0,0x3E,0x03,0xC0,0x70,0x2E,0x06,0xC0,0xEC,0x3C,0xFF,0x83,0xE0, // 'e'
	0x00,0x00,0x30,0x00,0x03,0x80,0x00,0x18,0x00,0x01,0xC0,0x00,0x1C,0x00,0x00,0xC0,0x00,0x0E,0x00,0x00,0xE0,0x00,0x06,0x00,0x00,0x70,0x00,0x03,0x00,0x00,0x38,0x00,0x03,0x80,0x00,0x18,0x00,0x01,0xC0,0x00,0x0C,0xE0,0x03,0xFF,0x80,0x3F,0xC0,0x01,0xE7,0x00,0x07,0x18,0x00,0x70,0xC0,0x03,0x86,0x00,0x38,0x60,0x01,0x86,0x00,0x1C,0x30,0x00,0xC3,0x00,0x0E,0x30,0x00,0x63,0x00,0x07,0x38,0x00,0x33,0x80,0x01,0xF0,0x00,0x0F,0x00,0x00,0x70,0x00,0x00, // 'f'
	0x00,0x76,0x01,0xFE,0x07,0xDC,0x0F,0x3C,0x1C,0x78,0x38,0xF0,0x70,0xF0,0x61,0xE0,0xE7,0xC2,0xCF,0xCF,0xDF,0x9E,0xF9,0xFC,0x73,0xF0,0x07,0xE0,0x07,0x80,0x0F,0x00,0x1E,0x00,0x3C,0x00,0x7C,0x00,0xF8,0x00,0xF0,0x00,0xE0,0x00,0xE0,0x00, // 'g'
	0x00,0x06,0x00,0x07,0x00,0x07,0x00,0x03,0x80,0x03,0x80,0x01,0x80,0x01,0xC0,0x01,0xC0,0x00,0xC0,0x00,0xE0,0x00,0x63,0x80,0x77,0xC0,0x37,0xE0,0x37,0x60,0x3F,0x70,0x1B,0x70,0x1F,0x38,0x0F,0x38,0x0F,0x9C,0x07,0x9E,0x27,0x8E,0x33,0x87,0x39,0x83,0xF8,0xC0,0xF0, // 'h'
	0x00,0x60,0x1C,0x03,0x00,0x00,0x00,0x0C,0x03,0x80,0xF0,0x1E,0x03,0x80,0x70,0x1C,0x03,0x80,0xE0,0x1C,0x47,0x18,0xE7,0x1F,0xC1,0xE0,0x00, // 'i'
	0x00,0x00,0x00,0x00,0x07,0x00,0x00,0xE0,0x00,0x0C,0x00,0x00,0x00,0x00,0x30,0x00,0x0F,0x00,0x01,0xF0,0x00,0x1E,0x00,0x00,0xC0,0x00,0x1C,0x00,0x03,0x80,0x00,0x38,0x00,0x07,0x18,0x00,0x63,0x80,0x0E,0xF0,0x00,0xDC,0x00,0x1F,0x80,0x01,0xE0,0x00,0x3C,0x00,0x07,0x80,0x00,0xF0,0x00,0x1E,0x00,0x03,0xC0,0x00,0x7C,0x00,0x0F,0x80,0x00,0xF0,0x00,0x0E,0x00,0x00, // 'j'
	0x00,0x0C,0x00,0x38,0x00,0x60,0x01,0xC0,0x07,0x00,0x0E,0x00,0x38,0x00,0x70,0x01,0xC0,0x03,0x80,0x0E,0x30,0x1C,0x60,0x71,0xC0,0xE7,0x03,0x9C,0x06,0x78,0x1F,0xC0,0x3F,0x00,0xFC,0x01,0xF8,0x17,0x38,0x6E,0x71,0xB8,0x7E,0x30,0x78, // 'k'
	0x00,0x18,0x01,0xF0,0x0E,0xC0,0x73,0x01,0x98,0x0E,0xE0,0x73,0x01,0x98,0x0C,0xE0,0x77,0x01,0x98,0x0C,0xC0,0x37,0x00,0xF8,0x06,0xC0,0x1E,0x00,0x70,0x03,0xC0,0x0E,0x00,0x30,0x20,0xC1,0x83,0x1E,0x0F,0xF0,0x1F,0x00, // 'l'
	0x01,0x8C,0x1C,0x07,0x7C,0xF8,0x0D,0xFB,0xF0,0x3F,0x6E,0xC0,0xF9,0xFB,0x81,0xE3,0xE6,0x07,0x8F,0x98,0x0F,0x1E,0x60,0x3C,0x78,0xC2,0x70,0xE3,0x0D,0xC3,0x86,0x7B,0x8E,0x0F,0xE6,0x1C,0x1F,0x80,0x00,0x1E,0x00, // 'm'
	0x00,0xC7,0x00,0xEF,0x80,0xEF,0xC0,0x7F,0xC0,0x7E,0xE0,0x3C,0xE0,0x3C,0x60,0x3E,0x70,0x1E,0x30,0x1E,0x38,0xCE,0x18,0xEE,0x0C,0xE6,0x07,0xE0,0x01,0xC0, // 'n'
	0x01,0xC0,0x3F,0x83,0xDE,0x3C,0x63,0x83,0x38,0x39,0xC3,0x8C,0x18,0xC1,0x86,0x1C,0x33,0xC1,0xF8,0x07,0x80,0x00, // 'o'
	0x00,0x01,0x80,0x00,0x38,0x00,0x07,0x00,0x00,0x60,0x00,0x0E,0x00,0x00,0xDF,0x00,0x1B,0xF0,0x03,0xF7,0x00,0x3E,0x60,0x07,0xC6,0x00,0xF8,0xC0,0x0F,0x1C,0x01,0xE3,0x80,0x3C,0x70,0x03,0x8E,0x00,0x7F,0xC0,0x06,0xF0,0x00,0xE0,0x00,0x1C,0x00,0x01,0x80,0x00,0x38,0x00,0x07,0x00,0x00,0x70,0x00,0x0E,0x00,0x00,0xC0,0x00,0x0C,0x00,0x00, // 'p'
	0x00,0x76,0x01,0xFE,0x03,0xCE,0x0F,0x1C,0x1E,0x3C,0x3C,0x38,0x38,0x78,0x70,0xF0,0x71,0xE0,0xE3,0xE0,0xEF,0xC0,0xFF,0xC0,0xFD,0x80,0x73,0x80,0x03,0x00,0x07,0x00,0x06,0x80,0x0F,0xC0,0x0F,0x80,0x1F,0x00,0x1E,0x00,0x1C,0x00,0x18,0x00, // 'q'
	0x00,0xCE,0x01,0xDE,0x03,0xFE,0x03,0xEE,0x07,0xCC,0x0F,0x88,0x0F,0x00,0x1E,0x00,0x1C,0x00,0x38,0x00,0x38,0x00,0x70,0x00,0x60,0x00, // 'r'
	0x00,0xF0,0x3F,0xC3,0xFC,0x7C,0xE3,0x80,0x30,0x01,0xC0,0x0F,0xE0,0x3F,0xC0,0x1E,0x00,0x71,0x8F,0x0F,0xF0,0x3E,0x00, // 's'
	0x00,0x30,0x01,0xC0,0x0E,0x00,0x3C,0x3F,0xFD,0xFF,0xE0,0x70,0x01,0xC0,0x0E,0x00,0x30,0x01,0xC0,0x0E,0x00,0x38,0x01,0xC0,0x06,0x00,0x18,0x00,0xC1,0x03,0x0C,0x0C,0xF0,0x3F,0x00,0x78,0x00, // 't'
	0x02,0x0E,0x07,0x0C,0x0E,0x1C,0x0E,0x18,0x1C,0x38,0x1C,0x70,0x38,0xF0,0x39,0xE0,0x73,0xE0,0x77,0xC2,0x6E,0xC6,0x7C,0xCE,0x78,0xFC,0x70,0x70, // 'u'
	0x00,0x00,0x61,0xC7,0x0E,0x78,0x77,0x87,0x3C,0x38,0xE3,0x86,0x1C,0x71,0xC3,0x1C,0x39,0xC1,0x9C,0x0D,0xC0,0x7C,0x01,0x80,0x00, // 'v'
	0x00,0x00,0x00,0xE0,0x0C,0x3C,0x61,0x8F,0x1C,0x73,0xE7,0x0C,0x38,0xE3,0x87,0x3C,0x61,0xCF,0x1C,0x3B,0xE7,0x06,0xF9,0xC1,0xFB,0x70,0x3E,0x7C,0x07,0xCF,0x00,0xF1,0xC0,0x18,0x00,0x00, // 'w'
	0x00,0x06,0x03,0x1E,0x07,0x3C,0x0F,0x78,0x0F,0xF0,0x03,0xE0,0x03,0xC0,0x03,0x80,0x0F,0x00,0x1F,0x00,0x3F,0x08,0x7B,0x18,0x73,0x38,0xE3,0xF0,0xC1,0xC0, // 'x'
	0x06,0x0C,0x0C,0x38,0x38,0x60,0x61,0xC1,0xC7,0x07,0x1E,0x0E,0x78,0x39,0xF1,0x77,0xC7,0xDF,0x1F,0xF6,0x77,0xD9,0x87,0x76,0x00,0xF8,0x03,0xE0,0x0F,0x80,0x3E,0x00,0xF8,0x01,0xE0,0x07,0xC0,0x0F,0x00,0x0C,0x00,0x00, // 'y'
	0x1F,0xF8,0xFF,0xE0,0x0F,0x00,0x78,0x07,0x80,0x3C,0x01,0xE0,0x0F,0x00,0x38,0x19,0xC7,0xEF,0xFF,0xBF,0xF0,0xF8,0x00, // 'z'
	0x00,0x03,0xE0,0x00,0x7F,0x80,0x07,0xFC,0x00,0x38,0x20,0x01,0xC0,0x00,0x0E,0x00,0x00,0x70,0x00,0x03,0x80,0x00,0x18,0x00,0x01,0xC0,0x00,0x1E,0x00,0x01,0xE0,0x00,0x1E,0x00,0x1F,0xE0,0x00,0xFC,0x00,0x01,0xF0,0x00,0x03,0x80,0x00,0x1C,0x00,0x01,0xE0,0x00,0x0E,0x00,0x00,0xE0,0x00,0x07,0x00,0x00,0x70,0x00,0x07,0x00,0x00,0x38,0x00,0x03,0x80,0x00,0x1C,0x00,0x01,0xC0,0x00,0x0E,0x00,0x00,0x7F,0x80,0x03,0xF8,0x00,0x0F,0x00,0x00, // '{'
	0x00,0x00,0xC0,0x00,0x70,0x00,0x18,0x00,0x0E,0x00,0x07,0x00,0x01,0x80,0x00,0xE0,0x00,0x30,0x00,0x1C,0x00,0x06,0x00,0x03,0x80,0x00,0xC0,0x00,0x70,0x00,0x38,0x00,0x0E,0x00,0x07,0x00,0x01,0xC0,0x00,0xE0,0x00,0x38,0x00,0x1C,0x00,0x07,0x00,0x03,0x80,0x00,0xC0,0x00,0x70,0x00,0x18,0x00,0x0E,0x00,0x03,0x00,0x01,0xC0,0x00,0x60,0x00,0x38,0x00,0x0C,0x00,0x00, // '|'
	0x00,0x01,0xF0,0x00,0x3F,0xC0,0x01,0xFE,0x00,0x00,0x70,0x00,0x03,0x80,0x00,0x38,0x00,0x01,0xC0,0x00,0x1C,0x00,0x01,0xC0,0x00,0x1C,0x00,0x00,0xE0,0x00,0x0E,0x00,0x00,0x70,0x00,0x03,0xC0,0x00,0x0F,0x80,0x00,0xFE,0x00,0x0F,0xE0,0x00,0xF0,0x00,0x0F,0x00,0x00,0x70,0x00,0x07,0x00,0x00,0x30,0x00,0x03,0x80,0x00,0x1C,0x00,0x00,0xE0,0x00,0x0E,0x00,0x00,0x70,0x00,0x03,0x80,0x00,0x1C,0x00,0x31,0xC0,0x01,0xFE,0x00,0x0F,0xC0,0x00 // '}'
};
const GFXglyph Yellowtail_32Glyphs[] PROGMEM = {
// bitmapOffset, width, height, xAdvance, xOffset, yOffset
	  {     0,   1,   1,   8,    0,    0 }, // ' '
	  {     1,  18,  24,  12,    2,  -23 }, // '!'
	  {    55,  10,   8,  12,    7,  -22 }, // '"'
	  {    65,  18,  18,  18,    2,  -19 }, // '#'
	  {   106,  14,  24,  14,    2,  -22 }, // '$'
	  {   148,  18,  20,  23,    4,  -20 }, // '%'
	  {   193,  15,  23,  15,    3,  -23 }, // '&'
	  {   237,   6,   8,   7,    7,  -22 }, // '''
	  {   243,  19,  32,  13,    3,  -26 }, // '('
	  {   319,  18,  32,  12,   -3,  -26 }, // ')'
	  {   391,  10,   9,  13,    7,  -22 }, // '*'
	  {   403,  12,  12,  14,    3,  -16 }, // '+'
	  {   421,   6,   6,   9,    0,   -3 }, // ','
	  {   426,   9,   4,  11,    2,   -9 }, // '-'
	  {   431,   4,   3,   9,    2,   -2 }, // '.'
	  {   433,  24,  30,  14,   -2,  -26 }, // '/'
	  {   523,  16,  20,  14,    1,  -20 }, // '0'
	  {   563,  13,  20,   9,    0,  -20 }, // '1'
	  {   596,  16,  20,  14,    1,  -20 }, // '2'
	  {   636,  15,  19,  14,    1,  -19 }, // '3'
	  {   672,  16,  21,  15,    2,  -20 }, // '4'
	  {   714,  17,  20,  14,    1,  -19 }, // '5'
	  {   757,  17,  21,  14,    1,  -20 }, // '6'
	  {   802,  15,  20,  12,    2,  -19 }, // '7'
	  {   840,  20,  20,  14,    0,  -19 }, // '8'
	  {   890,  17,  21,  15,    1,  -20 }, // '9'
	  {   935,   8,  12,  12,    3,  -11 }, // ':'
	  {   947,  11,  14,  13,    1,  -11 }, // ';'
	  {   967,   9,  15,  12,    5,  -17 }, // '<'
	  {   984,  15,   7,  15,    2,  -13 }, // '='
	  {   998,   9,  15,  13,    2,  -18 }, // '>'
	  {  1015,  18,  24,  18,    4,  -23 }, // '?'
	  {  1069,  24,  24,  24,    3,  -23 }, // '@'
	  {  1141,  24,  24,  20,    1,  -23 }, // 'A'
	  {  1213,  23,  23,  23,    3,  -23 }, // 'B'
	  {  1280,  21,  23,  20,    3,  -23 }, // 'C'
	  {  1341,  24,  23,  26,    4,  -23 }, // 'D'
	  {  1410,  20,  23,  20,    3,  -23 }, // 'E'
	  {  1468,  23,  23,  20,    5,  -23 }, // 'F'
	  {  1535,  25,  31,  22,    2,  -23 }, // 'G'
	  {  1632,  29,  24,  25,    3,  -23 }, // 'H'
	  {  1719,  18,  23,  10,    0,  -23 }, // 'I'
	  {  1771,  28,  33,  22,    0,  -23 }, // 'J'
	  {  1887,  29,  23,  25,    1,  -23 }, // 'K'
	  {  1971,  30,  24,  24,   -2,  -23 }, // 'L'
	  {  2061,  35,  27,  26,   -3,  -23 }, // 'M'
	  {  2180,  29,  25,  23,    0,  -24 }, // 'N'
	  {  2271,  23,  23,  22,    3,  -23 }, // 'O'
	  {  2338,  24,  24,  24,    5,  -23 }, // 'P'
	  {  2410,  25,  28,  24,    2,  -23 }, // 'Q'
	  {  2498,  23,  24,  25,    5,  -23 }, // 'R'
	  {  2567,  25,  24,  23,    2,  -23 }, // 'S'
	  {  2642,  23,  24,  19,    5,  -23 }, // 'T'
	  {  2711,  27,  23,  25,    3,  -23 }, // 'U'
	  {  2789,  26,  23,  22,    3,  -23 }, // 'V'
	  {  2864,  34,  23,  30,    2,  -23 }, // 'W'
	  {  2962,  30,  23,  21,   -1,  -23 }, // 'X'
	  {  3049,  26,  33,  23,    3,  -23 }, // 'Y'
	  {  3157,  26,  24,  20,    0,  -23 }, // 'Z'
	  {  3235,  21,  32,  13,    0,  -27 }, // '['
	  {  3319,   5,  30,  13,    6,  -26 }, // '\'
	  {  3338,  21,  32,  14,   -2,  -27 }, // ']'
	  {  3422,  11,  10,  17,    7,  -23 }, // '^'
	  {  3436,  15,   3,  13,   -3,    2 }, // '_'
	  {  3442,   5,   7,  14,    8,  -21 }, // '`'
	  {  3447,  16,  13,  15,    1,  -12 }, // 'a'
	  {  3473,  14,  24,  14,    1,  -23 }, // 'b'
	  {  3515,  13,  14,  13,    1,  -13 }, // 'c'
	  {  3538,  21,  24,  15,    1,  -23 }, // 'd'
	  {  3601,  12,  14,  12,    1,  -13 }, // 'e'
	  {  3622,  21,  33,  12,   -5,  -23 }, // 'f'
	  {  3709,  16,  23,  14,    0,  -13 }, // 'g'
	  {  3755,  17,  24,  14,   -1,  -23 }, // 'h'
	  {  3806,  11,  19,   8,    1,  -18 }, // 'i'
	  {  3833,  20,  28,   8,   -8,  -18 }, // 'j'
	  {  3903,  15,  24,  13,   -1,  -23 }, // 'k'
	  {  3948,  14,  24,  10,    2,  -23 }, // 'l'
	  {  3990,  23,  14,  21,   -1,  -13 }, // 'm'
	  {  4031,  17,  14,  14,   -2,  -13 }, // 'n'
	  {  4061,  13,  13,  13,    1,  -13 }, // 'o'
	  {  4083,  20,  26,  14,   -6,  -16 }, // 'p'
	  {  4148,  16,  23,  14,    0,  -13 }, // 'q'
	  {  4194,  16,  13,  12,   -2,  -13 }, // 'r'
	  {  4220,  13,  14,  13,    1,  -13 }, // 's'
	  {  4243,  14,  21,   8,    1,  -20 }, // 't'
	  {  4280,  16,  14,  15,    0,  -13 }, // 'u'
	  {  4308,  13,  15,  13,    1,  -14 }, // 'v'
	  {  4333,  19,  15,  18,    0,  -14 }, // 'w'
	  {  4369,  16,  15,  13,   -1,  -14 }, // 'x'
	  {  4399,  15,  22,  14,    0,  -12 }, // 'y'
	  {  4441,  14,  13,  13,    0,  -13 }, // 'z'
	  {  4464,  21,  32,  15,    3,  -27 }, // '{'
	  {  4548,  18,  31,  13,    1,  -27 }, // '|'
	  {  4618,  21,  32,  16,   -3,  -27 } // '}'
};
const GFXfont Yellowtail_32 PROGMEM = {
(uint8_t  *)Yellowtail_32Bitmaps,(GFXglyph *)Yellowtail_32Glyphs,0x20, 0x7D, 45};
