const uint8_t FreeSerifBold18pt7bBitmaps[] PROGMEM = {
  0x7B, 0xEF, 0xFF, 0xFF, 0xF7, 0x9E, 0x71, 0xC7, 0x0C, 0x20, 0x82, 0x00,
  0x00, 0x07, 0x3E, 0xFF, 0xFF, 0xDC, 0x60, 0x37, 0x83, 0xFC, 0x1F, 0xE0,
  0xFF, 0x07, 0xB8, 0x3D, 0xC0, 0xCC, 0x06, 0x20, 0x31, 0x01, 0x80, 0x03,
  0x8E, 0x00, 0xC3, 0x80, 0x30, 0xE0, 0x1C, 0x38, 0x07, 0x0E, 0x01, 0xC3,
  0x87, 0xFF, 0xFD, 0xFF, 0xFF, 0x7F, 0xFF, 0xC1, 0x87, 0x00, 0xE1, 0xC0,
  0x38, 0x70, 0x0E, 0x1C, 0x03, 0x86, 0x0F, 0xFF, 0xF3, 0xFF, 0xFC, 0xFF,
  0xFF, 0x07, 0x0E, 0x01, 0xC3, 0x80, 0x70, 0xE0, 0x1C, 0x30, 0x07, 0x0C,
  0x01, 0x87, 0x00, 0x61, 0xC0, 0x02, 0x00, 0x04, 0x00, 0x08, 0x00, 0xFF,
  0x03, 0x27, 0x8C, 0x47, 0x38, 0x86, 0x71, 0x0C, 0xF2, 0x09, 0xF4, 0x03,
  0xF8, 0x03, 0xF8, 0x07, 0xFC, 0x03, 0xFC, 0x03, 0xFE, 0x01, 0xFE, 0x03,
  0xFC, 0x04, 0xFC, 0x08, 0xFA, 0x10, 0xF4, 0x21, 0xEC, 0x43, 0xD8, 0x8F,
  0x3D, 0x3C, 0x3F, 0xF0, 0x1F, 0x00, 0x08, 0x00, 0x10, 0x00, 0x03, 0xC0,
  0x18, 0x01, 0xFE, 0x0F, 0x00, 0x7C, 0xFF, 0xC0, 0x1F, 0x0F, 0x90, 0x07,
  0xC1, 0x06, 0x00, 0xF0, 0x21, 0x80, 0x3E, 0x04, 0x30, 0x07, 0x81, 0x8C,
  0x00, 0xF0, 0x21, 0x80, 0x1E, 0x0C, 0x60, 0x03, 0xC1, 0x18, 0x1E, 0x3C,
  0xE3, 0x0F, 0xE7, 0xF8, 0xC3, 0xE6, 0x3C, 0x18, 0xF8, 0x40, 0x06, 0x3E,
  0x08, 0x01, 0x87, 0x81, 0x00, 0x31, 0xF0, 0x20, 0x0C, 0x3E, 0x04, 0x01,
  0x87, 0x81, 0x00, 0x60, 0xF0, 0x60, 0x18, 0x1E, 0x08, 0x03, 0x03, 0xC7,
  0x00, 0xC0, 0x3F, 0xC0, 0x18, 0x03, 0xE0, 0x00, 0x7E, 0x00, 0x00, 0x7F,
  0xE0, 0x00, 0x38, 0xF8, 0x00, 0x1E, 0x1F, 0x00, 0x07, 0x83, 0xC0, 0x01,
  0xF0, 0xF0, 0x00, 0x7C, 0x38, 0x00, 0x1F, 0x9C, 0x00, 0x03, 0xFC, 0x00,
  0x00, 0xFE, 0x0F, 0xF0, 0x3F, 0x80, 0xF0, 0x1F, 0xF0, 0x18, 0x1C, 0xFE,
  0x0C, 0x0E, 0x1F, 0xC3, 0x07, 0x87, 0xF1, 0x81, 0xE0, 0xFE, 0x40, 0xF8,
  0x1F, 0xF0, 0x3F, 0x07, 0xF8, 0x0F, 0xC0, 0xFE, 0x03, 0xF8, 0x1F, 0xC0,
  0xFE, 0x07, 0xF8, 0x9F, 0xE3, 0xFF, 0xE7, 0xFF, 0x9F, 0xF0, 0xFF, 0xC3,
  0xF8, 0x0F, 0x80, 0x3C, 0x00, 0x6F, 0xFF, 0xFF, 0x66, 0x66, 0x00, 0x81,
  0x81, 0x81, 0x81, 0x80, 0xC0, 0xE0, 0x70, 0x70, 0x38, 0x3C, 0x1E, 0x0F,
  0x07, 0x83, 0xC1, 0xE0, 0xF0, 0x78, 0x3C, 0x0E, 0x07, 0x03, 0x80, 0xE0,
  0x70, 0x18, 0x06, 0x01, 0x00, 0x40, 0x10, 0x04, 0x80, 0x30, 0x0C, 0x03,
  0x00, 0xC0, 0x60, 0x38, 0x1C, 0x07, 0x03, 0x81, 0xC0, 0xF0, 0x78, 0x3C,
  0x1E, 0x0F, 0x07, 0x83, 0xC1, 0xE0, 0xE0, 0x70, 0x38, 0x38, 0x1C, 0x0C,
  0x0C, 0x06, 0x04, 0x04, 0x04, 0x00, 0x03, 0x00, 0x1E, 0x00, 0x78, 0x1D,
  0xE6, 0xFB, 0x3D, 0xED, 0xF3, 0xFF, 0x01, 0xC0, 0x7F, 0xF3, 0xED, 0xFF,
  0x33, 0xD9, 0xE6, 0x07, 0x80, 0x1E, 0x00, 0x30, 0x00, 0x00, 0xE0, 0x00,
  0x1C, 0x00, 0x03, 0x80, 0x00, 0x70, 0x00, 0x0E, 0x00, 0x01, 0xC0, 0x00,
  0x38, 0x00, 0x07, 0x00, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x80,
  0x70, 0x00, 0x0E, 0x00, 0x01, 0xC0, 0x00, 0x38, 0x00, 0x07, 0x00, 0x00,
  0xE0, 0x00, 0x1C, 0x00, 0x03, 0x80, 0x00, 0x73, 0xEF, 0xFF, 0xFD, 0xF0,
  0xC2, 0x18, 0xC6, 0x30, 0xFF, 0xFF, 0xFF, 0xFF, 0x7B, 0xFF, 0xFF, 0xFD,
  0xE0, 0x00, 0xE0, 0x3C, 0x07, 0x00, 0xE0, 0x1C, 0x07, 0x00, 0xE0, 0x1C,
  0x07, 0x00, 0xE0, 0x1C, 0x07, 0x00, 0xE0, 0x1C, 0x07, 0x00, 0xE0, 0x1C,
  0x07, 0x00, 0xE0, 0x1C, 0x07, 0x00, 0xE0, 0x1C, 0x07, 0x00, 0xE0, 0x00,
  0x03, 0xC0, 0x0E, 0x70, 0x1E, 0x78, 0x3C, 0x3C, 0x3C, 0x3C, 0x7C, 0x3E,
  0x7C, 0x3E, 0x7C, 0x3E, 0xFC, 0x3F, 0xFC, 0x3F, 0xFC, 0x3F, 0xFC, 0x3F,
  0xFC, 0x3F, 0xFC, 0x3F, 0xFC, 0x3F, 0xFC, 0x3F, 0xFC, 0x3E, 0x7C, 0x3E,
  0x7C, 0x3E, 0x3C, 0x3C, 0x3C, 0x3C, 0x1E, 0x78, 0x0E, 0x70, 0x03, 0xC0,
  0x00, 0xC0, 0x3C, 0x0F, 0xC3, 0xFC, 0x4F, 0xC0, 0xFC, 0x0F, 0xC0, 0xFC,
  0x0F, 0xC0, 0xFC, 0x0F, 0xC0, 0xFC, 0x0F, 0xC0, 0xFC, 0x0F, 0xC0, 0xFC,
  0x0F, 0xC0, 0xFC, 0x0F, 0xC0, 0xFC, 0x0F, 0xC0, 0xFC, 0x1F, 0xEF, 0xFF,
  0x03, 0xE0, 0x0F, 0xF8, 0x1F, 0xFC, 0x3F, 0xFC, 0x30, 0xFE, 0x60, 0x7E,
  0x40, 0x3E, 0x00, 0x3E, 0x00, 0x3E, 0x00, 0x3C, 0x00, 0x3C, 0x00, 0x78,
  0x00, 0x70, 0x00, 0xE0, 0x00, 0xC0, 0x01, 0x80, 0x03, 0x00, 0x06, 0x01,
  0x0C, 0x03, 0x1F, 0xFF, 0x1F, 0xFF, 0x3F, 0xFE, 0x7F, 0xFE, 0xFF, 0xFE,
  0x03, 0xF0, 0x0F, 0xF8, 0x3F, 0xFC, 0x21, 0xFE, 0x40, 0xFE, 0x00, 0x7E,
  0x00, 0x7E, 0x00, 0x7C, 0x00, 0x78, 0x00, 0xF0, 0x01, 0xFC, 0x03, 0xFE,
  0x00, 0x7E, 0x00, 0x3F, 0x00, 0x1F, 0x00, 0x0F, 0x00, 0x0F, 0x00, 0x0F,
  0x00, 0x0E, 0x70, 0x0E, 0xFC, 0x1C, 0xFE, 0x38, 0x7F, 0xE0, 0x3F, 0x80,
  0x00, 0x38, 0x00, 0xF0, 0x03, 0xE0, 0x07, 0xC0, 0x1F, 0x80, 0x5F, 0x00,
  0xBE, 0x02, 0x7C, 0x08, 0xF8, 0x31, 0xF0, 0x43, 0xE1, 0x07, 0xC4, 0x0F,
  0x88, 0x1F, 0x20, 0x3E, 0x7F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8,
  0x07, 0xC0, 0x0F, 0x80, 0x1F, 0x00, 0x3E, 0x00, 0x7C, 0x0F, 0xFE, 0x1F,
  0xF8, 0x7F, 0xF0, 0xFF, 0xE1, 0x80, 0x03, 0x00, 0x0C, 0x00, 0x18, 0x00,
  0x3F, 0x80, 0xFF, 0xC1, 0xFF, 0xC3, 0xFF, 0xC3, 0xFF, 0x80, 0x3F, 0x80,
  0x0F, 0x00, 0x0E, 0x00, 0x1C, 0x00, 0x18, 0x00, 0x37, 0x80, 0x4F, 0x81,
  0x9F, 0xC6, 0x3F, 0xF8, 0x1F, 0x80, 0x00, 0x07, 0x00, 0x7C, 0x01, 0xF0,
  0x03, 0xC0, 0x0F, 0x80, 0x1F, 0x00, 0x1F, 0x00, 0x3E, 0x00, 0x7E, 0x00,
  0x7F, 0xF0, 0x7F, 0xFC, 0xFC, 0x7E, 0xFC, 0x7E, 0xFC, 0x3F, 0xFC, 0x3F,
  0xFC, 0x3F, 0xFC, 0x3F, 0xFC, 0x3F, 0x7C, 0x3F, 0x7C, 0x3E, 0x3C, 0x3E,
  0x3E, 0x3C, 0x1E, 0x78, 0x07, 0xE0, 0x7F, 0xFF, 0x7F, 0xFE, 0x7F, 0xFE,
  0xFF, 0xFE, 0xFF, 0xFC, 0xC0, 0x1C, 0x80, 0x18, 0x80, 0x38, 0x00, 0x38,
  0x00, 0x70, 0x00, 0x70, 0x00, 0x70, 0x00, 0xE0, 0x00, 0xE0, 0x00, 0xE0,
  0x01, 0xC0, 0x01, 0xC0, 0x01, 0xC0, 0x03, 0x80, 0x03, 0x80, 0x07, 0x80,
  0x07, 0x00, 0x07, 0x00, 0x0F, 0x00, 0x0F, 0xE0, 0x38, 0x78, 0x70, 0x3C,
  0xF0, 0x1E, 0xF0, 0x1E, 0xF8, 0x1E, 0xF8, 0x1E, 0xFE, 0x3C, 0x7F, 0xB0,
  0x7F, 0xE0, 0x3F, 0xF0, 0x0F, 0xF8, 0x1F, 0xFC, 0x39, 0xFE, 0x70, 0xFF,
  0xF0, 0x3F, 0xF0, 0x3F, 0xF0, 0x1F, 0xF0, 0x1F, 0xF0, 0x1E, 0x78, 0x3E,
  0x7C, 0x7C, 0x3F, 0xF8, 0x0F, 0xE0, 0x07, 0xE0, 0x1E, 0x78, 0x3C, 0x7C,
  0x7C, 0x3C, 0x7C, 0x3E, 0xFC, 0x3E, 0xFC, 0x3F, 0xFC, 0x3F, 0xFC, 0x3F,
  0xFC, 0x3F, 0xFC, 0x3F, 0x7E, 0x3F, 0x7E, 0x3F, 0x3F, 0xFE, 0x0F, 0xFE,
  0x00, 0x7E, 0x00, 0x7C, 0x00, 0xF8, 0x00, 0xF8, 0x01, 0xF0, 0x03, 0xC0,
  0x0F, 0x80, 0x3E, 0x00, 0xE0, 0x00, 0x7B, 0xFF, 0xFF, 0xFD, 0xE0, 0x00,
  0x00, 0x07, 0xBF, 0xFF, 0xFF, 0xDE, 0x39, 0xFB, 0xF7, 0xEF, 0xC7, 0x00,
  0x00, 0x00, 0x01, 0xE7, 0xEF, 0xFF, 0xFF, 0xBF, 0x06, 0x08, 0x30, 0xC2,
  0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0C, 0x00, 0x0F, 0x80, 0x07, 0xF0,
  0x03, 0xFC, 0x01, 0xFE, 0x00, 0xFE, 0x00, 0x7F, 0x00, 0x3F, 0x80, 0x1F,
  0xC0, 0x03, 0xF8, 0x00, 0x1F, 0xC0, 0x00, 0xFE, 0x00, 0x07, 0xF0, 0x00,
  0x3F, 0x80, 0x01, 0xFE, 0x00, 0x0F, 0xE0, 0x00, 0x7C, 0x00, 0x01, 0x80,
  0x00, 0x00, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x80, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1F,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF0, 0x00, 0x00, 0x18, 0x00, 0x03,
  0xE0, 0x00, 0x7F, 0x00, 0x07, 0xF8, 0x00, 0x1F, 0xC0, 0x00, 0xFE, 0x00,
  0x07, 0xF0, 0x00, 0x3F, 0x80, 0x01, 0xFC, 0x00, 0x3F, 0x80, 0x1F, 0xC0,
  0x0F, 0xE0, 0x07, 0xF0, 0x07, 0xF8, 0x03, 0xFC, 0x00, 0xFE, 0x00, 0x1F,
  0x00, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0F, 0xC0, 0xFF, 0xC7, 0x1F,
  0xB8, 0x3E, 0xF0, 0xFF, 0xC3, 0xFF, 0x0F, 0xD8, 0x3F, 0x00, 0xF8, 0x07,
  0xC0, 0x1E, 0x00, 0x60, 0x03, 0x00, 0x08, 0x00, 0x20, 0x00, 0x80, 0x00,
  0x00, 0x00, 0x00, 0x70, 0x03, 0xE0, 0x1F, 0x80, 0x7E, 0x01, 0xF8, 0x01,
  0xC0, 0x00, 0x7F, 0x00, 0x01, 0xFF, 0xE0, 0x07, 0xC0, 0xF0, 0x0F, 0x00,
  0x38, 0x1E, 0x00, 0x0C, 0x3C, 0x07, 0x06, 0x38, 0x1F, 0x72, 0x78, 0x3C,
  0xF3, 0x78, 0x78, 0xE1, 0xF0, 0x70, 0xE1, 0xF0, 0xF0, 0xE1, 0xF0, 0xE0,
  0xC1, 0xF1, 0xE1, 0xC1, 0xF1, 0xC1, 0xC1, 0xF1, 0xC3, 0x82, 0xF1, 0xC3,
  0x86, 0x71, 0xC7, 0x8C, 0x79, 0xFB, 0xF8, 0x78, 0xF1, 0xF0, 0x3C, 0x00,
  0x00, 0x1E, 0x00, 0x00, 0x0F, 0x00, 0x00, 0x07, 0xC0, 0x78, 0x03, 0xFF,
  0xE0, 0x00, 0x7F, 0x80, 0x00, 0x10, 0x00, 0x00, 0x38, 0x00, 0x00, 0x38,
  0x00, 0x00, 0x78, 0x00, 0x00, 0x7C, 0x00, 0x00, 0x7C, 0x00, 0x00, 0xFE,
  0x00, 0x00, 0xFE, 0x00, 0x01, 0xBF, 0x00, 0x01, 0xBF, 0x00, 0x01, 0x1F,
  0x00, 0x03, 0x1F, 0x80, 0x02, 0x1F, 0x80, 0x06, 0x0F, 0xC0, 0x06, 0x0F,
  0xC0, 0x04, 0x07, 0xE0, 0x0F, 0xFF, 0xE0, 0x0F, 0xFF, 0xE0, 0x18, 0x03,
  0xF0, 0x18, 0x03, 0xF0, 0x30, 0x01, 0xF8, 0x30, 0x01, 0xF8, 0x70, 0x01,
  0xFC, 0xFE, 0x0F, 0xFF, 0xFF, 0xFE, 0x07, 0xFF, 0xFE, 0x0F, 0xE1, 0xF8,
  0x3F, 0x07, 0xC1, 0xF8, 0x3F, 0x0F, 0xC1, 0xF8, 0x7E, 0x0F, 0xC3, 0xF0,
  0x7E, 0x1F, 0x87, 0xE0, 0xFC, 0x7C, 0x07, 0xFF, 0x00, 0x3F, 0xFF, 0x01,
  0xF8, 0xFE, 0x0F, 0xC1, 0xF8, 0x7E, 0x0F, 0xC3, 0xF0, 0x3F, 0x1F, 0x81,
  0xF8, 0xFC, 0x0F, 0xC7, 0xE0, 0x7E, 0x3F, 0x03, 0xF1, 0xF8, 0x3F, 0x0F,
  0xC3, 0xF0, 0xFF, 0xFF, 0x1F, 0xFF, 0xC0, 0x00, 0x7E, 0x04, 0x07, 0xFF,
  0x18, 0x1F, 0x07, 0xF0, 0x7C, 0x03, 0xE1, 0xF0, 0x03, 0xC7, 0xC0, 0x03,
  0x9F, 0x80, 0x03, 0x3F, 0x00, 0x06, 0x7C, 0x00, 0x05, 0xF8, 0x00, 0x03,
  0xF0, 0x00, 0x07, 0xE0, 0x00, 0x0F, 0xC0, 0x00, 0x1F, 0x80, 0x00, 0x3F,
  0x00, 0x00, 0x7E, 0x00, 0x00, 0xFC, 0x00, 0x00, 0xFC, 0x00, 0x01, 0xF8,
  0x00, 0x01, 0xF0, 0x00, 0x23, 0xF0, 0x00, 0xC3, 0xF0, 0x07, 0x03, 0xF0,
  0x3C, 0x01, 0xFF, 0xE0, 0x00, 0xFF, 0x00, 0xFF, 0xFE, 0x00, 0x7F, 0xFF,
  0x00, 0x7E, 0x1F, 0x80, 0xFC, 0x1F, 0x81, 0xF8, 0x1F, 0x83, 0xF0, 0x1F,
  0x07, 0xE0, 0x3F, 0x0F, 0xC0, 0x7E, 0x1F, 0x80, 0x7E, 0x3F, 0x00, 0xFC,
  0x7E, 0x01, 0xF8, 0xFC, 0x03, 0xF1, 0xF8, 0x07, 0xE3, 0xF0, 0x0F, 0xC7,
  0xE0, 0x1F, 0x8F, 0xC0, 0x3F, 0x1F, 0x80, 0x7C, 0x3F, 0x01, 0xF8, 0x7E,
  0x03, 0xE0, 0xFC, 0x0F, 0x81, 0xF8, 0x1F, 0x03, 0xF0, 0xFC, 0x0F, 0xFF,
  0xE0, 0x7F, 0xFF, 0x00, 0xFF, 0xFF, 0xE3, 0xFF, 0xFF, 0x0F, 0xC0, 0x78,
  0x7E, 0x01, 0xC3, 0xF0, 0x06, 0x1F, 0x80, 0x10, 0xFC, 0x10, 0x87, 0xE0,
  0x80, 0x3F, 0x0C, 0x01, 0xF8, 0xE0, 0x0F, 0xFF, 0x00, 0x7F, 0xF8, 0x03,
  0xF1, 0xC0, 0x1F, 0x86, 0x00, 0xFC, 0x10, 0x07, 0xE0, 0x80, 0x3F, 0x00,
  0x09, 0xF8, 0x00, 0xCF, 0xC0, 0x0C, 0x7E, 0x00, 0x63, 0xF0, 0x0F, 0x1F,
  0x81, 0xFB, 0xFF, 0xFF, 0xDF, 0xFF, 0xFC, 0xFF, 0xFF, 0xEF, 0xFF, 0xFC,
  0xFC, 0x0F, 0x9F, 0x80, 0x73, 0xF0, 0x06, 0x7E, 0x00, 0x4F, 0xC1, 0x09,
  0xF8, 0x20, 0x3F, 0x0C, 0x07, 0xE3, 0x80, 0xFF, 0xF0, 0x1F, 0xFE, 0x03,
  0xF1, 0xC0, 0x7E, 0x18, 0x0F, 0xC1, 0x01, 0xF8, 0x20, 0x3F, 0x00, 0x07,
  0xE0, 0x00, 0xFC, 0x00, 0x1F, 0x80, 0x03, 0xF0, 0x00, 0x7E, 0x00, 0x1F,
  0xE0, 0x07, 0xFF, 0x00, 0x00, 0x7E, 0x02, 0x01, 0xFF, 0xE3, 0x01, 0xF0,
  0x3F, 0x81, 0xF0, 0x07, 0xC1, 0xF0, 0x01, 0xE1, 0xF0, 0x00, 0x71, 0xF8,
  0x00, 0x18, 0xFC, 0x00, 0x0C, 0x7C, 0x00, 0x02, 0x7E, 0x00, 0x00, 0x3F,
  0x00, 0x00, 0x1F, 0x80, 0x00, 0x0F, 0xC0, 0x00, 0x07, 0xE0, 0x00, 0x03,
  0xF0, 0x0F, 0xFF, 0xF8, 0x01, 0xFE, 0x7C, 0x00, 0x7E, 0x3F, 0x00, 0x3F,
  0x1F, 0x80, 0x1F, 0x87, 0xC0, 0x0F, 0xC1, 0xF0, 0x07, 0xE0, 0xFC, 0x03,
  0xF0, 0x1F, 0x83, 0xF0, 0x07, 0xFF, 0xE0, 0x00, 0x7F, 0x80, 0x00, 0xFF,
  0xC3, 0xFF, 0x7F, 0x81, 0xFE, 0x3F, 0x00, 0xFC, 0x3F, 0x00, 0xFC, 0x3F,
  0x00, 0xFC, 0x3F, 0x00, 0xFC, 0x3F, 0x00, 0xFC, 0x3F, 0x00, 0xFC, 0x3F,
  0x00, 0xFC, 0x3F, 0x00, 0xFC, 0x3F, 0xFF, 0xFC, 0x3F, 0xFF, 0xFC, 0x3F,
  0x00, 0xFC, 0x3F, 0x00, 0xFC, 0x3F, 0x00, 0xFC, 0x3F, 0x00, 0xFC, 0x3F,
  0x00, 0xFC, 0x3F, 0x00, 0xFC, 0x3F, 0x00, 0xFC, 0x3F, 0x00, 0xFC, 0x3F,
  0x00, 0xFC, 0x3F, 0x00, 0xFC, 0x7F, 0x81, 0xFE, 0xFF, 0xC3, 0xFF, 0xFF,
  0xEF, 0xF0, 0xFC, 0x1F, 0x83, 0xF0, 0x7E, 0x0F, 0xC1, 0xF8, 0x3F, 0x07,
  0xE0, 0xFC, 0x1F, 0x83, 0xF0, 0x7E, 0x0F, 0xC1, 0xF8, 0x3F, 0x07, 0xE0,
  0xFC, 0x1F, 0x83, 0xF0, 0x7E, 0x1F, 0xE7, 0xFF, 0x07, 0xFF, 0x01, 0xFE,
  0x00, 0xFC, 0x00, 0xFC, 0x00, 0xFC, 0x00, 0xFC, 0x00, 0xFC, 0x00, 0xFC,
  0x00, 0xFC, 0x00, 0xFC, 0x00, 0xFC, 0x00, 0xFC, 0x00, 0xFC, 0x00, 0xFC,
  0x00, 0xFC, 0x00, 0xFC, 0x00, 0xFC, 0x00, 0xFC, 0x00, 0xFC, 0x00, 0xFC,
  0x70, 0xFC, 0xF8, 0xFC, 0xF8, 0xF8, 0xF0, 0xF8, 0x71, 0xF0, 0x7F, 0xE0,
  0x1F, 0x80, 0xFF, 0xC3, 0xFF, 0x3F, 0xC0, 0x3E, 0x0F, 0xC0, 0x1C, 0x07,
  0xE0, 0x18, 0x03, 0xF0, 0x18, 0x01, 0xF8, 0x18, 0x00, 0xFC, 0x18, 0x00,
  0x7E, 0x18, 0x00, 0x3F, 0x18, 0x00, 0x1F, 0x9C, 0x00, 0x0F, 0xDF, 0x00,
  0x07, 0xFF, 0xC0, 0x03, 0xFF, 0xF0, 0x01, 0xF9, 0xF8, 0x00, 0xFC, 0xFE,
  0x00, 0x7E, 0x3F, 0x80, 0x3F, 0x0F, 0xE0, 0x1F, 0x83, 0xF8, 0x0F, 0xC0,
  0xFC, 0x07, 0xE0, 0x7F, 0x03, 0xF0, 0x1F, 0xC1, 0xF8, 0x07, 0xF1, 0xFE,
  0x03, 0xFD, 0xFF, 0x8F, 0xFF, 0xFF, 0xE0, 0x03, 0xFC, 0x00, 0x0F, 0xC0,
  0x00, 0x7E, 0x00, 0x03, 0xF0, 0x00, 0x1F, 0x80, 0x00, 0xFC, 0x00, 0x07,
  0xE0, 0x00, 0x3F, 0x00, 0x01, 0xF8, 0x00, 0x0F, 0xC0, 0x00, 0x7E, 0x00,
  0x03, 0xF0, 0x00, 0x1F, 0x80, 0x00, 0xFC, 0x00, 0x07, 0xE0, 0x01, 0x3F,
  0x00, 0x19, 0xF8, 0x00, 0xCF, 0xC0, 0x0C, 0x7E, 0x00, 0x63, 0xF0, 0x0F,
  0x1F, 0x81, 0xFB, 0xFF, 0xFF, 0xDF, 0xFF, 0xFE, 0xFF, 0x80, 0x03, 0xFE,
  0x7F, 0x00, 0x07, 0xF8, 0x7E, 0x00, 0x0F, 0xE0, 0xFE, 0x00, 0x3F, 0xC1,
  0x7C, 0x00, 0x5F, 0x82, 0xFC, 0x01, 0xBF, 0x05, 0xF8, 0x02, 0x7E, 0x09,
  0xF8, 0x0C, 0xFC, 0x13, 0xF0, 0x11, 0xF8, 0x23, 0xE0, 0x23, 0xF0, 0x47,
  0xE0, 0xC7, 0xE0, 0x87, 0xC1, 0x0F, 0xC1, 0x0F, 0xC6, 0x1F, 0x82, 0x0F,
  0x88, 0x3F, 0x04, 0x1F, 0xB0, 0x7E, 0x08, 0x3F, 0x60, 0xFC, 0x10, 0x3E,
  0x81, 0xF8, 0x20, 0x7F, 0x03, 0xF0, 0x40, 0x7C, 0x07, 0xE0, 0x80, 0xF8,
  0x0F, 0xC1, 0x00, 0xE0, 0x1F, 0x82, 0x01, 0xC0, 0x3F, 0x0E, 0x03, 0x80,
  0xFF, 0x7F, 0x82, 0x03, 0xFF, 0xFE, 0x00, 0xFE, 0xFE, 0x00, 0x70, 0xFE,
  0x00, 0x40, 0xFE, 0x00, 0x81, 0xFC, 0x01, 0x03, 0xFC, 0x02, 0x05, 0xFC,
  0x04, 0x09, 0xFC, 0x08, 0x11, 0xFC, 0x10, 0x23, 0xF8, 0x20, 0x43, 0xF8,
  0x40, 0x83, 0xF8, 0x81, 0x03, 0xF9, 0x02, 0x03, 0xFA, 0x04, 0x03, 0xF4,
  0x08, 0x07, 0xF8, 0x10, 0x07, 0xF0, 0x20, 0x07, 0xE0, 0x40, 0x07, 0xC0,
  0x80, 0x07, 0x81, 0x00, 0x0F, 0x02, 0x00, 0x0E, 0x0E, 0x00, 0x0C, 0x7F,
  0x00, 0x08, 0x00, 0x7F, 0x00, 0x01, 0xFF, 0xF0, 0x01, 0xF0, 0x7C, 0x01,
  0xF0, 0x1F, 0x01, 0xF0, 0x07, 0xC1, 0xF0, 0x01, 0xF1, 0xF8, 0x00, 0xFC,
  0xFC, 0x00, 0x7E, 0x7C, 0x00, 0x1F, 0x7E, 0x00, 0x0F, 0xFF, 0x00, 0x07,
  0xFF, 0x80, 0x03, 0xFF, 0xC0, 0x01, 0xFF, 0xE0, 0x00, 0xFF, 0xF0, 0x00,
  0x7F, 0xF8, 0x00, 0x3F, 0x7C, 0x00, 0x1F, 0x3E, 0x00, 0x1F, 0x9F, 0x80,
  0x0F, 0xC7, 0xC0, 0x07, 0xC1, 0xF0, 0x07, 0xC0, 0xFC, 0x07, 0xE0, 0x3F,
  0x07, 0xC0, 0x07, 0xFF, 0xC0, 0x00, 0x7F, 0x00, 0x00, 0xFF, 0xFC, 0x0F,
  0xFF, 0xE0, 0xFC, 0x7E, 0x1F, 0x87, 0xE3, 0xF0, 0x7E, 0x7E, 0x0F, 0xCF,
  0xC1, 0xF9, 0xF8, 0x3F, 0x3F, 0x07, 0xE7, 0xE0, 0xFC, 0xFC, 0x3F, 0x1F,
  0x8F, 0xC3, 0xFF, 0xF0, 0x7F, 0xF8, 0x0F, 0xC0, 0x01, 0xF8, 0x00, 0x3F,
  0x00, 0x07, 0xE0, 0x00, 0xFC, 0x00, 0x1F, 0x80, 0x03, 0xF0, 0x00, 0x7E,
  0x00, 0x1F, 0xE0, 0x07, 0xFE, 0x00, 0x00, 0x7F, 0x00, 0x01, 0xFF, 0xF0,
  0x01, 0xF0, 0x7C, 0x01, 0xF0, 0x1F, 0x01, 0xF0, 0x07, 0xC1, 0xF0, 0x01,
  0xF1, 0xF8, 0x00, 0xFC, 0xFC, 0x00, 0x7E, 0x7C, 0x00, 0x1F, 0x7E, 0x00,
  0x0F, 0xFF, 0x00, 0x07, 0xFF, 0x80, 0x03, 0xFF, 0xC0, 0x01, 0xFF, 0xE0,
  0x00, 0xFF, 0xF0, 0x00, 0x7F, 0xF8, 0x00, 0x3F, 0x7C, 0x00, 0x1F, 0x3E,
  0x00, 0x0F, 0x9F, 0x80, 0x0F, 0xC7, 0xC0, 0x07, 0xC1, 0xF0, 0x07, 0xC0,
  0x78, 0x03, 0xC0, 0x1E, 0x07, 0xC0, 0x03, 0xFF, 0x80, 0x00, 0x7F, 0x00,
  0x00, 0x3F, 0xC0, 0x00, 0x0F, 0xF0, 0x00, 0x03, 0xFE, 0x00, 0x00, 0xFF,
  0xF8, 0x00, 0x0F, 0xE0, 0xFF, 0xFE, 0x00, 0xFF, 0xFF, 0x00, 0xFC, 0x3F,
  0x01, 0xF8, 0x3F, 0x03, 0xF0, 0x3F, 0x07, 0xE0, 0x7E, 0x0F, 0xC0, 0xFC,
  0x1F, 0x81, 0xF8, 0x3F, 0x03, 0xF0, 0x7E, 0x07, 0xC0, 0xFC, 0x1F, 0x81,
  0xF8, 0x7E, 0x03, 0xFF, 0xF0, 0x07, 0xFF, 0xC0, 0x0F, 0xDF, 0xC0, 0x1F,
  0x9F, 0x80, 0x3F, 0x1F, 0x80, 0x7E, 0x3F, 0x80, 0xFC, 0x3F, 0x81, 0xF8,
  0x3F, 0x03, 0xF0, 0x7F, 0x07, 0xE0, 0x7F, 0x1F, 0xE0, 0x7F, 0x7F, 0xE0,
  0xFF, 0x07, 0xC2, 0x1F, 0xF2, 0x3C, 0x3E, 0x70, 0x0E, 0xF0, 0x06, 0xF0,
  0x06, 0xF0, 0x02, 0xF8, 0x00, 0xFE, 0x00, 0xFF, 0x80, 0x7F, 0xE0, 0x3F,
  0xF8, 0x1F, 0xFC, 0x0F, 0xFE, 0x03, 0xFE, 0x00, 0xFF, 0x00, 0x3F, 0x80,
  0x1F, 0xC0, 0x0F, 0xC0, 0x0F, 0xE0, 0x0E, 0xF0, 0x1E, 0xF8, 0x3C, 0x9F,
  0xF8, 0x87, 0xE0, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFC, 0x7E, 0x3F, 0x83,
  0xF0, 0x7C, 0x1F, 0x81, 0xC0, 0xFC, 0x06, 0x07, 0xE0, 0x20, 0x3F, 0x00,
  0x01, 0xF8, 0x00, 0x0F, 0xC0, 0x00, 0x7E, 0x00, 0x03, 0xF0, 0x00, 0x1F,
  0x80, 0x00, 0xFC, 0x00, 0x07, 0xE0, 0x00, 0x3F, 0x00, 0x01, 0xF8, 0x00,
  0x0F, 0xC0, 0x00, 0x7E, 0x00, 0x03, 0xF0, 0x00, 0x1F, 0x80, 0x00, 0xFC,
  0x00, 0x0F, 0xF0, 0x01, 0xFF, 0xE0, 0xFF, 0xC1, 0xFD, 0xFE, 0x01, 0xC3,
  0xF0, 0x02, 0x0F, 0xC0, 0x08, 0x3F, 0x00, 0x20, 0xFC, 0x00, 0x83, 0xF0,
  0x02, 0x0F, 0xC0, 0x08, 0x3F, 0x00, 0x20, 0xFC, 0x00, 0x83, 0xF0, 0x02,
  0x0F, 0xC0, 0x08, 0x3F, 0x00, 0x20, 0xFC, 0x00, 0x83, 0xF0, 0x02, 0x0F,
  0xC0, 0x08, 0x3F, 0x00, 0x20, 0xFC, 0x00, 0x83, 0xF0, 0x02, 0x0F, 0xC0,
  0x18, 0x1F, 0x80, 0x40, 0x7E, 0x03, 0x00, 0xFC, 0x18, 0x01, 0xFF, 0xC0,
  0x00, 0xFC, 0x00, 0xFF, 0xF0, 0x7F, 0x3F, 0xC0, 0x1E, 0x1F, 0x80, 0x0C,
  0x1F, 0x80, 0x08, 0x0F, 0xC0, 0x18, 0x0F, 0xC0, 0x18, 0x07, 0xE0, 0x10,
  0x07, 0xE0, 0x30, 0x07, 0xE0, 0x20, 0x03, 0xF0, 0x60, 0x03, 0xF0, 0x60,
  0x01, 0xF8, 0x40, 0x01, 0xF8, 0xC0, 0x00, 0xF8, 0x80, 0x00, 0xFC, 0x80,
  0x00, 0xFD, 0x80, 0x00, 0x7F, 0x00, 0x00, 0x7F, 0x00, 0x00, 0x3E, 0x00,
  0x00, 0x3E, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x1C, 0x00, 0x00, 0x1C, 0x00,
  0x00, 0x0C, 0x00, 0xFF, 0xE7, 0xFF, 0x0F, 0xCF, 0xE0, 0x7F, 0x00, 0xE1,
  0xF8, 0x0F, 0xC0, 0x30, 0x7E, 0x03, 0xF0, 0x0C, 0x1F, 0x80, 0x7C, 0x02,
  0x03, 0xE0, 0x1F, 0x81, 0x80, 0xFC, 0x07, 0xE0, 0x60, 0x3F, 0x03, 0xF8,
  0x10, 0x07, 0xC0, 0xBF, 0x0C, 0x01, 0xF8, 0x2F, 0xC3, 0x00, 0x7E, 0x19,
  0xF0, 0x80, 0x0F, 0x84, 0x7C, 0x60, 0x03, 0xF3, 0x0F, 0x98, 0x00, 0xFC,
  0xC3, 0xE4, 0x00, 0x1F, 0x20, 0xFB, 0x00, 0x07, 0xF8, 0x1F, 0xC0, 0x00,
  0xFC, 0x07, 0xE0, 0x00, 0x3F, 0x01, 0xF8, 0x00, 0x0F, 0xC0, 0x3E, 0x00,
  0x01, 0xE0, 0x0F, 0x00, 0x00, 0x78, 0x03, 0xC0, 0x00, 0x1C, 0x00, 0x70,
  0x00, 0x03, 0x00, 0x18, 0x00, 0x00, 0xC0, 0x06, 0x00, 0x00, 0x20, 0x00,
  0x80, 0x00, 0xFF, 0xF3, 0xFE, 0x7F, 0x80, 0x78, 0x3F, 0x80, 0x70, 0x1F,
  0xC0, 0x60, 0x0F, 0xC0, 0xC0, 0x0F, 0xE1, 0x80, 0x07, 0xF1, 0x00, 0x03,
  0xF3, 0x00, 0x03, 0xFE, 0x00, 0x01, 0xFC, 0x00, 0x00, 0xFC, 0x00, 0x00,
  0xFE, 0x00, 0x00, 0x7E, 0x00, 0x00, 0x7F, 0x00, 0x00, 0xFF, 0x80, 0x00,
  0x9F, 0x80, 0x01, 0x8F, 0xC0, 0x03, 0x0F, 0xE0, 0x06, 0x07, 0xE0, 0x06,
  0x07, 0xF0, 0x0C, 0x03, 0xF8, 0x1C, 0x03, 0xF8, 0x3C, 0x03, 0xFC, 0xFF,
  0x0F, 0xFF, 0xFF, 0xF0, 0xFF, 0x7F, 0x80, 0x1E, 0x3F, 0x80, 0x1C, 0x1F,
  0x80, 0x18, 0x1F, 0xC0, 0x10, 0x0F, 0xC0, 0x30, 0x07, 0xE0, 0x20, 0x07,
  0xE0, 0x60, 0x03, 0xF0, 0xC0, 0x03, 0xF0, 0x80, 0x01, 0xF9, 0x80, 0x01,
  0xFF, 0x00, 0x00, 0xFF, 0x00, 0x00, 0xFE, 0x00, 0x00, 0x7E, 0x00, 0x00,
  0x7E, 0x00, 0x00, 0x7E, 0x00, 0x00, 0x7E, 0x00, 0x00, 0x7E, 0x00, 0x00,
  0x7E, 0x00, 0x00, 0x7E, 0x00, 0x00, 0x7E, 0x00, 0x00, 0xFF, 0x00, 0x01,
  0xFF, 0x80, 0x7F, 0xFF, 0xF3, 0xFF, 0xFF, 0x9F, 0x01, 0xF8, 0xE0, 0x1F,
  0x86, 0x01, 0xFC, 0x20, 0x0F, 0xC1, 0x00, 0xFC, 0x00, 0x07, 0xE0, 0x00,
  0x7E, 0x00, 0x07, 0xE0, 0x00, 0x3F, 0x00, 0x03, 0xF0, 0x00, 0x3F, 0x80,
  0x01, 0xF8, 0x00, 0x1F, 0x80, 0x01, 0xFC, 0x01, 0x0F, 0xC0, 0x18, 0xFC,
  0x00, 0xC7, 0xE0, 0x06, 0x7E, 0x00, 0x77, 0xF0, 0x07, 0x3F, 0x00, 0xFB,
  0xFF, 0xFF, 0xDF, 0xFF, 0xFE, 0xFF, 0xFF, 0xF0, 0xF0, 0xF0, 0xF0, 0xF0,
  0xF0, 0xF0, 0xF0, 0xF0, 0xF0, 0xF0, 0xF0, 0xF0, 0xF0, 0xF0, 0xF0, 0xF0,
  0xF0, 0xF0, 0xF0, 0xF0, 0xF0, 0xF0, 0xF0, 0xF0, 0xFF, 0xFF, 0xE0, 0x1E,
  0x01, 0xC0, 0x38, 0x07, 0x80, 0x70, 0x0E, 0x01, 0xC0, 0x1C, 0x03, 0x80,
  0x70, 0x07, 0x00, 0xE0, 0x1C, 0x01, 0xC0, 0x38, 0x07, 0x00, 0x70, 0x0E,
  0x01, 0xC0, 0x1C, 0x03, 0x80, 0x70, 0x0F, 0x00, 0xE0, 0xFF, 0xFF, 0x0F,
  0x0F, 0x0F, 0x0F, 0x0F, 0x0F, 0x0F, 0x0F, 0x0F, 0x0F, 0x0F, 0x0F, 0x0F,
  0x0F, 0x0F, 0x0F, 0x0F, 0x0F, 0x0F, 0x0F, 0x0F, 0x0F, 0x0F, 0x0F, 0x0F,
  0xFF, 0xFF, 0x03, 0x80, 0x0F, 0x00, 0x1F, 0x00, 0x7E, 0x00, 0xEE, 0x03,
  0x9C, 0x07, 0x1C, 0x1C, 0x38, 0x38, 0x38, 0xE0, 0x71, 0xC0, 0x77, 0x00,
  0xEE, 0x00, 0xE0, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFC, 0xE0, 0xF0,
  0x78, 0x3C, 0x0E, 0x07, 0x0F, 0xE0, 0x3F, 0xF0, 0x78, 0xF8, 0x78, 0x7C,
  0x78, 0x7C, 0x38, 0x7C, 0x00, 0x7C, 0x03, 0xFC, 0x1E, 0x7C, 0x7C, 0x7C,
  0xFC, 0x7C, 0xFC, 0x7C, 0xFC, 0xFC, 0xFF, 0xFD, 0x7F, 0x7F, 0x3C, 0x3C,
  0xFC, 0x00, 0x1F, 0x00, 0x07, 0xC0, 0x01, 0xF0, 0x00, 0x7C, 0x00, 0x1F,
  0x00, 0x07, 0xC0, 0x01, 0xF0, 0x00, 0x7C, 0xF8, 0x1F, 0x7F, 0x87, 0xE3,
  0xF1, 0xF0, 0x7E, 0x7C, 0x0F, 0x9F, 0x03, 0xF7, 0xC0, 0xFD, 0xF0, 0x3F,
  0x7C, 0x0F, 0xDF, 0x03, 0xF7, 0xC0, 0xFD, 0xF0, 0x3E, 0x7C, 0x1F, 0x1F,
  0x8F, 0xC6, 0x7F, 0xC1, 0x07, 0xC0, 0x07, 0xC0, 0x7F, 0xC3, 0xC7, 0x9F,
  0x1E, 0x78, 0x7B, 0xE1, 0xCF, 0x80, 0x3E, 0x00, 0xF8, 0x03, 0xE0, 0x0F,
  0x80, 0x3F, 0x00, 0x7C, 0x00, 0xFC, 0x61, 0xFF, 0x03, 0xF0, 0x00, 0x7F,
  0x00, 0x07, 0xC0, 0x01, 0xF0, 0x00, 0x7C, 0x00, 0x1F, 0x00, 0x07, 0xC0,
  0x01, 0xF0, 0x00, 0x7C, 0x07, 0x9F, 0x07, 0xF7, 0xC3, 0xE3, 0xF1, 0xF8,
  0x7C, 0x7C, 0x1F, 0x3F, 0x07, 0xCF, 0xC1, 0xF3, 0xF0, 0x7C, 0xFC, 0x1F,
  0x3F, 0x07, 0xCF, 0xC1, 0xF1, 0xF0, 0x7C, 0x7E, 0x1F, 0x0F, 0x8F, 0xC1,
  0xFD, 0xFC, 0x3E, 0x70, 0x0F, 0xC0, 0x7F, 0xC3, 0xC7, 0x1E, 0x1E, 0xF8,
  0x7B, 0xFF, 0xFF, 0xFF, 0xFE, 0x00, 0xF8, 0x03, 0xE0, 0x0F, 0xC0, 0x1F,
  0x03, 0x7E, 0x18, 0xFF, 0xC1, 0xFE, 0x03, 0xF0, 0x0F, 0x83, 0xF8, 0xF3,
  0xBE, 0xF7, 0xDC, 0xF8, 0x1F, 0x03, 0xE0, 0xFF, 0x1F, 0xE1, 0xF0, 0x3E,
  0x07, 0xC0, 0xF8, 0x1F, 0x03, 0xE0, 0x7C, 0x0F, 0x81, 0xF0, 0x3E, 0x07,
  0xC0, 0xF8, 0x1F, 0x07, 0xF8, 0x0F, 0xC0, 0x1F, 0xFF, 0xDF, 0x1F, 0xFF,
  0x07, 0x8F, 0x83, 0xE7, 0xC1, 0xF3, 0xE0, 0xF9, 0xF0, 0x7C, 0x78, 0x3C,
  0x1E, 0x3E, 0x03, 0xFC, 0x03, 0x00, 0x07, 0x00, 0x07, 0x80, 0x03, 0xFF,
  0xF1, 0xFF, 0xFE, 0x7F, 0xFF, 0x8F, 0xFF, 0xF8, 0x01, 0xFC, 0x00, 0x7F,
  0x00, 0x73, 0xFF, 0xF0, 0x7F, 0xC0, 0xFC, 0x00, 0x3E, 0x00, 0x1F, 0x00,
  0x0F, 0x80, 0x07, 0xC0, 0x03, 0xE0, 0x01, 0xF0, 0x00, 0xF8, 0x00, 0x7C,
  0x7C, 0x3E, 0xFF, 0x1F, 0xCF, 0xCF, 0x83, 0xE7, 0xC1, 0xF3, 0xE0, 0xF9,
  0xF0, 0x7C, 0xF8, 0x3E, 0x7C, 0x1F, 0x3E, 0x0F, 0x9F, 0x07, 0xCF, 0x83,
  0xE7, 0xC1, 0xF3, 0xE0, 0xF9, 0xF0, 0x7D, 0xFC, 0x7F, 0x39, 0xFB, 0xF7,
  0xE7, 0x80, 0x00, 0x00, 0xFC, 0xF9, 0xF3, 0xE7, 0xCF, 0x9F, 0x3E, 0x7C,
  0xF9, 0xF3, 0xE7, 0xCF, 0x9F, 0x7F, 0x03, 0xC0, 0xFC, 0x1F, 0x83, 0xF0,
  0x3C, 0x00, 0x00, 0x00, 0x00, 0x0F, 0xE0, 0x7C, 0x0F, 0x81, 0xF0, 0x3E,
  0x07, 0xC0, 0xF8, 0x1F, 0x03, 0xE0, 0x7C, 0x0F, 0x81, 0xF0, 0x3E, 0x07,
  0xC0, 0xF8, 0x1F, 0x03, 0xE0, 0x7D, 0xCF, 0xF9, 0xEE, 0x7C, 0xFF, 0x0F,
  0x80, 0xFC, 0x00, 0x1F, 0x00, 0x07, 0xC0, 0x01, 0xF0, 0x00, 0x7C, 0x00,
  0x1F, 0x00, 0x07, 0xC0, 0x01, 0xF0, 0x00, 0x7C, 0x7F, 0x9F, 0x07, 0x87,
  0xC1, 0x81, 0xF0, 0xC0, 0x7C, 0x60, 0x1F, 0x30, 0x07, 0xDE, 0x01, 0xFF,
  0xC0, 0x7F, 0xF0, 0x1F, 0x3E, 0x07, 0xCF, 0xC1, 0xF1, 0xF8, 0x7C, 0x3E,
  0x1F, 0x07, 0xC7, 0xC1, 0xFB, 0xF9, 0xFF, 0xFC, 0xF9, 0xF3, 0xE7, 0xCF,
  0x9F, 0x3E, 0x7C, 0xF9, 0xF3, 0xE7, 0xCF, 0x9F, 0x3E, 0x7C, 0xF9, 0xF3,
  0xE7, 0xCF, 0x9F, 0x7F, 0xFC, 0x7C, 0x1F, 0x0F, 0xBF, 0xCF, 0xF1, 0xF8,
  0xFF, 0x3F, 0x3E, 0x0F, 0x83, 0xE7, 0xC1, 0xF0, 0x7C, 0xF8, 0x3E, 0x0F,
  0x9F, 0x07, 0xC1, 0xF3, 0xE0, 0xF8, 0x3E, 0x7C, 0x1F, 0x07, 0xCF, 0x83,
  0xE0, 0xF9, 0xF0, 0x7C, 0x1F, 0x3E, 0x0F, 0x83, 0xE7, 0xC1, 0xF0, 0x7C,
  0xF8, 0x3E, 0x0F, 0x9F, 0x07, 0xC1, 0xF7, 0xF1, 0xFC, 0x7F, 0xFC, 0x7C,
  0x3E, 0xFF, 0x1F, 0xCF, 0xCF, 0x83, 0xE7, 0xC1, 0xF3, 0xE0, 0xF9, 0xF0,
  0x7C, 0xF8, 0x3E, 0x7C, 0x1F, 0x3E, 0x0F, 0x9F, 0x07, 0xCF, 0x83, 0xE7,
  0xC1, 0xF3, 0xE0, 0xF9, 0xF0, 0x7D, 0xFC, 0x7F, 0x07, 0xF0, 0x0F, 0xFE,
  0x0F, 0x8F, 0x8F, 0x87, 0xE7, 0xC1, 0xF7, 0xE0, 0xFF, 0xF0, 0x7F, 0xF8,
  0x3F, 0xFC, 0x1F, 0xFE, 0x0F, 0xFF, 0x07, 0xEF, 0x83, 0xE7, 0xC1, 0xF1,
  0xF1, 0xF0, 0x7F, 0xF0, 0x0F, 0xE0, 0xFE, 0x7C, 0x07, 0xDF, 0xE0, 0xFE,
  0x3E, 0x1F, 0x07, 0xE3, 0xE0, 0x7C, 0x7C, 0x0F, 0xCF, 0x81, 0xF9, 0xF0,
  0x3F, 0x3E, 0x07, 0xE7, 0xC0, 0xFC, 0xF8, 0x1F, 0x9F, 0x03, 0xE3, 0xE0,
  0xFC, 0x7E, 0x3F, 0x0F, 0xBF, 0xC1, 0xF3, 0xE0, 0x3E, 0x00, 0x07, 0xC0,
  0x00, 0xF8, 0x00, 0x1F, 0x00, 0x03, 0xE0, 0x00, 0x7E, 0x00, 0x1F, 0xE0,
  0x00, 0x07, 0xC1, 0x0F, 0xF9, 0x8F, 0xCD, 0xCF, 0xC3, 0xE7, 0xC1, 0xF7,
  0xE0, 0xFB, 0xF0, 0x7D, 0xF8, 0x3E, 0xFC, 0x1F, 0x7E, 0x0F, 0xBF, 0x07,
  0xDF, 0x83, 0xE7, 0xE1, 0xF1, 0xF1, 0xF8, 0x7F, 0x7C, 0x1F, 0x3E, 0x00,
  0x1F, 0x00, 0x0F, 0x80, 0x07, 0xC0, 0x03, 0xE0, 0x01, 0xF0, 0x01, 0xF8,
  0x01, 0xFE, 0xFC, 0x73, 0xEF, 0xDF, 0xFE, 0xFC, 0xF7, 0xC3, 0xBE, 0x01,
  0xF0, 0x0F, 0x80, 0x7C, 0x03, 0xE0, 0x1F, 0x00, 0xF8, 0x07, 0xC0, 0x3E,
  0x01, 0xF0, 0x1F, 0xE0, 0x1E, 0x23, 0xFE, 0x70, 0xEE, 0x06, 0xE0, 0x2F,
  0x80, 0xFF, 0x07, 0xFC, 0x3F, 0xE0, 0xFF, 0x81, 0xF8, 0x07, 0xC0, 0x7E,
  0x0E, 0xBF, 0xC8, 0xF8, 0x04, 0x03, 0x01, 0xC0, 0xF0, 0x7C, 0x3F, 0xEF,
  0xF9, 0xF0, 0x7C, 0x1F, 0x07, 0xC1, 0xF0, 0x7C, 0x1F, 0x07, 0xC1, 0xF0,
  0x7C, 0x5F, 0x37, 0xF8, 0xFE, 0x1E, 0x00, 0xFC, 0x7F, 0x1F, 0x07, 0xC7,
  0xC1, 0xF1, 0xF0, 0x7C, 0x7C, 0x1F, 0x1F, 0x07, 0xC7, 0xC1, 0xF1, 0xF0,
  0x7C, 0x7C, 0x1F, 0x1F, 0x07, 0xC7, 0xC1, 0xF1, 0xF0, 0x7C, 0x7C, 0x1F,
  0x1F, 0x8F, 0xC3, 0xFD, 0xFC, 0x7C, 0x60, 0xFF, 0x9F, 0xBF, 0x83, 0x0F,
  0x81, 0x87, 0xE0, 0x81, 0xF0, 0x40, 0xF8, 0x40, 0x3E, 0x20, 0x1F, 0x30,
  0x07, 0xD0, 0x03, 0xF8, 0x00, 0xF8, 0x00, 0x7C, 0x00, 0x3C, 0x00, 0x0E,
  0x00, 0x07, 0x00, 0x01, 0x00, 0xFF, 0x3F, 0xCF, 0x7E, 0x1F, 0x06, 0x3E,
  0x0F, 0x06, 0x3E, 0x0F, 0x84, 0x1F, 0x0F, 0x8C, 0x1F, 0x1F, 0x88, 0x0F,
  0x17, 0xC8, 0x0F, 0x97, 0xD8, 0x0F, 0xB3, 0xD0, 0x07, 0xE3, 0xF0, 0x07,
  0xE3, 0xE0, 0x03, 0xC1, 0xE0, 0x03, 0xC1, 0xE0, 0x03, 0x81, 0xC0, 0x01,
  0x80, 0xC0, 0x01, 0x80, 0x80, 0xFF, 0x3F, 0x7E, 0x0C, 0x3E, 0x08, 0x3F,
  0x18, 0x1F, 0x30, 0x0F, 0xE0, 0x0F, 0xC0, 0x07, 0xE0, 0x03, 0xE0, 0x03,
  0xF0, 0x05, 0xF8, 0x0C, 0xF8, 0x18, 0xFC, 0x30, 0x7E, 0x70, 0x7E, 0xFC,
  0xFF, 0xFF, 0x3F, 0x7E, 0x0C, 0x7C, 0x0C, 0x3E, 0x08, 0x3E, 0x08, 0x1E,
  0x18, 0x1F, 0x10, 0x0F, 0x30, 0x0F, 0xA0, 0x0F, 0xA0, 0x07, 0xE0, 0x07,
  0xC0, 0x03, 0xC0, 0x03, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0x00, 0x01,
  0x00, 0x61, 0x00, 0xF2, 0x00, 0xF6, 0x00, 0xFC, 0x00, 0x78, 0x00, 0x7F,
  0xFD, 0xFF, 0xF7, 0x0F, 0xD0, 0x3E, 0x01, 0xF0, 0x0F, 0xC0, 0x3E, 0x01,
  0xF0, 0x0F, 0xC0, 0x3E, 0x01, 0xF8, 0x0F, 0xC1, 0x3E, 0x05, 0xF8, 0x7F,
  0xFF, 0xFF, 0xFF, 0x01, 0xE0, 0xF8, 0x3E, 0x07, 0x80, 0xF0, 0x1E, 0x03,
  0xC0, 0x78, 0x0F, 0x01, 0xE0, 0x3C, 0x07, 0x80, 0xF0, 0x1E, 0x07, 0x87,
  0x80, 0x1E, 0x01, 0xE0, 0x3C, 0x07, 0x80, 0xF0, 0x1E, 0x03, 0xC0, 0x78,
  0x0F, 0x01, 0xE0, 0x3C, 0x07, 0x80, 0xF8, 0x0F, 0x80, 0x78, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xE0, 0xF0, 0x0F, 0x80, 0xF0,
  0x0F, 0x01, 0xE0, 0x3C, 0x07, 0x80, 0xF0, 0x1E, 0x03, 0xC0, 0x78, 0x0F,
  0x01, 0xE0, 0x3C, 0x03, 0xC0, 0x0F, 0x0F, 0x03, 0xC0, 0x78, 0x0F, 0x01,
  0xE0, 0x3C, 0x07, 0x80, 0xF0, 0x1E, 0x03, 0xC0, 0x78, 0x0F, 0x03, 0xE0,
  0xF8, 0x3C, 0x00, 0x3E, 0x00, 0x7F, 0xC6, 0xFF, 0xFF, 0x61, 0xFE, 0x00,
  0x7C };

const GFXglyph FreeSerifBold18pt7bGlyphs[] PROGMEM = {
  {     0,   0,   0,   9,    0,    1 },   // 0x20 ' '
  {     0,   6,  24,  12,    3,  -23 },   // 0x21 '!'
  {    18,  13,  10,  19,    3,  -23 },   // 0x22 '"'
  {    35,  18,  24,  17,    0,  -23 },   // 0x23 '#'
  {    89,  15,  28,  17,    1,  -25 },   // 0x24 '$'
  {   142,  27,  24,  35,    4,  -23 },   // 0x25 '%'
  {   223,  26,  25,  29,    2,  -23 },   // 0x26 '&'
  {   305,   4,  10,  10,    3,  -23 },   // 0x27 '''
  {   310,   9,  30,  12,    2,  -23 },   // 0x28 '('
  {   344,   9,  30,  12,    1,  -23 },   // 0x29 ')'
  {   378,  14,  15,  18,    2,  -23 },   // 0x2A '*'
  {   405,  19,  19,  24,    2,  -17 },   // 0x2B '+'
  {   451,   6,  12,   9,    1,   -5 },   // 0x2C ','
  {   460,   8,   4,  12,    2,   -9 },   // 0x2D '-'
  {   464,   6,   6,   9,    1,   -5 },   // 0x2E '.'
  {   469,  11,  25,  10,   -1,  -23 },   // 0x2F '/'
  {   504,  16,  24,  18,    1,  -23 },   // 0x30 '0'
  {   552,  12,  24,  18,    3,  -23 },   // 0x31 '1'
  {   588,  16,  24,  17,    1,  -23 },   // 0x32 '2'
  {   636,  16,  24,  18,    0,  -23 },   // 0x33 '3'
  {   684,  15,  24,  18,    1,  -23 },   // 0x34 '4'
  {   729,  15,  24,  18,    1,  -23 },   // 0x35 '5'
  {   774,  16,  24,  18,    1,  -23 },   // 0x36 '6'
  {   822,  16,  24,  17,    1,  -23 },   // 0x37 '7'
  {   870,  16,  24,  17,    1,  -23 },   // 0x38 '8'
  {   918,  16,  24,  18,    1,  -23 },   // 0x39 '9'
  {   966,   6,  16,  12,    3,  -15 },   // 0x3A ':'
  {   978,   7,  22,  12,    2,  -15 },   // 0x3B ';'
  {   998,  19,  20,  24,    2,  -18 },   // 0x3C '<'
  {  1046,  19,  12,  24,    2,  -14 },   // 0x3D '='
  {  1075,  19,  20,  24,    3,  -18 },   // 0x3E '>'
  {  1123,  14,  24,  18,    2,  -23 },   // 0x3F '?'
  {  1165,  24,  25,  33,    4,  -23 },   // 0x40 '@'
  {  1240,  24,  24,  25,    1,  -23 },   // 0x41 'A'
  {  1312,  21,  24,  23,    1,  -23 },   // 0x42 'B'
  {  1375,  23,  25,  25,    1,  -23 },   // 0x43 'C'
  {  1447,  23,  24,  26,    1,  -23 },   // 0x44 'D'
  {  1516,  21,  24,  23,    2,  -23 },   // 0x45 'E'
  {  1579,  19,  24,  22,    2,  -23 },   // 0x46 'F'
  {  1636,  25,  25,  27,    1,  -23 },   // 0x47 'G'
  {  1715,  24,  24,  27,    2,  -23 },   // 0x48 'H'
  {  1787,  11,  24,  14,    2,  -23 },   // 0x49 'I'
  {  1820,  16,  27,  18,    0,  -23 },   // 0x4A 'J'
  {  1874,  25,  24,  27,    2,  -23 },   // 0x4B 'K'
  {  1949,  21,  24,  23,    2,  -23 },   // 0x4C 'L'
  {  2012,  31,  24,  33,    1,  -23 },   // 0x4D 'M'
  {  2105,  23,  24,  25,    1,  -23 },   // 0x4E 'N'
  {  2174,  25,  25,  27,    1,  -23 },   // 0x4F 'O'
  {  2253,  19,  24,  22,    2,  -23 },   // 0x50 'P'
  {  2310,  25,  30,  27,    1,  -23 },   // 0x51 'Q'
  {  2404,  23,  24,  25,    2,  -23 },   // 0x52 'R'
  {  2473,  16,  25,  20,    2,  -23 },   // 0x53 'S'
  {  2523,  21,  24,  23,    1,  -23 },   // 0x54 'T'
  {  2586,  22,  25,  25,    2,  -23 },   // 0x55 'U'
  {  2655,  24,  24,  25,    0,  -23 },   // 0x56 'V'
  {  2727,  34,  25,  34,    0,  -23 },   // 0x57 'W'
  {  2834,  24,  24,  25,    1,  -23 },   // 0x58 'X'
  {  2906,  24,  24,  25,    1,  -23 },   // 0x59 'Y'
  {  2978,  21,  24,  23,    1,  -23 },   // 0x5A 'Z'
  {  3041,   8,  29,  12,    2,  -23 },   // 0x5B '['
  {  3070,  11,  25,  10,   -1,  -23 },   // 0x5C '\'
  {  3105,   8,  29,  12,    2,  -23 },   // 0x5D ']'
  {  3134,  15,  13,  20,    3,  -23 },   // 0x5E '^'
  {  3159,  18,   3,  17,    0,    3 },   // 0x5F '_'
  {  3166,   8,   6,  12,    0,  -23 },   // 0x60 '`'
  {  3172,  16,  16,  18,    1,  -15 },   // 0x61 'a'
  {  3204,  18,  24,  19,    1,  -23 },   // 0x62 'b'
  {  3258,  14,  16,  15,    1,  -15 },   // 0x63 'c'
  {  3286,  18,  24,  19,    1,  -23 },   // 0x64 'd'
  {  3340,  14,  16,  16,    1,  -15 },   // 0x65 'e'
  {  3368,  11,  24,  14,    2,  -23 },   // 0x66 'f'
  {  3401,  17,  23,  17,    1,  -15 },   // 0x67 'g'
  {  3450,  17,  24,  19,    1,  -23 },   // 0x68 'h'
  {  3501,   7,  24,  10,    2,  -23 },   // 0x69 'i'
  {  3522,  11,  31,  14,    0,  -23 },   // 0x6A 'j'
  {  3565,  18,  24,  19,    1,  -23 },   // 0x6B 'k'
  {  3619,   7,  24,  10,    1,  -23 },   // 0x6C 'l'
  {  3640,  27,  16,  29,    1,  -15 },   // 0x6D 'm'
  {  3694,  17,  16,  19,    1,  -15 },   // 0x6E 'n'
  {  3728,  17,  16,  18,    1,  -15 },   // 0x6F 'o'
  {  3762,  19,  23,  19,    0,  -15 },   // 0x70 'p'
  {  3817,  17,  23,  19,    1,  -15 },   // 0x71 'q'
  {  3866,  13,  16,  15,    1,  -15 },   // 0x72 'r'
  {  3892,  12,  16,  14,    1,  -15 },   // 0x73 's'
  {  3916,  10,  21,  12,    1,  -20 },   // 0x74 't'
  {  3943,  18,  16,  20,    1,  -15 },   // 0x75 'u'
  {  3979,  17,  16,  17,    0,  -15 },   // 0x76 'v'
  {  4013,  24,  16,  25,    0,  -15 },   // 0x77 'w'
  {  4061,  16,  16,  18,    1,  -15 },   // 0x78 'x'
  {  4093,  16,  23,  17,    0,  -15 },   // 0x79 'y'
  {  4139,  14,  16,  16,    0,  -15 },   // 0x7A 'z'
  {  4167,  11,  31,  14,    1,  -24 },   // 0x7B '{'
  {  4210,   3,  25,   8,    2,  -23 },   // 0x7C '|'
  {  4220,  11,  31,  14,    3,  -24 },   // 0x7D '}'
  {  4263,  16,   5,  18,    1,  -11 } }; // 0x7E '~'

const GFXfont FreeSerifBold18pt7b PROGMEM = {
  (uint8_t  *)FreeSerifBold18pt7bBitmaps,
  (GFXglyph *)FreeSerifBold18pt7bGlyphs,
  0x20, 0x7E, 42 };

// Approx. 4945 bytes
