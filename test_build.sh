#!/bin/bash

# Test build script for M5Dial GUI Demo with ESP-IDF v5.5.0 compatibility fixes

echo "=== M5Dial GUI Demo Build Test ==="
echo "Target: ESP32-S3 (M5Dial)"
echo "ESP-IDF: v5.5.0"
echo ""

echo "Setting up ESP-IDF environment..."
export IDF_PATH=/c/Users/<USER>/esp/v5.5/esp-idf
export PATH=$IDF_PATH/tools:$PATH

echo "ESP-IDF Path: $IDF_PATH"

# Check if ESP-IDF Python environment exists
if [ -f "/c/Users/<USER>/.espressif/python_env/idf5.5_py3.11_env/Scripts/python.exe" ]; then
    echo "Using ESP-IDF Python environment..."
    PYTHON_CMD="/c/Users/<USER>/.espressif/python_env/idf5.5_py3.11_env/Scripts/python.exe"
else
    echo "Using system Python..."
    PYTHON_CMD="python3"
fi

# Try to run idf.py
echo "Testing idf.py..."
$PYTHON_CMD $IDF_PATH/tools/idf.py --version

echo ""
echo "Attempting to build project..."
echo "Note: This may take several minutes on first build..."
$PYTHON_CMD $IDF_PATH/tools/idf.py build
