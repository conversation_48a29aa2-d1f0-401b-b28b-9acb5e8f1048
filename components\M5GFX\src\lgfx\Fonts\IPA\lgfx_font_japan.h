#ifndef LGFX_FONT_JAPAN_H_
#define LGFX_FONT_JAPAN_H_

#include <stdint.h>

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

extern const uint8_t lgfx_font_japan_mincho_8[];
extern const uint8_t lgfx_font_japan_mincho_12[];
extern const uint8_t lgfx_font_japan_mincho_16[];
extern const uint8_t lgfx_font_japan_mincho_20[];
extern const uint8_t lgfx_font_japan_mincho_24[];
extern const uint8_t lgfx_font_japan_mincho_28[];
extern const uint8_t lgfx_font_japan_mincho_32[];
extern const uint8_t lgfx_font_japan_mincho_36[];
extern const uint8_t lgfx_font_japan_mincho_40[];
extern const uint8_t lgfx_font_japan_mincho_p_8[];
extern const uint8_t lgfx_font_japan_mincho_p_12[];
extern const uint8_t lgfx_font_japan_mincho_p_16[];
extern const uint8_t lgfx_font_japan_mincho_p_20[];
extern const uint8_t lgfx_font_japan_mincho_p_24[];
extern const uint8_t lgfx_font_japan_mincho_p_28[];
extern const uint8_t lgfx_font_japan_mincho_p_32[];
extern const uint8_t lgfx_font_japan_mincho_p_36[];
extern const uint8_t lgfx_font_japan_mincho_p_40[];
extern const uint8_t lgfx_font_japan_gothic_8[];
extern const uint8_t lgfx_font_japan_gothic_12[];
extern const uint8_t lgfx_font_japan_gothic_16[];
extern const uint8_t lgfx_font_japan_gothic_20[];
extern const uint8_t lgfx_font_japan_gothic_24[];
extern const uint8_t lgfx_font_japan_gothic_28[];
extern const uint8_t lgfx_font_japan_gothic_32[];
extern const uint8_t lgfx_font_japan_gothic_36[];
extern const uint8_t lgfx_font_japan_gothic_40[];
extern const uint8_t lgfx_font_japan_gothic_p_8[];
extern const uint8_t lgfx_font_japan_gothic_p_12[];
extern const uint8_t lgfx_font_japan_gothic_p_16[];
extern const uint8_t lgfx_font_japan_gothic_p_20[];
extern const uint8_t lgfx_font_japan_gothic_p_24[];
extern const uint8_t lgfx_font_japan_gothic_p_28[];
extern const uint8_t lgfx_font_japan_gothic_p_32[];
extern const uint8_t lgfx_font_japan_gothic_p_36[];
extern const uint8_t lgfx_font_japan_gothic_p_40[];

#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif
