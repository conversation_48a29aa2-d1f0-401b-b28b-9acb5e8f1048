const uint8_t FreeMonoBoldOblique24pt7bBitmaps[] PROGMEM = {
  0x01, 0xE0, 0x3F, 0x07, 0xF0, 0xFF, 0x0F, 0xF0, 0xFF, 0x0F, 0xE0, 0xFE,
  0x0F, 0xE0, 0xFE, 0x0F, 0xC0, 0xFC, 0x1F, 0xC1, 0xF8, 0x1F, 0x81, 0xF8,
  0x1F, 0x81, 0xF0, 0x1F, 0x01, 0xF0, 0x1E, 0x00, 0x80, 0x00, 0x00, 0x00,
  0x00, 0x03, 0xC0, 0x7E, 0x0F, 0xE0, 0xFE, 0x0F, 0xC0, 0x78, 0x00, 0x7E,
  0x1F, 0xBF, 0x0F, 0xDF, 0x87, 0xCF, 0x83, 0xE7, 0xC1, 0xF3, 0xE0, 0xF1,
  0xE0, 0xF8, 0xF0, 0x7C, 0x78, 0x3C, 0x38, 0x1E, 0x1C, 0x0F, 0x0E, 0x07,
  0x0E, 0x03, 0x83, 0x01, 0x80, 0x00, 0x1C, 0x1C, 0x00, 0x3E, 0x3E, 0x00,
  0x3E, 0x3E, 0x00, 0x3C, 0x3C, 0x00, 0x7C, 0x7C, 0x00, 0x7C, 0x7C, 0x00,
  0x7C, 0x7C, 0x00, 0xF8, 0xF8, 0x00, 0xF8, 0xF8, 0x00, 0xF8, 0xF8, 0x0F,
  0xFF, 0xFF, 0x1F, 0xFF, 0xFF, 0x1F, 0xFF, 0xFF, 0x1F, 0xFF, 0xFF, 0x1F,
  0xFF, 0xFE, 0x03, 0xE3, 0xE0, 0x03, 0xE3, 0xE0, 0x03, 0xC3, 0xC0, 0x07,
  0xC7, 0xC0, 0x7F, 0xFF, 0xF8, 0xFF, 0xFF, 0xFC, 0xFF, 0xFF, 0xFC, 0xFF,
  0xFF, 0xF8, 0xFF, 0xFF, 0xF0, 0x0F, 0x0F, 0x00, 0x1F, 0x1F, 0x00, 0x1F,
  0x1F, 0x00, 0x1F, 0x1F, 0x00, 0x3E, 0x1E, 0x00, 0x3E, 0x3E, 0x00, 0x3E,
  0x3E, 0x00, 0x3C, 0x3C, 0x00, 0x7C, 0x7C, 0x00, 0x38, 0x38, 0x00, 0x00,
  0x00, 0xE0, 0x00, 0x00, 0xF0, 0x00, 0x00, 0xF8, 0x00, 0x00, 0x7C, 0x00,
  0x00, 0xFF, 0x00, 0x01, 0xFF, 0xFC, 0x03, 0xFF, 0xFE, 0x03, 0xFF, 0xFF,
  0x01, 0xFF, 0xFF, 0x81, 0xFC, 0x1F, 0xC1, 0xF8, 0x03, 0xC0, 0xF8, 0x01,
  0xE0, 0x7C, 0x00, 0x40, 0x3F, 0x00, 0x00, 0x1F, 0xF0, 0x00, 0x0F, 0xFF,
  0x80, 0x03, 0xFF, 0xF8, 0x00, 0xFF, 0xFE, 0x00, 0x0F, 0xFF, 0x00, 0x00,
  0x7F, 0xC0, 0x00, 0x07, 0xE0, 0xE0, 0x01, 0xF0, 0xF0, 0x00, 0xF8, 0xF8,
  0x00, 0xFC, 0x7E, 0x00, 0xFC, 0x3F, 0x81, 0xFE, 0x1F, 0xFF, 0xFE, 0x0F,
  0xFF, 0xFE, 0x0F, 0xFF, 0xFE, 0x03, 0xFF, 0xFC, 0x00, 0x07, 0xF0, 0x00,
  0x01, 0xF0, 0x00, 0x00, 0xF0, 0x00, 0x00, 0x78, 0x00, 0x00, 0x7C, 0x00,
  0x00, 0x3E, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x0E, 0x00, 0x00, 0x00, 0xF8,
  0x00, 0x0F, 0xF8, 0x00, 0x7F, 0xE0, 0x03, 0xC3, 0xC0, 0x0E, 0x07, 0x00,
  0x70, 0x1C, 0x01, 0xC0, 0x70, 0x07, 0x01, 0xC0, 0x1C, 0x0E, 0x00, 0x78,
  0x78, 0x00, 0xFF, 0xC0, 0x03, 0xFE, 0x1F, 0x03, 0xE3, 0xFC, 0x00, 0x7F,
  0xC0, 0x0F, 0xF8, 0x03, 0xFF, 0x00, 0x7F, 0xC0, 0x03, 0xF8, 0x7C, 0x0F,
  0x07, 0xFC, 0x00, 0x3F, 0xF0, 0x01, 0xE1, 0xE0, 0x07, 0x03, 0x80, 0x38,
  0x0E, 0x00, 0xE0, 0x38, 0x03, 0x80, 0xE0, 0x0E, 0x07, 0x00, 0x3C, 0x3C,
  0x00, 0x7F, 0xE0, 0x01, 0xFF, 0x00, 0x01, 0xF0, 0x00, 0x00, 0x1F, 0x00,
  0x01, 0xFF, 0x80, 0x3F, 0xFC, 0x03, 0xFF, 0xE0, 0x1F, 0xFE, 0x01, 0xF1,
  0xE0, 0x1F, 0x04, 0x00, 0xF8, 0x00, 0x07, 0xC0, 0x00, 0x3E, 0x00, 0x01,
  0xF8, 0x00, 0x0F, 0xC0, 0x00, 0x3F, 0x00, 0x07, 0xF8, 0x00, 0x7F, 0xE3,
  0xE7, 0xFF, 0x3F, 0x7E, 0xFF, 0xFB, 0xE7, 0xFF, 0x9E, 0x1F, 0xF1, 0xF0,
  0xFF, 0x8F, 0x83, 0xF8, 0x7C, 0x1F, 0xC3, 0xF0, 0xFF, 0x9F, 0xFF, 0xFC,
  0x7F, 0xFF, 0xE3, 0xFF, 0xFF, 0x0F, 0xFD, 0xF0, 0x1F, 0x80, 0x00, 0x7E,
  0xFD, 0xF3, 0xE7, 0xCF, 0x3E, 0x7C, 0xF1, 0xE3, 0xC7, 0x0E, 0x18, 0x00,
  0x00, 0x18, 0x00, 0xF0, 0x07, 0xC0, 0x3F, 0x01, 0xF8, 0x07, 0xC0, 0x3E,
  0x01, 0xF8, 0x07, 0xC0, 0x3E, 0x00, 0xF8, 0x07, 0xC0, 0x1F, 0x00, 0xF8,
  0x03, 0xE0, 0x1F, 0x00, 0x7C, 0x01, 0xF0, 0x07, 0xC0, 0x3E, 0x00, 0xF8,
  0x03, 0xE0, 0x0F, 0x80, 0x3E, 0x00, 0xF8, 0x03, 0xE0, 0x0F, 0x80, 0x3E,
  0x00, 0xFC, 0x01, 0xF0, 0x07, 0xC0, 0x1F, 0x80, 0x7E, 0x00, 0xFC, 0x03,
  0xF0, 0x07, 0xC0, 0x1E, 0x00, 0x00, 0xC0, 0x07, 0x80, 0x3F, 0x00, 0xFC,
  0x03, 0xF0, 0x07, 0xE0, 0x1F, 0x80, 0x3E, 0x00, 0xF8, 0x03, 0xF0, 0x07,
  0xC0, 0x1F, 0x00, 0x7C, 0x01, 0xF0, 0x07, 0xC0, 0x1F, 0x00, 0x7C, 0x01,
  0xF0, 0x07, 0xC0, 0x3E, 0x00, 0xF8, 0x03, 0xE0, 0x1F, 0x00, 0x7C, 0x01,
  0xF0, 0x0F, 0x80, 0x3E, 0x01, 0xF0, 0x0F, 0xC0, 0x3E, 0x01, 0xF0, 0x0F,
  0xC0, 0x3E, 0x01, 0xF0, 0x0F, 0x80, 0x3E, 0x00, 0xF0, 0x00, 0x00, 0x3C,
  0x00, 0x01, 0xE0, 0x00, 0x1F, 0x00, 0x00, 0xF8, 0x00, 0x07, 0xC0, 0x08,
  0x3C, 0x09, 0xF9, 0xE7, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFB, 0xFF, 0xFF,
  0x87, 0xFF, 0xE0, 0x07, 0xF8, 0x00, 0x7F, 0xC0, 0x07, 0xFF, 0x00, 0x7F,
  0xF8, 0x07, 0xE7, 0xE0, 0x3E, 0x3F, 0x01, 0xE0, 0xF8, 0x0E, 0x07, 0x80,
  0x00, 0x07, 0x00, 0x00, 0x0F, 0x80, 0x00, 0x0F, 0x00, 0x00, 0x0F, 0x00,
  0x00, 0x1F, 0x00, 0x00, 0x1F, 0x00, 0x00, 0x1F, 0x00, 0x00, 0x1E, 0x00,
  0x00, 0x3E, 0x00, 0x00, 0x3E, 0x00, 0x7F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFE, 0x00, 0x7C, 0x00,
  0x00, 0x78, 0x00, 0x00, 0x78, 0x00, 0x00, 0xF8, 0x00, 0x00, 0xF8, 0x00,
  0x00, 0xF8, 0x00, 0x00, 0xF0, 0x00, 0x00, 0xF0, 0x00, 0x01, 0xF0, 0x00,
  0x01, 0xF0, 0x00, 0x00, 0xE0, 0x00, 0x03, 0xF0, 0x7E, 0x07, 0xC0, 0xFC,
  0x0F, 0x81, 0xF0, 0x1E, 0x03, 0xE0, 0x3C, 0x07, 0x80, 0x78, 0x0F, 0x00,
  0xE0, 0x0C, 0x00, 0x7F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFE, 0x3C, 0xFF, 0xFF, 0xFF, 0xCF, 0x00,
  0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x0F, 0x00, 0x00, 0x01, 0xF0, 0x00,
  0x00, 0x3E, 0x00, 0x00, 0x03, 0xE0, 0x00, 0x00, 0x7C, 0x00, 0x00, 0x0F,
  0x80, 0x00, 0x00, 0xF8, 0x00, 0x00, 0x1F, 0x00, 0x00, 0x03, 0xE0, 0x00,
  0x00, 0x3E, 0x00, 0x00, 0x07, 0xC0, 0x00, 0x00, 0xF8, 0x00, 0x00, 0x1F,
  0x80, 0x00, 0x01, 0xF0, 0x00, 0x00, 0x3E, 0x00, 0x00, 0x07, 0xE0, 0x00,
  0x00, 0x7C, 0x00, 0x00, 0x0F, 0x80, 0x00, 0x01, 0xF8, 0x00, 0x00, 0x1F,
  0x00, 0x00, 0x03, 0xE0, 0x00, 0x00, 0x7C, 0x00, 0x00, 0x07, 0xC0, 0x00,
  0x00, 0xF8, 0x00, 0x00, 0x1F, 0x00, 0x00, 0x01, 0xF0, 0x00, 0x00, 0x3E,
  0x00, 0x00, 0x07, 0xC0, 0x00, 0x00, 0xFC, 0x00, 0x00, 0x0F, 0x80, 0x00,
  0x01, 0xF0, 0x00, 0x00, 0x3F, 0x00, 0x00, 0x03, 0xE0, 0x00, 0x00, 0x7C,
  0x00, 0x00, 0x07, 0xC0, 0x00, 0x00, 0xF8, 0x00, 0x00, 0x07, 0x00, 0x00,
  0x00, 0x00, 0x0F, 0xC0, 0x00, 0xFF, 0xE0, 0x03, 0xFF, 0xE0, 0x1F, 0xFF,
  0xE0, 0x7F, 0xFF, 0xC0, 0xFC, 0x1F, 0x83, 0xF0, 0x1F, 0x8F, 0xC0, 0x1F,
  0x1F, 0x00, 0x3E, 0x7C, 0x00, 0x7C, 0xF8, 0x00, 0xF9, 0xF0, 0x01, 0xF3,
  0xC0, 0x07, 0xCF, 0x80, 0x0F, 0x9F, 0x00, 0x1E, 0x3E, 0x00, 0x3C, 0x78,
  0x00, 0xF8, 0xF0, 0x01, 0xF3, 0xE0, 0x03, 0xE7, 0xC0, 0x07, 0x8F, 0x80,
  0x1F, 0x1F, 0x00, 0x3E, 0x3E, 0x00, 0xF8, 0x7C, 0x01, 0xF0, 0xFC, 0x07,
  0xC1, 0xFC, 0x3F, 0x81, 0xFF, 0xFE, 0x03, 0xFF, 0xF8, 0x03, 0xFF, 0xE0,
  0x03, 0xFF, 0x00, 0x01, 0xF8, 0x00, 0x00, 0x00, 0x07, 0xE0, 0x00, 0x7E,
  0x00, 0x0F, 0xF0, 0x01, 0xFF, 0x80, 0x1F, 0xFC, 0x03, 0xFB, 0xE0, 0x1F,
  0x9E, 0x00, 0xF1, 0xF0, 0x00, 0x0F, 0x80, 0x00, 0x7C, 0x00, 0x03, 0xE0,
  0x00, 0x1E, 0x00, 0x01, 0xF0, 0x00, 0x0F, 0x80, 0x00, 0x7C, 0x00, 0x03,
  0xC0, 0x00, 0x1E, 0x00, 0x01, 0xF0, 0x00, 0x0F, 0x80, 0x00, 0x7C, 0x00,
  0x03, 0xC0, 0x00, 0x3E, 0x00, 0x01, 0xF0, 0x00, 0x0F, 0x80, 0x00, 0x7C,
  0x01, 0xFF, 0xFF, 0x9F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x3F,
  0xFF, 0xF0, 0x00, 0x03, 0xF8, 0x00, 0x03, 0xFF, 0x80, 0x03, 0xFF, 0xF0,
  0x01, 0xFF, 0xFE, 0x00, 0xFF, 0xFF, 0x80, 0x7F, 0x07, 0xF0, 0x1F, 0x00,
  0xFC, 0x0F, 0x80, 0x1F, 0x03, 0xE0, 0x07, 0xC0, 0xF0, 0x01, 0xF0, 0x00,
  0x00, 0xF8, 0x00, 0x00, 0x3E, 0x00, 0x00, 0x1F, 0x00, 0x00, 0x1F, 0xC0,
  0x00, 0x0F, 0xE0, 0x00, 0x07, 0xF0, 0x00, 0x07, 0xF8, 0x00, 0x03, 0xF8,
  0x00, 0x03, 0xFC, 0x00, 0x01, 0xFE, 0x00, 0x01, 0xFE, 0x00, 0x00, 0xFF,
  0x00, 0x00, 0xFF, 0x00, 0x00, 0x7F, 0x80, 0x70, 0x3F, 0x80, 0x3E, 0x1F,
  0xFF, 0xFF, 0x07, 0xFF, 0xFF, 0xC1, 0xFF, 0xFF, 0xF0, 0xFF, 0xFF, 0xFC,
  0x3F, 0xFF, 0xFF, 0x00, 0x00, 0x07, 0xF8, 0x00, 0x0F, 0xFE, 0x00, 0x1F,
  0xFF, 0x80, 0x1F, 0xFF, 0xE0, 0x1F, 0xFF, 0xF8, 0x0F, 0x81, 0xFC, 0x07,
  0x00, 0x3E, 0x00, 0x00, 0x1F, 0x00, 0x00, 0x0F, 0x80, 0x00, 0x0F, 0xC0,
  0x00, 0x07, 0xC0, 0x00, 0x0F, 0xC0, 0x01, 0xFF, 0xC0, 0x01, 0xFF, 0xC0,
  0x00, 0xFF, 0x80, 0x00, 0x7F, 0xE0, 0x00, 0x1F, 0xF8, 0x00, 0x00, 0xFE,
  0x00, 0x00, 0x1F, 0x80, 0x00, 0x07, 0xC0, 0x00, 0x03, 0xE0, 0x00, 0x01,
  0xF0, 0x00, 0x00, 0xF8, 0x00, 0x00, 0xF8, 0x00, 0x00, 0xFC, 0x3C, 0x01,
  0xFC, 0x3F, 0xFF, 0xFC, 0x1F, 0xFF, 0xFC, 0x0F, 0xFF, 0xFC, 0x03, 0xFF,
  0xFC, 0x00, 0x3F, 0xF0, 0x00, 0x00, 0x00, 0xFC, 0x00, 0x07, 0xF0, 0x00,
  0x3F, 0xC0, 0x01, 0xFE, 0x00, 0x0F, 0xF8, 0x00, 0x7F, 0xE0, 0x03, 0xFF,
  0x80, 0x1F, 0xBE, 0x00, 0x7C, 0xF0, 0x03, 0xE7, 0xC0, 0x1F, 0x1F, 0x00,
  0xF8, 0x7C, 0x07, 0xE1, 0xE0, 0x3F, 0x07, 0x81, 0xF8, 0x3E, 0x07, 0xC0,
  0xF8, 0x3E, 0x03, 0xC1, 0xFF, 0xFF, 0xCF, 0xFF, 0xFF, 0xBF, 0xFF, 0xFE,
  0xFF, 0xFF, 0xF3, 0xFF, 0xFF, 0x80, 0x00, 0xF8, 0x00, 0x3F, 0xF8, 0x01,
  0xFF, 0xE0, 0x07, 0xFF, 0x80, 0x1F, 0xFE, 0x00, 0x7F, 0xF0, 0x01, 0xFF,
  0xFF, 0x00, 0xFF, 0xFF, 0x80, 0x7F, 0xFF, 0xC0, 0x3F, 0xFF, 0xE0, 0x3F,
  0xFF, 0xE0, 0x1F, 0x00, 0x00, 0x0F, 0x80, 0x00, 0x07, 0xC0, 0x00, 0x03,
  0xC0, 0x00, 0x03, 0xE0, 0x00, 0x01, 0xF7, 0xF0, 0x00, 0xFF, 0xFE, 0x00,
  0x7F, 0xFF, 0x80, 0x3F, 0xFF, 0xE0, 0x1F, 0xFF, 0xF0, 0x0F, 0x01, 0xFC,
  0x00, 0x00, 0x7E, 0x00, 0x00, 0x1F, 0x00, 0x00, 0x0F, 0x80, 0x00, 0x07,
  0xC0, 0x00, 0x03, 0xE0, 0x00, 0x01, 0xF0, 0x00, 0x01, 0xF0, 0x00, 0x00,
  0xF8, 0x00, 0x00, 0xF8, 0x3C, 0x03, 0xFC, 0x3F, 0xFF, 0xFC, 0x1F, 0xFF,
  0xFC, 0x0F, 0xFF, 0xFC, 0x03, 0xFF, 0xF8, 0x00, 0x3F, 0xE0, 0x00, 0x00,
  0x01, 0xFC, 0x00, 0x07, 0xFE, 0x00, 0x1F, 0xFF, 0x00, 0x7F, 0xFF, 0x00,
  0xFF, 0xFE, 0x01, 0xFE, 0x1C, 0x03, 0xF8, 0x00, 0x07, 0xE0, 0x00, 0x0F,
  0xC0, 0x00, 0x1F, 0x80, 0x00, 0x1F, 0x00, 0x00, 0x3E, 0x3E, 0x00, 0x3E,
  0xFF, 0x80, 0x7D, 0xFF, 0xC0, 0x7F, 0xFF, 0xE0, 0x7F, 0xFF, 0xE0, 0x7F,
  0x87, 0xF0, 0xFF, 0x03, 0xF0, 0xFC, 0x01, 0xF0, 0xF8, 0x01, 0xF0, 0xF8,
  0x01, 0xF0, 0xF8, 0x01, 0xF0, 0xF8, 0x03, 0xE0, 0xF8, 0x03, 0xE0, 0xFC,
  0x07, 0xC0, 0xFE, 0x0F, 0xC0, 0x7F, 0xFF, 0x80, 0x7F, 0xFF, 0x00, 0x3F,
  0xFE, 0x00, 0x1F, 0xFC, 0x00, 0x07, 0xF0, 0x00, 0x7F, 0xFF, 0xFD, 0xFF,
  0xFF, 0xE7, 0xFF, 0xFF, 0xBF, 0xFF, 0xFE, 0xFF, 0xFF, 0xFB, 0xE0, 0x07,
  0xCF, 0x00, 0x1F, 0x00, 0x00, 0xF8, 0x00, 0x03, 0xE0, 0x00, 0x1F, 0x00,
  0x00, 0x7C, 0x00, 0x03, 0xE0, 0x00, 0x0F, 0x80, 0x00, 0x7C, 0x00, 0x01,
  0xE0, 0x00, 0x0F, 0x80, 0x00, 0x7C, 0x00, 0x01, 0xF0, 0x00, 0x0F, 0x80,
  0x00, 0x3E, 0x00, 0x01, 0xF0, 0x00, 0x07, 0xC0, 0x00, 0x3E, 0x00, 0x00,
  0xF8, 0x00, 0x07, 0xC0, 0x00, 0x1F, 0x00, 0x00, 0xF8, 0x00, 0x03, 0xE0,
  0x00, 0x1F, 0x00, 0x00, 0x38, 0x00, 0x00, 0x00, 0x1F, 0xC0, 0x00, 0xFF,
  0xE0, 0x07, 0xFF, 0xE0, 0x1F, 0xFF, 0xE0, 0x7F, 0xFF, 0xC0, 0xFC, 0x1F,
  0xC3, 0xF0, 0x1F, 0x8F, 0xC0, 0x1F, 0x1F, 0x00, 0x3E, 0x3E, 0x00, 0x7C,
  0x7C, 0x01, 0xF0, 0xFC, 0x07, 0xE0, 0xFC, 0x1F, 0x81, 0xFF, 0xFE, 0x01,
  0xFF, 0xF0, 0x01, 0xFF, 0xE0, 0x0F, 0xFF, 0xE0, 0x3F, 0xFF, 0xE0, 0xFE,
  0x0F, 0xC3, 0xF0, 0x0F, 0xC7, 0xC0, 0x0F, 0x9F, 0x00, 0x1F, 0x3E, 0x00,
  0x3E, 0x7C, 0x00, 0xFC, 0xFC, 0x03, 0xF1, 0xFC, 0x1F, 0xE3, 0xFF, 0xFF,
  0x83, 0xFF, 0xFE, 0x03, 0xFF, 0xF8, 0x03, 0xFF, 0xC0, 0x01, 0xFC, 0x00,
  0x00, 0x00, 0x0F, 0xE0, 0x00, 0x3F, 0xF8, 0x00, 0xFF, 0xFC, 0x01, 0xFF,
  0xFE, 0x03, 0xFF, 0xFE, 0x03, 0xF0, 0x7F, 0x07, 0xE0, 0x3F, 0x07, 0xC0,
  0x1F, 0x0F, 0xC0, 0x1F, 0x0F, 0x80, 0x1F, 0x0F, 0x80, 0x1F, 0x0F, 0x80,
  0x3F, 0x0F, 0xC0, 0x7F, 0x0F, 0xE1, 0xFF, 0x07, 0xFF, 0xFE, 0x07, 0xFF,
  0xFE, 0x03, 0xFF, 0xBE, 0x01, 0xFF, 0x7C, 0x00, 0xFC, 0x7C, 0x00, 0x00,
  0xFC, 0x00, 0x01, 0xF8, 0x00, 0x01, 0xF8, 0x00, 0x03, 0xF0, 0x00, 0x0F,
  0xE0, 0x00, 0x1F, 0xC0, 0x38, 0x7F, 0x80, 0x7F, 0xFF, 0x00, 0xFF, 0xFE,
  0x00, 0xFF, 0xF8, 0x00, 0x7F, 0xE0, 0x00, 0x3F, 0x80, 0x00, 0x07, 0x83,
  0xF1, 0xFC, 0x7F, 0x1F, 0x83, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3C, 0x1F, 0x8F, 0xE3, 0xF8, 0xFC,
  0x1E, 0x00, 0x00, 0x3C, 0x00, 0xFC, 0x03, 0xF8, 0x07, 0xF0, 0x0F, 0xC0,
  0x0F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xF8, 0x03, 0xE0, 0x0F, 0xC0,
  0x1F, 0x00, 0x7C, 0x00, 0xF0, 0x03, 0xE0, 0x07, 0x80, 0x1E, 0x00, 0x38,
  0x00, 0xF0, 0x01, 0xC0, 0x07, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00,
  0x03, 0xC0, 0x00, 0x0F, 0xE0, 0x00, 0x1F, 0xF0, 0x00, 0x3F, 0xE0, 0x00,
  0x7F, 0xC0, 0x00, 0xFF, 0x80, 0x03, 0xFF, 0x00, 0x07, 0xFE, 0x00, 0x0F,
  0xFC, 0x00, 0x1F, 0xF0, 0x00, 0x1F, 0xFC, 0x00, 0x01, 0xFF, 0x00, 0x00,
  0x3F, 0xE0, 0x00, 0x0F, 0xFC, 0x00, 0x01, 0xFF, 0x00, 0x00, 0x3F, 0xE0,
  0x00, 0x07, 0xFC, 0x00, 0x01, 0xFF, 0x00, 0x00, 0x3F, 0x80, 0x00, 0x07,
  0x80, 0x1F, 0xFF, 0xFF, 0xCF, 0xFF, 0xFF, 0xF3, 0xFF, 0xFF, 0xFC, 0xFF,
  0xFF, 0xFF, 0x3F, 0xFF, 0xFF, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1F, 0xFF, 0xFF, 0xCF, 0xFF, 0xFF,
  0xF3, 0xFF, 0xFF, 0xFC, 0xFF, 0xFF, 0xFF, 0x3F, 0xFF, 0xFF, 0x80, 0x00,
  0x00, 0x00, 0x07, 0x80, 0x00, 0x07, 0xF0, 0x00, 0x03, 0xFC, 0x00, 0x00,
  0xFF, 0x80, 0x00, 0x1F, 0xF0, 0x00, 0x07, 0xFE, 0x00, 0x00, 0xFF, 0xC0,
  0x00, 0x1F, 0xF0, 0x00, 0x07, 0xFE, 0x00, 0x00, 0xFF, 0xC0, 0x00, 0x7F,
  0xE0, 0x00, 0xFF, 0xC0, 0x01, 0xFF, 0x80, 0x03, 0xFE, 0x00, 0x07, 0xFC,
  0x00, 0x1F, 0xF8, 0x00, 0x3F, 0xF0, 0x00, 0x3F, 0xC0, 0x00, 0x1F, 0x80,
  0x00, 0x0F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xFC, 0x01, 0xFF,
  0xE1, 0xFF, 0xFE, 0x3F, 0xFF, 0xE7, 0xFF, 0xFF, 0xF8, 0x1F, 0xFE, 0x00,
  0xFF, 0x80, 0x1F, 0xF0, 0x03, 0xE0, 0x00, 0x7C, 0x00, 0x1F, 0x00, 0x0F,
  0xE0, 0x07, 0xF8, 0x07, 0xFE, 0x01, 0xFF, 0x80, 0x7F, 0xC0, 0x0F, 0xE0,
  0x01, 0xF0, 0x00, 0x3C, 0x00, 0x03, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x00, 0x00, 0x00, 0xF0, 0x00, 0x3F, 0x00, 0x0F, 0xE0, 0x01, 0xFC, 0x00,
  0x3F, 0x00, 0x03, 0xC0, 0x00, 0x00, 0x0F, 0xE0, 0x00, 0x7F, 0xF0, 0x01,
  0xFF, 0xF0, 0x0F, 0xFF, 0xE0, 0x3F, 0x07, 0xE0, 0x7C, 0x07, 0xC1, 0xE0,
  0x07, 0x87, 0xC0, 0x0F, 0x0F, 0x00, 0x1C, 0x3C, 0x00, 0x78, 0x78, 0x07,
  0xF1, 0xE0, 0x3F, 0xE3, 0xC1, 0xFF, 0x87, 0x87, 0xFF, 0x0E, 0x1F, 0x9E,
  0x3C, 0x7C, 0x3C, 0x78, 0xF0, 0x78, 0xF3, 0xC0, 0xE1, 0xC7, 0x83, 0xC3,
  0x8F, 0x07, 0x8F, 0x1E, 0x0F, 0x1E, 0x3E, 0x1C, 0x3C, 0x7F, 0xFC, 0x78,
  0x7F, 0xFC, 0xF0, 0x7F, 0xF1, 0xE0, 0x3F, 0xE3, 0xC0, 0x00, 0x07, 0x80,
  0x00, 0x0F, 0x00, 0x00, 0x1F, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x3F, 0x01,
  0xC0, 0x7F, 0xFF, 0x80, 0x7F, 0xFE, 0x00, 0x7F, 0xF8, 0x00, 0x3F, 0x80,
  0x00, 0x00, 0x3F, 0xFE, 0x00, 0x01, 0xFF, 0xF8, 0x00, 0x07, 0xFF, 0xE0,
  0x00, 0x1F, 0xFF, 0x80, 0x00, 0x7F, 0xFE, 0x00, 0x00, 0x0F, 0xFC, 0x00,
  0x00, 0x7F, 0xF0, 0x00, 0x01, 0xE7, 0xC0, 0x00, 0x0F, 0x9F, 0x00, 0x00,
  0x7C, 0x7C, 0x00, 0x01, 0xE1, 0xF8, 0x00, 0x0F, 0x87, 0xE0, 0x00, 0x7C,
  0x0F, 0x80, 0x01, 0xF0, 0x3E, 0x00, 0x0F, 0x80, 0xF8, 0x00, 0x3F, 0xFF,
  0xF0, 0x01, 0xFF, 0xFF, 0xC0, 0x0F, 0xFF, 0xFF, 0x00, 0x3F, 0xFF, 0xFC,
  0x01, 0xFF, 0xFF, 0xF8, 0x0F, 0xC0, 0x07, 0xE0, 0x3E, 0x00, 0x0F, 0x87,
  0xFF, 0x03, 0xFF, 0xBF, 0xFC, 0x1F, 0xFF, 0xFF, 0xF0, 0x7F, 0xFF, 0xFF,
  0xC1, 0xFF, 0xEF, 0xFE, 0x07, 0xFF, 0x00, 0x03, 0xFF, 0xFF, 0x80, 0x3F,
  0xFF, 0xFF, 0x01, 0xFF, 0xFF, 0xFC, 0x0F, 0xFF, 0xFF, 0xE0, 0x7F, 0xFF,
  0xFF, 0x80, 0x7C, 0x00, 0xFC, 0x03, 0xE0, 0x03, 0xE0, 0x1E, 0x00, 0x1F,
  0x01, 0xF0, 0x00, 0xF8, 0x0F, 0x80, 0x0F, 0x80, 0x7C, 0x01, 0xF8, 0x03,
  0xFF, 0xFF, 0x80, 0x1F, 0xFF, 0xF8, 0x01, 0xFF, 0xFF, 0xC0, 0x0F, 0xFF,
  0xFF, 0x80, 0x7F, 0xFF, 0xFC, 0x03, 0xC0, 0x0F, 0xF0, 0x3E, 0x00, 0x1F,
  0x81, 0xF0, 0x00, 0x7C, 0x0F, 0x80, 0x03, 0xE0, 0x78, 0x00, 0x1F, 0x03,
  0xC0, 0x03, 0xF1, 0xFF, 0xFF, 0xFF, 0x9F, 0xFF, 0xFF, 0xF8, 0xFF, 0xFF,
  0xFF, 0x87, 0xFF, 0xFF, 0xF0, 0x1F, 0xFF, 0xFE, 0x00, 0x00, 0x07, 0xF0,
  0x00, 0x03, 0xFF, 0xE6, 0x00, 0x7F, 0xFF, 0xF0, 0x1F, 0xFF, 0xFF, 0x03,
  0xFF, 0xFF, 0xF0, 0x7F, 0x81, 0xFF, 0x0F, 0xE0, 0x07, 0xE1, 0xF8, 0x00,
  0x3E, 0x1F, 0x00, 0x03, 0xE3, 0xF0, 0x00, 0x3C, 0x3E, 0x00, 0x03, 0xC7,
  0xE0, 0x00, 0x00, 0x7C, 0x00, 0x00, 0x07, 0xC0, 0x00, 0x00, 0x7C, 0x00,
  0x00, 0x0F, 0x80, 0x00, 0x00, 0xF8, 0x00, 0x00, 0x0F, 0x80, 0x00, 0x00,
  0xF8, 0x00, 0x00, 0x0F, 0x80, 0x00, 0x00, 0xFC, 0x00, 0x00, 0x0F, 0xC0,
  0x00, 0x70, 0x7E, 0x00, 0x1F, 0x07, 0xF8, 0x07, 0xF0, 0x3F, 0xFF, 0xFF,
  0x03, 0xFF, 0xFF, 0xE0, 0x1F, 0xFF, 0xF8, 0x00, 0x7F, 0xFE, 0x00, 0x00,
  0xFF, 0x00, 0x00, 0x03, 0xFF, 0xFC, 0x00, 0x7F, 0xFF, 0xF0, 0x07, 0xFF,
  0xFF, 0x80, 0x7F, 0xFF, 0xFC, 0x03, 0xFF, 0xFF, 0xE0, 0x1F, 0x00, 0xFE,
  0x01, 0xF0, 0x07, 0xE0, 0x1E, 0x00, 0x3F, 0x01, 0xE0, 0x01, 0xF0, 0x3E,
  0x00, 0x1F, 0x03, 0xE0, 0x01, 0xF0, 0x3E, 0x00, 0x1F, 0x03, 0xC0, 0x01,
  0xF0, 0x7C, 0x00, 0x1F, 0x07, 0xC0, 0x03, 0xF0, 0x7C, 0x00, 0x3E, 0x07,
  0x80, 0x03, 0xE0, 0x78, 0x00, 0x7E, 0x0F, 0x80, 0x07, 0xC0, 0xF8, 0x00,
  0xFC, 0x0F, 0x80, 0x1F, 0x80, 0xF0, 0x07, 0xF0, 0x7F, 0xFF, 0xFE, 0x07,
  0xFF, 0xFF, 0xC0, 0xFF, 0xFF, 0xF8, 0x0F, 0xFF, 0xFE, 0x00, 0x7F, 0xFF,
  0x00, 0x00, 0x03, 0xFF, 0xFF, 0xF8, 0x3F, 0xFF, 0xFF, 0xC3, 0xFF, 0xFF,
  0xFE, 0x1F, 0xFF, 0xFF, 0xE0, 0x7F, 0xFF, 0xFF, 0x00, 0x78, 0x00, 0xF8,
  0x07, 0xC0, 0x07, 0xC0, 0x3E, 0x00, 0x3E, 0x01, 0xF0, 0xF1, 0xE0, 0x0F,
  0x0F, 0x8E, 0x00, 0x78, 0x7C, 0x00, 0x07, 0xFF, 0xE0, 0x00, 0x3F, 0xFE,
  0x00, 0x01, 0xFF, 0xF0, 0x00, 0x0F, 0xFF, 0x80, 0x00, 0xFF, 0xFC, 0x00,
  0x07, 0xC3, 0xC0, 0x00, 0x3E, 0x1E, 0x1E, 0x01, 0xE0, 0xE0, 0xF0, 0x0F,
  0x00, 0x0F, 0x80, 0xF8, 0x00, 0x7C, 0x07, 0xC0, 0x03, 0xE1, 0xFF, 0xFF,
  0xFE, 0x1F, 0xFF, 0xFF, 0xF0, 0xFF, 0xFF, 0xFF, 0x87, 0xFF, 0xFF, 0xFC,
  0x3F, 0xFF, 0xFF, 0xC0, 0x03, 0xFF, 0xFF, 0xFE, 0x0F, 0xFF, 0xFF, 0xF8,
  0x1F, 0xFF, 0xFF, 0xF0, 0x3F, 0xFF, 0xFF, 0xE0, 0x3F, 0xFF, 0xFF, 0xC0,
  0x1F, 0x00, 0x0F, 0x80, 0x3E, 0x00, 0x1E, 0x00, 0x78, 0x00, 0x7C, 0x00,
  0xF0, 0x70, 0xF8, 0x03, 0xE1, 0xF0, 0xE0, 0x07, 0xC3, 0xC0, 0x00, 0x0F,
  0xFF, 0x80, 0x00, 0x1F, 0xFF, 0x00, 0x00, 0x7F, 0xFE, 0x00, 0x00, 0xFF,
  0xFC, 0x00, 0x01, 0xFF, 0xF0, 0x00, 0x03, 0xC3, 0xE0, 0x00, 0x07, 0x87,
  0xC0, 0x00, 0x1F, 0x07, 0x00, 0x00, 0x3E, 0x00, 0x00, 0x00, 0x7C, 0x00,
  0x00, 0x00, 0xF0, 0x00, 0x00, 0x0F, 0xFF, 0xC0, 0x00, 0x3F, 0xFF, 0x80,
  0x00, 0xFF, 0xFF, 0x00, 0x01, 0xFF, 0xFE, 0x00, 0x01, 0xFF, 0xF8, 0x00,
  0x00, 0x00, 0x07, 0xF8, 0x60, 0x03, 0xFF, 0xFF, 0x00, 0x7F, 0xFF, 0xF0,
  0x1F, 0xFF, 0xFF, 0x03, 0xFF, 0xFF, 0xE0, 0x7F, 0x80, 0xFE, 0x0F, 0xE0,
  0x03, 0xE0, 0xF8, 0x00, 0x3C, 0x1F, 0x00, 0x03, 0xC3, 0xF0, 0x00, 0x00,
  0x3E, 0x00, 0x00, 0x03, 0xE0, 0x00, 0x00, 0x7C, 0x00, 0x00, 0x07, 0xC0,
  0x00, 0x00, 0x7C, 0x00, 0x00, 0x07, 0xC0, 0x7F, 0xFC, 0xF8, 0x0F, 0xFF,
  0xEF, 0x80, 0xFF, 0xFE, 0xF8, 0x0F, 0xFF, 0xCF, 0x80, 0x7F, 0xF8, 0xF8,
  0x00, 0x1F, 0x0F, 0xC0, 0x01, 0xF0, 0xFE, 0x00, 0x1F, 0x07, 0xF8, 0x07,
  0xE0, 0x7F, 0xFF, 0xFE, 0x03, 0xFF, 0xFF, 0xE0, 0x1F, 0xFF, 0xFC, 0x00,
  0x7F, 0xFE, 0x00, 0x00, 0xFF, 0x00, 0x00, 0x01, 0xFF, 0x0F, 0xF8, 0x0F,
  0xFC, 0x7F, 0xF0, 0x7F, 0xF1, 0xFF, 0xC1, 0xFF, 0xC7, 0xFE, 0x03, 0xFE,
  0x1F, 0xF0, 0x07, 0xC0, 0x0F, 0x80, 0x1F, 0x00, 0x3C, 0x00, 0x78, 0x00,
  0xF0, 0x01, 0xE0, 0x07, 0xC0, 0x0F, 0x80, 0x1F, 0x00, 0x3E, 0x00, 0x7C,
  0x00, 0xFF, 0xFF, 0xE0, 0x03, 0xFF, 0xFF, 0x80, 0x1F, 0xFF, 0xFE, 0x00,
  0x7F, 0xFF, 0xF8, 0x01, 0xFF, 0xFF, 0xC0, 0x07, 0x80, 0x1F, 0x00, 0x1E,
  0x00, 0x7C, 0x00, 0xF8, 0x01, 0xF0, 0x03, 0xE0, 0x07, 0xC0, 0x0F, 0x80,
  0x1E, 0x00, 0x3C, 0x00, 0xF8, 0x07, 0xFE, 0x1F, 0xF8, 0x3F, 0xF8, 0xFF,
  0xF0, 0xFF, 0xE3, 0xFF, 0xC3, 0xFF, 0x8F, 0xFE, 0x0F, 0xFC, 0x3F, 0xF8,
  0x00, 0x03, 0xFF, 0xFF, 0x83, 0xFF, 0xFF, 0xC1, 0xFF, 0xFF, 0xE0, 0xFF,
  0xFF, 0xF0, 0x7F, 0xFF, 0xF0, 0x00, 0x7C, 0x00, 0x00, 0x3E, 0x00, 0x00,
  0x1E, 0x00, 0x00, 0x1F, 0x00, 0x00, 0x0F, 0x80, 0x00, 0x07, 0xC0, 0x00,
  0x03, 0xC0, 0x00, 0x03, 0xE0, 0x00, 0x01, 0xF0, 0x00, 0x00, 0xF8, 0x00,
  0x00, 0x7C, 0x00, 0x00, 0x3C, 0x00, 0x00, 0x3E, 0x00, 0x00, 0x1F, 0x00,
  0x00, 0x0F, 0x80, 0x00, 0x07, 0x80, 0x00, 0x07, 0xC0, 0x01, 0xFF, 0xFF,
  0xC1, 0xFF, 0xFF, 0xE0, 0xFF, 0xFF, 0xF0, 0x7F, 0xFF, 0xF8, 0x1F, 0xFF,
  0xF8, 0x00, 0x00, 0x07, 0xFF, 0xFE, 0x00, 0x1F, 0xFF, 0xFC, 0x00, 0x3F,
  0xFF, 0xF8, 0x00, 0x7F, 0xFF, 0xF0, 0x00, 0x7F, 0xFF, 0xC0, 0x00, 0x01,
  0xF0, 0x00, 0x00, 0x03, 0xE0, 0x00, 0x00, 0x07, 0x80, 0x00, 0x00, 0x0F,
  0x00, 0x00, 0x00, 0x3E, 0x00, 0x00, 0x00, 0x7C, 0x00, 0x00, 0x00, 0xF8,
  0x00, 0x00, 0x01, 0xE0, 0x00, 0x00, 0x07, 0xC0, 0x07, 0x00, 0x0F, 0x80,
  0x1F, 0x00, 0x1F, 0x00, 0x3E, 0x00, 0x3E, 0x00, 0x78, 0x00, 0x78, 0x01,
  0xF0, 0x01, 0xF0, 0x03, 0xE0, 0x03, 0xE0, 0x07, 0xC0, 0x0F, 0x80, 0x0F,
  0x80, 0x3F, 0x00, 0x1F, 0xC0, 0xFC, 0x00, 0x7F, 0xFF, 0xF8, 0x00, 0xFF,
  0xFF, 0xE0, 0x00, 0x7F, 0xFF, 0x00, 0x00, 0x3F, 0xFC, 0x00, 0x00, 0x1F,
  0xC0, 0x00, 0x00, 0x03, 0xFF, 0xC3, 0xFE, 0x0F, 0xFF, 0x8F, 0xFC, 0x1F,
  0xFF, 0x3F, 0xF8, 0x3F, 0xFE, 0x7F, 0xF0, 0x7F, 0xF8, 0x7F, 0xC0, 0x1F,
  0x01, 0xFC, 0x00, 0x3E, 0x07, 0xF0, 0x00, 0x78, 0x3F, 0x80, 0x01, 0xF0,
  0xFE, 0x00, 0x03, 0xE3, 0xF0, 0x00, 0x07, 0xDF, 0xC0, 0x00, 0x0F, 0xFE,
  0x00, 0x00, 0x1F, 0xFE, 0x00, 0x00, 0x7F, 0xFE, 0x00, 0x00, 0xFF, 0xFE,
  0x00, 0x01, 0xFC, 0xFC, 0x00, 0x03, 0xE0, 0xFC, 0x00, 0x0F, 0x81, 0xF8,
  0x00, 0x1F, 0x01, 0xF8, 0x00, 0x3E, 0x03, 0xF0, 0x00, 0x78, 0x03, 0xE0,
  0x00, 0xF0, 0x07, 0xE0, 0x1F, 0xFE, 0x0F, 0xF8, 0x7F, 0xFC, 0x1F, 0xF8,
  0xFF, 0xF8, 0x1F, 0xF1, 0xFF, 0xF0, 0x3F, 0xE1, 0xFF, 0xC0, 0x7F, 0x80,
  0x03, 0xFF, 0xF8, 0x00, 0xFF, 0xFF, 0x00, 0x1F, 0xFF, 0xE0, 0x03, 0xFF,
  0xFC, 0x00, 0x7F, 0xFF, 0x00, 0x00, 0x7C, 0x00, 0x00, 0x0F, 0x80, 0x00,
  0x01, 0xE0, 0x00, 0x00, 0x7C, 0x00, 0x00, 0x0F, 0x80, 0x00, 0x01, 0xF0,
  0x00, 0x00, 0x3C, 0x00, 0x00, 0x07, 0x80, 0x00, 0x01, 0xF0, 0x00, 0x00,
  0x3E, 0x00, 0x00, 0x07, 0xC0, 0x0E, 0x00, 0xF0, 0x01, 0xE0, 0x3E, 0x00,
  0x7C, 0x07, 0xC0, 0x0F, 0x80, 0xF8, 0x01, 0xF0, 0x1E, 0x00, 0x7C, 0x07,
  0xC0, 0x0F, 0x9F, 0xFF, 0xFF, 0xF7, 0xFF, 0xFF, 0xFE, 0xFF, 0xFF, 0xFF,
  0x9F, 0xFF, 0xFF, 0xF1, 0xFF, 0xFF, 0xFE, 0x00, 0x03, 0xFC, 0x00, 0x3F,
  0xC1, 0xFF, 0x00, 0x1F, 0xF0, 0x7F, 0xC0, 0x07, 0xFC, 0x1F, 0xF0, 0x03,
  0xFE, 0x01, 0xFE, 0x01, 0xFE, 0x00, 0xFF, 0x80, 0xFF, 0x80, 0x3F, 0xE0,
  0x3F, 0xE0, 0x0F, 0xF8, 0x1F, 0xF0, 0x03, 0xFF, 0x0F, 0xFC, 0x00, 0xF7,
  0xC3, 0xFF, 0x00, 0x7D, 0xF1, 0xF7, 0xC0, 0x1F, 0x7C, 0xFD, 0xF0, 0x07,
  0xDF, 0xBE, 0x78, 0x01, 0xE3, 0xFF, 0x3E, 0x00, 0x78, 0xFF, 0xCF, 0x80,
  0x3E, 0x3F, 0xE3, 0xE0, 0x0F, 0x87, 0xF0, 0xF8, 0x03, 0xE1, 0xFC, 0x3C,
  0x00, 0xF0, 0x7E, 0x1F, 0x00, 0x7C, 0x1F, 0x07, 0xC0, 0x1F, 0x00, 0x01,
  0xF0, 0x07, 0xC0, 0x00, 0x78, 0x07, 0xFE, 0x01, 0xFF, 0x83, 0xFF, 0xC0,
  0xFF, 0xF0, 0xFF, 0xF0, 0x7F, 0xFC, 0x3F, 0xF8, 0x1F, 0xFE, 0x0F, 0xFC,
  0x03, 0xFF, 0x00, 0x07, 0xF8, 0x07, 0xFF, 0x0F, 0xFC, 0x0F, 0xFF, 0x0F,
  0xFC, 0x0F, 0xFF, 0x0F, 0xFC, 0x0F, 0xFF, 0x0F, 0xFE, 0x0F, 0xFE, 0x01,
  0xFE, 0x00, 0xF8, 0x01, 0xFF, 0x00, 0xF0, 0x01, 0xFF, 0x01, 0xF0, 0x03,
  0xFF, 0x81, 0xF0, 0x03, 0xFF, 0x81, 0xF0, 0x03, 0xEF, 0xC1, 0xF0, 0x03,
  0xCF, 0xC1, 0xE0, 0x07, 0xC7, 0xE3, 0xE0, 0x07, 0xC7, 0xE3, 0xE0, 0x07,
  0xC3, 0xF3, 0xE0, 0x07, 0xC3, 0xF3, 0xC0, 0x07, 0x81, 0xF7, 0xC0, 0x0F,
  0x81, 0xFF, 0xC0, 0x0F, 0x80, 0xFF, 0xC0, 0x0F, 0x80, 0xFF, 0xC0, 0x0F,
  0x00, 0xFF, 0x80, 0x0F, 0x00, 0x7F, 0x80, 0x7F, 0xF0, 0x7F, 0x80, 0xFF,
  0xF0, 0x3F, 0x80, 0xFF, 0xF0, 0x3F, 0x00, 0xFF, 0xF0, 0x1F, 0x00, 0x7F,
  0xE0, 0x1F, 0x00, 0x00, 0x07, 0xF0, 0x00, 0x03, 0xFF, 0x80, 0x01, 0xFF,
  0xF8, 0x00, 0xFF, 0xFF, 0x80, 0x3F, 0xFF, 0xF8, 0x0F, 0xF0, 0x7F, 0x83,
  0xF8, 0x03, 0xF0, 0xFC, 0x00, 0x7E, 0x1F, 0x00, 0x07, 0xE7, 0xE0, 0x00,
  0x7C, 0xF8, 0x00, 0x0F, 0xBE, 0x00, 0x01, 0xF7, 0xC0, 0x00, 0x3E, 0xF0,
  0x00, 0x07, 0xFE, 0x00, 0x00, 0xFF, 0xC0, 0x00, 0x3E, 0xF8, 0x00, 0x07,
  0xDF, 0x00, 0x00, 0xFB, 0xE0, 0x00, 0x3E, 0x7C, 0x00, 0x0F, 0xCF, 0xC0,
  0x01, 0xF0, 0xF8, 0x00, 0x7E, 0x1F, 0x80, 0x3F, 0x83, 0xFC, 0x1F, 0xE0,
  0x3F, 0xFF, 0xF8, 0x03, 0xFF, 0xFE, 0x00, 0x3F, 0xFF, 0x00, 0x03, 0xFF,
  0x80, 0x00, 0x1F, 0xC0, 0x00, 0x03, 0xFF, 0xFE, 0x00, 0x7F, 0xFF, 0xF8,
  0x07, 0xFF, 0xFF, 0xC0, 0x7F, 0xFF, 0xFE, 0x07, 0xFF, 0xFF, 0xF0, 0x0F,
  0x80, 0x7F, 0x00, 0xF8, 0x01, 0xF0, 0x0F, 0x00, 0x1F, 0x01, 0xF0, 0x01,
  0xF0, 0x1F, 0x00, 0x1F, 0x01, 0xF0, 0x03, 0xE0, 0x1E, 0x00, 0x7E, 0x01,
  0xE0, 0x0F, 0xC0, 0x3F, 0xFF, 0xFC, 0x03, 0xFF, 0xFF, 0x80, 0x3F, 0xFF,
  0xE0, 0x03, 0xFF, 0xFC, 0x00, 0x7F, 0xFE, 0x00, 0x07, 0xC0, 0x00, 0x00,
  0x7C, 0x00, 0x00, 0x07, 0x80, 0x00, 0x00, 0x78, 0x00, 0x00, 0x7F, 0xFF,
  0x00, 0x0F, 0xFF, 0xF0, 0x00, 0xFF, 0xFF, 0x00, 0x0F, 0xFF, 0xF0, 0x00,
  0x7F, 0xFE, 0x00, 0x00, 0x00, 0x07, 0xF0, 0x00, 0x07, 0xFF, 0x80, 0x03,
  0xFF, 0xF8, 0x00, 0xFF, 0xFF, 0x80, 0x3F, 0xFF, 0xF8, 0x0F, 0xF0, 0x7F,
  0x83, 0xF8, 0x03, 0xF0, 0xFC, 0x00, 0x3F, 0x1F, 0x00, 0x07, 0xE7, 0xC0,
  0x00, 0x7D, 0xF8, 0x00, 0x0F, 0xBE, 0x00, 0x01, 0xF7, 0xC0, 0x00, 0x3F,
  0xF0, 0x00, 0x07, 0xFE, 0x00, 0x00, 0xFF, 0xC0, 0x00, 0x3E, 0xF8, 0x00,
  0x07, 0xDF, 0x00, 0x01, 0xFB, 0xE0, 0x00, 0x3E, 0x7E, 0x00, 0x0F, 0x8F,
  0xC0, 0x03, 0xF0, 0xFC, 0x01, 0xFC, 0x1F, 0xE0, 0xFF, 0x01, 0xFF, 0xFF,
  0xC0, 0x1F, 0xFF, 0xF0, 0x01, 0xFF, 0xFC, 0x00, 0x1F, 0xFE, 0x00, 0x01,
  0xFE, 0x00, 0x00, 0x78, 0x00, 0x00, 0x1F, 0xF8, 0x38, 0x0F, 0xFF, 0xFF,
  0x81, 0xFF, 0xFF, 0xF0, 0x7F, 0xFF, 0xFC, 0x0F, 0xFF, 0xFF, 0x00, 0xF0,
  0x1F, 0x80, 0x00, 0x03, 0xFF, 0xFE, 0x00, 0x3F, 0xFF, 0xFC, 0x01, 0xFF,
  0xFF, 0xF0, 0x0F, 0xFF, 0xFF, 0xC0, 0x3F, 0xFF, 0xFF, 0x00, 0x7C, 0x03,
  0xF8, 0x03, 0xE0, 0x07, 0xC0, 0x1E, 0x00, 0x3E, 0x00, 0xF0, 0x01, 0xF0,
  0x0F, 0x80, 0x1F, 0x80, 0x7C, 0x01, 0xF8, 0x03, 0xE0, 0x3F, 0x80, 0x1F,
  0xFF, 0xFC, 0x01, 0xFF, 0xFF, 0x80, 0x0F, 0xFF, 0xF8, 0x00, 0x7F, 0xFF,
  0x00, 0x03, 0xFF, 0xFC, 0x00, 0x1E, 0x07, 0xF0, 0x01, 0xF0, 0x1F, 0xC0,
  0x0F, 0x80, 0x7E, 0x00, 0x7C, 0x03, 0xF8, 0x03, 0xC0, 0x0F, 0xC0, 0xFF,
  0xE0, 0x7F, 0xCF, 0xFF, 0x01, 0xFF, 0xFF, 0xF8, 0x0F, 0xFF, 0xFF, 0xC0,
  0x3F, 0xDF, 0xFC, 0x01, 0xFC, 0x00, 0x0F, 0xE1, 0x80, 0x0F, 0xFF, 0xF0,
  0x0F, 0xFF, 0xFC, 0x07, 0xFF, 0xFF, 0x03, 0xFF, 0xFF, 0xC1, 0xFC, 0x0F,
  0xE0, 0x7C, 0x01, 0xF8, 0x3E, 0x00, 0x3E, 0x0F, 0x80, 0x0F, 0x03, 0xE0,
  0x03, 0xC0, 0xFC, 0x00, 0x00, 0x3F, 0xE0, 0x00, 0x07, 0xFF, 0x80, 0x01,
  0xFF, 0xFC, 0x00, 0x3F, 0xFF, 0x80, 0x03, 0xFF, 0xF0, 0x00, 0x07, 0xFE,
  0x00, 0x00, 0x3F, 0x80, 0x00, 0x03, 0xE1, 0xE0, 0x00, 0xF8, 0xF8, 0x00,
  0x3E, 0x3E, 0x00, 0x1F, 0x8F, 0xC0, 0x0F, 0xC3, 0xFC, 0x0F, 0xF0, 0xFF,
  0xFF, 0xF8, 0x3F, 0xFF, 0xFC, 0x0F, 0xFF, 0xFE, 0x03, 0x9F, 0xFE, 0x00,
  0x01, 0xFE, 0x00, 0x00, 0x3F, 0xFF, 0xFF, 0xCF, 0xFF, 0xFF, 0xF7, 0xFF,
  0xFF, 0xFD, 0xFF, 0xFF, 0xFE, 0x7F, 0xFF, 0xFF, 0x9F, 0x07, 0x83, 0xE7,
  0x83, 0xE0, 0xFB, 0xE0, 0xF8, 0x3E, 0xF8, 0x3E, 0x0F, 0x3E, 0x0F, 0x07,
  0xCF, 0x07, 0xC1, 0xF3, 0x81, 0xF0, 0x38, 0x00, 0x7C, 0x00, 0x00, 0x1E,
  0x00, 0x00, 0x07, 0x80, 0x00, 0x03, 0xE0, 0x00, 0x00, 0xF8, 0x00, 0x00,
  0x3E, 0x00, 0x00, 0x0F, 0x00, 0x00, 0x07, 0xC0, 0x00, 0x01, 0xF0, 0x00,
  0x00, 0x7C, 0x00, 0x07, 0xFF, 0xF8, 0x01, 0xFF, 0xFE, 0x00, 0xFF, 0xFF,
  0x80, 0x3F, 0xFF, 0xE0, 0x07, 0xFF, 0xF0, 0x00, 0x3F, 0xF0, 0x7F, 0xE7,
  0xFF, 0x8F, 0xFF, 0x7F, 0xF9, 0xFF, 0xF7, 0xFF, 0x1F, 0xFE, 0x7F, 0xF0,
  0xFF, 0xC1, 0xE0, 0x01, 0xF0, 0x1E, 0x00, 0x1F, 0x03, 0xE0, 0x01, 0xF0,
  0x3E, 0x00, 0x1F, 0x03, 0xE0, 0x01, 0xE0, 0x3C, 0x00, 0x3E, 0x07, 0xC0,
  0x03, 0xE0, 0x7C, 0x00, 0x3E, 0x07, 0xC0, 0x03, 0xC0, 0x7C, 0x00, 0x3C,
  0x07, 0x80, 0x07, 0xC0, 0xF8, 0x00, 0x7C, 0x0F, 0x80, 0x07, 0xC0, 0xF8,
  0x00, 0x78, 0x0F, 0x80, 0x0F, 0x80, 0xFC, 0x01, 0xF8, 0x0F, 0xC0, 0x3F,
  0x00, 0xFF, 0x07, 0xE0, 0x07, 0xFF, 0xFE, 0x00, 0x7F, 0xFF, 0xC0, 0x03,
  0xFF, 0xF0, 0x00, 0x0F, 0xFE, 0x00, 0x00, 0x3F, 0x00, 0x00, 0x7F, 0xF0,
  0x1F, 0xFF, 0xFF, 0xC0, 0xFF, 0xFF, 0xFF, 0x03, 0xFF, 0xFF, 0xFC, 0x0F,
  0xFF, 0x7F, 0xE0, 0x3F, 0xF8, 0x7C, 0x00, 0x1F, 0x01, 0xF0, 0x00, 0xF8,
  0x07, 0xC0, 0x03, 0xE0, 0x1F, 0x80, 0x1F, 0x00, 0x3E, 0x00, 0xF8, 0x00,
  0xF8, 0x03, 0xE0, 0x03, 0xE0, 0x1F, 0x00, 0x0F, 0xC0, 0xFC, 0x00, 0x1F,
  0x03, 0xE0, 0x00, 0x7C, 0x1F, 0x00, 0x01, 0xF0, 0xFC, 0x00, 0x07, 0xC3,
  0xE0, 0x00, 0x1F, 0x9F, 0x00, 0x00, 0x3E, 0xFC, 0x00, 0x00, 0xFB, 0xE0,
  0x00, 0x03, 0xFF, 0x00, 0x00, 0x0F, 0xFC, 0x00, 0x00, 0x3F, 0xE0, 0x00,
  0x00, 0x7F, 0x00, 0x00, 0x01, 0xFC, 0x00, 0x00, 0x07, 0xE0, 0x00, 0x00,
  0x1F, 0x00, 0x00, 0x00, 0x7F, 0xF0, 0x3F, 0xFF, 0xFF, 0x83, 0xFF, 0xFF,
  0xFC, 0x1F, 0xFF, 0xFF, 0xE0, 0xFF, 0xFF, 0xFE, 0x07, 0xFF, 0x1E, 0x00,
  0x01, 0xE0, 0xF0, 0x7C, 0x1F, 0x0F, 0x87, 0xE0, 0xF0, 0x7C, 0x3F, 0x0F,
  0x83, 0xE3, 0xF8, 0x7C, 0x1F, 0x1F, 0xE3, 0xC0, 0xF9, 0xFF, 0x3E, 0x07,
  0xCF, 0xF9, 0xF0, 0x3E, 0xFF, 0xCF, 0x01, 0xF7, 0xBE, 0xF8, 0x0F, 0xFD,
  0xF7, 0xC0, 0x7B, 0xCF, 0xFC, 0x03, 0xFE, 0x7F, 0xE0, 0x3F, 0xE3, 0xFF,
  0x01, 0xFF, 0x0F, 0xF0, 0x0F, 0xF0, 0x7F, 0x80, 0x7F, 0x83, 0xFC, 0x03,
  0xF8, 0x1F, 0xC0, 0x1F, 0xC0, 0xFE, 0x00, 0xFC, 0x07, 0xF0, 0x07, 0xE0,
  0x3F, 0x00, 0x3E, 0x01, 0xF8, 0x00, 0x01, 0xFE, 0x03, 0xFE, 0x03, 0xFF,
  0x07, 0xFF, 0x07, 0xFF, 0x07, 0xFF, 0x07, 0xFE, 0x07, 0xFE, 0x03, 0xFC,
  0x03, 0xFC, 0x00, 0xFC, 0x03, 0xF0, 0x00, 0xFE, 0x07, 0xE0, 0x00, 0x7E,
  0x1F, 0xC0, 0x00, 0x3F, 0x3F, 0x00, 0x00, 0x1F, 0xFE, 0x00, 0x00, 0x1F,
  0xFC, 0x00, 0x00, 0x0F, 0xF8, 0x00, 0x00, 0x07, 0xF0, 0x00, 0x00, 0x07,
  0xE0, 0x00, 0x00, 0x0F, 0xF0, 0x00, 0x00, 0x1F, 0xF8, 0x00, 0x00, 0x7F,
  0xF8, 0x00, 0x00, 0xFC, 0xFC, 0x00, 0x01, 0xF8, 0x7E, 0x00, 0x03, 0xF0,
  0x7E, 0x00, 0x07, 0xE0, 0x3F, 0x00, 0x0F, 0xC0, 0x1F, 0x80, 0x7F, 0xE0,
  0x7F, 0xE0, 0xFF, 0xE0, 0xFF, 0xE0, 0xFF, 0xE0, 0xFF, 0xE0, 0xFF, 0xE0,
  0xFF, 0xE0, 0x7F, 0xC0, 0xFF, 0xC0, 0x7F, 0xC0, 0x7F, 0xFF, 0xF0, 0x3F,
  0xFF, 0xFC, 0x0F, 0xFF, 0xFF, 0x03, 0xFF, 0x7F, 0x80, 0xFF, 0x87, 0xC0,
  0x1F, 0x01, 0xF8, 0x0F, 0x80, 0x3E, 0x07, 0xC0, 0x0F, 0xC3, 0xE0, 0x01,
  0xF1, 0xF0, 0x00, 0x7E, 0xF8, 0x00, 0x0F, 0xFC, 0x00, 0x03, 0xFE, 0x00,
  0x00, 0x7F, 0x80, 0x00, 0x1F, 0xC0, 0x00, 0x03, 0xE0, 0x00, 0x00, 0xF0,
  0x00, 0x00, 0x7C, 0x00, 0x00, 0x1F, 0x00, 0x00, 0x07, 0xC0, 0x00, 0x01,
  0xE0, 0x00, 0x00, 0x78, 0x00, 0x07, 0xFF, 0xF0, 0x03, 0xFF, 0xFE, 0x00,
  0xFF, 0xFF, 0x80, 0x3F, 0xFF, 0xC0, 0x0F, 0xFF, 0xE0, 0x00, 0x01, 0xFF,
  0xFF, 0xC0, 0x3F, 0xFF, 0xF8, 0x07, 0xFF, 0xFF, 0x00, 0xFF, 0xFF, 0xE0,
  0x3F, 0xFF, 0xFC, 0x07, 0xC0, 0x3F, 0x00, 0xF8, 0x0F, 0xC0, 0x1F, 0x03,
  0xF0, 0x03, 0xC0, 0xFC, 0x00, 0xF8, 0x3F, 0x00, 0x0E, 0x0F, 0xC0, 0x00,
  0x03, 0xF0, 0x00, 0x00, 0xFC, 0x00, 0x00, 0x7E, 0x00, 0x00, 0x1F, 0x80,
  0x00, 0x07, 0xE0, 0x00, 0x01, 0xF8, 0x0E, 0x00, 0x7E, 0x03, 0xE0, 0x1F,
  0x80, 0x7C, 0x07, 0xE0, 0x0F, 0x01, 0xF8, 0x03, 0xE0, 0x7E, 0x00, 0x7C,
  0x1F, 0xFF, 0xFF, 0x83, 0xFF, 0xFF, 0xF0, 0x7F, 0xFF, 0xFC, 0x0F, 0xFF,
  0xFF, 0x81, 0xFF, 0xFF, 0xF0, 0x00, 0x00, 0xFF, 0xC0, 0x3F, 0xF0, 0x0F,
  0xFC, 0x07, 0xFF, 0x01, 0xFF, 0x80, 0x7C, 0x00, 0x1E, 0x00, 0x07, 0x80,
  0x03, 0xE0, 0x00, 0xF8, 0x00, 0x3E, 0x00, 0x0F, 0x00, 0x07, 0xC0, 0x01,
  0xF0, 0x00, 0x7C, 0x00, 0x1F, 0x00, 0x07, 0x80, 0x03, 0xE0, 0x00, 0xF8,
  0x00, 0x3E, 0x00, 0x0F, 0x00, 0x03, 0xC0, 0x01, 0xF0, 0x00, 0x7C, 0x00,
  0x1F, 0x00, 0x07, 0x80, 0x01, 0xE0, 0x00, 0xF8, 0x00, 0x3E, 0x00, 0x0F,
  0x80, 0x03, 0xC0, 0x01, 0xF0, 0x00, 0x7F, 0xE0, 0x1F, 0xF8, 0x07, 0xFE,
  0x01, 0xFF, 0x80, 0xFF, 0xC0, 0x00, 0x20, 0x03, 0xC0, 0x3E, 0x01, 0xF0,
  0x07, 0xC0, 0x3E, 0x01, 0xF0, 0x0F, 0x80, 0x3E, 0x01, 0xF0, 0x0F, 0x80,
  0x7C, 0x01, 0xF0, 0x0F, 0x80, 0x7C, 0x03, 0xE0, 0x0F, 0x80, 0x7C, 0x03,
  0xE0, 0x1F, 0x80, 0x7C, 0x03, 0xE0, 0x1F, 0x00, 0x7C, 0x03, 0xE0, 0x1F,
  0x00, 0xF8, 0x03, 0xE0, 0x1F, 0x00, 0xF8, 0x07, 0xC0, 0x1F, 0x00, 0xF8,
  0x07, 0xC0, 0x3E, 0x00, 0xF0, 0x07, 0x80, 0x38, 0x00, 0xFF, 0xC0, 0x7F,
  0xE0, 0x1F, 0xF8, 0x07, 0xFE, 0x01, 0xFF, 0x80, 0x03, 0xE0, 0x00, 0xF0,
  0x00, 0x7C, 0x00, 0x1F, 0x00, 0x07, 0xC0, 0x01, 0xE0, 0x00, 0x78, 0x00,
  0x3E, 0x00, 0x0F, 0x80, 0x03, 0xE0, 0x00, 0xF0, 0x00, 0x3C, 0x00, 0x1F,
  0x00, 0x07, 0xC0, 0x01, 0xF0, 0x00, 0x78, 0x00, 0x3E, 0x00, 0x0F, 0x80,
  0x03, 0xE0, 0x00, 0xF8, 0x00, 0x3C, 0x00, 0x1F, 0x00, 0x07, 0xC0, 0x01,
  0xF0, 0x00, 0x78, 0x00, 0x1E, 0x00, 0x0F, 0x80, 0x7F, 0xE0, 0x3F, 0xF8,
  0x0F, 0xFC, 0x03, 0xFF, 0x00, 0xFF, 0xC0, 0x00, 0x00, 0x08, 0x00, 0x01,
  0xC0, 0x00, 0x3C, 0x00, 0x07, 0xE0, 0x00, 0xFE, 0x00, 0x1F, 0xF0, 0x03,
  0xFF, 0x80, 0xFF, 0xF8, 0x1F, 0xCF, 0xC3, 0xF8, 0xFE, 0x7E, 0x07, 0xEF,
  0xC0, 0x3F, 0xF8, 0x03, 0xFF, 0x00, 0x1F, 0xE0, 0x00, 0xE0, 0x7F, 0xFF,
  0xFF, 0xFB, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xEF, 0xFF, 0xFF, 0xFF, 0x00, 0x60, 0xF0, 0xF8, 0x7C, 0x3E, 0x1F, 0x0F,
  0x06, 0x00, 0x3F, 0xE0, 0x03, 0xFF, 0xF8, 0x07, 0xFF, 0xFC, 0x07, 0xFF,
  0xFE, 0x07, 0xFF, 0xFE, 0x00, 0x00, 0x7E, 0x00, 0x00, 0x3E, 0x00, 0x00,
  0x3E, 0x00, 0x7F, 0xFE, 0x03, 0xFF, 0xFC, 0x0F, 0xFF, 0xFC, 0x1F, 0xFF,
  0xFC, 0x3F, 0xFF, 0xFC, 0x7F, 0x00, 0x78, 0x7C, 0x00, 0x78, 0xF8, 0x00,
  0xF8, 0xF8, 0x03, 0xF8, 0xFC, 0x0F, 0xFE, 0xFF, 0xFF, 0xFF, 0x7F, 0xFF,
  0xFF, 0x7F, 0xFF, 0xFF, 0x3F, 0xFD, 0xFE, 0x0F, 0xE0, 0x00, 0x03, 0xFC,
  0x00, 0x00, 0x3F, 0xE0, 0x00, 0x01, 0xFF, 0x00, 0x00, 0x0F, 0xF0, 0x00,
  0x00, 0x3F, 0x80, 0x00, 0x00, 0x7C, 0x00, 0x00, 0x03, 0xE0, 0x00, 0x00,
  0x1F, 0x00, 0x00, 0x00, 0xF0, 0xFE, 0x00, 0x0F, 0xBF, 0xFC, 0x00, 0x7F,
  0xFF, 0xF8, 0x03, 0xFF, 0xFF, 0xC0, 0x1F, 0xFF, 0xFF, 0x00, 0xFF, 0x03,
  0xF8, 0x0F, 0xE0, 0x07, 0xE0, 0x7E, 0x00, 0x3F, 0x03, 0xE0, 0x00, 0xF8,
  0x1F, 0x00, 0x07, 0xC0, 0xF0, 0x00, 0x3E, 0x0F, 0x80, 0x01, 0xF0, 0x7C,
  0x00, 0x1F, 0x03, 0xE0, 0x00, 0xF8, 0x1F, 0x00, 0x0F, 0xC0, 0xFC, 0x00,
  0x7C, 0x0F, 0xE0, 0x07, 0xE3, 0xFF, 0xC0, 0xFE, 0x3F, 0xFF, 0xFF, 0xE1,
  0xFF, 0xFF, 0xFE, 0x0F, 0xFF, 0xFF, 0xE0, 0x7F, 0x9F, 0xFC, 0x00, 0x00,
  0x3F, 0x80, 0x00, 0x00, 0x1F, 0xE3, 0x80, 0x7F, 0xFF, 0xC0, 0x7F, 0xFF,
  0xE0, 0xFF, 0xFF, 0xF0, 0xFF, 0xFF, 0xF8, 0xFF, 0x01, 0xFC, 0x7E, 0x00,
  0x7C, 0x7E, 0x00, 0x3E, 0x3E, 0x00, 0x0E, 0x3E, 0x00, 0x00, 0x1F, 0x00,
  0x00, 0x1F, 0x00, 0x00, 0x0F, 0x80, 0x00, 0x07, 0xC0, 0x00, 0x03, 0xE0,
  0x00, 0x01, 0xF0, 0x00, 0x00, 0xFC, 0x00, 0x0C, 0x7F, 0x80, 0x3F, 0x1F,
  0xFF, 0xFF, 0x8F, 0xFF, 0xFF, 0x83, 0xFF, 0xFF, 0x80, 0x7F, 0xFF, 0x00,
  0x0F, 0xFC, 0x00, 0x00, 0x00, 0x0F, 0xF0, 0x00, 0x01, 0xFE, 0x00, 0x00,
  0x1F, 0xE0, 0x00, 0x01, 0xFE, 0x00, 0x00, 0x1F, 0xE0, 0x00, 0x00, 0x3E,
  0x00, 0x00, 0x03, 0xC0, 0x00, 0x00, 0x7C, 0x00, 0x3F, 0x87, 0xC0, 0x0F,
  0xFF, 0x7C, 0x03, 0xFF, 0xFF, 0xC0, 0x7F, 0xFF, 0xF8, 0x0F, 0xFF, 0xFF,
  0x81, 0xFC, 0x0F, 0xF8, 0x3F, 0x00, 0x3F, 0x83, 0xE0, 0x01, 0xF0, 0x7C,
  0x00, 0x1F, 0x07, 0xC0, 0x01, 0xF0, 0xF8, 0x00, 0x1F, 0x0F, 0x80, 0x01,
  0xF0, 0xF8, 0x00, 0x1E, 0x0F, 0x80, 0x03, 0xE0, 0xF8, 0x00, 0x3E, 0x0F,
  0xC0, 0x07, 0xE0, 0xFC, 0x00, 0xFE, 0x07, 0xF0, 0x3F, 0xF8, 0x7F, 0xFF,
  0xFF, 0xC3, 0xFF, 0xFF, 0xFC, 0x3F, 0xFF, 0xFF, 0xC0, 0xFF, 0xE7, 0xF8,
  0x03, 0xF8, 0x00, 0x00, 0x00, 0x1F, 0xC0, 0x00, 0xFF, 0xF0, 0x03, 0xFF,
  0xF8, 0x07, 0xFF, 0xFC, 0x0F, 0xFF, 0xFE, 0x1F, 0xE0, 0x7E, 0x3F, 0x80,
  0x1F, 0x3F, 0x00, 0x0F, 0x7E, 0x00, 0x0F, 0x7F, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xF8, 0x00,
  0x00, 0xFC, 0x00, 0x00, 0xFC, 0x00, 0x1C, 0x7F, 0x01, 0xFE, 0x7F, 0xFF,
  0xFE, 0x3F, 0xFF, 0xFE, 0x1F, 0xFF, 0xFC, 0x0F, 0xFF, 0xF0, 0x03, 0xFF,
  0x00, 0x00, 0x00, 0x7F, 0xC0, 0x00, 0x3F, 0xFF, 0x00, 0x07, 0xFF, 0xF0,
  0x00, 0xFF, 0xFF, 0x00, 0x1F, 0xFF, 0xE0, 0x01, 0xF0, 0x00, 0x00, 0x3E,
  0x00, 0x00, 0x03, 0xE0, 0x00, 0x00, 0x3C, 0x00, 0x00, 0x7F, 0xFF, 0xF0,
  0x0F, 0xFF, 0xFF, 0x00, 0xFF, 0xFF, 0xF0, 0x0F, 0xFF, 0xFF, 0x00, 0xFF,
  0xFF, 0xE0, 0x00, 0x78, 0x00, 0x00, 0x0F, 0x80, 0x00, 0x00, 0xF8, 0x00,
  0x00, 0x0F, 0x80, 0x00, 0x00, 0xF0, 0x00, 0x00, 0x1F, 0x00, 0x00, 0x01,
  0xF0, 0x00, 0x00, 0x1F, 0x00, 0x00, 0x01, 0xF0, 0x00, 0x00, 0x1E, 0x00,
  0x00, 0x03, 0xE0, 0x00, 0x07, 0xFF, 0xFF, 0x00, 0xFF, 0xFF, 0xF0, 0x0F,
  0xFF, 0xFF, 0x00, 0xFF, 0xFF, 0xF0, 0x07, 0xFF, 0xFE, 0x00, 0x00, 0x3F,
  0x80, 0x00, 0x0F, 0xFE, 0xFF, 0x03, 0xFF, 0xFF, 0xF0, 0x7F, 0xFF, 0xFF,
  0x0F, 0xFF, 0xFF, 0xF1, 0xFC, 0x1F, 0xFE, 0x3F, 0x80, 0x7F, 0x03, 0xE0,
  0x03, 0xF0, 0x7E, 0x00, 0x3E, 0x07, 0xC0, 0x03, 0xE0, 0xF8, 0x00, 0x3E,
  0x0F, 0x80, 0x03, 0xE0, 0xF8, 0x00, 0x3E, 0x0F, 0x80, 0x03, 0xC0, 0xF8,
  0x00, 0x7C, 0x0F, 0xC0, 0x0F, 0xC0, 0xFC, 0x01, 0xFC, 0x07, 0xF0, 0x7F,
  0x80, 0x7F, 0xFF, 0xF8, 0x03, 0xFF, 0xFF, 0x80, 0x3F, 0xFF, 0xF8, 0x00,
  0xFF, 0xEF, 0x80, 0x03, 0xF0, 0xF0, 0x00, 0x00, 0x1F, 0x00, 0x00, 0x01,
  0xF0, 0x00, 0x00, 0x7E, 0x00, 0x1F, 0xFF, 0xE0, 0x03, 0xFF, 0xFC, 0x00,
  0x3F, 0xFF, 0x80, 0x03, 0xFF, 0xE0, 0x00, 0x3F, 0xF8, 0x00, 0x00, 0x03,
  0xF8, 0x00, 0x01, 0xFE, 0x00, 0x00, 0xFF, 0x80, 0x00, 0x3F, 0xE0, 0x00,
  0x07, 0xF0, 0x00, 0x00, 0x3C, 0x00, 0x00, 0x1F, 0x00, 0x00, 0x07, 0xC0,
  0x00, 0x01, 0xF1, 0xF8, 0x00, 0x79, 0xFF, 0x80, 0x1E, 0xFF, 0xF0, 0x0F,
  0xFF, 0xFC, 0x03, 0xFF, 0xFF, 0x80, 0xFF, 0x07, 0xE0, 0x3F, 0x00, 0xF8,
  0x1F, 0x80, 0x3E, 0x07, 0xC0, 0x0F, 0x81, 0xF0, 0x03, 0xC0, 0x7C, 0x00,
  0xF0, 0x1E, 0x00, 0x7C, 0x0F, 0x80, 0x1F, 0x03, 0xE0, 0x07, 0xC0, 0xF8,
  0x01, 0xE0, 0x3C, 0x00, 0xF8, 0x0F, 0x00, 0x3E, 0x1F, 0xF8, 0x3F, 0xEF,
  0xFE, 0x1F, 0xFF, 0xFF, 0x87, 0xFF, 0xFF, 0xE1, 0xFF, 0xFF, 0xF0, 0x3F,
  0xE0, 0x00, 0x07, 0xE0, 0x00, 0x0F, 0xC0, 0x00, 0x1F, 0x80, 0x00, 0x3E,
  0x00, 0x00, 0x7C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x01, 0xFF, 0xC0, 0x07, 0xFF, 0x80, 0x0F, 0xFE, 0x00, 0x1F, 0xFC, 0x00,
  0x3F, 0xF8, 0x00, 0x01, 0xF0, 0x00, 0x03, 0xC0, 0x00, 0x07, 0x80, 0x00,
  0x1F, 0x00, 0x00, 0x3E, 0x00, 0x00, 0x7C, 0x00, 0x00, 0xF0, 0x00, 0x01,
  0xE0, 0x00, 0x07, 0xC0, 0x00, 0x0F, 0x80, 0x00, 0x1F, 0x00, 0x3F, 0xFF,
  0xFC, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xE7, 0xFF, 0xFF,
  0x80, 0x00, 0x00, 0x7C, 0x00, 0x00, 0xF8, 0x00, 0x03, 0xF0, 0x00, 0x07,
  0xE0, 0x00, 0x0F, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
  0x01, 0xFF, 0xFE, 0x07, 0xFF, 0xFC, 0x0F, 0xFF, 0xF8, 0x1F, 0xFF, 0xF0,
  0x3F, 0xFF, 0xC0, 0x00, 0x07, 0x80, 0x00, 0x1F, 0x00, 0x00, 0x3E, 0x00,
  0x00, 0x7C, 0x00, 0x00, 0xF0, 0x00, 0x01, 0xE0, 0x00, 0x07, 0xC0, 0x00,
  0x0F, 0x80, 0x00, 0x1F, 0x00, 0x00, 0x3C, 0x00, 0x00, 0xF8, 0x00, 0x01,
  0xF0, 0x00, 0x03, 0xE0, 0x00, 0x07, 0x80, 0x00, 0x0F, 0x00, 0x00, 0x3E,
  0x00, 0x00, 0x7C, 0x00, 0x00, 0xF8, 0x00, 0x03, 0xE0, 0x00, 0x0F, 0xC0,
  0xFF, 0xFF, 0x03, 0xFF, 0xFC, 0x07, 0xFF, 0xF0, 0x0F, 0xFF, 0xC0, 0x0F,
  0xFC, 0x00, 0x00, 0x03, 0xFC, 0x00, 0x00, 0xFF, 0x00, 0x00, 0x7F, 0xC0,
  0x00, 0x1F, 0xF0, 0x00, 0x03, 0xF8, 0x00, 0x00, 0x3E, 0x00, 0x00, 0x0F,
  0x80, 0x00, 0x03, 0xE0, 0x00, 0x00, 0xF8, 0x00, 0x00, 0x3C, 0x3F, 0xF0,
  0x1F, 0x1F, 0xFC, 0x07, 0xC7, 0xFF, 0x01, 0xF1, 0xFF, 0xC0, 0x78, 0x7F,
  0xE0, 0x1E, 0x7F, 0x80, 0x0F, 0xBF, 0x80, 0x03, 0xFF, 0xC0, 0x00, 0xFF,
  0xC0, 0x00, 0x3F, 0xE0, 0x00, 0x0F, 0xFC, 0x00, 0x07, 0xFF, 0x80, 0x01,
  0xF7, 0xF0, 0x00, 0x7C, 0xFE, 0x00, 0x1E, 0x1F, 0xC0, 0x0F, 0x83, 0xF8,
  0x1F, 0xE0, 0xFF, 0xEF, 0xF8, 0x3F, 0xFB, 0xFE, 0x1F, 0xFE, 0xFF, 0x07,
  0xFF, 0x9F, 0xC0, 0xFF, 0xC0, 0x00, 0x7F, 0xF0, 0x01, 0xFF, 0xC0, 0x03,
  0xFF, 0x80, 0x07, 0xFF, 0x00, 0x0F, 0xFE, 0x00, 0x00, 0x7C, 0x00, 0x00,
  0xF0, 0x00, 0x03, 0xE0, 0x00, 0x07, 0xC0, 0x00, 0x0F, 0x80, 0x00, 0x1F,
  0x00, 0x00, 0x3C, 0x00, 0x00, 0xF8, 0x00, 0x01, 0xF0, 0x00, 0x03, 0xE0,
  0x00, 0x07, 0x80, 0x00, 0x0F, 0x00, 0x00, 0x3E, 0x00, 0x00, 0x7C, 0x00,
  0x00, 0xF8, 0x00, 0x01, 0xE0, 0x00, 0x03, 0xC0, 0x00, 0x0F, 0x80, 0x00,
  0x1F, 0x00, 0x00, 0x3E, 0x00, 0x7F, 0xFF, 0xF9, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xCF, 0xFF, 0xFF, 0x00, 0x00, 0x07, 0x81, 0xE0,
  0x3F, 0xBF, 0x9F, 0xE1, 0xFF, 0xFE, 0xFF, 0x87, 0xFF, 0xFF, 0xFF, 0x1F,
  0xFF, 0xFF, 0xFC, 0x7F, 0xC7, 0xF1, 0xF0, 0x7E, 0x1F, 0x87, 0xC1, 0xF0,
  0x7C, 0x1F, 0x07, 0x81, 0xE0, 0x7C, 0x1E, 0x0F, 0x81, 0xE0, 0xF8, 0x3E,
  0x0F, 0x83, 0xE0, 0xF8, 0x3E, 0x0F, 0x83, 0xE0, 0xF8, 0x3C, 0x0F, 0x03,
  0xC1, 0xF0, 0x7C, 0x0F, 0x07, 0xC1, 0xF0, 0x7C, 0x1F, 0x07, 0xC1, 0xF1,
  0xFE, 0x1F, 0x87, 0xEF, 0xFC, 0x7F, 0x1F, 0xFF, 0xF3, 0xFC, 0x7F, 0xFF,
  0xCF, 0xF3, 0xFF, 0xFE, 0x3F, 0x8F, 0xE0, 0x00, 0x01, 0xF8, 0x01, 0xF9,
  0xFF, 0x80, 0xFE, 0xFF, 0xF0, 0x7F, 0xFF, 0xFC, 0x1F, 0xFF, 0xFF, 0x83,
  0xFF, 0x07, 0xE0, 0x3F, 0x00, 0xF8, 0x1F, 0x80, 0x3E, 0x07, 0xC0, 0x0F,
  0x81, 0xF0, 0x03, 0xC0, 0x7C, 0x00, 0xF0, 0x1E, 0x00, 0x7C, 0x0F, 0x80,
  0x1F, 0x03, 0xE0, 0x07, 0xC0, 0xF8, 0x01, 0xE0, 0x3C, 0x00, 0xF8, 0x0F,
  0x00, 0x3E, 0x1F, 0xF8, 0x3F, 0xEF, 0xFE, 0x1F, 0xFF, 0xFF, 0x87, 0xFF,
  0xFF, 0xE1, 0xFF, 0xFF, 0xF0, 0x3F, 0xE0, 0x00, 0x1F, 0xC0, 0x00, 0x7F,
  0xFC, 0x00, 0x7F, 0xFF, 0x00, 0xFF, 0xFF, 0xC0, 0xFF, 0xFF, 0xF0, 0xFF,
  0x03, 0xF8, 0xFE, 0x00, 0xFE, 0x7C, 0x00, 0x3F, 0x7C, 0x00, 0x0F, 0xBE,
  0x00, 0x07, 0xFE, 0x00, 0x03, 0xFF, 0x00, 0x01, 0xFF, 0x80, 0x00, 0xFF,
  0xC0, 0x00, 0xFB, 0xE0, 0x00, 0xFD, 0xF8, 0x00, 0x7C, 0xFE, 0x00, 0xFE,
  0x3F, 0x81, 0xFE, 0x1F, 0xFF, 0xFE, 0x07, 0xFF, 0xFE, 0x01, 0xFF, 0xFC,
  0x00, 0x7F, 0xFC, 0x00, 0x07, 0xF0, 0x00, 0x00, 0x00, 0x3F, 0x80, 0x07,
  0xF9, 0xFF, 0xC0, 0x1F, 0xF7, 0xFF, 0xC0, 0x3F, 0xFF, 0xFF, 0xC0, 0x7F,
  0xFF, 0xFF, 0xC0, 0x7F, 0xF0, 0x3F, 0x80, 0x3F, 0x80, 0x1F, 0x80, 0x7E,
  0x00, 0x3F, 0x00, 0xF8, 0x00, 0x3E, 0x01, 0xF0, 0x00, 0x7C, 0x03, 0xC0,
  0x00, 0xF8, 0x0F, 0x80, 0x01, 0xF0, 0x1F, 0x00, 0x07, 0xE0, 0x3E, 0x00,
  0x0F, 0x80, 0x7C, 0x00, 0x3F, 0x01, 0xFC, 0x00, 0xFC, 0x03, 0xFE, 0x07,
  0xF8, 0x07, 0xFF, 0xFF, 0xE0, 0x0F, 0xFF, 0xFF, 0x80, 0x1E, 0xFF, 0xFC,
  0x00, 0x7C, 0xFF, 0xF0, 0x00, 0xF8, 0x7F, 0x00, 0x01, 0xF0, 0x00, 0x00,
  0x03, 0xE0, 0x00, 0x00, 0x07, 0x80, 0x00, 0x00, 0x1F, 0x00, 0x00, 0x01,
  0xFF, 0xF0, 0x00, 0x07, 0xFF, 0xE0, 0x00, 0x0F, 0xFF, 0xC0, 0x00, 0x1F,
  0xFF, 0x80, 0x00, 0x3F, 0xFE, 0x00, 0x00, 0x00, 0x00, 0x3F, 0x80, 0x00,
  0x07, 0xFF, 0x3F, 0xC0, 0xFF, 0xFD, 0xFE, 0x0F, 0xFF, 0xFF, 0xF0, 0xFF,
  0xFF, 0xFF, 0x8F, 0xE0, 0x7F, 0xF8, 0xFC, 0x00, 0xFE, 0x07, 0xC0, 0x03,
  0xE0, 0x7C, 0x00, 0x1F, 0x03, 0xE0, 0x00, 0xF8, 0x1E, 0x00, 0x07, 0xC1,
  0xF0, 0x00, 0x3E, 0x0F, 0x80, 0x01, 0xE0, 0x7C, 0x00, 0x1F, 0x03, 0xF0,
  0x01, 0xF8, 0x1F, 0x80, 0x1F, 0xC0, 0xFF, 0x03, 0xFC, 0x03, 0xFF, 0xFF,
  0xE0, 0x1F, 0xFF, 0xFF, 0x00, 0x7F, 0xFF, 0xF8, 0x00, 0xFF, 0xE7, 0xC0,
  0x01, 0xFC, 0x3C, 0x00, 0x00, 0x01, 0xE0, 0x00, 0x00, 0x1F, 0x00, 0x00,
  0x00, 0xF8, 0x00, 0x00, 0x07, 0xC0, 0x00, 0x07, 0xFF, 0x80, 0x00, 0x7F,
  0xFE, 0x00, 0x07, 0xFF, 0xF0, 0x00, 0x3F, 0xFF, 0x00, 0x00, 0xFF, 0xF0,
  0x00, 0x00, 0x00, 0x0F, 0x80, 0x3F, 0xC3, 0xFE, 0x07, 0xFC, 0xFF, 0xE0,
  0x7F, 0xDF, 0xFF, 0x07, 0xFF, 0xFF, 0xE0, 0x7F, 0xFF, 0x1C, 0x00, 0x7F,
  0xC0, 0x00, 0x07, 0xF0, 0x00, 0x00, 0xFE, 0x00, 0x00, 0x0F, 0xC0, 0x00,
  0x00, 0xF8, 0x00, 0x00, 0x0F, 0x00, 0x00, 0x01, 0xF0, 0x00, 0x00, 0x1F,
  0x00, 0x00, 0x01, 0xF0, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x01, 0xE0, 0x00,
  0x07, 0xFF, 0xFF, 0x00, 0xFF, 0xFF, 0xF0, 0x0F, 0xFF, 0xFF, 0x00, 0xFF,
  0xFF, 0xF0, 0x07, 0xFF, 0xFE, 0x00, 0x00, 0x3F, 0xCE, 0x03, 0xFF, 0xFC,
  0x0F, 0xFF, 0xF8, 0x3F, 0xFF, 0xF0, 0xFF, 0xFF, 0xC3, 0xF8, 0x0F, 0x87,
  0xC0, 0x0E, 0x0F, 0x80, 0x00, 0x1F, 0xF0, 0x00, 0x3F, 0xFF, 0x80, 0x3F,
  0xFF, 0xC0, 0x3F, 0xFF, 0xC0, 0x1F, 0xFF, 0xC0, 0x01, 0xFF, 0x80, 0x00,
  0x3F, 0x1C, 0x00, 0x3E, 0x7C, 0x00, 0x7C, 0xFC, 0x03, 0xF3, 0xFF, 0xFF,
  0xE7, 0xFF, 0xFF, 0x8F, 0xFF, 0xFE, 0x1F, 0xFF, 0xF0, 0x00, 0xFF, 0x00,
  0x00, 0x03, 0xC0, 0x00, 0x7C, 0x00, 0x07, 0xC0, 0x00, 0x7C, 0x00, 0x07,
  0x80, 0x00, 0x78, 0x00, 0x7F, 0xFF, 0xEF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
  0xFF, 0xFF, 0xFF, 0xFF, 0xE1, 0xF0, 0x00, 0x1F, 0x00, 0x01, 0xE0, 0x00,
  0x1E, 0x00, 0x03, 0xE0, 0x00, 0x3E, 0x00, 0x03, 0xE0, 0x00, 0x3C, 0x00,
  0x07, 0xC0, 0x00, 0x7C, 0x00, 0x07, 0xC0, 0x00, 0x7E, 0x00, 0xF7, 0xFF,
  0xFF, 0x7F, 0xFF, 0xF3, 0xFF, 0xFE, 0x1F, 0xFF, 0x80, 0x7F, 0x80, 0x7F,
  0x01, 0xFF, 0xFE, 0x07, 0xFF, 0xF8, 0x1F, 0xFF, 0xF0, 0x3F, 0xFF, 0xE0,
  0x3F, 0xC7, 0xC0, 0x07, 0x8F, 0x80, 0x1F, 0x3E, 0x00, 0x3E, 0x7C, 0x00,
  0x7C, 0xF8, 0x00, 0xF1, 0xF0, 0x03, 0xE3, 0xE0, 0x07, 0xC7, 0xC0, 0x0F,
  0x8F, 0x80, 0x1F, 0x1F, 0x00, 0x7C, 0x3E, 0x01, 0xF8, 0x7E, 0x0F, 0xFC,
  0xFF, 0xFF, 0xF8, 0xFF, 0xFF, 0xF1, 0xFF, 0xEF, 0xE1, 0xFF, 0xBF, 0x80,
  0xFC, 0x00, 0x00, 0x7F, 0xF0, 0x7F, 0xFF, 0xFF, 0x0F, 0xFF, 0xFF, 0xF0,
  0xFF, 0xFF, 0xFF, 0x0F, 0xFF, 0xFF, 0xE0, 0xFF, 0xE1, 0xF8, 0x03, 0xE0,
  0x0F, 0x80, 0x3E, 0x00, 0xF8, 0x07, 0xC0, 0x0F, 0x80, 0xF8, 0x00, 0xFC,
  0x1F, 0x80, 0x07, 0xC1, 0xF0, 0x00, 0x7C, 0x3E, 0x00, 0x07, 0xE7, 0xE0,
  0x00, 0x3E, 0x7C, 0x00, 0x03, 0xEF, 0x80, 0x00, 0x3F, 0xF0, 0x00, 0x03,
  0xFF, 0x00, 0x00, 0x1F, 0xE0, 0x00, 0x01, 0xFC, 0x00, 0x00, 0x1F, 0xC0,
  0x00, 0x00, 0xF8, 0x00, 0x00, 0x7F, 0xC0, 0x1F, 0xEF, 0xFC, 0x03, 0xFF,
  0xFF, 0xC0, 0x7F, 0xFF, 0xFC, 0x07, 0xFE, 0x7F, 0x80, 0x3F, 0xC3, 0xE1,
  0xF0, 0xF8, 0x3E, 0x3F, 0x0F, 0x03, 0xE3, 0xF1, 0xF0, 0x3E, 0x7F, 0x1E,
  0x03, 0xE7, 0xF3, 0xE0, 0x3E, 0xFF, 0xBC, 0x03, 0xFF, 0xFF, 0xC0, 0x3F,
  0xFF, 0xFC, 0x03, 0xFE, 0xFF, 0x80, 0x3F, 0xEF, 0xF8, 0x03, 0xFC, 0xFF,
  0x00, 0x3F, 0x8F, 0xF0, 0x03, 0xF8, 0x7E, 0x00, 0x3F, 0x07, 0xE0, 0x01,
  0xF0, 0x7C, 0x00, 0x1E, 0x07, 0xC0, 0x00, 0x03, 0xFE, 0x0F, 0xF8, 0x3F,
  0xF0, 0xFF, 0xC1, 0xFF, 0x8F, 0xFE, 0x0F, 0xFC, 0x7F, 0xF0, 0x7F, 0xC1,
  0xFF, 0x00, 0xFE, 0x1F, 0xC0, 0x03, 0xF9, 0xFC, 0x00, 0x0F, 0xFF, 0xC0,
  0x00, 0x3F, 0xF8, 0x00, 0x00, 0xFF, 0x80, 0x00, 0x07, 0xF8, 0x00, 0x00,
  0x7F, 0xE0, 0x00, 0x0F, 0xFF, 0x80, 0x00, 0xFE, 0xFE, 0x00, 0x0F, 0xE3,
  0xF8, 0x00, 0xFE, 0x0F, 0xE0, 0x3F, 0xE0, 0x7F, 0xC3, 0xFF, 0x87, 0xFF,
  0x3F, 0xFC, 0x7F, 0xF9, 0xFF, 0xE3, 0xFF, 0x87, 0xFE, 0x0F, 0xF8, 0x00,
  0x01, 0xFE, 0x03, 0xFE, 0x03, 0xFF, 0x07, 0xFF, 0x07, 0xFF, 0x07, 0xFF,
  0x07, 0xFF, 0x07, 0xFE, 0x03, 0xFC, 0x03, 0xFC, 0x01, 0xF8, 0x01, 0xF0,
  0x00, 0xF8, 0x03, 0xF0, 0x00, 0xF8, 0x03, 0xE0, 0x00, 0xFC, 0x07, 0xC0,
  0x00, 0x7C, 0x0F, 0x80, 0x00, 0x7C, 0x0F, 0x80, 0x00, 0x7E, 0x1F, 0x00,
  0x00, 0x7E, 0x3E, 0x00, 0x00, 0x3E, 0x7C, 0x00, 0x00, 0x3E, 0x7C, 0x00,
  0x00, 0x3F, 0xF8, 0x00, 0x00, 0x1F, 0xF0, 0x00, 0x00, 0x1F, 0xE0, 0x00,
  0x00, 0x1F, 0xC0, 0x00, 0x00, 0x0F, 0xC0, 0x00, 0x00, 0x0F, 0x80, 0x00,
  0x00, 0x1F, 0x00, 0x00, 0x00, 0x1E, 0x00, 0x00, 0x00, 0x3E, 0x00, 0x00,
  0x00, 0x7C, 0x00, 0x00, 0x7F, 0xFF, 0x00, 0x00, 0xFF, 0xFF, 0x00, 0x00,
  0xFF, 0xFF, 0x00, 0x00, 0xFF, 0xFF, 0x00, 0x00, 0x7F, 0xFE, 0x00, 0x00,
  0x07, 0xFF, 0xFF, 0x83, 0xFF, 0xFF, 0x81, 0xFF, 0xFF, 0xC1, 0xFF, 0xFF,
  0xE0, 0xFF, 0xFF, 0xE0, 0x7C, 0x0F, 0xE0, 0x3C, 0x0F, 0xE0, 0x1E, 0x0F,
  0xC0, 0x00, 0x1F, 0xC0, 0x00, 0x1F, 0xC0, 0x00, 0x1F, 0xC0, 0x00, 0x1F,
  0x80, 0x00, 0x3F, 0x80, 0x00, 0x3F, 0x80, 0x00, 0x3F, 0x80, 0xF0, 0x3F,
  0x00, 0xF8, 0x3F, 0xFF, 0xFC, 0x3F, 0xFF, 0xFE, 0x1F, 0xFF, 0xFE, 0x0F,
  0xFF, 0xFF, 0x0F, 0xFF, 0xFF, 0x80, 0x00, 0x0F, 0x00, 0x1F, 0xC0, 0x1F,
  0xE0, 0x1F, 0xF0, 0x0F, 0xE0, 0x0F, 0xC0, 0x07, 0xC0, 0x07, 0xC0, 0x03,
  0xE0, 0x01, 0xF0, 0x00, 0xF8, 0x00, 0x78, 0x00, 0x7C, 0x00, 0x3E, 0x00,
  0x1F, 0x00, 0x1F, 0x00, 0x0F, 0x80, 0x3F, 0x80, 0x3F, 0xC0, 0x1F, 0xC0,
  0x0F, 0xE0, 0x07, 0xF8, 0x00, 0xFC, 0x00, 0x3E, 0x00, 0x1F, 0x00, 0x0F,
  0x80, 0x07, 0x80, 0x07, 0xC0, 0x03, 0xE0, 0x01, 0xF0, 0x00, 0xF8, 0x00,
  0x7E, 0x00, 0x3F, 0x80, 0x1F, 0xE0, 0x07, 0xF0, 0x03, 0xF8, 0x00, 0x78,
  0x00, 0x01, 0xE0, 0x3C, 0x0F, 0x81, 0xF0, 0x3E, 0x07, 0x80, 0xF0, 0x3E,
  0x07, 0xC0, 0xF0, 0x1E, 0x03, 0xC0, 0xF8, 0x1F, 0x03, 0xC0, 0x78, 0x0F,
  0x03, 0xE0, 0x7C, 0x0F, 0x01, 0xE0, 0x3C, 0x0F, 0x81, 0xF0, 0x3C, 0x07,
  0x80, 0xF0, 0x3E, 0x07, 0xC0, 0xF0, 0x1E, 0x07, 0xC0, 0xF8, 0x1F, 0x03,
  0xC0, 0x70, 0x00, 0x00, 0xF0, 0x00, 0xFC, 0x00, 0x7F, 0x00, 0x3F, 0xC0,
  0x0F, 0xE0, 0x03, 0xF0, 0x00, 0xF8, 0x00, 0x7C, 0x00, 0x3E, 0x00, 0x1F,
  0x00, 0x0F, 0x80, 0x07, 0x80, 0x03, 0xC0, 0x03, 0xE0, 0x01, 0xF0, 0x00,
  0xF8, 0x00, 0x7E, 0x00, 0x3F, 0xC0, 0x0F, 0xE0, 0x07, 0xF0, 0x07, 0xF8,
  0x07, 0xF8, 0x03, 0xE0, 0x03, 0xE0, 0x01, 0xF0, 0x00, 0xF0, 0x00, 0x78,
  0x00, 0x7C, 0x00, 0x3E, 0x00, 0x1F, 0x00, 0x0F, 0x00, 0x1F, 0x80, 0x7F,
  0xC0, 0x7F, 0xC0, 0x3F, 0xC0, 0x1F, 0xC0, 0x07, 0x80, 0x00, 0x03, 0xE0,
  0x00, 0x1F, 0xE0, 0x00, 0x7F, 0xE0, 0x39, 0xFF, 0xE0, 0xF7, 0xFF, 0xE7,
  0xFF, 0xCF, 0xFF, 0xFE, 0x0F, 0xFF, 0x38, 0x0F, 0xFC, 0x00, 0x0F, 0xE0,
  0x00, 0x0F, 0x80 };

const GFXglyph FreeMonoBoldOblique24pt7bGlyphs[] PROGMEM = {
  {     0,   0,   0,  28,    0,    1 },   // 0x20 ' '
  {     0,  12,  31,  28,   12,  -29 },   // 0x21 '!'
  {    47,  17,  14,  28,   11,  -28 },   // 0x22 '"'
  {    77,  24,  34,  28,    5,  -30 },   // 0x23 '#'
  {   179,  25,  38,  28,    4,  -31 },   // 0x24 '$'
  {   298,  22,  30,  28,    6,  -28 },   // 0x25 '%'
  {   381,  21,  28,  28,    5,  -26 },   // 0x26 '&'
  {   455,   7,  14,  28,   16,  -28 },   // 0x27 '''
  {   468,  14,  37,  28,   14,  -29 },   // 0x28 '('
  {   533,  14,  37,  28,    5,  -29 },   // 0x29 ')'
  {   598,  21,  19,  28,    8,  -28 },   // 0x2A '*'
  {   648,  24,  26,  28,    5,  -25 },   // 0x2B '+'
  {   726,  12,  14,  28,    6,   -6 },   // 0x2C ','
  {   747,  24,   5,  28,    5,  -15 },   // 0x2D '-'
  {   762,   7,   6,  28,   11,   -4 },   // 0x2E '.'
  {   768,  28,  38,  28,    3,  -32 },   // 0x2F '/'
  {   901,  23,  31,  28,    6,  -29 },   // 0x30 '0'
  {   991,  21,  30,  28,    4,  -29 },   // 0x31 '1'
  {  1070,  26,  30,  28,    3,  -29 },   // 0x32 '2'
  {  1168,  25,  31,  28,    4,  -29 },   // 0x33 '3'
  {  1265,  22,  28,  28,    5,  -27 },   // 0x34 '4'
  {  1342,  25,  31,  28,    4,  -29 },   // 0x35 '5'
  {  1439,  24,  31,  28,    7,  -29 },   // 0x36 '6'
  {  1532,  22,  30,  28,    9,  -29 },   // 0x37 '7'
  {  1615,  23,  31,  28,    6,  -29 },   // 0x38 '8'
  {  1705,  24,  31,  28,    5,  -29 },   // 0x39 '9'
  {  1798,  10,  22,  28,   11,  -20 },   // 0x3A ':'
  {  1826,  15,  28,  28,    5,  -20 },   // 0x3B ';'
  {  1879,  25,  21,  28,    5,  -23 },   // 0x3C '<'
  {  1945,  26,  14,  28,    4,  -19 },   // 0x3D '='
  {  1991,  25,  22,  28,    4,  -23 },   // 0x3E '>'
  {  2060,  19,  29,  28,   10,  -27 },   // 0x3F '?'
  {  2129,  23,  36,  28,    5,  -28 },   // 0x40 '@'
  {  2233,  30,  27,  28,    0,  -26 },   // 0x41 'A'
  {  2335,  29,  27,  28,    1,  -26 },   // 0x42 'B'
  {  2433,  28,  29,  28,    3,  -27 },   // 0x43 'C'
  {  2535,  28,  27,  28,    1,  -26 },   // 0x44 'D'
  {  2630,  29,  27,  28,    1,  -26 },   // 0x45 'E'
  {  2728,  31,  27,  28,    0,  -26 },   // 0x46 'F'
  {  2833,  28,  29,  28,    3,  -27 },   // 0x47 'G'
  {  2935,  30,  27,  28,    1,  -26 },   // 0x48 'H'
  {  3037,  25,  27,  28,    3,  -26 },   // 0x49 'I'
  {  3122,  31,  28,  28,    0,  -26 },   // 0x4A 'J'
  {  3231,  31,  27,  28,    0,  -26 },   // 0x4B 'K'
  {  3336,  27,  27,  28,    1,  -26 },   // 0x4C 'L'
  {  3428,  34,  27,  28,    0,  -26 },   // 0x4D 'M'
  {  3543,  32,  27,  28,    1,  -26 },   // 0x4E 'N'
  {  3651,  27,  29,  28,    3,  -27 },   // 0x4F 'O'
  {  3749,  28,  27,  28,    1,  -26 },   // 0x50 'P'
  {  3844,  27,  35,  28,    3,  -27 },   // 0x51 'Q'
  {  3963,  29,  27,  28,    0,  -26 },   // 0x52 'R'
  {  4061,  26,  29,  28,    3,  -27 },   // 0x53 'S'
  {  4156,  26,  27,  28,    4,  -26 },   // 0x54 'T'
  {  4244,  28,  28,  28,    4,  -26 },   // 0x55 'U'
  {  4342,  30,  27,  28,    2,  -26 },   // 0x56 'V'
  {  4444,  29,  27,  28,    3,  -26 },   // 0x57 'W'
  {  4542,  32,  27,  28,    0,  -26 },   // 0x58 'X'
  {  4650,  26,  27,  28,    4,  -26 },   // 0x59 'Y'
  {  4738,  27,  27,  28,    2,  -26 },   // 0x5A 'Z'
  {  4830,  18,  37,  28,   10,  -29 },   // 0x5B '['
  {  4914,  13,  38,  28,   10,  -32 },   // 0x5C '\'
  {  4976,  18,  37,  28,    5,  -29 },   // 0x5D ']'
  {  5060,  20,  15,  28,    8,  -29 },   // 0x5E '^'
  {  5098,  29,   5,  28,   -2,    5 },   // 0x5F '_'
  {  5117,   8,   8,  28,   13,  -30 },   // 0x60 '`'
  {  5125,  24,  23,  28,    3,  -21 },   // 0x61 'a'
  {  5194,  29,  31,  28,    0,  -29 },   // 0x62 'b'
  {  5307,  25,  23,  28,    3,  -21 },   // 0x63 'c'
  {  5379,  28,  31,  28,    3,  -29 },   // 0x64 'd'
  {  5488,  24,  23,  28,    3,  -21 },   // 0x65 'e'
  {  5557,  28,  30,  28,    4,  -29 },   // 0x66 'f'
  {  5662,  28,  31,  28,    3,  -21 },   // 0x67 'g'
  {  5771,  26,  30,  28,    2,  -29 },   // 0x68 'h'
  {  5869,  23,  29,  28,    3,  -28 },   // 0x69 'i'
  {  5953,  23,  38,  28,    3,  -28 },   // 0x6A 'j'
  {  6063,  26,  30,  28,    2,  -29 },   // 0x6B 'k'
  {  6161,  23,  30,  28,    3,  -29 },   // 0x6C 'l'
  {  6248,  30,  22,  28,    0,  -21 },   // 0x6D 'm'
  {  6331,  26,  22,  28,    2,  -21 },   // 0x6E 'n'
  {  6403,  25,  23,  28,    3,  -21 },   // 0x6F 'o'
  {  6475,  31,  31,  28,   -1,  -21 },   // 0x70 'p'
  {  6596,  29,  31,  28,    2,  -21 },   // 0x71 'q'
  {  6709,  28,  22,  28,    2,  -21 },   // 0x72 'r'
  {  6786,  23,  23,  28,    4,  -21 },   // 0x73 's'
  {  6853,  20,  28,  28,    5,  -26 },   // 0x74 't'
  {  6923,  23,  22,  28,    5,  -20 },   // 0x75 'u'
  {  6987,  28,  21,  28,    3,  -20 },   // 0x76 'v'
  {  7061,  28,  21,  28,    3,  -20 },   // 0x77 'w'
  {  7135,  29,  21,  28,    1,  -20 },   // 0x78 'x'
  {  7212,  32,  30,  28,   -1,  -20 },   // 0x79 'y'
  {  7332,  25,  21,  28,    4,  -20 },   // 0x7A 'z'
  {  7398,  17,  37,  28,   10,  -29 },   // 0x7B '{'
  {  7477,  11,  36,  28,   11,  -28 },   // 0x7C '|'
  {  7527,  17,  37,  28,    6,  -29 },   // 0x7D '}'
  {  7606,  23,  10,  28,    5,  -17 } }; // 0x7E '~'

const GFXfont FreeMonoBoldOblique24pt7b PROGMEM = {
  (uint8_t  *)FreeMonoBoldOblique24pt7bBitmaps,
  (GFXglyph *)FreeMonoBoldOblique24pt7bGlyphs,
  0x20, 0x7E, 47 };

// Approx. 8307 bytes
