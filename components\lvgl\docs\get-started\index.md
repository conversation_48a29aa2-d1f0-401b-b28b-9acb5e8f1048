# Get started

There are several ways to get your feet wet with LVGL. Here is one recommended order of documents to read and things to play with when you are learning to use LVGL:
1. Check the [Online demos](https://lvgl.io/demos) to see LVGL in action (3 minutes)
2. Read the [Introduction](https://docs.lvgl.io/latest/en/html/intro/index.html) page of the documentation (5 minutes)
3. Read the [Quick overview](https://docs.lvgl.io/master/get-started/quick-overview.html) page of the documentation (15 minutes)
4. Set up a [Simulator](https://docs.lvgl.io/master/get-started/platforms/pc-simulator.html) (10 minutes)
5. Try out some [Examples](https://docs.lvgl.io/master/examples.html)
6. Check out the Platform-specific tutorials. (in this section below). (10 minutes)
7. Port LVGL to a board. See the [Porting](https://docs.lvgl.io/master/porting/index.html) guide or check the ready to use [Projects](https://github.com/lvgl?q=lv_port_&type=&language=)
8. Read the [Overview](https://docs.lvgl.io/master/overview/index.html) page to get a better understanding of the library. (2-3 hours)
9. Check the documentation of the [Widgets](https://docs.lvgl.io/master/widgets/index.html) to see their features and usage
10. If you have questions got to the [Forum](http://forum.lvgl.io/)
11. Read the [Contributing](https://docs.lvgl.io/master/CONTRIBUTING.html) guide to see how you can help to improve LVGL (15 minutes)


```eval_rst

.. toctree::
   :maxdepth: 2

   quick-overview
   platforms/index
   os/index
   bindings/index
```

