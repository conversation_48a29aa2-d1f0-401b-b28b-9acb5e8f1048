# M5GFX

Graphics library for M5Stack series  

Supported framework  
----------------
- ESP-IDF
- Arduino for ESP32


Supported device  
----------------
- M5Stack ( Basic / Gray / GO / Fire )
- M5Stack Core2
- M5Stack CoreInk
- M5Stick C
- M5Stick C Plus
- M5Stick C Plus2
- M5Paper
- M5Tough
- M5Station
- M5ATOMS3
- UnitOLED
- UnitMiniOLED
- UnitLCD
- [UnitRCA / ModuleRCA](docs/UnitRCA.md)
- [UnitGLASS](docs/UnitGLASS.md)
- UnitGLASS2
- [AtomDisplay](docs/ATOMDisplay.md) / ModuleDisplay


License
----------------
M5GFX : [MIT](LICENSE)  
LovyanGFX : [FreeBSD](https://github.com/lovyan03/LovyanGFX/blob/master/license.txt) lovyan03  
TJpgDec : [original](src/lgfx/utility/lgfx_tjpgd.c) Cha<PERSON>  
Pngle : [MIT](https://github.com/kikuchan/pngle/blob/master/LICENSE) kikuchan  
QRCode : [MIT](https://github.com/ricmoo/QRCode/blob/master/LICENSE.txt) Richard Moore and Nayuki  
result : [MIT](https://github.com/bitwizeshift/result/blob/master/LICENSE) Matthew Rodusek  
GFX font and GLCD font : [2-clause BSD](https://github.com/adafruit/Adafruit-GFX-Library/blob/master/license.txt) Adafruit Industries  
Font 2,4,6,7,8 :  [FreeBSD](https://github.com/Bodmer/TFT_eSPI/blob/master/license.txt) Bodmer  
converted IPA font : [IPA Font License](src/lgfx/Fonts/IPA/IPA_Font_License_Agreement_v1.0.txt) IPA  
efont : [3-clause BSD](src/lgfx/Fonts/efont/COPYRIGHT.txt) The Electronic Font Open Laboratory  
TomThumb font : [3-clause BSD](src/lgfx/Fonts/GFXFF/TomThumb.h) Brian J. Swetland / Vassilii Khachaturov / Dan Marks  


